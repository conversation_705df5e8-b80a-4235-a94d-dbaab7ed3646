{"name": "limob-crm-front-end", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "CRM-dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview --mode production", "preview:dev": "vite preview --mode development", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test:stress": "artillery run tests/artillery/quick-test.yml", "test:stress:light": "artillery run tests/artillery/light-test.yml", "test:stress:peak": "artillery run tests/artillery/peak-test.yml", "test:artillery": "artillery run tests/artillery/quick-test.yml", "test:monitor": "node tests/scripts/monitor.js", "test:report": "node tests/scripts/generate-report.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "jszip": "^3.10.1", "mammoth": "^1.9.1", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "pinia": "^2.1.0", "pptx2json": "^0.0.10", "qrcode.vue": "^3.6.0", "tesseract.js": "^6.0.1", "vue": "^3.4.0", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.3.0", "prettier": "^3.2.5", "terser": "^5.41.0", "vite": "^5.2.0"}}