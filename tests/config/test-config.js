/**
 * CRM System Stress Testing Configuration
 * 
 * Safety Configuration Principles:
 * - Conservative concurrency settings
 * - Short test cycles
 * - Real-time monitoring thresholds
 */

export const testConfig = {
  // Basic configuration
  baseUrl: 'https://invite.limob.cn',
  
  // Test account configuration
  testAccounts: {
    primary: {
      phone: '***********',
      password: '123456' // Please replace with actual password
    }
  },
  
  // Safety limits configuration
  safetyLimits: {
    maxConcurrentUsers: 100,        // Maximum concurrent users
    maxTestDuration: 600,           // Maximum test duration (seconds) - 10 minutes
    maxErrorRate: 0.05,             // Maximum error rate 5%
    maxResponseTime: 2000,          // Maximum response time (milliseconds)
    
    // System resource monitoring thresholds
    cpuThreshold: 80,               // CPU usage threshold (%)
    memoryThreshold: 85,            // Memory usage threshold (%)
    dbConnectionThreshold: 90       // Database connection pool threshold (%)
  },
  
  // Test scenario configuration
  scenarios: {
    // Light test - for initial validation
    light: {
      stages: [
        { duration: '30s', target: 5 },
        { duration: '2m', target: 10 },
        { duration: '30s', target: 0 }
      ]
    },
    
    // Standard test - daily stress testing
    standard: {
      stages: [
        { duration: '1m', target: 20 },
        { duration: '3m', target: 50 },
        { duration: '1m', target: 0 }
      ]
    },
    
    // Peak test - use with caution
    peak: {
      stages: [
        { duration: '1m', target: 30 },
        { duration: '2m', target: 80 },
        { duration: '2m', target: 100 },
        { duration: '1m', target: 0 }
      ]
    }
  },
  
  // API endpoints configuration
  endpoints: {
    auth: {
      login: '/login',
      register: '/register'
    },
    talent: {
      pool: '/kol/public',
      myTalents: '/kol/my-talents',
      claimTalent: '/kol/claim',
      editTalent: '/kol/edit-my-talent'
    },
    leads: {
      list: '/leads/list',
      search: '/leads/search'
    },
    team: {
      create: '/team/create',
      list: '/team/list',
      invite: '/team/invite'
    }
  },
  
  // Test data configuration
  testData: {
    // Pagination parameters
    pagination: {
      defaultPageSize: 20,
      maxPageSize: 50
    },
    
    // Search keywords
    searchKeywords: [
      '美妆', '时尚', '生活', '科技', '美食'
    ],
    
    // Team test data
    teamData: {
      names: ['测试团队A', '测试团队B', '测试团队C'],
      descriptions: ['压力测试团队', '性能测试团队', '功能测试团队']
    }
  },
  
  // Monitoring configuration
  monitoring: {
    // Key metric thresholds
    thresholds: {
      'http_req_duration': ['p(95)<500', 'p(99)<1000'],
      'http_req_failed': ['rate<0.01'],
      'http_reqs': ['rate>50']
    },
    
    // Custom metrics
    customMetrics: [
      'login_success_rate',
      'talent_query_performance',
      'leads_search_performance'
    ]
  },
  
  // Environment configuration
  environment: {
    // Test time windows (avoid business peak hours)
    allowedHours: [0, 1, 2, 3, 4, 5, 22, 23],
    
    // Skip testing on holidays
    skipWeekends: true,
    
    // Emergency stop conditions
    emergencyStop: {
      errorRateThreshold: 0.1,      // Error rate exceeds 10%
      responseTimeThreshold: 3000,   // Response time exceeds 3 seconds
      concurrentErrorsThreshold: 5   // 5 consecutive errors
    }
  }
}

export default testConfig
