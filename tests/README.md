# CRM System Stress Testing Suite

## 🎯 Testing Objectives
Conduct safe production environment stress testing for the CRM system to evaluate system performance under different load conditions.

## 📋 Testing Scope
- **User Authentication**: Login/registration flows
- **Talent Management**: Talent queries, claiming, editing
- **Leads Management**: Lead search, pagination queries
- **Team Management**: Team creation, member invitations
- **WeChat Talents**: WeChat talent data operations

## 🛠️ Testing Tools
- **K6**: Primary stress testing tool
- **Artillery**: Quick validation tool
- **Custom Scripts**: Business scenario simulation

## 📊 Performance Metrics
- **Response Time**: 95% < 500ms, 99% < 1000ms
- **Throughput**: Login TPS > 100, Query TPS > 200
- **Error Rate**: < 1%
- **Concurrent Users**: Maximum support 500 concurrent

## 🚨 Safety Limits
- Maximum Concurrency: 100 users
- Test Duration: Single test not exceeding 10 minutes
- Test Time: Avoid business peak hours (9-18)
- Real-time Monitoring: CPU < 80%, Memory < 85%

## 📁 File Structure
- `k6/`: K6 stress testing scripts
- `artillery/`: Artillery quick test configurations
- `scripts/`: Helper scripts and tools
- `results/`: Test results and reports
- `config/`: Test configuration files

## 🔧 Usage
1. Install testing tools: `npm install -g k6 artillery`
2. Configure test parameters: Edit `config/test-config.js`
3. Run tests: `npm run test:stress`
4. View results: `npm run test:report`

## ⚠️ Important Notes
- Must backup database before testing
- Monitor system resource usage in real-time
- Stop testing immediately if anomalies detected
- Use dedicated test account (***********)
