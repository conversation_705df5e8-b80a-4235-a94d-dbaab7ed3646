/**
 * CRM System Real-time Monitoring Script
 * 
 * Features:
 * - Real-time system resource usage monitoring
 * - Detect abnormal metrics and issue warnings
 * - Automatic test stopping safety mechanism
 * - Generate monitoring reports
 */

import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs/promises'
import path from 'path'

const execAsync = promisify(exec)

class SystemMonitor {
  constructor(config = {}) {
    this.config = {
      checkInterval: 5000,        // Check interval (milliseconds)
      cpuThreshold: 80,          // CPU threshold (%)
      memoryThreshold: 85,       // Memory threshold (%)
      alertCount: 0,             // Alert count
      maxAlerts: 3,              // Maximum alert count
      logFile: 'monitor.log',    // Log file
      ...config
    }
    
    this.isMonitoring = false
    this.alerts = []
    this.metrics = []
  }

  /**
   * Start monitoring
   */
  async startMonitoring() {
    console.log('🔍 Starting system monitoring...')
    this.isMonitoring = true
    
    while (this.isMonitoring) {
      try {
        const metrics = await this.collectMetrics()
        this.metrics.push(metrics)
        
        // Check thresholds
        this.checkThresholds(metrics)
        
        // Display current status
        this.displayStatus(metrics)
        
        // Wait for next check
        await this.sleep(this.config.checkInterval)
        
      } catch (error) {
        console.error('❌ Error during monitoring:', error.message)
        await this.sleep(this.config.checkInterval)
      }
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    console.log('🛑 Stopping system monitoring')
    this.isMonitoring = false
  }

  /**
   * Collect system metrics
   */
  async collectMetrics() {
    const timestamp = new Date().toISOString()
    
    // Get CPU usage
    const cpuUsage = await this.getCpuUsage()
    
    // Get memory usage
    const memoryUsage = await this.getMemoryUsage()
    
    // Get network connections
    const networkConnections = await this.getNetworkConnections()
    
    return {
      timestamp,
      cpu: cpuUsage,
      memory: memoryUsage,
      network: networkConnections
    }
  }

  /**
   * Get CPU usage
   */
  async getCpuUsage() {
    try {
      // Windows system
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('wmic cpu get loadpercentage /value')
        const match = stdout.match(/LoadPercentage=(\d+)/)
        return match ? parseInt(match[1]) : 0
      }
      
      // Linux/Mac system
      const { stdout } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1")
      return parseFloat(stdout.trim()) || 0
      
    } catch (error) {
      console.warn('⚠️  Unable to get CPU usage:', error.message)
      return 0
    }
  }

  /**
   * Get memory usage
   */
  async getMemoryUsage() {
    try {
      // Windows system
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value')
        const totalMatch = stdout.match(/TotalVisibleMemorySize=(\d+)/)
        const freeMatch = stdout.match(/FreePhysicalMemory=(\d+)/)
        
        if (totalMatch && freeMatch) {
          const total = parseInt(totalMatch[1])
          const free = parseInt(freeMatch[1])
          const used = total - free
          return Math.round((used / total) * 100)
        }
        return 0
      }
      
      // Linux/Mac system
      const { stdout } = await execAsync("free | grep Mem | awk '{printf(\"%.1f\"), $3/$2 * 100.0}'")
      return parseFloat(stdout.trim()) || 0
      
    } catch (error) {
      console.warn('⚠️  Unable to get memory usage:', error.message)
      return 0
    }
  }

  /**
   * Get network connections
   */
  async getNetworkConnections() {
    try {
      // Windows system
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('netstat -an | find "ESTABLISHED" /c')
        return parseInt(stdout.trim()) || 0
      }
      
      // Linux/Mac system
      const { stdout } = await execAsync('netstat -an | grep ESTABLISHED | wc -l')
      return parseInt(stdout.trim()) || 0
      
    } catch (error) {
      console.warn('⚠️  Unable to get network connections:', error.message)
      return 0
    }
  }

  /**
   * Check thresholds and issue warnings
   */
  checkThresholds(metrics) {
    const alerts = []
    
    // Check CPU usage
    if (metrics.cpu > this.config.cpuThreshold) {
      alerts.push({
        type: 'CPU',
        value: metrics.cpu,
        threshold: this.config.cpuThreshold,
        message: `CPU usage too high: ${metrics.cpu}% (threshold: ${this.config.cpuThreshold}%)`
      })
    }
    
    // Check memory usage
    if (metrics.memory > this.config.memoryThreshold) {
      alerts.push({
        type: 'MEMORY',
        value: metrics.memory,
        threshold: this.config.memoryThreshold,
        message: `Memory usage too high: ${metrics.memory}% (threshold: ${this.config.memoryThreshold}%)`
      })
    }
    
    // Handle alerts
    if (alerts.length > 0) {
      this.handleAlerts(alerts)
    }
  }

  /**
   * Handle alerts
   */
  handleAlerts(alerts) {
    alerts.forEach(alert => {
      console.log(`🚨 ${alert.message}`)
      this.alerts.push({
        ...alert,
        timestamp: new Date().toISOString()
      })
    })
    
    this.config.alertCount += alerts.length
    
    // Check if emergency stop is needed
    if (this.config.alertCount >= this.config.maxAlerts) {
      console.log('🛑 Too many alerts, recommend stopping test immediately!')
      this.emergencyStop()
    }
  }

  /**
   * Emergency stop
   */
  emergencyStop() {
    console.log('🚨 Executing emergency stop procedure...')
    
    // Stop monitoring
    this.stopMonitoring()
    
    // Try to stop K6 process
    try {
      if (process.platform === 'win32') {
        exec('taskkill /f /im k6.exe')
      } else {
        exec('pkill -f k6')
      }
    } catch (error) {
      console.error('Failed to stop K6 process:', error.message)
    }
    
    // Generate emergency report
    this.generateEmergencyReport()
  }

  /**
   * Display current status
   */
  displayStatus(metrics) {
    const status = `[${metrics.timestamp.split('T')[1].split('.')[0]}] CPU: ${metrics.cpu}% | Memory: ${metrics.memory}% | Connections: ${metrics.network}`
    
    // Use different colors based on status
    if (metrics.cpu > this.config.cpuThreshold || metrics.memory > this.config.memoryThreshold) {
      console.log(`🔴 ${status}`)
    } else if (metrics.cpu > this.config.cpuThreshold * 0.8 || metrics.memory > this.config.memoryThreshold * 0.8) {
      console.log(`🟡 ${status}`)
    } else {
      console.log(`🟢 ${status}`)
    }
  }

  /**
   * Generate monitoring report
   */
  async generateReport() {
    const report = {
      summary: {
        totalChecks: this.metrics.length,
        totalAlerts: this.alerts.length,
        avgCpu: this.calculateAverage(this.metrics.map(m => m.cpu)),
        avgMemory: this.calculateAverage(this.metrics.map(m => m.memory)),
        maxCpu: Math.max(...this.metrics.map(m => m.cpu)),
        maxMemory: Math.max(...this.metrics.map(m => m.memory))
      },
      alerts: this.alerts,
      metrics: this.metrics
    }
    
    const reportPath = path.join('tests/results', `monitor-report-${Date.now()}.json`)
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    
    console.log(`📊 Monitoring report generated: ${reportPath}`)
    return report
  }

  /**
   * Generate emergency report
   */
  async generateEmergencyReport() {
    const report = await this.generateReport()
    console.log('🚨 Emergency situation report:')
    console.log(`   - Total checks: ${report.summary.totalChecks}`)
    console.log(`   - Total alerts: ${report.summary.totalAlerts}`)
    console.log(`   - Max CPU usage: ${report.summary.maxCpu}%`)
    console.log(`   - Max memory usage: ${report.summary.maxMemory}%`)
  }

  /**
   * Calculate average
   */
  calculateAverage(values) {
    return values.length > 0 ? Math.round(values.reduce((a, b) => a + b, 0) / values.length) : 0
  }

  /**
   * Sleep function
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export monitor
export default SystemMonitor

// If running this script directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new SystemMonitor()
  
  // Handle exit signals
  process.on('SIGINT', () => {
    monitor.stopMonitoring()
    process.exit(0)
  })
  
  // Start monitoring
  monitor.startMonitoring()
}
