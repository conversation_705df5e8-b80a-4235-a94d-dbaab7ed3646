#!/bin/bash

echo "🚀 开始CRM系统压力测试"
echo "📊 目标URL: https://invite.limob.cn"

# 检查K6是否安装
if ! command -v k6 &> /dev/null; then
    echo "❌ K6未安装，请运行: npm install -g k6"
    exit 1
fi

echo "✅ K6工具检查通过"

# 获取测试类型参数
TEST_TYPE=${1:-light}
echo "📋 测试类型: $TEST_TYPE"

# 确保结果目录存在
mkdir -p tests/results

# 生成输出文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="tests/results/k6-result-$TIMESTAMP.json"

echo "🎯 开始执行${TEST_TYPE}压力测试..."
echo "📄 结果将保存到: $OUTPUT_FILE"

# 运行K6测试
k6 run --out json="$OUTPUT_FILE" --tag testType="$TEST_TYPE" tests/k6/crm-stress-test.js

if [ $? -eq 0 ]; then
    echo "✅ 测试执行完成"
    echo "📁 详细结果请查看: $OUTPUT_FILE"
else
    echo "❌ 测试执行失败"
fi
