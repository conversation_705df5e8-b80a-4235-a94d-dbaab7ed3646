# CRM System Artillery Light Stress Test Configuration
# Minimal load for initial testing

config:
  target: 'https://invite.limob.cn'
  phases:
    # Very light warm-up
    - duration: 20
      arrivalRate: 1
      name: "Warm-up Phase"
    
    # Light load test
    - duration: 60
      arrivalRate: 3
      name: "Light Load Test"
    
    # Ramp down
    - duration: 20
      arrivalRate: 1
      name: "Ramp Down Phase"
  
  # Default request configuration
  defaults:
    headers:
      Content-Type: 'application/json'
      User-Agent: 'CRM-LightTest/1.0'
  
  # Performance metrics configuration
  ensure:
    p95: 1000  # 95% requests response time < 1000ms
    p99: 2000  # 99% requests response time < 2000ms
    maxErrorRate: 5  # Maximum error rate 5%
  
  # Plugin configuration
  plugins:
    expect: {}
    metrics-by-endpoint: {}

# Test scenario definitions
scenarios:
  # User authentication scenario (50% weight)
  - name: "User Authentication Flow"
    weight: 50
    flow:
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
          expect:
            - statusCode: 200
  
  # Talent management scenario (30% weight)
  - name: "Talent Management Features"
    weight: 30
    flow:
      # Login first to get token
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Query talent pool
      - post:
          url: "/kol/public"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 10
            筛选条件: {}
          expect:
            - statusCode: 200
  
  # Leads query scenario (20% weight)
  - name: "Leads Query Features"
    weight: 20
    flow:
      # Login
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Leads list query
      - post:
          url: "/leads/list"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 10
          expect:
            - statusCode: 200
