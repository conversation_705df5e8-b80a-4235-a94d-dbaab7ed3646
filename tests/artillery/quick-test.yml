# CRM System Artillery Quick Stress Test Configuration
# For quick validation of basic system performance

config:
  target: 'https://invite.limob.cn'
  phases:
    # Warm-up phase
    - duration: 30
      arrivalRate: 2
      name: "Warm-up Phase"
    
    # Light load test
    - duration: 60
      arrivalRate: 5
      name: "Light Load Test"
    
    # Medium load test
    - duration: 120
      arrivalRate: 15
      name: "Medium Load Test"
    
    # Ramp down
    - duration: 30
      arrivalRate: 2
      name: "Ramp Down Phase"
  
  # Default request configuration
  defaults:
    headers:
      Content-Type: 'application/json'
      User-Agent: 'CRM-StressTest/1.0'
  
  # Performance metrics configuration
  ensure:
    p95: 800  # 95% requests response time < 800ms
    p99: 1500 # 99% requests response time < 1500ms
    maxErrorRate: 2  # Maximum error rate 2%
  
  # Plugin configuration
  plugins:
    expect: {}
    metrics-by-endpoint: {}

# Test scenario definitions
scenarios:
  # User authentication scenario (40% weight)
  - name: "User Authentication Flow"
    weight: 40
    flow:
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "{{ $env.TEST_PASSWORD }}"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
            - json: "$.data.用户id"
              as: "userId"
          expect:
            - statusCode: 200
            - hasProperty: "data.访问令牌"
      
      # User profile query after successful login
      - post:
          url: "/user/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
  
  # Talent management scenario (30% weight)
  - name: "Talent Management Features"
    weight: 30
    flow:
      # Login first to get token
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "{{ $env.TEST_PASSWORD }}"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Query talent pool
      - post:
          url: "/kol/public"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 20
            筛选条件: {}
          expect:
            - statusCode: 200
      
      # Query my talents
      - post:
          url: "/kol/my-talents"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 10
          expect:
            - statusCode: 200
  
  # Leads query scenario (20% weight)
  - name: "Leads Query Features"
    weight: 20
    flow:
      # Login
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "{{ $env.TEST_PASSWORD }}"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Leads list query
      - post:
          url: "/leads/list"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 20
            筛选_信息值: "{{ $randomString() }}"
          expect:
            - statusCode: 200
  
  # Team management scenario (10% weight)
  - name: "Team Management Features"
    weight: 10
    flow:
      # Login
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "{{ $env.TEST_PASSWORD }}"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Team list query
      - post:
          url: "/team/list"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 10
          expect:
            - statusCode: 200

# Custom functions
functions:
  randomSearchKeyword:
    - "美妆"
    - "时尚"
    - "生活"
    - "科技"
    - "美食"
