# CRM System Artillery Peak Stress Test Configuration
# High load testing - use with caution!

config:
  target: 'https://invite.limob.cn'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 5
      name: "Warm-up Phase"
    
    # Medium load
    - duration: 120
      arrivalRate: 20
      name: "Medium Load Test"
    
    # Peak load
    - duration: 180
      arrivalRate: 50
      name: "Peak Load Test"
    
    # Ramp down
    - duration: 60
      arrivalRate: 5
      name: "Ramp Down Phase"
  
  # Default request configuration
  defaults:
    headers:
      Content-Type: 'application/json'
      User-Agent: 'CRM-PeakTest/1.0'
  
  # Performance metrics configuration
  ensure:
    p95: 1500  # 95% requests response time < 1500ms
    p99: 3000  # 99% requests response time < 3000ms
    maxErrorRate: 10  # Maximum error rate 10%
  
  # Plugin configuration
  plugins:
    expect: {}
    metrics-by-endpoint: {}

# Test scenario definitions
scenarios:
  # User authentication scenario (40% weight)
  - name: "User Authentication Flow"
    weight: 40
    flow:
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
          expect:
            - statusCode: 200
  
  # Talent management scenario (35% weight)
  - name: "Talent Management Features"
    weight: 35
    flow:
      # Login first to get token
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Query talent pool
      - post:
          url: "/kol/public"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 20
            筛选条件: {}
          expect:
            - statusCode: 200
      
      # Query my talents
      - post:
          url: "/kol/my-talents"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 10
          expect:
            - statusCode: 200
  
  # Leads query scenario (25% weight)
  - name: "Leads Query Features"
    weight: 25
    flow:
      # Login
      - post:
          url: "/login"
          json:
            phone: "15258387413"
            password: "123456"
          capture:
            - json: "$.data.访问令牌"
              as: "authToken"
      
      # Leads list query
      - post:
          url: "/leads/list"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            页码: 1
            每页数量: 20
            筛选_信息值: "美妆"
          expect:
            - statusCode: 200
