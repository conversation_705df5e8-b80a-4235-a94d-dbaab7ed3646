# CRM System Stress Testing - Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Install Required Tools
```bash
# Install K6 and Artillery globally
npm install -g k6 artillery

# Verify installation
k6 version
artillery version
```

### 2. Configure Test Account
Edit `tests/config/test-config.js`:
```javascript
testAccounts: {
  primary: {
    phone: '***********',
    password: 'YOUR_ACTUAL_PASSWORD' // Replace with real password
  }
}
```

### 3. Run Your First Test
```bash
# Light stress test (recommended for first time)
npm run test:stress:light

# Monitor system in another terminal
npm run test:monitor
```

## 📊 Available Test Commands

```bash
# Light test (5-10 users, 3 minutes)
npm run test:stress:light

# Standard test (20-50 users, 5 minutes)  
npm run test:stress

# Peak test (30-100 users, 6 minutes) - Use with caution!
npm run test:stress:peak

# Quick Artillery test
npm run test:artillery

# System monitoring only
npm run test:monitor
```

## 🎯 Test Scenarios Covered

- **User Authentication**: Login flow performance
- **Talent Management**: Talent pool queries, my talents
- **Leads Search**: Lead list queries with filters
- **Team Management**: Team operations

## 📈 Key Metrics to Watch

- **Response Time**: Should be < 500ms for 95% of requests
- **Error Rate**: Should be < 1%
- **CPU Usage**: Should stay < 80%
- **Memory Usage**: Should stay < 85%

## 🚨 Safety Features

- **Automatic Monitoring**: Real-time system resource tracking
- **Emergency Stop**: Ctrl+C for safe exit
- **Conservative Limits**: Maximum 100 concurrent users
- **Time Restrictions**: Avoid business hours (9-18)

## 📁 Results Location

All test results are saved in `tests/results/`:
- `test-report-*.json`: Complete test analysis
- `k6-result-*.json`: Raw K6 performance data
- `monitor-report-*.json`: System monitoring data

## ⚠️ Important Safety Rules

1. **Always start with light tests**
2. **Monitor system resources continuously**
3. **Test during off-peak hours only**
4. **Stop immediately if CPU/Memory > 85%**
5. **Use dedicated test account only**

## 🔧 Troubleshooting

### Common Issues:
- **K6 not found**: Run `npm install -g k6`
- **Permission denied**: Use `sudo` on Linux/Mac
- **Network timeout**: Check firewall and VPN settings
- **High error rate**: Reduce concurrent users

### Emergency Stop:
```bash
# If test gets stuck, force stop:
Ctrl+C  # Safe exit

# If that doesn't work:
# Windows: taskkill /f /im k6.exe
# Linux/Mac: pkill -f k6
```

## 📞 Need Help?

1. Check `tests/installation-guide.md` for detailed setup
2. Review error logs in `tests/results/`
3. Ensure test account password is correct
4. Verify network connection to https://invite.limob.cn
