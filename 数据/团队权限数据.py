"""
团队权限数据操作模块
负责处理团队权限、角色等相关的数据库操作
"""

from datetime import datetime
from typing import Optional, Dict, Any, List

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器


async def 从数据库获取用户在团队中的权限列表(团队id: int, 用户id: int) -> List[str]:
    """从用户团队权限表获取用户的权限代码列表"""
    try:
        SQL = """
        SELECT tp.权限名称
        FROM 用户团队权限表 utp
        JOIN 团队权限表 tp ON utp.权限ID = tp.id
        WHERE utp.用户id = $1 AND utp.团队id = $2 AND utp.状态 = '生效'
        AND (utp.过期时间 IS NULL OR utp.过期时间 > NOW())
        """
        
        权限记录 = await 异步连接池实例.执行查询(SQL, (用户id, 团队id))
        权限列表 = [记录["权限名称"] for 记录 in 权限记录]
        
        数据库日志器.info(f"从数据库获取用户权限成功: 用户id={用户id}, 团队id={团队id}, 权限数量={len(权限列表)}")
        return 权限列表
        
    except Exception as e:
        错误日志器.error(f"从数据库获取用户权限失败: 用户id={用户id}, 团队id={团队id}, 错误={e}", exc_info=True)
        return []


async def 批量设置用户权限(
    团队id: int, 
    用户id: int, 
    权限名称列表: List[str], 
    授权人ID: int,
    过期时间: Optional[datetime] = None
) -> bool:
    """批量设置用户在团队中的权限"""
    try:
        # 先清理用户的现有权限
        await 清理用户在指定团队中的所有权限记录(团队id, 用户id)
        
        if not 权限名称列表:
            数据库日志器.info(f"权限列表为空，仅清理权限: 用户id={用户id}, 团队id={团队id}")
            return True
        
        # 获取权限ID映射
        权限查询SQL = """
        SELECT id, 权限名称 FROM 团队权限表
        WHERE 权限名称 IN ({}) AND 状态 = '启用'
        """.format(','.join([f'${i+1}' for i in range(len(权限名称列表))]))
        
        权限映射记录 = await 异步连接池实例.执行查询(权限查询SQL, 权限名称列表)
        权限ID映射 = {记录["权限名称"]: 记录["id"] for 记录 in 权限映射记录}
        
        # 批量插入权限记录
        插入数据 = []
        授权时间 = datetime.now()
        
        for 权限名称 in 权限名称列表:
            if 权限名称 in 权限ID映射:
                插入数据.append((
                    用户id, 团队id, 权限ID映射[权限名称], 
                    授权人ID, 授权时间, 过期时间, '生效'
                ))
        
        if 插入数据:
            插入SQL = """
            INSERT INTO 用户团队权限表(
                用户id, 团队id, 权限ID, 授权人ID, 授权时间, 过期时间, 状态
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            
            # 批量插入
            for 数据 in 插入数据:
                await 异步连接池实例.执行插入(插入SQL, 数据)
            
            数据库日志器.info(f"批量设置用户权限成功: 用户id={用户id}, 团队id={团队id}, 权限数量={len(插入数据)}")
            return True
        else:
            数据库日志器.warning(f"未找到有效权限名称: 用户id={用户id}, 团队id={团队id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"批量设置用户权限失败: 用户id={用户id}, 团队id={团队id}, 错误={e}", exc_info=True)
        return False


async def 清理用户在指定团队中的所有权限记录(团队id: int, 用户id: int) -> bool:
    """清理用户在指定团队中的所有权限记录"""
    try:
        SQL = """
        DELETE FROM 用户团队权限表
        WHERE 用户id = $1 AND 团队id = $2
        """
        
        结果 = await 异步连接池实例.执行数据库删除(SQL, (用户id, 团队id))
        
        数据库日志器.info(f"清理用户权限: 用户id={用户id}, 团队id={团队id}, 结果={结果}")
        return True
        
    except Exception as e:
        错误日志器.error(f"清理用户权限失败: 用户id={用户id}, 团队id={团队id}, 错误={e}", exc_info=True)
        return False


async def 获取用户团队权限状态(团队id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """获取用户在指定团队中的详细权限状态（基于数据库表的新版本）"""
    try:
        # 查询用户在团队中的基本信息
        SQL = """
        SELECT 
            ut.用户id, ut.团队id, ut.职位, ut.状态, ut.加入时间,
            COALESCE(u.昵称, u.phone, '') as 用户名,
            t.团队名称, t.创建人id, t.团队负责人id
        FROM 用户团队关联表 ut
        LEFT JOIN 用户表 u ON ut.用户id = u.id
        LEFT JOIN 团队表 t ON ut.团队id = t.id
        WHERE ut.用户id = $1 AND ut.团队id = $2
        """
        
        用户信息 = await 异步连接池实例.执行查询(SQL, (用户id, 团队id))
        
        # 如果用户不在团队中，检查是否可以作为访客查看
        if not 用户信息:
            # 查询团队基本信息，允许访客查看
            团队SQL = "SELECT 团队名称, 创建人id, 团队负责人id FROM 团队表 WHERE id = $1"
            团队信息 = await 异步连接池实例.执行查询(团队SQL, (团队id,))
            
            if not 团队信息:
                return None
                
            # 返回访客权限状态
            return {
                "用户id": 用户id,
                "团队id": 团队id,
                "在团队中": False,
                "职位": "访客",
                "状态": "访客",
                "是否团队成员": False,
                "是否团队创建者": False,
                "是否团队负责人": False,
                "用户名": "访客",
                "团队名称": 团队信息[0]["团队名称"],
                "权限列表": ["查看团队信息"], # 访客只有最基本权限
                # 快捷权限检查
                "能否查看团队": True,
                "能否编辑团队": False,
                "能否删除团队": False,
                "能否邀请成员": False,
                "能否移除成员": False,
                "能否管理成员": False,
                "能否管理权限": False,
                "能否查看统计": False,
                "能否管理邀请": False
            }
        
        用户记录 = 用户信息[0]
        职位 = 用户记录["职位"]
        创建人id = 用户记录["创建人id"]
        
        # 判断特殊身份
        是否创建者 = (用户id == 创建人id)
        
        # 核心逻辑：总是先从数据库获取精确权限
        权限列表 = await 从数据库获取用户在团队中的权限列表(团队id, 用户id)
        
        # 如果是创建者且数据库中没有权限记录，则授予所有权限
        if 是否创建者 and not 权限列表:
            所有权限结果 = await 异步连接池实例.执行查询("SELECT 权限名称 FROM 团队权限表 WHERE 状态 = '启用'")
            权限列表 = [行['权限名称'] for 行 in 所有权限结果]

        # 快捷权限检查 - 基于从数据库获取的列表
        快捷权限 = {
            "能否查看团队": "查看团队" in 权限列表,
            "能否编辑团队": "管理团队" in 权限列表,
            "能否删除团队": "管理团队" in 权限列表, # 通常删除和管理是同一个权限
            "能否邀请成员": "邀请成员" in 权限列表,
            "能否移除成员": "移除成员" in 权限列表,
            "能否管理成员": "管理团队" in 权限列表,
            "能否管理权限": "权限管理" in 权限列表,
            "能否查看统计": "数据导出" in 权限列表, # 假设查看统计属于数据导出权限
            "能否管理邀请": "邀请成员" in 权限列表, # 邀请管理和邀请成员是同一个权限
            "能否查看日志": "日志查看" in 权限列表,
            "能否查看财务": "财务查看" in 权限列表,
            "能否管理财务": "财务管理" in 权限列表
        }

        # 最终返回的权限状态
        return {
            "用户id": 用户id,
            "团队id": 团队id,
            "在团队中": True,
            "职位": 职位,
            "状态": 用户记录["状态"],
            "是否团队成员": 用户记录["状态"] == "正常",
            "是否团队创建者": 是否创建者,
            "是否团队负责人": 职位 == "团队负责人" or 是否创建者, # 创建者也是负责人
            "用户名": 用户记录["用户名"],
            "团队名称": 用户记录["团队名称"],
            "权限列表": 权限列表,
            **快捷权限
        }

    except Exception as e:
        错误日志器.error(f"获取用户团队权限状态失败: 用户id={用户id}, 团队id={团队id}, 错误={e}", exc_info=True)
        return None


async def 初始化负责人权限(团队id: int, 用户id: int, 授权人ID: int) -> bool:
    """初始化团队负责人的权限，动态获取所有可用权限并授予"""
    try:
        # 采纳用户建议：从数据库动态获取所有启用的权限，授予负责人
        SQL = "SELECT 权限名称 FROM 团队权限表 WHERE 状态 = '启用'"
        权限结果 = await 异步连接池实例.执行查询(SQL)

        if not 权限结果:
            错误日志器.warning(f"团队权限表中没有任何启用的权限，无法为负责人(用户id: {用户id})授权。")
            # 即使没有权限可授，操作本身不算失败
            return True

        负责人权限 = [行['权限名称'] for 行 in 权限结果]
        
        数据库日志器.info(f"为负责人(用户id: {用户id}) 初始化所有可用权限，共 {len(负责人权限)} 项")
        return await 批量设置用户权限(团队id, 用户id, 负责人权限, 授权人ID)
    except Exception as e:
        错误日志器.error(f"初始化负责人权限失败: {e}", exc_info=True)
        return False


async def 初始化成员权限(团队id: int, 用户id: int, 授权人ID: int) -> bool:
    """初始化普通成员的权限，动态获取所有基础权限并授予"""
    try:
        # 采纳用户建议：从数据库动态获取所有分类为'基础权限'的权限
        SQL = "SELECT 权限名称 FROM 团队权限表 WHERE 权限分类 = '基础权限' AND 状态 = '启用'"
        权限结果 = await 异步连接池实例.执行查询(SQL)

        if not 权限结果:
            错误日志器.warning(f"团队权限表中未找到'基础权限'分类的权限，将为成员(用户id: {用户id})授予空权限集。")
            成员权限 = []
        else:
            成员权限 = [行['权限名称'] for 行 in 权限结果]
        
        数据库日志器.info(f"为成员(用户id: {用户id}) 初始化基础权限，共 {len(成员权限)} 项")
        return await 批量设置用户权限(团队id, 用户id, 成员权限, 授权人ID)
    except Exception as e:
        错误日志器.error(f"初始化成员权限失败: {e}", exc_info=True)
        return False


async def 获取所有权限() -> List[Dict[str, Any]]:
    """获取所有启用的权限，按分类组织"""
    try:
        SQL = """
        SELECT 权限名称, 权限分类, 权限描述
        FROM 团队权限表
        WHERE 状态 = '启用'
        ORDER BY 权限分类, id
        """
        
        权限记录 = await 异步连接池实例.执行查询(SQL)
        return 权限记录

    except Exception as e:
        错误日志器.error(f"获取所有权限失败: {e}", exc_info=True)
        return []


 
