# 已使用统一日志系统替代
import asyncio
from typing import List, Dict, Any

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
# 导入统一日志系统
from 日志 import 错误日志器


async def 异步获取更新数据() -> List[Dict[str, Any]]:
    """
    异步获取所有版本更新数据

    Returns:
        包含所有版本记录的列表，每个记录包含id,版本号,更新内容,更新地址,强制更新字段
        若无记录或发生错误则返回空列表

    Raises:
        ConnectionError: 数据库连接异常时抛出
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 版本号, 更新内容, 更新地址, 强制更新 FROM 版本"
        )
        return 结果 or []
    except ConnectionError as conn_error:
        错误日志器.error(f"获取更新数据时数据库连接异常: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"异步获取更新数据失败: {e}", exc_info=True)
        return []

async def 异步获取最新更新() -> List[Dict[str, Any]]:
    """
    异步获取数据库中最新的版本更新记录

    Returns:
        包含最新版本记录的列表，若无记录或发生错误则返回空列表

    Raises:
        ConnectionError: 数据库连接异常时抛出
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 版本号, 更新内容, 更新地址, 强制更新 FROM 版本 ORDER BY id DESC LIMIT 1"
        )
        return 结果 or []
    except ConnectionError as conn_error:
        错误日志器.error(f"获取最新更新时数据库连接异常: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"异步获取最新更新失败: {e}", exc_info=True)
        return []

# 测试代码，仅在直接运行该脚本时执行
if __name__ == "__main__":
    # 移除此处的导入语句，已移到文件顶部
    # from 日志 import 系统日志器, 错误日志器, 安全日志器, 接口日志器, 数据库日志器
    
    async def 测试():
        rows = await 异步获取更新数据()
        for row in rows:
            print(row)
        
        最新 = await 异步获取最新更新()
        if 最新:
            print(最新[0])
        else:
            print("无最新更新记录")
    
    # 运行异步测试函数
    asyncio.run(测试()) 