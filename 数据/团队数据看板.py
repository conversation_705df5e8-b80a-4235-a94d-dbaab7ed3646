"""
团队数据管理模块

此模块主要作为兼容层，重新导出团队基础数据和团队成员数据模块的函数，同时保留一些复杂的业务逻辑函数。
文件拆分说明：
- 团队基础数据.py: 基础团队CRUD操作
- 团队成员数据.py: 团队成员管理操作 
- 团队数据.py: 复杂业务逻辑和兼容导出

创建时间: 2024
更新时间: 2024
作者: AI系统优化
"""

from datetime import datetime
from typing import Optional, Dict, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
# 重新导出团队基础数据模块的函数
from 数据.团队基础数据 import (
    创建团队,
    获取团队基本信息,
    格式化团队详情数据,
    解散团队
)
# 重新导出团队成员数据模块的函数
from 数据.团队成员数据 import (
    加入团队,
    获取团队成员列表,
    获取用户团队列表,
    踢出团队成员
)
from 日志 import 错误日志器, 数据库日志器


# 以下是保留的复杂业务逻辑函数

async def 获取团队详情(
    团队id: int, 
    用户id: Optional[int] = None,
    包含成员统计: bool = True,
    包含权限信息: bool = True
) -> Dict[str, Any]:
    """获取团队详情（优化版），复杂业务逻辑组合函数"""
    try:
        # 参数验证
        if not isinstance(团队id, int) or 团队id <= 0:
            return {"success": False, "message": "无效的团队id", "code": "INVALID_TEAM_ID"}
        
        # 获取团队基本信息
        团队信息 = await 获取团队基本信息(团队id)
        if not 团队信息:
            return {"success": False, "message": "团队不存在或已解散", "code": "TEAM_NOT_FOUND"}
        
        # 获取用户权限信息
        用户权限 = None
        if 用户id and 包含权限信息:
            # 导入团队权限模块
            from 数据.团队权限数据 import 获取用户团队权限状态
            
            用户权限 = await 获取用户团队权限状态(团队id, 用户id)
            
            # 如果用户权限为空，且用户是团队创建者，则初始化创建者权限
            if not 用户权限 and 团队信息.get("创建人id") == 用户id:
                from 数据.团队权限数据 import 初始化负责人权限
                初始化成功 = await 初始化负责人权限(团队id, 用户id, 用户id)
                if 初始化成功:
                    数据库日志器.info(f"为团队创建者初始化权限: 团队id={团队id}, 用户id={用户id}")
                    # 重新获取权限
                    用户权限 = await 获取用户团队权限状态(团队id, 用户id)
            
            # 如果仍然没有权限，但用户在团队中，则初始化基础权限
            elif not 用户权限:
                # 检查用户是否在团队中
                from 数据.团队成员数据 import 获取用户团队权限信息
                团队成员信息 = await 获取用户团队权限信息(团队id, 用户id)
                
                if 团队成员信息.get("在团队中", False):
                    from 数据.团队权限数据 import 初始化成员权限
                    初始化成功 = await 初始化成员权限(团队id, 用户id, 团队信息.get("创建人id", 用户id))
                    if 初始化成功:
                        数据库日志器.info(f"为团队成员初始化权限: 团队id={团队id}, 用户id={用户id}")
                        # 重新获取权限
                        用户权限 = await 获取用户团队权限状态(团队id, 用户id)
            
            # 如果用户权限仍然为空，说明用户不在团队中，应该拒绝访问
            if not 用户权限:
                数据库日志器.warning(f"用户 {用户id} 不在团队 {团队id} 中，拒绝访问")
                return {
                    "success": False,
                    "message": "用户不在该团队中",
                    "data": None
                }
        
        # 获取成员统计信息并确保数据一致性
        成员统计 = None
        if 包含成员统计:
            # 执行数据一致性检查和修复
            修复结果 = await 检查并修复团队数据一致性(团队id)
            if 修复结果["修复项目"]:
                数据库日志器.info(f"团队数据一致性修复完成: 团队id={团队id}, 修复项目={len(修复结果['修复项目'])}")
                # 重新获取团队信息以确保数据最新
                团队信息 = await 获取团队基本信息(团队id)
                if not 团队信息:
                    return {"success": False, "message": "团队不存在", "code": "TEAM_NOT_FOUND"}

            实际成员数 = await 获取团队成员数(团队id)
            成员统计 = {"实际成员数": 实际成员数, "修复结果": 修复结果}
        
        # 格式化数据
        格式化数据 = 格式化团队详情数据(团队信息, 用户权限, 成员统计)
        
        数据库日志器.info(f"获取团队详情成功: 团队id={团队id}, 用户id={用户id}")
        return {"success": True, "data": 格式化数据}
        
    except Exception as e:
        错误日志器.error(f"获取团队详情失败: 团队id={团队id}, 用户id={用户id}, 错误={e}", exc_info=True)
        return {"success": False, "message": "获取团队详情失败", "code": "INTERNAL_ERROR"}


async def 获取用户团队统计(用户id: int) -> Dict[str, Any]:
    """获取用户团队统计信息"""
    try:
        # 修复后的统计查询 - 避免重复计算，正确处理团队状态
        统计SQL = """
        SELECT
            COUNT(DISTINCT CASE WHEN t.团队状态 != '解散' THEN ut.团队id END) as 参与团队数,
            COUNT(DISTINCT CASE WHEN t.创建人id = $1 AND t.团队状态 != '解散' THEN t.id END) as 创建团队数,
            COUNT(DISTINCT CASE WHEN (t.团队负责人id = $2 OR ut.职位 IN ('团队负责人', '管理员'))
                                   AND t.创建人id != $3 AND t.团队状态 != '解散' THEN t.id END) as 管理团队数,
            COUNT(DISTINCT CASE WHEN ut.状态 = '正常' AND t.团队状态 = '正常' THEN ut.团队id END) as 活跃团队数
        FROM 用户团队关联表 ut
        LEFT JOIN 团队表 t ON ut.团队id = t.id
        WHERE ut.用户id = $4 AND ut.状态 IN ('正常', '暂停')
        """

        统计结果 = await 异步连接池实例.执行查询(统计SQL, (用户id, 用户id, 用户id, 用户id))
        基础统计 = 统计结果[0] if 统计结果 else {}
        
        # 最近活动查询
        最近活动SQL = """
        SELECT t.团队名称, ut.加入时间, ut.状态
        FROM 用户团队关联表 ut
        LEFT JOIN 团队表 t ON ut.团队id = t.id
        WHERE ut.用户id = $1 AND t.团队状态 != '解散'
        ORDER BY ut.加入时间 DESC 
        LIMIT 5
        """
        
        最近活动结果 = await 异步连接池实例.执行查询(最近活动SQL, (用户id,))

        # 查询待处理邀请数（从用户团队关联表中查询状态为"邀请待处理"的记录）
        待处理邀请SQL = """
        SELECT COUNT(*) as 待处理邀请数
        FROM 用户团队关联表
        WHERE 用户id = $1 AND 状态 = '邀请待处理'
        """

        try:
            待处理邀请结果 = await 异步连接池实例.执行查询(待处理邀请SQL, (用户id,))
            待处理邀请数 = 待处理邀请结果[0]["待处理邀请数"] if 待处理邀请结果 else 0
        except Exception as e:
            # 如果查询失败，设为0
            数据库日志器.warning(f"查询待处理邀请数失败: {e}")
            待处理邀请数 = 0

        return {
            "参与团队数": 基础统计.get("参与团队数", 0),
            "创建团队数": 基础统计.get("创建团队数", 0),
            "管理团队数": 基础统计.get("管理团队数", 0),
            "活跃团队数": 基础统计.get("活跃团队数", 0),
            "待处理邀请数": 待处理邀请数,
            "最近参与团队": [
                {
                    "团队名称": 记录["团队名称"],
                    "加入时间": 记录["加入时间"],
                    "状态": 记录["状态"]
                }
                for 记录 in 最近活动结果
            ]
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户团队统计失败: 用户id={用户id}, 错误={e}")
        return {"参与团队数": 0, "创建团队数": 0, "管理团队数": 0, "活跃团队数": 0, "最近参与团队": []}


async def 获取团队成员数(团队id: int) -> int:
    """获取团队实际成员数"""
    try:
        SQL = "SELECT COUNT(*) as 成员数 FROM 用户团队关联表 WHERE 团队id = $1 AND 状态 = '正常'"
        结果 = await 异步连接池实例.执行查询(SQL, (团队id,))
        return 结果[0]["成员数"] if 结果 else 0
    except Exception as e:
        错误日志器.error(f"获取团队成员数失败: 团队id={团队id}, 错误={e}")
        return 0


async def 检查并修复团队数据一致性(团队id: int) -> Dict[str, Any]:
    """检查并修复团队数据一致性问题"""
    try:
        修复结果 = {"修复项目": [], "错误": []}

        # 1. 检查团队成员数一致性
        实际成员数 = await 获取团队成员数(团队id)

        团队信息SQL = "SELECT 当前成员数, 团队名称 FROM 团队表 WHERE id = $1"
        团队信息 = await 异步连接池实例.执行查询(团队信息SQL, (团队id,))

        if 团队信息:
            记录成员数 = 团队信息[0]["当前成员数"]
            团队名称 = 团队信息[0]["团队名称"]

            if 记录成员数 != 实际成员数:
                # 修复成员数不一致
                更新SQL = "UPDATE 团队表 SET 当前成员数 = $1, 更新时间 = $2 WHERE id = $3"
                await 异步连接池实例.执行更新(更新SQL, (实际成员数, datetime.now(), 团队id))

                修复结果["修复项目"].append({
                    "类型": "成员数修复",
                    "团队名称": 团队名称,
                    "修复前": 记录成员数,
                    "修复后": 实际成员数
                })

                数据库日志器.info(f"修复团队成员数: {团队名称} {记录成员数} -> {实际成员数}")

        # 2. 检查加入时间为空的记录
        空时间SQL = """
        SELECT COUNT(*) as count FROM 用户团队关联表
        WHERE 团队id = $1 AND 加入时间 IS NULL AND 状态 != '已移除'
        """
        空时间结果 = await 异步连接池实例.执行查询(空时间SQL, (团队id,))
        空时间数量 = 空时间结果[0]["count"] if 空时间结果 else 0

        if 空时间数量 > 0:
            # 修复空的加入时间
            修复时间SQL = """
            UPDATE 用户团队关联表
            SET 加入时间 = COALESCE(处理邀请时间, 创建时间, NOW())
            WHERE 团队id = $1 AND 加入时间 IS NULL AND 状态 != '已移除'
            """
            await 异步连接池实例.执行更新(修复时间SQL, (团队id,))

            修复结果["修复项目"].append({
                "类型": "加入时间修复",
                "修复数量": 空时间数量
            })

            数据库日志器.info(f"修复团队 {团队id} 的 {空时间数量} 条空加入时间记录")

        return 修复结果

    except Exception as e:
        错误日志器.error(f"检查修复团队数据一致性失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {"修复项目": [], "错误": [str(e)]}


async def 获取团队概览统计(团队id: int) -> Dict[str, Any]:
    """获取团队概览统计信息"""
    try:
        # 获取团队基本信息（最大成员数、创建时间）
        团队信息SQL = """
        SELECT 最大成员数, 创建时间, 团队名称, 团队状态
        FROM 团队表
        WHERE id = $1
        """
        
        团队信息结果 = await 异步连接池实例.执行查询(团队信息SQL, (团队id,))
        if not 团队信息结果:
            return {"error": "团队不存在"}
        
        团队信息 = 团队信息结果[0]
        最大成员数 = 团队信息["最大成员数"]
        创建时间 = 团队信息["创建时间"]
        
        # 计算运行天数
        当前时间 = datetime.now()
        运行天数 = (当前时间 - 创建时间).days
        
        # 成员统计 - 修复统计逻辑，排除已移除成员
        成员统计SQL = """
        SELECT
            COUNT(CASE WHEN 状态 IN ('正常', '暂停') THEN 1 END) as 总成员数,
            COUNT(CASE WHEN 状态 = '正常' THEN 1 END) as 活跃成员数,
            COUNT(CASE WHEN 状态 = '暂停' THEN 1 END) as 暂停成员数,
            COUNT(CASE WHEN 状态 = '已移除' THEN 1 END) as 已移除成员数
        FROM 用户团队关联表
        WHERE 团队id = $1
        """

        成员结果 = await 异步连接池实例.执行查询(成员统计SQL, (团队id,))
        成员统计 = 成员结果[0] if 成员结果 else {}

        当前成员数 = 成员统计.get("活跃成员数", 0)
        
        # 今日活跃成员统计（今天有登录记录的）
        今日活跃SQL = """
        SELECT COUNT(DISTINCT ut.用户id) as 今日活跃数
        FROM 用户团队关联表 ut
        JOIN 用户登陆记录表 ulr ON ut.用户id = ulr.用户id
        WHERE ut.团队id = $1 AND ut.状态 = '正常'
        AND DATE(ulr.登陆时间) = CURRENT_DATE
        """
        
        今日活跃结果 = await 异步连接池实例.执行查询(今日活跃SQL, (团队id,))
        今日活跃数 = 今日活跃结果[0]["今日活跃数"] if 今日活跃结果 else 0
        
        # 在线成员统计（最近5分钟内有登录记录的）
        在线成员SQL = """
        SELECT COUNT(DISTINCT ut.用户id) as 在线成员数
        FROM 用户团队关联表 ut
        JOIN 用户登陆记录表 ulr ON ut.用户id = ulr.用户id
        WHERE ut.团队id = $1 AND ut.状态 = '正常'
        AND ulr.登陆时间 >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        """
        
        在线成员结果 = await 异步连接池实例.执行查询(在线成员SQL, (团队id,))
        在线成员数 = 在线成员结果[0]["在线成员数"] if 在线成员结果 else 0
        
        return {
            "团队成员数": 当前成员数,
            "最大成员数": 最大成员数,
            "在线成员数": 在线成员数,
            "今日活跃数": 今日活跃数,
            "运行天数": 运行天数,
            "成员活跃率": round((今日活跃数 / max(当前成员数, 1)) * 100, 1),
            "在线率": round((在线成员数 / max(当前成员数, 1)) * 100, 1),
            "总成员数": 成员统计.get("总成员数", 0),
            "暂停成员数": 成员统计.get("暂停成员数", 0)
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队概览统计失败: 团队id={团队id}, 错误={e}")
        # 直接抛出异常，让上层统一处理
        raise e


async def 获取团队活动统计(团队id: int) -> Dict[str, Any]:
    """获取团队活动统计"""
    try:
        # 基础统计信息
        统计数据 = {
            "统计天数": 30,  # 最近30天的统计
            "新增成员": 0,
            "离开成员": 0,
            "活跃度": 85,  # 模拟活跃度百分比
            "活动趋势": "上升"  # 模拟趋势
        }
        
        # 新增成员统计（最近30天）
        新增成员SQL = """
        SELECT COUNT(*) as 新增成员数
        FROM 用户团队关联表
        WHERE 团队id = $1
        AND 加入时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
        """
        
        新增成员结果 = await 异步连接池实例.执行查询(新增成员SQL, (团队id,))
        统计数据["新增成员"] = 新增成员结果[0]["新增成员数"] if 新增成员结果 else 0
        
        # 离开成员统计（最近30天内状态变为已移除的）
        离开成员SQL = """
        SELECT COUNT(*) as 离开成员数
        FROM 用户团队关联表
        WHERE 团队id = $1
        AND 状态 = '已移除'
        AND 更新时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
        """
        
        离开成员结果 = await 异步连接池实例.执行查询(离开成员SQL, (团队id,))
        统计数据["离开成员"] = 离开成员结果[0]["离开成员数"] if 离开成员结果 else 0
        
        return 统计数据
        
    except Exception as e:
        错误日志器.error(f"获取团队活动统计失败: 团队id={团队id}, 错误={e}")
        return {"统计天数": 30, "新增成员": 0, "离开成员": 0, "活跃度": 0, "活动趋势": "无数据"}





async def 获取团队操作日志(
    团队id: int,
    页码: int = 1,
    每页数量: int = 20,
    天数: int = 30,
    操作类型: Optional[str] = None
) -> Dict[str, Any]:
    """获取团队操作日志"""
    try:
        # 构建WHERE条件
        where_conditions = ["t.团队id = $1"]
        params = [团队id]
        参数索引 = 2

        # 时间范围条件
        if 天数 > 0:
            where_conditions.append(f"t.操作时间 >= CURRENT_TIMESTAMP - INTERVAL '{参数索引} days'")
            params.append(天数)
            参数索引 += 1

        # 操作类型筛选
        if 操作类型:
            where_conditions.append(f"t.操作类型 = $2")
            params.append(操作类型)
            参数索引 += 1
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        总数SQL = f"""
        SELECT COUNT(*) as total
        FROM 团队操作日志表 t
        WHERE {where_clause}
        """
        
        总数结果 = await 异步连接池实例.执行查询(总数SQL, tuple(params))
        总数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 分页查询
        偏移量 = (页码 - 1) * 每页数量
        
        查询SQL = f"""
        SELECT 
            t.id,
            t.操作类型,
            t.操作内容,
            t.操作对象ID,
            t.操作时间,
            u.昵称 as 操作人姓名,
            u.phone as 操作人手机号
        FROM 团队操作日志表 t
        LEFT JOIN 用户表 u ON t.操作人ID = u.id
        WHERE {where_clause}
        ORDER BY t.操作时间 DESC
        LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}
        """

        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数占位符文档 }}
        params.extend([每页数量, 偏移量])
        日志结果 = await 异步连接池实例.执行查询(查询SQL, tuple(params))
        
        # 格式化活动列表
        活动列表 = []
        for 记录 in 日志结果:
            活动列表.append({
                "id": 记录["id"],
                "操作类型": 记录["操作类型"],
                "操作内容": 记录["操作内容"],
                "操作人姓名": 记录["操作人姓名"] or "未知用户",
                "操作人手机号": 记录["操作人手机号"] or "",
                "操作时间": 记录["操作时间"].strftime("%Y-%m-%d %H:%M:%S") if 记录["操作时间"] else "",
                "操作对象ID": 记录["操作对象ID"]
            })
        
        总页数 = (总数 + 每页数量 - 1) // 每页数量
        
        return {
            "活动列表": 活动列表,
            "总数": 总数,
            "总页数": 总页数,
            "当前页": 页码,
            "每页数量": 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队操作日志失败: 团队id={团队id}, 错误={e}")
        return {
            "活动列表": [],
            "总数": 0,
            "总页数": 0,
            "当前页": 页码,
            "每页数量": 每页数量
        }

async def 获取团队数据看板聚合(团队id: int) -> Dict[str, Any]:
    """
    统一的团队数据看板聚合接口 - 已废弃，请使用服务层的并行加载逻辑
    保留此函数仅为向后兼容，建议使用 服务.团队数据看板服务.获取团队数据看板聚合
    """
    try:
        数据库日志器.warning(f"⚠️ 使用已废弃的数据层聚合接口: 团队id={团队id}，建议使用服务层接口")

        # 为了向后兼容，仍然提供基础功能，但建议迁移到服务层
        from 数据.团队数据看板查询 import 团队数据查询工具实例
        from 数据.团队数据看板处理 import 团队数据处理工具实例

        # 并发获取所有数据
        原始数据 = await 团队数据查询工具实例.获取团队全部数据聚合(团队id)

        # 检查是否有错误
        if "error" in 原始数据:
            return 原始数据

        # 提取各部分数据
        团队信息 = 原始数据["团队信息"]
        成员统计 = 原始数据["成员统计"]
        微信好友汇总 = 原始数据["微信好友汇总"]
        寄样汇总 = 原始数据["寄样汇总"]
        成员列表 = 原始数据["成员列表"]

        # 获取业务流程数据
        业务流程数据 = await 团队数据处理工具实例.获取团队业务流程数据(团队id)

        # 使用处理工具构建最终响应数据
        聚合数据 = 团队数据处理工具实例.构建聚合响应数据(
            团队信息, 成员统计, 微信好友汇总, 寄样汇总, 成员列表, 业务流程数据
        )

        数据库日志器.info(f"✅ 获取团队数据看板聚合成功（兼容模式）: 团队id={团队id}")
        return 聚合数据
        
    except Exception as e:
        错误日志器.error(f"获取团队数据看板聚合失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {"error": f"获取数据失败: {str(e)}"}


# 重新导出的函数（保持向后兼容性）
# 这些函数从其他模块导入并重新导出，供路由层使用
__all__ = [
    # 团队基础数据模块的函数
    "创建团队",
    "获取团队基本信息",
    "格式化团队详情数据",
    "解散团队",
    # 团队成员数据模块的函数
    "加入团队",
    "获取团队成员列表",
    "获取用户团队列表",
    "踢出团队成员",
    # 本模块的复杂业务逻辑函数
    "获取团队详情",
    "获取用户团队统计",
    "获取团队成员数",
    "检查并修复团队数据一致性",
    "获取团队概览统计",
    "获取团队活动统计",
    "获取团队操作日志",
    "获取团队数据看板聚合"
]