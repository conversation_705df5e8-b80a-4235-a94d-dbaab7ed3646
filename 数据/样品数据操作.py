"""
样品数据操作模块 - PostgreSQL版本
基于asyncpg实现的样品管理相关数据库操作

功能：
1. 样品基础CRUD操作
2. 样品状态管理
3. 样品分类和搜索
4. 样品统计分析
"""

from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器

# ==================== 样品基础操作 ====================


async def 创建样品(
    样品名称: str,
    样品类型: str,
    创建人id: int,
    样品描述: Optional[str] = None,
    样品规格: Optional[str] = None,
    样品数量: int = 1,
    样品单位: str = "个",
    存储位置: Optional[str] = None,
    团队id: Optional[int] = None,
) -> Optional[int]:
    """
    创建新样品

    Args:
        样品名称: 样品名称
        样品类型: 样品类型
        创建人id: 创建人用户id
        样品描述: 样品描述
        样品规格: 样品规格
        样品数量: 样品数量
        样品单位: 样品单位
        存储位置: 存储位置
        团队id: 所属团队id

    Returns:
        新创建样品的ID，失败返回None
    """
    try:
        插入SQL = """
        INSERT INTO 样品表 (
            样品名称, 样品类型, 创建人id, 样品描述, 样品规格,
            样品数量, 样品单位, 存储位置, 团队id, 样品状态, 创建时间
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, '正常', CURRENT_TIMESTAMP
        ) RETURNING id
        """

        结果 = await 异步连接池实例.执行查询(
            插入SQL,
            (
                样品名称,
                样品类型,
                创建人id,
                样品描述,
                样品规格,
                样品数量,
                样品单位,
                存储位置,
                团队id,
            ),
        )

        if 结果:
            样品id = 结果[0]["id"]
            数据库日志器.info(f"创建样品成功: ID={样品id}, 名称={样品名称}")
            return 样品id
        else:
            错误日志器.error(f"创建样品失败: 名称={样品名称}")
            return None

    except Exception as e:
        错误日志器.error(f"创建样品异常: 名称={样品名称}, 错误={str(e)}")
        return None


async def 获取样品信息(样品id: int) -> Optional[Dict[str, Any]]:
    """
    获取样品详细信息

    Args:
        样品id: 样品id

    Returns:
        样品信息字典，失败返回None
    """
    try:
        查询SQL = """
        SELECT 
            s.id, s.样品名称, s.样品类型, s.创建人id, s.样品描述,
            s.样品规格, s.样品数量, s.样品单位, s.存储位置, s.团队id,
            s.样品状态, s.创建时间, s.更新时间,
            u.用户名 as 创建人姓名,
            t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人id = u.id
        LEFT JOIN 团队表 t ON s.团队id = t.id
        WHERE s.id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (样品id,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"获取样品信息异常: 样品id={样品id}, 错误={str(e)}")
        return None


async def 更新样品信息(样品id: int, 更新字段: Dict[str, Any], 操作人ID: int) -> bool:
    """
    更新样品信息

    Args:
        样品id: 样品id
        更新字段: 要更新的字段字典
        操作人ID: 操作人ID

    Returns:
        是否更新成功
    """
    try:
        if not 更新字段:
            return True

        # 构建更新SQL
        字段列表 = []
        参数列表 = []
        参数索引 = 1

        允许字段 = [
            "样品名称",
            "样品类型",
            "样品描述",
            "样品规格",
            "样品数量",
            "样品单位",
            "存储位置",
            "样品状态",
            "团队id",
        ]

        for 字段名, 值 in 更新字段.items():
            if 字段名 in 允许字段:
                字段列表.append(f'"{字段名}" = ${参数索引}')
                参数列表.append(值)
                参数索引 += 1

        if not 字段列表:
            数据库日志器.warning(f"更新样品信息时没有有效字段: 样品id={样品id}")
            return False

        # 添加更新时间
        字段列表.append("更新时间 = CURRENT_TIMESTAMP")

        更新SQL = f"""
        UPDATE 样品表 
        SET {", ".join(字段列表)}
        WHERE id = $1
        """
        参数列表.append(样品id)

        影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

        if 影响行数 > 0:
            数据库日志器.info(f"更新样品信息成功: 样品id={样品id}, 操作人={操作人ID}")
            return True
        else:
            数据库日志器.warning(f"更新样品信息未影响任何行: 样品id={样品id}")
            return False

    except Exception as e:
        错误日志器.error(f"更新样品信息异常: 样品id={样品id}, 错误={str(e)}")
        return False


async def 删除样品(样品id: int, 操作人ID: int) -> bool:
    """
    删除样品（软删除）

    Args:
        样品id: 样品id
        操作人ID: 操作人ID

    Returns:
        是否删除成功
    """
    try:
        更新SQL = """
        UPDATE 样品表 
        SET 样品状态 = '已删除', 更新时间 = CURRENT_TIMESTAMP
        WHERE id = $1
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, (样品id,))

        if 影响行数 > 0:
            数据库日志器.info(f"删除样品成功: 样品id={样品id}, 操作人={操作人ID}")
            return True
        else:
            数据库日志器.warning(f"删除样品未影响任何行: 样品id={样品id}")
            return False

    except Exception as e:
        错误日志器.error(f"删除样品异常: 样品id={样品id}, 错误={str(e)}")
        return False


# ==================== 样品查询和搜索 ====================


async def 分页查询样品列表(
    页码: int = 1,
    每页数量: int = 20,
    样品类型筛选: Optional[str] = None,
    样品状态筛选: Optional[str] = None,
    团队id筛选: Optional[int] = None,
    创建人id筛选: Optional[int] = None,
    关键词搜索: Optional[str] = None,
) -> tuple[List[Dict[str, Any]], int]:
    """
    分页查询样品列表

    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        样品类型筛选: 按样品类型筛选
        样品状态筛选: 按样品状态筛选
        团队id筛选: 按团队筛选
        创建人id筛选: 按创建人筛选
        关键词搜索: 关键词搜索

    Returns:
        (样品列表, 总记录数)
    """
    try:
        where_条件 = []
        参数列表 = []
        参数索引 = 1

        if 样品类型筛选:
            where_条件.append("s.样品类型 = $1")
            参数列表.append(样品类型筛选)
            参数索引 += 1

        if 样品状态筛选:
            where_条件.append("s.样品状态 = $1")
            参数列表.append(样品状态筛选)
            参数索引 += 1
        else:
            # 默认不显示已删除的样品
            where_条件.append("s.样品状态 != '已删除'")

        if 团队id筛选:
            where_条件.append("s.团队id = $1")
            参数列表.append(团队id筛选)
            参数索引 += 1

        if 创建人id筛选:
            where_条件.append("s.创建人id = $1")
            参数列表.append(创建人id筛选)
            参数索引 += 1

        if 关键词搜索:
            where_条件.append("(s.样品名称 ILIKE $1 OR s.样品描述 ILIKE $1)")
            参数列表.append(f"%{关键词搜索}%")
            参数索引 += 1

        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 样品表 s
        {where_clause}
        """

        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT 
            s.id, s.样品名称, s.样品类型, s.创建人id, s.样品描述,
            s.样品规格, s.样品数量, s.样品单位, s.存储位置, s.团队id,
            s.样品状态, s.创建时间, s.更新时间,
            u.用户名 as 创建人姓名,
            t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人id = u.id
        LEFT JOIN 团队表 t ON s.团队id = t.id
        {where_clause}
        ORDER BY s.创建时间 DESC
        LIMIT $1 OFFSET $2
        """

        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])

        样品列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表)

        数据库日志器.debug(
            f"分页查询样品列表成功，页码: {页码}, 每页: {每页数量}, 总数: {总数}"
        )
        return 样品列表 or [], 总数

    except Exception as e:
        错误日志器.error(
            f"分页查询样品列表异常，页码: {页码}, 每页: {每页数量}, 错误: {str(e)}"
        )
        return [], 0


async def 获取样品类型列表() -> List[Dict[str, Any]]:
    """
    获取所有样品类型列表

    Returns:
        样品类型列表
    """
    try:
        查询SQL = """
        SELECT 样品类型, COUNT(*) as 数量
        FROM 样品表
        WHERE 样品状态 != '已删除'
        GROUP BY 样品类型
        ORDER BY 数量 DESC, 样品类型
        """

        结果 = await 异步连接池实例.执行查询(查询SQL)

        数据库日志器.debug(f"获取样品类型列表成功，共{len(结果)}种类型")
        return 结果 or []

    except Exception as e:
        错误日志器.error(f"获取样品类型列表异常: {str(e)}")
        return []


async def 获取样品统计信息(
    团队id: Optional[int] = None, 创建人id: Optional[int] = None
) -> Dict[str, Any]:
    """
    获取样品统计信息

    Args:
        团队id: 团队id筛选（可选）
        创建人id: 创建人id筛选（可选）

    Returns:
        统计信息字典
    """
    try:
        where_条件 = ["样品状态 != '已删除'"]
        参数列表 = []
        参数索引 = 1

        if 团队id:
            where_条件.append("团队id = $1")
            参数列表.append(团队id)
            参数索引 += 1

        if 创建人id:
            where_条件.append("创建人id = $1")
            参数列表.append(创建人id)
            参数索引 += 1

        where_clause = " AND ".join(where_条件)

        # 基础统计
        基础统计SQL = f"""
        SELECT
            COUNT(*) as 总样品数,
            COUNT(DISTINCT 样品类型) as 样品类型数,
            SUM(样品数量) as 总数量,
            COUNT(CASE WHEN 样品状态 = '正常' THEN 1 END) as 正常样品数,
            COUNT(CASE WHEN 样品状态 = '缺货' THEN 1 END) as 缺货样品数,
            COUNT(CASE WHEN 样品状态 = '停用' THEN 1 END) as 停用样品数
        FROM 样品表
        WHERE {where_clause}
        """

        基础结果 = await 异步连接池实例.执行查询(基础统计SQL, 参数列表)
        基础统计 = 基础结果[0] if 基础结果 else {}

        # 按类型统计
        类型统计SQL = f"""
        SELECT 样品类型, COUNT(*) as 数量, SUM(样品数量) as 总数量
        FROM 样品表
        WHERE {where_clause}
        GROUP BY 样品类型
        ORDER BY 数量 DESC
        LIMIT 10
        """

        类型统计 = await 异步连接池实例.执行查询(类型统计SQL, 参数列表)

        # 按状态统计
        状态统计SQL = f"""
        SELECT 样品状态, COUNT(*) as 数量
        FROM 样品表
        WHERE {where_clause}
        GROUP BY 样品状态
        ORDER BY 数量 DESC
        """

        状态统计 = await 异步连接池实例.执行查询(状态统计SQL, 参数列表)

        统计结果 = {
            "基础统计": 基础统计,
            "按类型统计": 类型统计 or [],
            "按状态统计": 状态统计 or [],
        }

        数据库日志器.debug("获取样品统计信息成功")
        return 统计结果

    except Exception as e:
        错误日志器.error(f"获取样品统计信息异常: {str(e)}")
        return {"基础统计": {}, "按类型统计": [], "按状态统计": []}


async def 批量更新样品状态(
    样品id列表: List[int], 新状态: str, 操作人ID: int
) -> tuple[int, int]:
    """
    批量更新样品状态

    Args:
        样品id列表: 样品id列表
        新状态: 新状态
        操作人ID: 操作人ID

    Returns:
        (成功数量, 失败数量)
    """
    try:
        if not 样品id列表:
            return 0, 0

        成功数量 = 0
        失败数量 = 0

        # 使用事务进行批量更新
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.transaction():
                for 样品id in 样品id列表:
                    try:
                        更新SQL = """
                        UPDATE 样品表
                        SET 样品状态 = $1, 更新时间 = CURRENT_TIMESTAMP
                        WHERE id = $2
                        """

                        影响行数 = await 连接.execute(更新SQL, 新状态, 样品id)

                        if 影响行数 > 0:
                            成功数量 += 1
                        else:
                            失败数量 += 1

                    except Exception as e:
                        错误日志器.error(
                            f"批量更新单个样品状态失败: ID={样品id}, 错误={str(e)}"
                        )
                        失败数量 += 1

        数据库日志器.info(
            f"批量更新样品状态完成: 成功={成功数量}, 失败={失败数量}, 操作人={操作人ID}"
        )
        return 成功数量, 失败数量

    except Exception as e:
        错误日志器.error(f"批量更新样品状态异常: 错误={str(e)}")
        return 0, len(样品id列表) if 样品id列表 else 0


async def 搜索样品(
    关键词: str, 限制数量: int = 50, 团队id: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    搜索样品

    Args:
        关键词: 搜索关键词
        限制数量: 限制返回数量
        团队id: 团队id筛选（可选）

    Returns:
        搜索结果列表
    """
    try:
        where_条件 = [
            "样品状态 != '已删除'",
            "(样品名称 ILIKE $1 OR 样品描述 ILIKE $1 OR 样品规格 ILIKE $1)",
        ]
        参数列表 = [f"%{关键词}%"]
        参数索引 = 2

        if 团队id:
            where_条件.append(f"团队id = ${参数索引}")
            参数列表.append(团队id)
            参数索引 += 1

        where_clause = " AND ".join(where_条件)

        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
        搜索SQL = f"""
        SELECT
            s.id, s.样品名称, s.样品类型, s.样品描述, s.样品规格,
            s.样品数量, s.样品单位, s.存储位置, s.样品状态,
            s.创建时间, s.团队id,
            u.用户名 as 创建人姓名,
            t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人id = u.id
        LEFT JOIN 团队表 t ON s.团队id = t.id
        WHERE {where_clause}
        ORDER BY
            CASE
                WHEN s.样品名称 ILIKE ${len(参数列表) + 1} THEN 1
                WHEN s.样品描述 ILIKE ${len(参数列表) + 1} THEN 2
                ELSE 3
            END,
            s.创建时间 DESC
        LIMIT ${len(参数列表) + 2}
        """

        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数占位符最佳实践 }}
        # 添加排序和限制参数
        参数列表.extend([f"%{关键词}%", 限制数量])

        结果 = await 异步连接池实例.执行查询(搜索SQL, 参数列表)

        数据库日志器.debug(
            f"搜索样品成功: 关键词={关键词}, 结果数={len(结果) if 结果 else 0}"
        )
        return 结果 or []

    except Exception as e:
        错误日志器.error(f"搜索样品异常: 关键词={关键词}, 错误={str(e)}")
        return []
