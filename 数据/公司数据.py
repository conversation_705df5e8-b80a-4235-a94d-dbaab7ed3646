"""
公司数据操作模块
负责处理公司相关的数据库操作
"""

from datetime import datetime
from typing import Optional, Dict, Any

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器
from 状态 import 状态


async def 创建公司(
    公司名称: str,
    创建人id: int,
    公司简称: Optional[str] = None,
    公司代码: Optional[str] = None,
    公司地址: Optional[str] = None,
    联系电话: Optional[str] = None,
    邮箱: Optional[str] = None,
    法人代表: Optional[str] = None,
    营业执照号: Optional[str] = None,
    备注: Optional[str] = None
) -> Dict[str, Any]:
    """创建公司"""
    try:
        # 检查用户是否已有未审核通过的公司
        未审核公司检查 = await 检查用户未审核公司(创建人id)
        if not 未审核公司检查["success"]:
            return 未审核公司检查

        # 检查公司名称是否重复
        if await 检查公司名称重复(公司名称):
            return {"success": False, "status": 状态.公司管理.公司名称重复, "message": "公司名称已存在"}

        # 统一处理可选参数，将空字符串转换为None
        参数字典 = {
            "公司简称": None if 公司简称 == '' else 公司简称,
            "公司代码": None if 公司代码 == '' else 公司代码,
            "公司地址": None if 公司地址 == '' else 公司地址,
            "联系电话": None if 联系电话 == '' else 联系电话,
            "邮箱": None if 邮箱 == '' else 邮箱,
            "法人代表": None if 法人代表 == '' else 法人代表,
            "营业执照号": None if 营业执照号 == '' else 营业执照号,
            "备注": None if 备注 == '' else 备注
        }

        # 检查处理后的公司代码是否重复（如果非None）
        if 参数字典["公司代码"] and await 检查公司代码重复(参数字典["公司代码"]):
            return {"success": False, "status": 状态.公司管理.公司代码重复, "message": "公司代码已存在"}
        
        # 构建插入SQL和参数
        SQL = """
        INSERT INTO 公司表
        (公司名称, 公司简称, 公司代码, 公司地址, 联系电话, 邮箱, 法人代表, 营业执照号, 创建人id, 备注)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        """
        
        参数 = (
            公司名称, 参数字典["公司简称"], 参数字典["公司代码"], 参数字典["公司地址"], 
            参数字典["联系电话"], 参数字典["邮箱"], 参数字典["法人代表"], 参数字典["营业执照号"], 
            创建人id, 参数字典["备注"]
        )

        公司ID = await 异步连接池实例.执行插入(SQL, 参数)
        
        if 公司ID:
            数据库日志器.info(f"创建公司成功，公司ID: {公司ID}，创建人: {创建人id}")
            
            return {"success": True, "status": 状态.公司管理.创建成功, "company_id": 公司ID, "message": "公司创建成功"}
        else:
            错误日志器.error(f"公司数据插入失败: 公司名称='{公司名称}', 创建人id={创建人id}")
            return {
                "success": False,
                "status": 状态.公司管理.创建失败,
                "message": "公司数据保存失败。可能原因：公司代码或其他唯一信息已存在，或输入数据不符合要求。请检查后重试或联系技术支持。"
            }
            
    except Exception as e:
        错误日志器.error(f"创建公司过程中发生异常: {e}", exc_info=True)
        return {"success": False, "status": 状态.公司管理.创建失败, "message": "创建公司服务遇到内部错误，请稍后尝试。"}


async def 检查字段重复(字段名: str, 字段值: str, 排除公司ID: Optional[int] = None) -> bool:
    """通用字段重复检查方法"""
    try:
        # 如果字段值为空或None，不进行重复检查
        if not 字段值 or not 字段值.strip():
            return False

        if 排除公司ID:
            SQL = f"SELECT 1 FROM 公司表 WHERE {字段名} = $1 AND id != $2 LIMIT 1"
            参数 = (字段值, 排除公司ID)
        else:
            SQL = f"SELECT 1 FROM 公司表 WHERE {字段名} = $1 LIMIT 1"
            参数 = (字段值,)

        结果 = await 异步连接池实例.执行查询(SQL, 参数)
        return len(结果) > 0
    except Exception as e:
        错误日志器.error(f"检查{字段名}重复失败: {e}")
        return False


# 保持向后兼容的便捷方法
async def 检查公司名称重复(公司名称: str, 排除公司ID: Optional[int] = None) -> bool:
    """检查公司名称是否重复"""
    return await 检查字段重复("公司名称", 公司名称, 排除公司ID)


async def 检查公司代码重复(公司代码: str, 排除公司ID: Optional[int] = None) -> bool:
    """检查公司代码是否重复"""
    return await 检查字段重复("公司代码", 公司代码, 排除公司ID)


async def 检查用户未审核公司(创建人id: int) -> Dict[str, Any]:
    """检查用户是否已有未审核通过的公司"""
    try:
        # 查询用户是否有审核状态为0（未审核）或2（审核不通过）的公司
        SQL = """
        SELECT COUNT(*) as count,
               GROUP_CONCAT(公司名称) as 公司名称列表
        FROM 公司表
        WHERE 创建人id = $1 AND 审核状态 IN (0, 2)
        """
        结果 = await 异步连接池实例.执行查询(SQL, (创建人id,))

        if 结果 and 结果[0]["count"] > 0:
            公司名称列表 = 结果[0]["公司名称列表"] or "未知公司"
            return {
                "success": False,
                "status": 状态.公司管理.已有未审核公司,
                "message": f"您已有未审核通过的公司（{公司名称列表}），请等待当前公司审核完成后再创建新公司"
            }

        return {"success": True}

    except Exception as e:
        错误日志器.error(f"检查用户未审核公司失败: 创建人id={创建人id}, 错误={e}")
        return {"success": False, "status": 状态.通用.服务器错误, "message": "系统检查失败，请稍后重试"}


async def 获取公司列表(
    页码: int = 1,
    每页数量: int = 10,
    搜索关键词: Optional[str] = None,
    公司状态: Optional[str] = None,
    审核状态: Optional[int] = None,
    创建人id: Optional[int] = None
) -> Dict[str, Any]:
    """获取公司列表"""
    try:
        # 构建WHERE条件
        WHERE条件 = []
        参数列表 = []
        参数索引 = 1

        if 创建人id:
            WHERE条件.append(f"c.创建人id = ${参数索引}")
            参数列表.append(创建人id)
            参数索引 += 1

        if 公司状态:
            WHERE条件.append(f"c.公司状态 = ${参数索引}")
            参数列表.append(公司状态)
            参数索引 += 1

        if 审核状态 is not None:
            WHERE条件.append(f"c.审核状态 = ${参数索引}")
            参数列表.append(审核状态)
            参数索引 += 1

        # 处理搜索关键词
        if 搜索关键词:
            关键词模式 = f"%{搜索关键词}%"
            WHERE条件.append(f"(c.公司名称 LIKE ${参数索引} OR c.公司简称 LIKE ${参数索引 + 1} OR c.公司代码 LIKE ${参数索引 + 2})")
            参数列表.extend([关键词模式, 关键词模式, 关键词模式])
            参数索引 += 3
        
        WHERE子句 = " AND ".join(WHERE条件) if WHERE条件 else "1=1"
        
        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 公司表 c
        WHERE {WHERE子句}
        """
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数量 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 分页查询
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT 
            c.id as 公司ID, c.公司名称, c.公司简称, c.公司代码, c.公司地址, c.联系电话, c.邮箱, 
            c.法人代表, c.营业执照号, c.创建人id, c.创建时间, c.更新时间, c.公司状态, c.审核状态, 
            c.审核时间, c.审核人ID, c.审核备注, c.备注,
            COALESCE(t.团队数量, 0) as 团队数量
        FROM 公司表 c
        LEFT JOIN (
            SELECT 公司ID, COUNT(*) as 团队数量 
            FROM 团队表
            WHERE 团队状态 != '解散'
            GROUP BY 公司ID
        ) t ON c.id = t.公司ID
        WHERE {WHERE子句}
        ORDER BY c.创建时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        分页参数 = 参数列表 + [每页数量, 偏移量]
        公司列表 = await 异步连接池实例.执行查询(查询SQL, 分页参数)
        
        总页数 = (总数量 + 每页数量 - 1) // 每页数量
        
        return {
            "list": 公司列表,
            "total": 总数量,
            "page": 页码,
            "size": 每页数量,
            "pages": 总页数
        }
        
    except Exception as e:
        错误日志器.error(f"获取公司列表失败: {e}", exc_info=True)
        return {"list": [], "total": 0, "page": 页码, "size": 每页数量, "pages": 0}


async def 审核公司(
    公司ID: int,
    审核状态: int,
    审核人ID: int,
    审核备注: Optional[str] = None
) -> Dict[str, Any]:
    """审核公司"""
    try:
        # 检查公司是否存在
        检查SQL = "SELECT id FROM 公司表 WHERE id = $1"
        公司信息 = await 异步连接池实例.执行查询(检查SQL, (公司ID,))
        if not 公司信息:
            return {"success": False, "message": "公司不存在"}
        
        # 更新公司审核状态
        SQL = """
        UPDATE 公司表 SET 
            审核状态 = $1, 审核人ID = $2, 审核时间 = $3, 审核备注 = $4, 更新时间 = $5
        WHERE id = $6
        """
        
        当前时间 = datetime.now()
        参数 = (审核状态, 审核人ID, 当前时间, 审核备注, 当前时间, 公司ID)
        
        影响行数 = await 异步连接池实例.执行更新(SQL, 参数)
        
        if 影响行数 > 0:
            数据库日志器.info(f"公司审核成功: 公司ID={公司ID}, 审核状态={审核状态}, 审核人={审核人ID}")
            return {"success": True, "message": "公司审核成功"}
        else:
            return {"success": False, "message": "公司审核失败"}
            
    except Exception as e:
        错误日志器.error(f"审核公司失败: 公司ID={公司ID}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"审核公司失败: {str(e)}"}


async def 获取公司详情(公司ID: int) -> Optional[Dict[str, Any]]:
    """获取公司详情"""
    try:
        SQL = """
        SELECT 
            c.id as 公司ID, c.公司名称, c.公司简称, c.公司代码, c.公司地址, c.联系电话, c.邮箱,
            c.法人代表, c.营业执照号, c.创建人id, c.创建时间, c.更新时间, c.公司状态, c.审核状态,
            c.审核时间, c.审核人ID, c.审核备注, c.备注,
            COALESCE(creator.昵称, creator.phone, '') as 创建人姓名,
            COALESCE(auditor.昵称, auditor.phone, '') as 审核人姓名,
            COALESCE(t.团队数量, 0) as 团队数量
        FROM 公司表 c
        LEFT JOIN 用户表 creator ON c.创建人id = creator.id
        LEFT JOIN 用户表 auditor ON c.审核人ID = auditor.id
        LEFT JOIN (
            SELECT 公司ID, COUNT(*) as 团队数量 
            FROM 团队表
            WHERE 团队状态 != '解散'
            GROUP BY 公司ID
        ) t ON c.id = t.公司ID
        WHERE c.id = $1
        """
        
        结果 = await 异步连接池实例.执行查询(SQL, (公司ID,))
        return 结果[0] if 结果 else None
        
    except Exception as e:
        错误日志器.error(f"获取公司详情失败: 公司ID={公司ID}, 错误={e}")
        return None


async def 更新公司信息(
    公司ID: int,
    更新数据: Dict[str, Any],
    操作人ID: int
) -> Dict[str, Any]:
    """更新公司信息"""
    try:
        # 检查公司是否存在
        检查SQL = "SELECT id FROM 公司表 WHERE id = $1"
        公司信息 = await 异步连接池实例.执行查询(检查SQL, (公司ID,))
        if not 公司信息:
            return {"success": False, "message": "公司不存在"}
        
        # 构建更新SQL
        更新字段 = []
        参数列表 = []
        
        允许更新字段 = [
            "公司名称", "公司简称", "公司代码", "公司地址", "联系电话", "邮箱",
            "法人代表", "营业执照号", "备注",
            # 审核相关字段（用于重新提交时重置审核状态）
            "审核状态", "审核人ID", "审核时间", "审核备注"
        ]
        
        参数索引 = 1
        for 字段 in 允许更新字段:
            if 字段 in 更新数据:
                更新字段.append(f"{字段} = ${参数索引}")
                字段值 = 更新数据[字段]

                # 只对非审核字段进行空字符串转换，避免唯一约束冲突
                审核字段 = ["审核状态", "审核人ID", "审核时间", "审核备注"]
                if 字段 not in 审核字段 and 字段值 == "":
                    字段值 = None

                参数列表.append(字段值)
                参数索引 += 1

        if not 更新字段:
            return {"success": False, "message": "没有需要更新的数据"}

        # 添加更新时间
        更新字段.append(f"更新时间 = ${参数索引}")
        参数列表.append(datetime.now())
        参数索引 += 1

        # 添加WHERE条件参数
        参数列表.append(公司ID)

        SQL = f"UPDATE 公司表 SET {', '.join(更新字段)} WHERE id = ${参数索引}"
        
        影响行数 = await 异步连接池实例.执行更新(SQL, 参数列表)
        
        if 影响行数 > 0:
            数据库日志器.info(f"更新公司信息成功: 公司ID={公司ID}, 操作人={操作人ID}")
            return {"success": True, "message": "公司信息更新成功"}
        else:
            return {"success": False, "message": "公司信息更新失败"}
            
    except Exception as e:
        错误日志器.error(f"更新公司信息失败: 公司ID={公司ID}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"更新公司信息失败: {str(e)}"}


async def 数据层更新公司(
    公司ID: int,
    公司名称: str,
    更新人ID: int,
    公司简称: Optional[str] = None,
    公司代码: Optional[str] = None,
    公司地址: Optional[str] = None,
    联系电话: Optional[str] = None,
    邮箱: Optional[str] = None,
    法人代表: Optional[str] = None,
    营业执照号: Optional[str] = None,
    备注: Optional[str] = None
) -> Dict[str, Any]:
    """更新公司信息并支持重新提交审核"""
    try:
        # 检查公司是否存在且属于当前用户
        检查SQL = "SELECT 创建人id, 审核状态 FROM 公司表 WHERE id = $1"
        公司信息 = await 异步连接池实例.执行查询(检查SQL, (公司ID,))

        if not 公司信息:
            return {"success": False, "status": 状态.公司管理.公司不存在, "message": "公司不存在"}

        公司数据 = 公司信息[0]
        if 公司数据['创建人id'] != 更新人ID:
            return {"success": False, "status": 状态.公司管理.权限不足, "message": "无权限修改此公司"}

        # 检查公司名称是否重复（排除自己）
        if await 检查公司名称重复(公司名称, 排除公司ID=公司ID):
            return {"success": False, "status": 状态.公司管理.公司名称重复, "message": "公司名称已存在"}

        # 检查公司代码是否重复（如果提供了代码且不为空字符串）
        if 公司代码 and 公司代码.strip() and await 检查公司代码重复(公司代码, 排除公司ID=公司ID):
            return {"success": False, "status": 状态.公司管理.公司代码重复, "message": "公司代码已存在"}

        # 构建更新数据，将空字符串转换为None
        def 处理空值(值):
            return None if 值 == "" else 值

        更新数据 = {
            "公司名称": 公司名称,  # 公司名称不能为空，不需要处理
            "公司简称": 处理空值(公司简称),
            "公司代码": 处理空值(公司代码),
            "公司地址": 处理空值(公司地址),
            "联系电话": 处理空值(联系电话),
            "邮箱": 处理空值(邮箱),
            "法人代表": 处理空值(法人代表),
            "营业执照号": 处理空值(营业执照号),
            "备注": 处理空值(备注)
        }

        # 处理审核状态重置逻辑
        原审核状态 = 公司数据['审核状态']
        数据库日志器.info(f"公司 {公司ID} 原审核状态: {原审核状态}")

        if 原审核状态 == 2:  # 审核不通过 -> 重新提交审核
            更新数据["审核状态"] = 0  # 重置为未审核
            更新数据["审核人ID"] = None
            更新数据["审核时间"] = None
            更新数据["审核备注"] = None
            数据库日志器.info(f"公司 {公司ID} 审核状态已重置为未审核")
        elif 原审核状态 == 0:  # 未审核 -> 保持未审核状态
            # 不需要修改审核相关字段，保持原状态
            数据库日志器.info(f"公司 {公司ID} 保持未审核状态")
            pass
        # 注意：审核通过(状态=1)的公司理论上不应该被编辑，前端应该已经限制

        # 调用通用更新方法
        结果 = await 更新公司信息(公司ID, 更新数据, 更新人ID)

        if 结果["success"]:
            # 根据原审核状态设置不同的成功消息
            if 原审核状态 == 2:
                结果["message"] = "公司信息更新成功，已重新提交审核"
            elif 原审核状态 == 0:
                结果["message"] = "公司信息更新成功"
            else:  # 审核通过的情况（理论上不应该发生）
                结果["message"] = "公司信息更新成功"
            结果["status"] = 状态.公司管理.创建成功
        else:
            结果["status"] = 状态.公司管理.创建失败

        return 结果

    except Exception as e:
        错误日志器.error(f"数据层更新公司失败: 公司ID={公司ID}, 错误={e}", exc_info=True)
        return {"success": False, "status": 状态.公司管理.创建失败, "message": "更新公司失败"}
