"""
异步微信数据访问层

负责微信相关的数据库操作，包括微信账号权限验证、微信好友信息更新等
遵循三层分离架构中的数据访问层职责
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器


async def 异步数据_验证用户微信账号权限(用户id: int, 微信id: int) -> bool:
    """
    数据层：验证用户对指定微信账号的权限

    Args:
        用户id: 用户标识
        微信id: 微信账号ID

    Returns:
        bool: True表示有权限，False表示无权限
    """
    try:
        验证SQL = """
        SELECT 1 FROM 用户微信关联表
        WHERE 用户id = $1 AND 微信id = $2 AND 状态 = 1
        """

        验证结果 = await 异步连接池实例.执行查询(验证SQL, (用户id, 微信id))

        return len(验证结果) > 0

    except Exception as e:
        错误日志器.error(f"数据_验证用户微信账号权限失败: {str(e)}")
        return False


async def 异步数据_获取微信好友下次沟通时间列表(
    用户id: Optional[int] = None,
    我方微信号id: Optional[int] = None,
    页码: int = 1,
    每页条数: int = 20,
) -> Dict[str, Any]:
    """
    数据层：查询微信好友表中所有微信好友的下次沟通时间信息

    Args:
        用户id: 用户ID，用于筛选该用户的微信号
        我方微信号id: 指定的微信号ID，如果不指定则获取用户所有微信号的好友
        页码: 页码，默认为1
        每页条数: 每页显示条数，默认为20

    Returns:
        Dict[str, Any]: 包含好友下次沟通时间列表和分页信息的字典
    """
    try:
        # 构建查询参数
        查询参数 = []
        查询条件_列表 = []
        参数索引 = 1

        # 如果指定了用户ID，需要通过用户微信关联表筛选
        if 用户id:
            if 我方微信号id:
                # 验证微信号是否属于该用户
                查询条件_列表.append(f"f.我方微信号id = ${参数索引}")
                查询参数.append(我方微信号id)
                参数索引 += 1

                # 添加用户权限验证条件
                查询条件_列表.append(f"uwx.用户id = ${参数索引}")
                查询参数.append(用户id)
                参数索引 += 1
            else:
                # 获取用户所有微信号的好友
                查询条件_列表.append(f"uwx.用户id = ${参数索引}")
                查询参数.append(用户id)
                参数索引 += 1
        elif 我方微信号id:
            # 只指定了微信号ID，不验证用户权限
            查询条件_列表.append(f"f.我方微信号id = ${参数索引}")
            查询参数.append(我方微信号id)
            参数索引 += 1

        # 只查询有下次沟通时间的记录
        查询条件_列表.append("f.下次沟通时间 IS NOT NULL")

        查询条件 = "WHERE " + " AND ".join(查询条件_列表) if 查询条件_列表 else ""

        # 构建基础查询SQL
        基础查询_FROM = """
        FROM 微信好友表 f
        INNER JOIN 微信信息表 w1 ON f.我方微信号id = w1.id
        INNER JOIN 微信信息表 w2 ON f.对方微信号id = w2.id
        """

        # 如果需要用户权限验证，添加用户微信关联表
        if 用户id:
            基础查询_FROM += "INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id AND uwx.状态 = 1"

        # 获取总数
        总数查询 = f"""
        SELECT COUNT(*) as total
        {基础查询_FROM}
        {查询条件}
        """

        # 获取分页数据
        偏移量 = (页码 - 1) * 每页条数
        数据查询 = f"""
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.下次沟通时间,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            f.备注,
            w1.微信号 as 我方微信号,
            w1.昵称 as 我方昵称,
            w2.微信号 as 对方微信号,
            w2.昵称 as 对方昵称
        {基础查询_FROM}
        {查询条件}
        ORDER BY f.下次沟通时间 ASC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        查询参数_分页 = 查询参数.copy()
        查询参数_分页.extend([每页条数, 偏移量])

        # 执行查询
        总数结果 = await 异步连接池实例.执行查询(总数查询, tuple(查询参数))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        好友列表 = await 异步连接池实例.执行查询(数据查询, tuple(查询参数_分页))

        return {
            "status": 状态.通用.成功,
            "data": {
                "列表": 好友列表,
                "总数": 总数,
                "页码": 页码,
                "每页条数": 每页条数,
                "总页数": (总数 + 每页条数 - 1) // 每页条数,
            },
        }

    except Exception as e:
        错误日志器.error(f"数据_获取微信好友下次沟通时间列表失败: {str(e)}")
        return {
            "status": 状态.通用.数据库错误,
            "message": f"查询微信好友下次沟通时间列表失败: {str(e)}",
            "data": None,
        }


async def 异步数据_检查微信好友记录是否存在(我方微信id: int, 好友识别ID: str) -> bool:
    """
    数据层：检查微信好友记录是否存在

    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID

    Returns:
        bool: True表示记录存在，False表示不存在
    """
    try:
        检查SQL = """
        SELECT COUNT(*) as count FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        检查结果 = await 异步连接池实例.执行查询(检查SQL, (我方微信id, 好友识别ID))

        记录数量 = 检查结果[0]["count"] if 检查结果 else 0

        return 记录数量 > 0

    except Exception as e:
        错误日志器.error(f"数据_检查微信好友记录是否存在失败: {str(e)}")
        return False


async def 异步数据_通过识别ID更新微信好友信息(
    我方微信id: int, 好友识别ID: str, 更新字段: Dict[str, Any]
) -> Dict[str, Any]:
    """
    数据层：通过识别ID更新微信好友信息

    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID
        更新字段: 需要更新的字段及其值的映射

    Returns:
        Dict[str, Any]: 包含影响行数等操作结果的字典
    """
    try:
        # 1. 先检查记录是否存在
        记录存在 = await 异步数据_检查微信好友记录是否存在(我方微信id, 好友识别ID)
        if not 记录存在:
            return {"影响行数": 0, "错误信息": "微信好友记录不存在"}

        # 2. 构建动态更新SQL
        字段映射 = {
            "是否失效": "是否失效",
            "发送请求时间": "发送请求时间",
            "好友入库时间": "好友入库时间",
            "我方最后一条消息发送时间": "我方最后一条消息发送时间",
            "对方最后一条消息发送时间": "对方最后一条消息发送时间",
            "备注": "备注",
        }

        更新SQL片段 = []
        更新参数 = []
        参数索引 = 1

        for 字段名, 字段值 in 更新字段.items():
            if 字段名 in 字段映射:
                更新SQL片段.append(f"{字段映射[字段名]} = ${参数索引}")
                更新参数.append(字段值)
                参数索引 += 1

        if not 更新SQL片段:
            return {"影响行数": 0, "错误信息": "没有有效的更新字段"}

        # 3. 执行更新操作
        更新参数.extend([我方微信id, 好友识别ID])
        更新SQL = f"""
        UPDATE 微信好友表
        SET {", ".join(更新SQL片段)}
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(更新参数))

        return {"影响行数": 影响行数, "更新字段数": len(更新SQL片段)}

    except Exception as e:
        错误日志器.error(f"数据_通过识别ID更新微信好友信息失败: {str(e)}")
        return {"影响行数": 0, "错误信息": f"数据库操作失败: {str(e)}"}


async def 异步数据_获取微信好友详细信息_通过识别ID(
    我方微信id: int, 好友识别ID: str
) -> Optional[Dict[str, Any]]:
    """
    数据层：通过识别ID获取微信好友详细信息

    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID

    Returns:
        Optional[Dict[str, Any]]: 好友详细信息，如果不存在则返回None
    """
    try:
        查询SQL = """
        SELECT 
            id,
            我方微信号id,
            对方微信号id,
            识别id,
            是否失效,
            发送请求时间,
            好友入库时间,
            我方最后一条消息发送时间,
            对方最后一条消息发送时间,
            备注,
            创建时间,
            更新时间
        FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, (我方微信id, 好友识别ID))

        if 查询结果:
            return 查询结果[0]

        return None

    except Exception as e:
        错误日志器.error(f"数据_获取微信好友详细信息_通过识别ID失败: {str(e)}")
        return None


async def 异步数据_获取用户绑定的微信账号列表(用户id: int) -> list:
    """
    数据层：获取用户绑定的微信账号列表

    Args:
        用户id: 用户标识

    Returns:
        list: 用户绑定的微信账号列表
    """
    try:
        查询SQL = """
        SELECT 
            w.id as 微信id,
            w.微信号,
            w.昵称,
            w.绑定手机号,
            w.头像,
            uw.绑定时间,
            uw.状态,
            uw.备注
        FROM 用户微信关联表 uw
        INNER JOIN 微信信息表 w ON uw.微信id = w.id
        WHERE uw.用户id = $1 AND uw.状态 = 1
        ORDER BY uw.绑定时间 DESC
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        return 查询结果 or []

    except Exception as e:
        错误日志器.error(f"数据_获取用户绑定的微信账号列表失败: {str(e)}")
        return []


async def 异步数据_验证用户微信好友权限(
    用户id: int,
    我方微信号id: int,
    对方微信号id: Optional[int] = None,
    识别id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    数据层：验证用户对指定微信好友记录的权限并返回记录信息

    Args:
        用户id: 当前用户ID
        我方微信号id: 我方微信号ID
        对方微信号id: 对方微信号ID（可选）
        识别id: 识别ID（可选）

    Returns:
        Dict[str, Any]: 包含权限验证结果和记录信息
    """
    try:
        # 构建查询条件
        查询条件_列表 = ["uwx.用户id = $1", "uwx.状态 = 1", "f.我方微信号id = $2"]
        查询参数 = [用户id, 我方微信号id]
        参数索引 = 3

        if 对方微信号id:
            查询条件_列表.append(f"f.对方微信号id = ${参数索引}")
            查询参数.append(对方微信号id)
            参数索引 += 1

        if 识别id:
            查询条件_列表.append(f"f.识别id = ${参数索引}")
            查询参数.append(识别id)
            参数索引 += 1

        查询条件 = " AND ".join(查询条件_列表)

        # 查询记录并验证权限
        查询SQL = f"""
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            f.下次沟通时间,
            f.备注,
            f.创建时间
        FROM 微信好友表 f
        INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
        WHERE {查询条件}
        LIMIT 1
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, 查询参数)

        if not 查询结果:
            return {
                "status": 状态.用户.权限不足,
                "message": "您没有权限访问此微信好友记录或记录不存在",
                "data": None,
            }

        记录 = 查询结果[0]
        return {
            "status": 状态.通用.成功,
            "message": "权限验证通过",
            "data": {
                "记录信息": dict(记录),
                "查询条件": 查询条件,
                "查询参数": 查询参数,
            },
        }

    except Exception as e:
        错误日志器.error(f"数据_验证用户微信好友权限失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"权限验证失败: {str(e)}",
            "data": None,
        }


async def 异步数据_更新微信好友下次沟通时间(
    查询条件: str, 查询参数: List, 新的下次沟通时间: datetime
) -> Dict[str, Any]:
    """
    数据层：更新微信好友的下次沟通时间

    Args:
        查询条件: SQL查询条件字符串
        查询参数: 查询参数列表
        新的下次沟通时间: 新的下次沟通时间

    Returns:
        Dict[str, Any]: 包含更新结果的响应
    """
    try:
        # 重新构建查询条件，调整参数索引以避免冲突
        # 原查询条件使用 $1, $2, $3...，现在需要调整为 $2, $3, $4...
        # 因为 $1 要用于 SET 下次沟通时间 = $1

        # 将查询条件中的参数索引向后偏移1位
        调整后的查询条件 = 查询条件
        for i in range(len(查询参数), 0, -1):
            调整后的查询条件 = 调整后的查询条件.replace(f"${i}", f"${i + 1}")

        # 执行更新操作
        更新SQL = f"""
        UPDATE 微信好友表 f
        SET 下次沟通时间 = $1
        FROM 用户微信关联表 uwx
        WHERE f.我方微信号id = uwx.微信id
        AND {调整后的查询条件}
        """

        更新参数 = [新的下次沟通时间] + 查询参数
        更新结果 = await 异步连接池实例.执行更新(更新SQL, 更新参数)

        if 更新结果 > 0:
            return {
                "status": 状态.通用.成功,
                "message": "下次沟通时间更新成功",
                "data": {
                    "下次沟通时间": 新的下次沟通时间.strftime("%Y-%m-%d %H:%M:%S")
                    if 新的下次沟通时间
                    else None
                },
            }
        else:
            return {
                "status": 状态.通用.不存在,
                "message": "未找到对应的微信好友记录",
                "data": None,
            }

    except Exception as e:
        错误日志器.error(f"数据_更新微信好友下次沟通时间失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"更新失败: {str(e)}",
            "data": None,
        }


async def 异步数据_查询微信好友消息详情(
    用户id: int, 我方微信号id: int, 识别id: int
) -> Dict[str, Any]:
    """
    数据层：查询微信好友的消息详情信息

    Args:
        用户id: 当前用户ID
        我方微信号id: 我方微信号ID
        识别id: 微信好友识别ID

    Returns:
        Dict[str, Any]: 包含查询结果的响应
    """
    try:
        查询SQL = """
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            f.下次沟通时间,
            f.备注,
            f.创建时间,
            f.好友入库时间,
            f.发送请求时间,
            f.好友通过时间,
            wi_me.微信号 as 我方微信号,
            wi_me.昵称 as 我方微信昵称,
            wi_me.微信头像 as 我方微信头像,
            wi_other.微信号 as 对方微信号,
            wi_other.昵称 as 对方微信昵称,
            wi_other.微信头像 as 对方微信头像
        FROM 微信好友表 f
        INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
        LEFT JOIN 微信信息表 wi_me ON f.我方微信号id = wi_me.id
        LEFT JOIN 微信信息表 wi_other ON f.对方微信号id = wi_other.id
        WHERE uwx.用户id = $1
        AND uwx.状态 = 1
        AND f.我方微信号id = $2
        AND f.识别id = $3
        LIMIT 1
        """

        查询结果 = await 异步连接池实例.执行查询(
            查询SQL, (用户id, 我方微信号id, 识别id)
        )

        if not 查询结果:
            return {
                "status": 状态.通用.不存在,
                "message": "未找到对应的微信好友记录",
                "data": None,
            }

        记录 = dict(查询结果[0])
        return {
            "status": 状态.通用.成功,
            "message": "查询微信好友消息详情成功",
            "data": {"好友详情": 记录},
        }

    except Exception as e:
        错误日志器.error(f"数据_查询微信好友消息详情失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"查询失败: {str(e)}",
            "data": None,
        }
