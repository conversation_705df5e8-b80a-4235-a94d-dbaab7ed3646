"""
LangChain数据操作模块 - PostgreSQL版本
基于asyncpg实现的LangChain智能体相关数据库操作

功能：
1. 智能体配置管理
2. 对话记录管理
3. 模型配置管理
4. 知识库关联管理
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器

# ==================== 智能体配置管理 ====================


async def 创建智能体配置(
    用户id: int,
    智能体名称: str,
    智能体描述: Optional[str] = None,
    模型名称: str = "gpt-3.5-turbo",
    温度参数: float = 0.7,
    最大令牌数: int = 4000,
    系统提示词: Optional[str] = None,
    用户提示词: Optional[str] = None,
    自定义变量: Optional[Dict[str, Any]] = None,
    组织ID: Optional[int] = None,
) -> Optional[int]:
    """
    创建智能体配置

    Args:
        用户id: 用户id
        智能体名称: 智能体名称
        智能体描述: 智能体描述
        模型名称: 模型名称
        温度参数: 温度参数
        最大令牌数: 最大令牌数
        系统提示词: 系统提示词
        用户提示词: 用户提示词
        自定义变量: 自定义变量字典
        组织ID: 组织ID

    Returns:
        新创建智能体配置的ID，失败返回None
    """
    try:
        自定义变量_json = (
            json.dumps(自定义变量, ensure_ascii=False) if 自定义变量 else None
        )

        插入SQL = """
        INSERT INTO 智能体配置表 (
            用户id, 智能体名称, 智能体描述, 模型名称, 温度参数,
            最大令牌数, 系统提示词, 用户提示词, 自定义变量, 组织ID,
            状态, 创建时间
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, '正常', CURRENT_TIMESTAMP
        ) RETURNING id
        """

        结果 = await 异步连接池实例.执行查询(
            插入SQL,
            (
                用户id,
                智能体名称,
                智能体描述,
                模型名称,
                温度参数,
                最大令牌数,
                系统提示词,
                用户提示词,
                自定义变量_json,
                组织ID,
            ),
        )

        if 结果:
            智能体id = 结果[0]["id"]
            数据库日志器.info(f"创建智能体配置成功: ID={智能体id}, 名称={智能体名称}")
            return 智能体id
        else:
            错误日志器.error(f"创建智能体配置失败: 名称={智能体名称}")
            return None

    except Exception as e:
        错误日志器.error(f"创建智能体配置异常: 名称={智能体名称}, 错误={str(e)}")
        return None


async def 获取智能体配置(智能体id: int) -> Optional[Dict[str, Any]]:
    """
    获取智能体配置详情

    Args:
        智能体id: 智能体id

    Returns:
        智能体配置字典，失败返回None
    """
    try:
        查询SQL = """
        SELECT 
            a.id, a.用户id, a.智能体名称, a.智能体描述, a.模型名称,
            a.温度参数, a.最大令牌数, a.系统提示词, a.用户提示词,
            a.自定义变量, a.组织ID, a.状态, a.创建时间, a.更新时间,
            u.用户名 as 创建人姓名,
            o.组织名称
        FROM 智能体配置表 a
        LEFT JOIN 用户表 u ON a.用户id = u.id
        LEFT JOIN 组织表 o ON a.组织ID = o.id
        WHERE a.id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (智能体id,))

        if 结果:
            智能体配置 = dict(结果[0])
            # 解析JSON字段
            if 智能体配置.get("自定义变量"):
                try:
                    智能体配置["自定义变量"] = json.loads(智能体配置["自定义变量"])
                except json.JSONDecodeError:
                    智能体配置["自定义变量"] = {}

            return 智能体配置
        else:
            return None

    except Exception as e:
        错误日志器.error(f"获取智能体配置异常: 智能体id={智能体id}, 错误={str(e)}")
        return None


async def 更新智能体配置(
    智能体id: int, 更新字段: Dict[str, Any], 操作人ID: int
) -> bool:
    """
    更新智能体配置

    Args:
        智能体id: 智能体id
        更新字段: 要更新的字段字典
        操作人ID: 操作人ID

    Returns:
        是否更新成功
    """
    try:
        if not 更新字段:
            return True

        # 构建更新SQL
        字段列表 = []
        参数列表 = []
        参数索引 = 1

        允许字段 = [
            "智能体名称",
            "智能体描述",
            "模型名称",
            "温度参数",
            "最大令牌数",
            "系统提示词",
            "用户提示词",
            "自定义变量",
            "状态",
        ]

        for 字段名, 值 in 更新字段.items():
            if 字段名 in 允许字段:
                if 字段名 == "自定义变量" and isinstance(值, dict):
                    值 = json.dumps(值, ensure_ascii=False)
                字段列表.append(f'"{字段名}" = ${参数索引}')
                参数列表.append(值)
                参数索引 += 1

        if not 字段列表:
            数据库日志器.warning(f"更新智能体配置时没有有效字段: 智能体id={智能体id}")
            return False

        # 添加更新时间
        字段列表.append("更新时间 = CURRENT_TIMESTAMP")

        更新SQL = f"""
        UPDATE 智能体配置表 
        SET {", ".join(字段列表)}
        WHERE id = $1
        """
        参数列表.append(智能体id)

        影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(参数列表))

        if 影响行数 > 0:
            数据库日志器.info(
                f"更新智能体配置成功: 智能体id={智能体id}, 操作人={操作人ID}"
            )
            return True
        else:
            数据库日志器.warning(f"更新智能体配置未影响任何行: 智能体id={智能体id}")
            return False

    except Exception as e:
        错误日志器.error(f"更新智能体配置异常: 智能体id={智能体id}, 错误={str(e)}")
        return False


async def 删除智能体配置(智能体id: int, 操作人ID: int) -> bool:
    """
    删除智能体配置（软删除）

    Args:
        智能体id: 智能体id
        操作人ID: 操作人ID

    Returns:
        是否删除成功
    """
    try:
        更新SQL = """
        UPDATE 智能体配置表 
        SET 状态 = '已删除', 更新时间 = CURRENT_TIMESTAMP
        WHERE id = $1
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, (智能体id,))

        if 影响行数 > 0:
            数据库日志器.info(
                f"删除智能体配置成功: 智能体id={智能体id}, 操作人={操作人ID}"
            )
            return True
        else:
            数据库日志器.warning(f"删除智能体配置未影响任何行: 智能体id={智能体id}")
            return False

    except Exception as e:
        错误日志器.error(f"删除智能体配置异常: 智能体id={智能体id}, 错误={str(e)}")
        return False


# ==================== 对话记录管理 ====================


async def 创建对话记录(
    智能体id: int,
    用户id: int,
    会话ID: str,
    用户消息: str,
    智能体回复: str,
    消息类型: str = "文本",
    令牌消耗: Optional[int] = None,
    响应时间: Optional[float] = None,
    额外数据: Optional[Dict[str, Any]] = None,
) -> Optional[int]:
    """
    创建对话记录

    Args:
        智能体id: 智能体id
        用户id: 用户id
        会话ID: 会话ID
        用户消息: 用户消息内容
        智能体回复: 智能体回复内容
        消息类型: 消息类型
        令牌消耗: 令牌消耗数量
        响应时间: 响应时间（秒）
        额外数据: 额外数据字典

    Returns:
        新创建对话记录的ID，失败返回None
    """
    try:
        额外数据_json = json.dumps(额外数据, ensure_ascii=False) if 额外数据 else None

        插入SQL = """
        INSERT INTO 对话记录表 (
            智能体id, 用户id, 会话ID, 用户消息, 智能体回复,
            消息类型, 令牌消耗, 响应时间, 额外数据, 创建时间
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP
        ) RETURNING id
        """

        结果 = await 异步连接池实例.执行查询(
            插入SQL,
            (
                智能体id,
                用户id,
                会话ID,
                用户消息,
                智能体回复,
                消息类型,
                令牌消耗,
                响应时间,
                额外数据_json,
            ),
        )

        if 结果:
            记录ID = 结果[0]["id"]
            数据库日志器.debug(f"创建对话记录成功: ID={记录ID}, 会话={会话ID}")
            return 记录ID
        else:
            错误日志器.error(f"创建对话记录失败: 会话={会话ID}")
            return None

    except Exception as e:
        错误日志器.error(f"创建对话记录异常: 会话={会话ID}, 错误={str(e)}")
        return None


async def 获取对话记录列表(
    会话ID: Optional[str] = None,
    用户id: Optional[int] = None,
    智能体id: Optional[int] = None,
    页码: int = 1,
    每页数量: int = 50,
    开始时间: Optional[datetime] = None,
    结束时间: Optional[datetime] = None,
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取对话记录列表

    Args:
        会话ID: 会话ID筛选
        用户id: 用户id筛选
        智能体id: 智能体id筛选
        页码: 页码（从1开始）
        每页数量: 每页记录数
        开始时间: 开始时间
        结束时间: 结束时间

    Returns:
        (对话记录列表, 总记录数)
    """
    try:
        where_条件 = []
        参数列表 = []
        参数索引 = 1

        if 会话ID:
            where_条件.append("cr.会话ID = $1")
            参数列表.append(会话ID)
            参数索引 += 1

        if 用户id:
            where_条件.append("cr.用户id = $1")
            参数列表.append(用户id)
            参数索引 += 1

        if 智能体id:
            where_条件.append("cr.智能体id = $1")
            参数列表.append(智能体id)
            参数索引 += 1

        if 开始时间:
            where_条件.append("cr.创建时间 >= $1")
            参数列表.append(开始时间)
            参数索引 += 1

        if 结束时间:
            where_条件.append("cr.创建时间 <= $1")
            参数列表.append(结束时间)
            参数索引 += 1

        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 对话记录表 cr
        {where_clause}
        """

        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT
            cr.id, cr.智能体id, cr.用户id, cr.会话ID, cr.用户消息,
            cr.智能体回复, cr.消息类型, cr.令牌消耗, cr.响应时间,
            cr.额外数据, cr.创建时间,
            u.用户名 as 用户姓名,
            a.智能体名称
        FROM 对话记录表 cr
        LEFT JOIN 用户表 u ON cr.用户id = u.id
        LEFT JOIN 智能体配置表 a ON cr.智能体id = a.id
        {where_clause}
        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数占位符最佳实践 }}
        ORDER BY cr.创建时间 DESC
        LIMIT ${len(参数列表) + 1} OFFSET ${len(参数列表) + 2}
        """

        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])

        对话记录 = await 异步连接池实例.执行查询(查询SQL, 参数列表)

        # 解析JSON字段
        if 对话记录:
            for 记录 in 对话记录:
                if 记录.get("额外数据"):
                    try:
                        记录["额外数据"] = json.loads(记录["额外数据"])
                    except json.JSONDecodeError:
                        记录["额外数据"] = {}

        数据库日志器.debug(f"获取对话记录列表成功，总数: {总数}")
        return 对话记录 or [], 总数

    except Exception as e:
        错误日志器.error(f"获取对话记录列表异常: {str(e)}")
        return [], 0


async def 获取会话统计(会话ID: str) -> Dict[str, Any]:
    """
    获取会话统计信息

    Args:
        会话ID: 会话ID

    Returns:
        会话统计信息字典
    """
    try:
        统计SQL = """
        SELECT
            COUNT(*) as 消息总数,
            SUM(令牌消耗) as 总令牌消耗,
            AVG(响应时间) as 平均响应时间,
            MIN(创建时间) as 会话开始时间,
            MAX(创建时间) as 最后消息时间,
            COUNT(DISTINCT 用户id) as 参与用户数
        FROM 对话记录表
        WHERE 会话ID = $1
        """

        结果 = await 异步连接池实例.执行查询(统计SQL, (会话ID,))
        统计信息 = 结果[0] if 结果 else {}

        数据库日志器.debug(f"获取会话统计成功: 会话ID={会话ID}")
        return 统计信息

    except Exception as e:
        错误日志器.error(f"获取会话统计异常: 会话ID={会话ID}, 错误={str(e)}")
        return {}


# ==================== 模型配置管理 ====================


async def 创建模型配置(
    模型名称: str,
    模型类型: str,
    API端点: str,
    API密钥: Optional[str] = None,
    默认参数: Optional[Dict[str, Any]] = None,
    模型描述: Optional[str] = None,
    创建人id: Optional[int] = None,
) -> Optional[int]:
    """
    创建模型配置

    Args:
        模型名称: 模型名称
        模型类型: 模型类型（OpenAI/Claude/本地等）
        API端点: API端点URL
        API密钥: API密钥
        默认参数: 默认参数字典
        模型描述: 模型描述
        创建人id: 创建人id

    Returns:
        新创建模型配置的ID，失败返回None
    """
    try:
        默认参数_json = json.dumps(默认参数, ensure_ascii=False) if 默认参数 else None

        插入SQL = """
        INSERT INTO 模型配置表 (
            模型名称, 模型类型, API端点, API密钥, 默认参数,
            模型描述, 创建人id, 状态, 创建时间
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, '启用', CURRENT_TIMESTAMP
        ) RETURNING id
        """

        结果 = await 异步连接池实例.执行查询(
            插入SQL,
            (模型名称, 模型类型, API端点, API密钥, 默认参数_json, 模型描述, 创建人id),
        )

        if 结果:
            模型id = 结果[0]["id"]
            数据库日志器.info(f"创建模型配置成功: ID={模型id}, 名称={模型名称}")
            return 模型id
        else:
            错误日志器.error(f"创建模型配置失败: 名称={模型名称}")
            return None

    except Exception as e:
        错误日志器.error(f"创建模型配置异常: 名称={模型名称}, 错误={str(e)}")
        return None


async def 获取模型配置列表(
    模型类型筛选: Optional[str] = None, 状态筛选: str = "启用"
) -> List[Dict[str, Any]]:
    """
    获取模型配置列表

    Args:
        模型类型筛选: 按模型类型筛选
        状态筛选: 按状态筛选

    Returns:
        模型配置列表
    """
    try:
        where_条件 = ["状态 = $1"]
        参数列表 = [状态筛选]
        参数索引 = 2

        if 模型类型筛选:
            where_条件.append(f"模型类型 = ${参数索引}")
            参数列表.append(模型类型筛选)
            参数索引 += 1

        where_clause = " AND ".join(where_条件)

        查询SQL = f"""
        SELECT
            m.id, m.模型名称, m.模型类型, m.API端点, m.默认参数,
            m.模型描述, m.创建人id, m.状态, m.创建时间, m.更新时间,
            u.用户名 as 创建人姓名
        FROM 模型配置表 m
        LEFT JOIN 用户表 u ON m.创建人id = u.id
        WHERE {where_clause}
        ORDER BY m.创建时间 DESC
        """

        模型列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表)

        # 解析JSON字段并隐藏敏感信息
        if 模型列表:
            for 模型 in 模型列表:
                if 模型.get("默认参数"):
                    try:
                        模型["默认参数"] = json.loads(模型["默认参数"])
                    except json.JSONDecodeError:
                        模型["默认参数"] = {}
                # 隐藏API密钥
                if "API密钥" in 模型:
                    del 模型["API密钥"]

        数据库日志器.debug(
            f"获取模型配置列表成功，数量: {len(模型列表) if 模型列表 else 0}"
        )
        return 模型列表 or []

    except Exception as e:
        错误日志器.error(f"获取模型配置列表异常: {str(e)}")
        return []
