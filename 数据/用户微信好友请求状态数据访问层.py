"""
用户微信好友请求状态数据访问层
专门处理用户微信好友请求状态相关的数据库操作
包括查询用户_联系方式_微信添加记录表、用户达人补充信息表、用户达人关联表等
"""

from typing import Dict, Optional, Any, List
from datetime import datetime
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 数据库日志器, 错误日志器


class 用户微信好友请求状态数据访问层:
    """
    用户微信好友请求状态数据访问层
    
    负责处理所有与用户微信好友请求状态相关的数据库操作
    包括查询、创建、更新等操作
    """

    @staticmethod
    async def 查询用户微信添加记录表中好友请求状态为空或为0的记录(用户id: int, 微信信息表id: int) -> Optional[Dict[str, Any]]:
        """
        查询用户_联系方式_微信添加记录表中好友请求状态为null或为0的记录
        这些记录表示需要处理的添加请求

        Args:
            用户id: 用户id
            微信信息表id: 微信信息表ID（必填，用于多微信账号场景）

        Returns:
            Optional[Dict[str, Any]]: 微信添加记录信息，如果不存在则返回None
        """
        try:
            # 优化后的SQL查询，微信信息表id为必填参数
            查询SQL = """
            SELECT
                wxar.id as 微信添加记录id,
                wxar.用户达人补充信息表id,
                wxar.好友请求状态,
                wxar.我方添加的微信信息表id
            FROM 用户_联系方式_微信添加记录表 wxar
            WHERE wxar.用户达人补充信息表id IN (
                SELECT usi.id
                FROM 用户达人补充信息表 usi
                INNER JOIN 用户达人关联表 uar ON usi.用户达人关联表id = uar.id
                WHERE uar.用户id = $1 AND uar.状态 = 1
            )
            AND wxar.我方添加的微信信息表id = $2
            AND (wxar.好友请求状态 IS NULL OR wxar.好友请求状态 = 0)
            ORDER BY wxar.创建时间 DESC
            LIMIT 1
            """

            查询参数 = (用户id, 微信信息表id)

            数据库日志器.debug(f"查询用户微信添加记录表中好友请求状态为空或为0的记录: 用户id={用户id}, 微信信息表id={微信信息表id}")
            查询结果 = await 异步连接池实例.执行查询(查询SQL, 查询参数)
            
            if 查询结果:
                数据库日志器.debug(f"找到微信添加记录: 用户id={用户id}, 记录ID={查询结果[0]['微信添加记录id']}")
                return 查询结果[0]
            else:
                数据库日志器.debug(f"未找到微信添加记录: 用户id={用户id}")
                return None
                
        except Exception as e:
            错误日志器.error(f"查询用户微信添加记录表中好友请求状态不为空的记录失败: 用户id={用户id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 根据用户达人补充信息表ID查询达人详细信息(用户达人补充信息表ID: int) -> Optional[Dict[str, Any]]:
        """
        根据用户达人补充信息表ID查询达人详细信息
        包括达人昵称、联系方式、联系方式类型等
        
        Args:
            用户达人补充信息表ID: 用户达人补充信息表ID
            
        Returns:
            Optional[Dict[str, Any]]: 达人详细信息，如果不存在则返回None
        """
        try:
            查询SQL = """
            SELECT 
                usi.id as 用户达人补充信息表ID,
                usi.用户达人关联表id,
                usi.联系方式表id,
                uar.达人id,
                uar.平台,
                CASE 
                    WHEN uar.平台 = '抖音' THEN dt.昵称
                    WHEN uar.平台 = '微信' THEN wdt.昵称
                    ELSE NULL
                END as 达人昵称,
                ct.联系方式,
                ct.类型 as 联系方式类型
            FROM 用户达人补充信息表 usi
            INNER JOIN 用户达人关联表 uar ON usi.用户达人关联表id = uar.id
            LEFT JOIN 联系方式表 ct ON usi.联系方式表id = ct.id
            LEFT JOIN 达人表 dt ON uar.达人id = dt.id AND uar.平台 = '抖音'
            LEFT JOIN 微信达人表 wdt ON uar.达人id = wdt.id AND uar.平台 = '微信'
            WHERE usi.id = $1
            """
            
            数据库日志器.debug(f"根据用户达人补充信息表ID查询达人详细信息: 用户达人补充信息表ID={用户达人补充信息表ID}")
            查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户达人补充信息表ID,))
            
            if 查询结果:
                数据库日志器.debug(f"查询达人详细信息成功: 用户达人补充信息表ID={用户达人补充信息表ID}")
                return 查询结果[0]
            else:
                数据库日志器.debug(f"未找到达人详细信息: 用户达人补充信息表ID={用户达人补充信息表ID}")
                return None
                
        except Exception as e:
            错误日志器.error(f"根据用户达人补充信息表ID查询达人详细信息失败: 用户达人补充信息表ID={用户达人补充信息表ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 查询用户达人补充信息表中未完成微信添加的记录(用户id: int, 微信信息表id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        查询用户达人补充信息表中完全没有微信添加记录的记录
        只返回那些从未被处理过的用户达人补充信息记录，避免重复处理

        Args:
            用户id: 用户id
            微信信息表id: 微信信息表ID（保留参数兼容性，但当前逻辑不使用）

        Returns:
            Optional[Dict[str, Any]]: 用户达人补充信息记录，如果不存在则返回None
        """
        try:
            # 构建基础SQL查询
            查询SQL = """
            SELECT
                usi.id as 用户达人补充信息表ID,
                usi.用户达人关联表id,
                usi.联系方式表id,
                uar.达人id,
                uar.平台,
                CASE
                    WHEN uar.平台 = '抖音' THEN dt.昵称
                    WHEN uar.平台 = '微信' THEN wdt.昵称
                    ELSE NULL
                END as 达人昵称,
                ct.联系方式,
                ct.类型 as 联系方式类型,
                wxar.id as 现有微信添加记录id,
                wxar.好友请求状态 as 现有好友请求状态
            FROM 用户达人补充信息表 usi
            INNER JOIN 用户达人关联表 uar ON usi.用户达人关联表id = uar.id
            LEFT JOIN 联系方式表 ct ON usi.联系方式表id = ct.id
            LEFT JOIN 达人表 dt ON uar.达人id = dt.id AND uar.平台 = '抖音'
            LEFT JOIN 微信达人表 wdt ON uar.达人id = wdt.id AND uar.平台 = '微信'
            LEFT JOIN 用户_联系方式_微信添加记录表 wxar ON usi.id = wxar.用户达人补充信息表id
            WHERE uar.用户id = $1 AND uar.状态 = 1
            AND wxar.id IS NULL
            ORDER BY usi.创建时间 DESC LIMIT 1
            """

            查询参数 = [用户id]

            数据库日志器.debug(f"查询用户达人补充信息表中未完成微信添加的记录: 用户id={用户id}, 微信信息表id={微信信息表id}")
            查询结果 = await 异步连接池实例.执行查询(查询SQL, tuple(查询参数))
            
            if 查询结果:
                数据库日志器.debug(f"找到未完成微信添加的记录: 用户id={用户id}, 用户达人补充信息表ID={查询结果[0]['用户达人补充信息表ID']}")
                return 查询结果[0]
            else:
                数据库日志器.debug(f"未找到未完成微信添加的记录: 用户id={用户id}")
                return None
                
        except Exception as e:
            错误日志器.error(f"查询用户达人补充信息表中未完成微信添加的记录失败: 用户id={用户id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 创建用户联系方式微信添加记录(用户达人补充信息表ID: int, 微信信息表id: Optional[int] = None, 用户id: Optional[int] = None, 计划添加时间: Optional[datetime] = None) -> int:
        """
        在用户_联系方式_微信添加记录表中创建新记录

        Args:
            用户达人补充信息表ID: 用户达人补充信息表ID
            微信信息表id: 微信信息表ID（可选，用于多微信账号场景）
            用户id: 用户id（可选，用于设置用户表id字段）
            计划添加时间: 计划添加时间（可选，创建时直接设置）

        Returns:
            int: 新创建的记录ID
        """
        try:
            # 构建插入字段和值
            插入字段 = ["用户达人补充信息表id", "创建时间"]
            插入值 = ["$1", "NOW()"]
            插入参数: List[Any] = [用户达人补充信息表ID]
            参数索引 = 2

            if 微信信息表id is not None:
                插入字段.append("我方添加的微信信息表id")
                插入值.append(f"$1")
                插入参数.append(微信信息表id)
                参数索引 += 1

            if 用户id is not None:
                插入字段.append("用户表id")
                插入值.append(f"$1")
                插入参数.append(用户id)
                参数索引 += 1

            if 计划添加时间 is not None:
                插入字段.append("计划添加时间")
                插入值.append(f"$1")
                插入参数.append(计划添加时间)
                参数索引 += 1

            # 构建完整的SQL
            创建SQL = f"""
            INSERT INTO 用户_联系方式_微信添加记录表
            ({', '.join(插入字段)})
            VALUES ({', '.join(插入值)})
            """

            数据库日志器.debug(f"创建用户联系方式微信添加记录: 用户达人补充信息表ID={用户达人补充信息表ID}, 微信信息表id={微信信息表id}, 用户id={用户id}, 计划添加时间={计划添加时间}")
            新记录ID = await 异步连接池实例.执行插入(创建SQL, tuple(插入参数))
            
            数据库日志器.info(f"创建用户联系方式微信添加记录成功: 新记录ID={新记录ID}, 用户达人补充信息表ID={用户达人补充信息表ID}")
            return 新记录ID or 0  # 确保返回int类型
            
        except Exception as e:
            错误日志器.error(f"创建用户联系方式微信添加记录失败: 用户达人补充信息表ID={用户达人补充信息表ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 检查用户是否还有未处理的达人补充信息记录(用户id: int) -> bool:
        """
        检查用户是否还有未处理的达人补充信息记录
        即所有记录都已关联且好友请求状态都不为null且不为0

        Args:
            用户id: 用户id

        Returns:
            bool: True表示还有未处理的记录，False表示所有记录都已处理完成
        """
        try:
            查询SQL = """
            SELECT COUNT(*) as 未处理记录数
            FROM 用户达人补充信息表 usi
            INNER JOIN 用户达人关联表 uar ON usi.用户达人关联表id = uar.id
            LEFT JOIN 用户_联系方式_微信添加记录表 wxar ON usi.id = wxar.用户达人补充信息表id
            WHERE uar.用户id = $1 AND uar.状态 = 1
            AND (wxar.id IS NULL OR wxar.好友请求状态 IS NULL OR wxar.好友请求状态 = 0)
            """
            
            数据库日志器.debug(f"检查用户是否还有未处理的达人补充信息记录: 用户id={用户id}")
            查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))
            
            未处理记录数 = 查询结果[0]['未处理记录数'] if 查询结果 else 0
            有未处理记录 = 未处理记录数 > 0
            
            数据库日志器.debug(f"用户未处理记录检查结果: 用户id={用户id}, 未处理记录数={未处理记录数}, 有未处理记录={有未处理记录}")
            return 有未处理记录
            
        except Exception as e:
            错误日志器.error(f"检查用户是否还有未处理的达人补充信息记录失败: 用户id={用户id}, 错误={str(e)}")
            raise 