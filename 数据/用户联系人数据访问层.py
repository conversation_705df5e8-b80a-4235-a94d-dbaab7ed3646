"""
用户联系人数据访问层 - PostgreSQL版本
基于asyncpg实现的用户联系人数据访问层

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的联系人查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的关联查询和数据处理
5. 完整的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 用户联系人数据访问:
    """用户联系人表数据访问"""

    @staticmethod
    async def 查询用户联系人_通过ID(联系人ID: UUID) -> Optional[Dict[str, Any]]:
        """
        通过ID查询用户联系人

        Args:
            联系人ID: 用户联系人UUID

        Returns:
            联系人信息或None
        """
        try:
            查询SQL = """
            SELECT
                用户联系人id, 姓名, 用户表id
            FROM 用户联系人表
            WHERE 用户联系人id = $1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系人ID,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询用户联系人失败: ID={联系人ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 查询用户联系人_通过用户ID(用户ID: int) -> List[Dict[str, Any]]:
        """
        通过用户ID查询所有联系人

        Args:
            用户ID: 用户表ID

        Returns:
            联系人列表
        """
        try:
            查询SQL = """
            SELECT
                用户联系人id, 姓名, 用户表id
            FROM 用户联系人表
            WHERE 用户表id = $1
            ORDER BY 姓名
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (用户ID,))
            return 结果

        except Exception as e:
            错误日志器.error(f"查询用户联系人列表失败: 用户ID={用户ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 创建用户联系人(
        用户ID: int,
        姓名: str
    ) -> Optional[Dict[str, Any]]:
        """
        创建新的用户联系人记录

        Args:
            用户ID: 用户表ID
            姓名: 联系人姓名

        Returns:
            新创建的联系人信息或None
        """
        try:
            插入SQL = """
            INSERT INTO 用户联系人表 (用户表id, 姓名)
            VALUES ($1, $2)
            RETURNING 用户联系人id, 姓名, 用户表id
            """

            结果 = await 异步连接池实例.执行查询(插入SQL, (用户ID, 姓名))

            if 结果:
                联系人信息 = 结果[0]
                数据库日志器.info(f"创建用户联系人成功: ID={联系人信息['用户联系人id']}, 姓名={姓名}")
                return 联系人信息
            else:
                错误日志器.error("创建用户联系人失败，未返回数据")
                return None

        except Exception as e:
            错误日志器.error(f"创建用户联系人失败: 用户ID={用户ID}, 姓名={姓名}, 错误={str(e)}")
            raise

    @staticmethod
    async def 更新用户联系人(
        联系人ID: UUID,
        姓名: Optional[str] = None
    ) -> bool:
        """
        更新用户联系人信息

        Args:
            联系人ID: 用户联系人UUID
            姓名: 新的姓名（可选）

        Returns:
            是否更新成功
        """
        try:
            if not 姓名:
                return True  # 没有要更新的字段

            更新SQL = """
            UPDATE 用户联系人表 
            SET 姓名 = $1
            WHERE 用户联系人id = $2
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (姓名, 联系人ID))

            if 影响行数 > 0:
                数据库日志器.info(f"更新用户联系人成功: ID={联系人ID}")
                return True
            else:
                数据库日志器.warning(f"更新用户联系人未影响任何行: ID={联系人ID}")
                return False

        except Exception as e:
            错误日志器.error(f"更新用户联系人失败: ID={联系人ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 删除用户联系人(联系人ID: UUID) -> bool:
        """
        删除用户联系人记录

        Args:
            联系人ID: 用户联系人UUID

        Returns:
            是否删除成功
        """
        try:
            删除SQL = "DELETE FROM 用户联系人表 WHERE 用户联系人id = $1"

            影响行数 = await 异步连接池实例.执行更新(删除SQL, (联系人ID,))

            if 影响行数 > 0:
                数据库日志器.info(f"删除用户联系人成功: ID={联系人ID}")
                return True
            else:
                数据库日志器.warning(f"删除用户联系人未影响任何行: ID={联系人ID}")
                return False

        except Exception as e:
            错误日志器.error(f"删除用户联系人失败: ID={联系人ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 检查联系人是否存在(联系人ID: UUID) -> bool:
        """
        检查联系人是否存在

        Args:
            联系人ID: 用户联系人UUID

        Returns:
            是否存在
        """
        try:
            查询SQL = """
            SELECT 1 FROM 用户联系人表 
            WHERE 用户联系人id = $1
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系人ID,))
            return len(结果) > 0

        except Exception as e:
            错误日志器.error(f"检查联系人是否存在失败: ID={联系人ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 检查用户是否拥有联系人(用户ID: int, 联系人ID: UUID) -> bool:
        """
        检查用户是否拥有指定联系人

        Args:
            用户ID: 用户表ID
            联系人ID: 用户联系人UUID

        Returns:
            是否拥有
        """
        try:
            查询SQL = """
            SELECT 1 FROM 用户联系人表 
            WHERE 用户表id = $1 AND 用户联系人id = $2
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (用户ID, 联系人ID))
            return len(结果) > 0

        except Exception as e:
            错误日志器.error(f"检查用户联系人所有权失败: 用户ID={用户ID}, 联系人ID={联系人ID}, 错误={str(e)}")
            raise


class 达人补充信息数据访问:
    """达人补充信息表数据访问"""

    @staticmethod
    async def 关联用户联系人(补充信息ID: int, 联系人ID: UUID) -> bool:
        """
        将用户联系人关联到达人补充信息

        Args:
            补充信息ID: 用户达人补充信息表ID
            联系人ID: 用户联系人UUID

        Returns:
            是否关联成功
        """
        try:
            更新SQL = """
            UPDATE 用户达人补充信息表 
            SET 用户联系人表id = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (联系人ID, 补充信息ID))

            if 影响行数 > 0:
                数据库日志器.info(f"关联用户联系人成功: 补充信息ID={补充信息ID}, 联系人ID={联系人ID}")
                return True
            else:
                数据库日志器.warning(f"关联用户联系人未影响任何行: 补充信息ID={补充信息ID}")
                return False

        except Exception as e:
            错误日志器.error(f"关联用户联系人失败: 补充信息ID={补充信息ID}, 联系人ID={联系人ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 取消关联用户联系人(补充信息ID: int) -> bool:
        """
        取消达人补充信息与用户联系人的关联

        Args:
            补充信息ID: 用户达人补充信息表ID

        Returns:
            是否取消关联成功
        """
        try:
            更新SQL = """
            UPDATE 用户达人补充信息表 
            SET 用户联系人表id = NULL, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $1
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (补充信息ID,))

            if 影响行数 > 0:
                数据库日志器.info(f"取消关联用户联系人成功: 补充信息ID={补充信息ID}")
                return True
            else:
                数据库日志器.warning(f"取消关联用户联系人未影响任何行: 补充信息ID={补充信息ID}")
                return False

        except Exception as e:
            错误日志器.error(f"取消关联用户联系人失败: 补充信息ID={补充信息ID}, 错误={str(e)}")
            raise


# 创建数据访问层实例
用户联系人数据访问实例 = 用户联系人数据访问()
达人补充信息数据访问实例 = 达人补充信息数据访问()
