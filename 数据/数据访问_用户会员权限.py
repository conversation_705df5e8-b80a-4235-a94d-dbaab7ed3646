"""
用户会员权限数据访问层
负责用户会员关联、权限查询等数据库操作
"""

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 数据访问日志器, 错误日志器


async def 异步查询_用户会员关联信息_通过用户id_返回详情列表(用户id: int):
    """
    查询用户的会员关联信息，包含会员详情和权限列表
    
    Args:
        用户id (int): 用户的唯一标识
        
    Returns:
        list: 用户会员关联详情列表，包含会员信息和权限列表
    """
    try:
        数据访问日志器.info(f"开始查询用户会员关联信息: 用户id={用户id}")
        
        查询SQL = """
        SELECT
            m.id as 会员id,
            m.名称 as 会员名称,
            m.每月费用 as 会员每月费用,
            m.每年费用 as 会员每年费用,
            m.每月算力点 as 会员每月算力点,
            m.可创建团队数,
            m.创建团队默认人数上限,
            m.可加入团队数,
            um.开通时间,
            um.到期时间,
            CASE
                WHEN um.到期时间 > NOW() THEN '有效'
                ELSE '已过期'
            END as 会员状态,
            EXTRACT(DAY FROM (um.到期时间::timestamp - NOW())) as 剩余天数,
            STRING_AGG(p.名称, ', ') as 权限列表,
            STRING_AGG(p.描述, '; ') as 权限描述列表
        FROM 用户_会员_关联表 um
        LEFT JOIN 会员表 m ON um.会员id = m.id
        LEFT JOIN 会员_权限_关联表 mp ON m.id = mp.会员id
        LEFT JOIN 权限表 p ON mp.权限id = p.id
        WHERE um.用户id = $1
        GROUP BY m.id, m.名称, m.每月费用, m.每年费用, m.每月算力点, um.开通时间, um.到期时间
        ORDER BY um.开通时间 DESC
        """
        
        查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))
        数据访问日志器.info(f"用户会员关联信息查询完成: 用户id={用户id}, 记录数={len(查询结果)}")
        
        return 查询结果
        
    except Exception as e:
        错误日志器.error(f"查询用户会员关联信息失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步查询_用户关联店铺列表_通过用户id_支持分页(用户id: int, 页码: int = 1, 每页数量: int = 10):
    """
    分页查询用户关联的店铺列表
    
    Args:
        用户id (int): 用户的唯一标识
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的记录数
        
    Returns:
        dict: 包含总数、列表、页码、每页数量的分页数据
    """
    try:
        数据访问日志器.info(f"开始查询用户关联店铺列表: 用户id={用户id}, 页码={页码}, 每页数量={每页数量}")
        
        # 查询总数
        总数查询SQL = """
        SELECT COUNT(*) as total
        FROM "用户_店铺" us
        JOIN "店铺" s ON us.店铺id = s.id
        WHERE us.用户id = $1
        """
        总数结果 = await 异步连接池实例.执行查询(总数查询SQL, (用户id,))
        总数 = 总数结果[0]['total'] if 总数结果 else 0
        
        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量
        
        # 查询分页数据
        分页查询SQL = """
        SELECT 
            s.id as 店铺id,
            s.shop_id as 店铺标识,
            s.shop_name as 店铺名称,
            s.avatar as 店铺头像,
            s.创建时间,
            us.创建时间 as 关联时间
        FROM "用户_店铺" us
        JOIN "店铺" s ON us.店铺id = s.id
        WHERE us.用户id = $1
        ORDER BY us.创建时间 DESC
        LIMIT $2 OFFSET $3
        """
        店铺列表 = await 异步连接池实例.执行查询(分页查询SQL, (用户id, 每页数量, 偏移量))
        
        数据访问日志器.info(f"用户关联店铺列表查询完成: 用户id={用户id}, 总数={总数}")
        
        return {
            "总数": 总数,
            "列表": 店铺列表,
            "页码": 页码,
            "每页数量": 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"查询用户关联店铺列表失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步查询_用户登录历史记录_通过用户id_支持分页(用户id: int, 页码: int = 1, 每页数量: int = 20):
    """
    分页查询用户的登录历史记录
    
    Args:
        用户id (int): 用户的唯一标识
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的记录数
        
    Returns:
        dict: 包含总数、列表、页码、每页数量的分页数据
    """
    try:
        数据访问日志器.info(f"开始查询用户登录历史记录: 用户id={用户id}, 页码={页码}, 每页数量={每页数量}")
        
        # 查询总数
        总数查询SQL = """
        SELECT COUNT(*) as total
        FROM 用户登陆记录表
        WHERE 用户id = $1
        """
        总数结果 = await 异步连接池实例.执行查询(总数查询SQL, (用户id,))
        总数 = 总数结果[0]['total'] if 总数结果 else 0
        
        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量
        
        # 查询分页数据
        分页查询SQL = """
        SELECT
            id,
            登陆时间,
            ip地址,
            ip归属地,
            TO_CHAR(登陆时间, 'YYYY-MM-DD HH24:MI:SS') as 格式化时间
        FROM 用户登陆记录表
        WHERE 用户id = $1
        ORDER BY 登陆时间 DESC
        LIMIT $2 OFFSET $3
        """
        登录记录列表 = await 异步连接池实例.执行查询(分页查询SQL, (用户id, 每页数量, 偏移量))
        
        数据访问日志器.info(f"用户登录历史记录查询完成: 用户id={用户id}, 总数={总数}")
        
        return {
            "总数": 总数,
            "列表": 登录记录列表,
            "页码": 页码,
            "每页数量": 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"查询用户登录历史记录失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步查询_用户安全审计数据_通过用户id_生成报告(用户id: int):
    """
    查询用户安全审计相关数据，生成安全审计报告

    Args:
        用户id (int): 用户的唯一标识

    Returns:
        dict: 用户安全审计数据
    """
    try:
        数据访问日志器.info(f"开始查询用户安全审计数据: 用户id={用户id}")

        # 查询登录安全数据
        登录安全查询SQL = """
        SELECT
            COUNT(DISTINCT ip地址) as 不同IP数量,
            COUNT(CASE WHEN 登陆时间::date = CURRENT_DATE THEN 1 END) as 今日登录,
            COUNT(*) as 总登录次数,
            MAX(登陆时间) as 最后登录时间,
            MIN(登陆时间) as 首次登录时间
        FROM 用户登陆记录表
        WHERE 用户id = $1
        """
        登录安全结果 = await 异步连接池实例.执行查询(登录安全查询SQL, (用户id,))
        登录安全数据 = 登录安全结果[0] if 登录安全结果 else {}

        # 查询常用IP地址
        常用IP查询SQL = """
        SELECT
            ip地址,
            ip归属地,
            COUNT(*) as 登录次数,
            MAX(登陆时间) as 最后使用时间,
            MIN(登陆时间) as 首次使用时间
        FROM 用户登陆记录表
        WHERE 用户id = $1
        GROUP BY ip地址, ip归属地
        ORDER BY 登录次数 DESC
        LIMIT 10
        """
        常用IP结果 = await 异步连接池实例.执行查询(常用IP查询SQL, (用户id,))

        # 查询近期登录记录
        近期登录查询SQL = """
        SELECT
            登陆时间,
            ip地址,
            ip归属地,
            TO_CHAR(登陆时间, 'YYYY-MM-DD HH24:MI:SS') as 格式化时间
        FROM 用户登陆记录表
        WHERE 用户id = $1
        ORDER BY 登陆时间 DESC
        LIMIT 20
        """
        近期登录结果 = await 异步连接池实例.执行查询(近期登录查询SQL, (用户id,))

        # 查询接口调用安全数据（模拟数据，实际需要根据接口调用记录表查询）
        接口安全数据 = {
            "近1小时调用次数": 0,  # 需要根据实际的接口调用记录表查询
            "客户端错误次数": 0,   # 需要根据实际的错误日志表查询
            "服务器错误次数": 0,   # 需要根据实际的错误日志表查询
            "平均响应时间": 0      # 需要根据实际的性能监控数据查询
        }

        安全审计数据 = {
            "登录安全": {
                "不同IP数量": 登录安全数据.get('不同IP数量', 0),
                "今日登录": 登录安全数据.get('今日登录', 0),
                "总登录次数": 登录安全数据.get('总登录次数', 0),
                "最后登录时间": 登录安全数据.get('最后登录时间'),
                "首次登录时间": 登录安全数据.get('首次登录时间')
            },
            "接口安全": 接口安全数据,
            "常用IP": 常用IP结果,
            "近期登录": 近期登录结果
        }

        数据访问日志器.info(f"用户安全审计数据查询完成: 用户id={用户id}")
        return 安全审计数据

    except Exception as e:
        错误日志器.error(f"查询用户安全审计数据失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise



