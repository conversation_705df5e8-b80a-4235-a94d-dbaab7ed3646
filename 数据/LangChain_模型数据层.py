"""
LangChain模型数据层

功能：
1. 模型配置数据操作
2. 模型统计信息管理
3. 模型验证和查询
4. 模型状态管理
"""

from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步数据库连接池

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 模型数据日志器


class LangChain模型数据层:
    """LangChain模型数据层 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于模型数据操作
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据库连接池: Optional[Postgre_异步数据库连接池] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据库连接池: PostgreSQL连接池实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据库连接池永远不为None
        self.数据库连接池: Postgre_异步数据库连接池 = 数据库连接池 or 异步连接池实例
        self.已初始化 = True  # 简化初始化逻辑

        模型数据日志器.info("LangChain模型数据层创建成功")

    async def 初始化(self):
        """初始化模型数据层"""
        try:
            # 确保连接池已初始化
            if not self.数据库连接池.已初始化:
                await self.数据库连接池.初始化数据库连接池()

            模型数据日志器.info("LangChain模型数据层初始化成功")
        except Exception as e:
            模型数据日志器.error(f"LangChain模型数据层初始化失败: {str(e)}")
            raise

    @classmethod
    async def 创建实例(cls) -> "LangChain模型数据层":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 创建数据层实例
        实例 = cls(异步连接池实例)

        # 执行异步初始化逻辑
        await 实例.初始化()

        return 实例

    # ==================== 模型配置基础操作 ====================

    async def 创建模型配置(self, 模型数据: Dict[str, Any]) -> Optional[int]:
        """创建模型配置"""
        try:
            插入SQL = """
            INSERT INTO langchain_模型配置表 (
                模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL,
                最大令牌数, 算力消耗
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
            """

            参数 = (
                模型数据["模型名称"],
                模型数据.get("模型类型", "chat"),
                模型数据.get("显示名称", 模型数据["模型名称"]),
                模型数据.get("提供商", "openai"),
                模型数据.get("API密钥", ""),
                模型数据.get("API基础URL", ""),
                模型数据.get("最大令牌数", 4000),
                模型数据.get("算力消耗", 1.0),
            )

            结果 = await self.数据库连接池.执行查询(插入SQL, 参数)
            模型id = 结果[0]["id"] if 结果 else None

            模型数据日志器.info(
                f"模型配置创建成功: {模型数据['模型名称']} (ID: {模型id})"
            )
            return 模型id

        except Exception as e:
            模型数据日志器.error(f"创建模型配置失败: {str(e)}")
            raise

    async def 检查模型名称是否存在(self, 模型名称: str) -> bool:
        """检查模型名称是否已存在"""
        try:
            查询SQL = (
                "SELECT COUNT(*) as count FROM langchain_模型配置表 WHERE 模型名称 = $1"
            )
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return bool(结果 and len(结果) > 0 and 结果[0].get("count", 0) > 0)
        except Exception as e:
            模型数据日志器.error(f"检查模型名称存在失败: {str(e)}")
            raise

    async def 获取模型配置详情(self, 模型id: int) -> Optional[Dict[str, Any]]:
        """获取模型配置详情"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url,
                   最大令牌数, 算力消耗
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型id,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"获取模型配置详情失败: {str(e)}")
            raise

    async def 根据模型名称获取配置(self, 模型名称: str) -> Optional[Dict[str, Any]]:
        """根据模型名称获取配置"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url,
                   最大令牌数, 算力消耗
            FROM langchain_模型配置表
            WHERE 模型名称 = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据模型名称获取配置失败: {str(e)}")
            raise

    async def 更新模型配置(self, 模型id: int, 更新数据: Dict[str, Any]) -> bool:
        """更新模型配置"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []

            允许更新字段 = [
                "模型名称",
                "模型类型",
                "显示名称",
                "提供商",
                "API密钥",
                "API基础URL",
                "最大令牌数",
                "算力消耗",
            ]

            参数索引 = 1
            for 字段名, 字段值 in 更新数据.items():
                if 字段名 in 允许更新字段:
                    更新字段.append(f"{字段名} = ${参数索引}")
                    参数值.append(字段值)
                    参数索引 += 1

            if not 更新字段:
                模型数据日志器.warning(f"没有有效的更新字段: {模型id}")
                return False

            参数值.append(模型id)

            更新SQL = f"""
            UPDATE langchain_模型配置表
            SET {", ".join(更新字段)}
            WHERE id = $1
            """

            await self.数据库连接池.执行更新(更新SQL, tuple(参数值))
            模型数据日志器.info(f"模型配置更新成功: {模型id}")
            return True

        except Exception as e:
            模型数据日志器.error(f"更新模型配置失败: {str(e)}")
            raise

    async def 删除模型配置(self, 模型id: int) -> bool:
        """删除模型配置"""
        try:
            删除SQL = "DELETE FROM langchain_模型配置表 WHERE id = $1"
            await self.数据库连接池.执行更新(删除SQL, (模型id,))
            模型数据日志器.info(f"模型配置删除成功: {模型id}")
            return True
        except Exception as e:
            模型数据日志器.error(f"删除模型配置失败: {str(e)}")
            raise

    # ==================== 模型查询和验证 ====================

    async def 根据ID获取模型基本信息(self, 模型id: int) -> Optional[Dict[str, Any]]:
        """根据模型id获取基本信息"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型id,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据ID获取模型基本信息失败: {str(e)}")
            return None

    async def 根据名称获取模型基本信息(self, 模型名称: str) -> Optional[Dict[str, Any]]:
        """根据模型名称获取基本信息"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商
            FROM langchain_模型配置表
            WHERE 模型名称 = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据名称获取模型基本信息失败: {str(e)}")
            return None

    async def 获取模型列表(
        self, 模型类型: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取模型列表（支持模型类型过滤）"""
        try:
            # 构建基础查询
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api基础url, 最大令牌数
            FROM langchain_模型配置表
            """

            # 构建WHERE条件
            条件列表 = []
            参数列表 = []
            参数索引 = 1

            if 模型类型:
                条件列表.append(f"模型类型 = ${参数索引}")
                参数列表.append(模型类型)
                参数索引 += 1

            if 条件列表:
                查询SQL += " WHERE " + " AND ".join(条件列表)

            查询SQL += " ORDER BY 模型类型, 显示名称"

            # 执行查询
            if 参数列表:
                结果 = await self.数据库连接池.执行查询(查询SQL, tuple(参数列表))
            else:
                结果 = await self.数据库连接池.执行查询(查询SQL)

            return 结果 if 结果 else []
        except Exception as e:
            模型数据日志器.error(f"获取模型列表失败: {str(e)}")
            return []

    async def 获取启用的模型列表(
        self, 模型类型: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取模型列表"""
        return await self.获取模型列表(模型类型=模型类型)

    async def 验证模型存在且启用(self, 模型id: int) -> bool:
        """验证模型是否存在"""
        try:
            查询SQL = """
            SELECT COUNT(*) as count
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型id,))
            return bool(结果 and len(结果) > 0 and 结果[0].get("count", 0) > 0)
        except Exception as e:
            模型数据日志器.error(f"验证模型存在失败: {str(e)}")
            return False


# 创建全局模型数据层实例
LangChain模型数据层实例 = LangChain模型数据层()
