import json
import traceback
from typing import Any, Dict, List, Optional, Tuple

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 系统日志器, 错误日志器


class 异步用户产品数据:
    """异步用户产品数据操作类"""

    @staticmethod
    # 修改方法签名，直接接收各个组件
    async def 保存产品信息(
        用户id: int,
        基础信息: Dict[str, Any],
        产品信息: Dict[str, Any],
        缺失信息: List[Any],
        产品id: Optional[int] = None,
        手卡文件本地路径: Optional[str] = None,
    ) -> Tuple[bool, Optional[int], str]:
        """
        保存/更新用户产品信息

        参数:
        - 用户id: 用户id
        - 基础信息: 产品基础信息字典
        - 产品信息: 产品详细信息字典
        - 缺失信息: 产品缺失的信息列表
        - 产品id: 产品ID(更新时提供，新增时为None)
        - 手卡文件本地路径: 手卡文件的本地路径 (可选)

        返回:
        - 元组(成功标志, 产品ID, 消息)
        """
        try:
            系统日志器.info(f"开始保存/更新用户产品, 用户id: {用户id}")

            # 从基础信息中提取产品名称（用于显示和操作）
            产品名称 = 基础信息.get("产品名称", "未命名产品")

            系统日志器.info(f"产品名称: {产品名称}, 产品ID: {产品id}")

            # 转换为JSON格式存储
            产品信息_json存储 = json.dumps(产品信息, ensure_ascii=False)
            缺失信息_json = json.dumps(缺失信息, ensure_ascii=False)

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 检查是否是更新操作
                if 产品id:
                    # 检查产品是否存在且属于该用户
                    检查SQL = "SELECT id FROM 用户产品表 WHERE id = $1 AND 用户id = $2 AND 状态 = 1"
                    记录 = await 连接.fetchrow(检查SQL, 产品id, 用户id)

                    if not 记录:
                        系统日志器.warning(
                            f"未找到ID为{产品id}的产品或产品不属于用户{用户id}"
                        )
                        return False, None, "未找到要更新的产品或权限不足"

                    # 构建动态更新语句
                    更新字段列表 = []
                    参数列表 = []

                    # 添加必须更新的字段
                    参数索引 = 1
                    更新字段列表.append(f"产品名称 = ${参数索引}")
                    参数列表.append(产品名称)
                    参数索引 += 1
                    更新字段列表.append(f"产品信息 = ${参数索引}")
                    参数列表.append(产品信息_json存储)
                    参数索引 += 1
                    更新字段列表.append(f"缺失信息 = ${参数索引}")
                    参数列表.append(缺失信息_json)
                    参数索引 += 1
                    更新字段列表.append("更新时间 = NOW()")

                    # 可选更新字段
                    if 手卡文件本地路径 is not None:
                        更新字段列表.append(f"手卡文件本地路径 = ${参数索引}")
                        参数列表.append(手卡文件本地路径)
                        参数索引 += 1

                    # 组合更新语句
                    更新SQL = f"""
                    UPDATE 用户产品表 SET
                    {", ".join(更新字段列表)}
                    WHERE id = ${参数索引} AND 用户id = ${参数索引 + 1} AND 状态 = 1
                    """
                    参数列表.extend([产品id, 用户id])

                    更新结果 = await 连接.execute(更新SQL, *参数列表)
                    影响行数 = (
                        int(更新结果.split()[-1])
                        if 更新结果.startswith("UPDATE")
                        else 0
                    )

                    if 影响行数 > 0:
                        系统日志器.info(f"成功更新产品信息，产品ID: {产品id}")
                        return True, 产品id, "产品信息更新成功"
                    else:
                        系统日志器.warning(
                            f"未能更新产品，可能已被删除或修改，产品ID: {产品id}"
                        )
                        return False, None, "产品信息更新失败，可能已被删除或修改"
                else:
                    # 如果没有产品ID，则根据产品名称查询是否已存在
                    检查SQL = (
                        "SELECT id FROM 用户产品表 WHERE 用户id = $1 AND 产品名称 = $2"
                    )
                    记录 = await 连接.fetchrow(检查SQL, 用户id, 产品名称)

                    if 记录:
                        # 存在同名产品，进行更新
                        现有产品id = 记录["id"] if isinstance(记录, dict) else 记录[0]

                        # 构建动态更新语句
                        更新字段列表 = []
                        参数列表 = []

                        # 添加必须更新的字段
                        参数索引 = 1
                        更新字段列表.append(f"产品名称 = ${参数索引}")
                        参数列表.append(产品名称)
                        参数索引 += 1
                        更新字段列表.append(f"产品信息 = ${参数索引}")
                        参数列表.append(产品信息_json存储)
                        参数索引 += 1
                        更新字段列表.append(f"缺失信息 = ${参数索引}")
                        参数列表.append(缺失信息_json)
                        参数索引 += 1
                        更新字段列表.append("更新时间 = NOW()")

                        # 可选更新字段
                        if 手卡文件本地路径 is not None:
                            更新字段列表.append(f"手卡文件本地路径 = ${参数索引}")
                            参数列表.append(手卡文件本地路径)
                            参数索引 += 1

                        # 组合更新语句
                        更新SQL = f"""
                        UPDATE 用户产品表 SET
                        {", ".join(更新字段列表)}
                        WHERE id = ${参数索引} AND 用户id = ${参数索引 + 1} AND 状态 = 1
                        """
                        参数列表.extend([现有产品id, 用户id])

                        更新结果 = await 连接.execute(更新SQL, *参数列表)
                        影响行数 = (
                            int(更新结果.split()[-1])
                            if 更新结果.startswith("UPDATE")
                            else 0
                        )

                        if 影响行数 > 0:
                            系统日志器.info(
                                f"找到同名产品并更新成功，产品ID: {现有产品id}"
                            )
                            return True, 现有产品id, "找到同名产品并更新成功"
                        else:
                            系统日志器.warning(
                                f"未能更新同名产品，可能已被删除或修改，产品ID: {现有产品id}"
                            )
                            return False, None, "产品信息更新失败，可能已被删除或修改"
                    else:
                        # 新建产品
                        插入字段列表 = [
                            "用户id",
                            "产品名称",
                            "产品信息",
                            "缺失信息",
                            "创建时间",
                            "更新时间",
                            "状态",
                        ]
                        插入占位符列表 = ["$1", "$2", "$3", "$4", "NOW()", "NOW()", "1"]
                        插入参数列表 = [
                            用户id,
                            产品名称,
                            产品信息_json存储,
                            缺失信息_json,
                        ]

                        if 手卡文件本地路径 is not None:
                            插入字段列表.append("手卡文件本地路径")
                            插入占位符列表.append("$5")
                            插入参数列表.append(手卡文件本地路径)

                        插入SQL = f"""
                        INSERT INTO 用户产品表 ({", ".join(插入字段列表)})
                        VALUES ({", ".join(插入占位符列表)})
                        RETURNING id
                        """
                        新产品id = await 连接.fetchval(插入SQL, *插入参数列表)

                        系统日志器.info(f"成功创建新产品，产品ID: {新产品id}")
                        return True, 新产品id, "产品信息创建成功"

        except Exception as e:
            错误信息 = f"保存产品信息时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return False, None, 错误信息

    @staticmethod
    async def 创建产品信息(
        用户id: int,
        基础信息: Dict[str, Any],
        产品信息: Dict[str, Any],
        缺失信息: List[Any],
        手卡文件本地路径: Optional[str] = None,
    ) -> Tuple[bool, Optional[int], str]:
        """
        创建新的产品信息

        参数:
        - 用户id: 用户id
        - 基础信息: 产品基础信息字典
        - 产品信息: 产品详细信息字典
        - 缺失信息: 产品缺失的信息列表
        - 手卡文件本地路径: 手卡文件的本地路径 (可选)

        返回:
        - 元组(成功标志, 产品ID, 消息)
        """
        try:
            系统日志器.info(f"开始创建用户产品, 用户id: {用户id}")

            # 从基础信息中提取产品名称（用于显示和操作）
            产品名称 = 基础信息.get("产品名称", "未命名产品")
            产品分类 = 基础信息.get("产品分类", [])
            产品描述 = 基础信息.get("产品描述", "")
            产品规格 = 基础信息.get("产品规格", {})

            # 如果产品名称为空，则返回错误
            if not 产品名称 or 产品名称 == "未命名产品":
                系统日志器.warning(f"创建产品时未提供产品名称，用户id: {用户id}")
                return False, None, "创建产品时必须提供产品名称"

            系统日志器.info(f"产品名称: {产品名称}")

            # 转换为JSON格式存储
            产品信息_json存储 = json.dumps(产品信息, ensure_ascii=False)
            缺失信息_json = json.dumps(缺失信息, ensure_ascii=False)
            产品规格_json存储 = (
                json.dumps(产品规格, ensure_ascii=False) if 产品规格 else None
            )

            # 处理产品分类
            产品分类_str = (
                ",".join(产品分类) if isinstance(产品分类, list) else 产品分类
            )

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 检查是否已存在同名产品
                检查SQL = "SELECT id FROM 用户产品表 WHERE 用户id = $1 AND 产品名称 = $2 AND 状态 = 1"
                记录 = await 连接.fetchrow(检查SQL, 用户id, 产品名称)

                if 记录:
                    现有产品id = 记录["id"] if isinstance(记录, dict) else 记录[0]
                    系统日志器.warning(f"已存在同名产品，产品ID: {现有产品id}")
                    return (
                        False,
                        None,
                        f"已存在同名产品（ID: {现有产品id}），请使用更新接口或更换产品名称",
                    )

                # 创建新产品
                插入字段列表 = [
                    "用户id",
                    "产品名称",
                    "产品分类",
                    "产品描述",
                    "产品信息",
                    "产品规格",
                    "缺失信息",
                    "创建时间",
                    "更新时间",
                    "状态",
                ]
                插入占位符列表 = [
                    "$1",
                    "$2",
                    "$3",
                    "$4",
                    "$5",
                    "$6",
                    "$7",
                    "NOW()",
                    "NOW()",
                    "1",
                ]
                插入参数列表 = [
                    用户id,
                    产品名称,
                    产品分类_str,
                    产品描述,
                    产品信息_json存储,
                    产品规格_json存储,
                    缺失信息_json,
                ]

                if 手卡文件本地路径 is not None:
                    插入字段列表.append("手卡文件本地路径")
                    插入占位符列表.append("$8")
                    插入参数列表.append(手卡文件本地路径)

                插入SQL = f"""
                INSERT INTO 用户产品表 ({", ".join(插入字段列表)})
                VALUES ({", ".join(插入占位符列表)})
                RETURNING id
                """
                新产品id = await 连接.fetchval(插入SQL, *插入参数列表)

                系统日志器.info(f"成功创建新产品，产品ID: {新产品id}")
                return True, 新产品id, "产品创建成功"

        except Exception as e:
            错误信息 = f"创建产品信息时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return False, None, 错误信息

    @staticmethod
    async def 更新产品信息(
        用户id: int,
        产品id: int,
        基础信息: Dict[str, Any],
        产品信息: Dict[str, Any],
        缺失信息: List[Any],
        手卡文件本地路径: Optional[str] = None,
    ) -> Tuple[bool, Optional[int], str]:
        """
        更新现有产品信息

        参数:
        - 用户id: 用户id
        - 产品id: 要更新的产品ID
        - 基础信息: 产品基础信息字典
        - 产品信息: 产品详细信息字典
        - 缺失信息: 产品缺失的信息列表
        - 手卡文件本地路径: 手卡文件的本地路径 (可选)

        返回:
        - 元组(成功标志, 产品ID, 消息)
        """
        try:
            系统日志器.info(f"开始更新产品, 用户id: {用户id}, 产品ID: {产品id}")

            # 从基础信息中提取产品名称（用于显示和操作）
            产品名称 = 基础信息.get("产品名称", "未命名产品")
            产品分类 = 基础信息.get("产品分类", [])
            产品描述 = 基础信息.get("产品描述", "")
            产品规格 = 基础信息.get("产品规格", {})

            系统日志器.info(f"要更新的产品: 名称={产品名称}, ID={产品id}")

            # 转换为JSON格式存储
            产品信息_json存储 = json.dumps(产品信息, ensure_ascii=False)
            缺失信息_json = json.dumps(缺失信息, ensure_ascii=False)
            产品规格_json存储 = (
                json.dumps(产品规格, ensure_ascii=False) if 产品规格 else None
            )

            # 处理产品分类
            产品分类_str = (
                ",".join(产品分类) if isinstance(产品分类, list) else 产品分类
            )

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 检查产品是否存在且属于该用户
                检查SQL = "SELECT id FROM 用户产品表 WHERE id = $1 AND 用户id = $2 AND 状态 = 1"
                记录 = await 连接.fetchrow(检查SQL, 产品id, 用户id)

                if not 记录:
                    系统日志器.warning(
                        f"未找到ID为{产品id}的产品或产品不属于用户{用户id}"
                    )
                    return False, None, "未找到要更新的产品或权限不足"

                # 构建动态更新语句
                更新字段列表 = []
                参数列表 = []

                # 添加需要更新的字段
                参数索引 = 1
                if 产品名称 and 产品名称 != "未命名产品":
                    更新字段列表.append(f"产品名称 = ${参数索引}")
                    参数列表.append(产品名称)
                    参数索引 += 1

                if 产品分类_str:
                    更新字段列表.append(f"产品分类 = ${参数索引}")
                    参数列表.append(产品分类_str)
                    参数索引 += 1

                if 产品描述:
                    更新字段列表.append(f"产品描述 = ${参数索引}")
                    参数列表.append(产品描述)
                    参数索引 += 1

                更新字段列表.append(f"产品信息 = ${参数索引}")
                参数列表.append(产品信息_json存储)
                参数索引 += 1

                if 产品规格_json存储 is not None:
                    更新字段列表.append(f"产品规格 = ${参数索引}")
                    参数列表.append(产品规格_json存储)
                    参数索引 += 1

                更新字段列表.append(f"缺失信息 = ${参数索引}")
                参数列表.append(缺失信息_json)
                参数索引 += 1

                更新字段列表.append("更新时间 = NOW()")

                # 可选更新字段
                if 手卡文件本地路径 is not None:
                    更新字段列表.append(f"手卡文件本地路径 = ${参数索引}")
                    参数列表.append(手卡文件本地路径)
                    参数索引 += 1

                # 组合更新语句
                更新SQL = f"""
                UPDATE 用户产品表 SET
                {", ".join(更新字段列表)}
                WHERE id = ${len(参数列表) + 1} AND 用户id = ${len(参数列表) + 2} AND 状态 = 1
                """
                参数列表.extend([产品id, 用户id])

                更新结果 = await 连接.execute(更新SQL, *参数列表)
                影响行数 = (
                    int(更新结果.split()[-1]) if 更新结果.startswith("UPDATE") else 0
                )

                if 影响行数 > 0:
                    系统日志器.info(f"成功更新产品，产品ID: {产品id}")
                    return True, 产品id, "产品更新成功"
                else:
                    系统日志器.warning(
                        f"未能更新产品，可能已被删除或修改，产品ID: {产品id}"
                    )
                    return False, None, "产品信息更新失败，可能已被删除或修改"

        except Exception as e:
            错误信息 = f"更新产品信息时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return False, None, 错误信息

    @staticmethod
    async def 查询产品id通过名称(用户id: int, 产品名称: str) -> Optional[int]:
        """
        根据用户id和产品名称查询产品ID

        参数:
            用户id: 用户id
            产品名称: 产品名称

        返回:
            Optional[int]: 如果找到产品，返回产品ID；否则返回None
        """
        try:
            系统日志器.info(
                f"根据名称查询产品ID，用户id: {用户id}，产品名称: {产品名称}"
            )

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                查询SQL = "SELECT id FROM 用户产品表 WHERE 用户id = $1 AND 产品名称 = $2 AND 状态 = 1"
                结果 = await 连接.fetchrow(查询SQL, 用户id, 产品名称)

                if not 结果:
                    系统日志器.info(
                        f"未找到产品，用户id: {用户id}，产品名称: {产品名称}"
                    )
                    return None

                # 提取产品ID
                if isinstance(结果, dict):
                    产品id = 结果.get("id")
                else:  # 假设是元组
                    产品id = 结果[0] if len(结果) > 0 else None

                系统日志器.info(
                    f"找到产品ID: {产品id}，用户id: {用户id}，产品名称: {产品名称}"
                )
                return 产品id

        except Exception as e:
            错误信息 = f"查询产品ID时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return None

    @staticmethod
    async def 获取用户产品列表(
        用户id: int,
        页码: int = 1,
        每页数量: int = 10,
        产品名称_筛选: Optional[str] = None,
        产品分类_筛选: Optional[str] = None,
        状态_筛选: Optional[int] = None,
    ) -> Tuple[bool, Dict[str, Any], str]:
        """
        获取用户产品列表
        :param 用户id: 用户id
        :param 页码: 页码，默认1
        :param 每页数量: 每页数量，默认10
        :param 产品名称_筛选: 产品名称 (可选)
        :param 产品分类_筛选: 产品分类 (可选)
        :param 状态_筛选: 产品状态 (可选, 1-已上架, 0-已下架)
        :return: (成功标志, 数据字典, 消息)
        """
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            # 构建基础WHERE子句和参数列表
            where_conditions = ["用户id = $1"]
            params: list = [用户id]  # 明确声明为混合类型列表，支持int和str
            param_index = 2

            # 根据筛选条件动态添加WHERE子句
            if 产品名称_筛选:
                where_conditions.append(f"产品名称 LIKE ${param_index}")
                params.append(f"%{产品名称_筛选}%")
                param_index += 1

            if 产品分类_筛选:
                # 假设产品分类在数据库中是逗号分隔的字符串，需要精确匹配或部分匹配
                # 为简单起见，这里使用 LIKE，如果分类是单一值或需要更复杂逻辑则需调整
                where_conditions.append(f"产品分类 LIKE ${param_index}")
                params.append(f"%{产品分类_筛选}%")
                param_index += 1

            if 状态_筛选 is not None:
                where_conditions.append(f"状态 = ${param_index}")
                params.append(状态_筛选)
                param_index += 1
            else:
                # 如果前端不传状态筛选，默认查询状态为1 (已上架) 的产品
                where_conditions.append(f"状态 = ${param_index}")
                params.append(1)
                param_index += 1

            where_clause = " AND ".join(where_conditions)

            # 计算总数
            计数SQL = f"SELECT COUNT(*) as total FROM 用户产品表 WHERE {where_clause}"
            总数结果列表 = await 异步连接池实例.执行查询(计数SQL, tuple(params))
            总数结果 = 总数结果列表[0] if 总数结果列表 else None

            总数 = 0
            if 总数结果:
                # 优雅地处理不同类型的数据库返回结果
                if isinstance(总数结果, dict):
                    # 如果是字典，尝试常见的键名
                    总数 = (
                        总数结果.get("total")
                        or 总数结果.get("count")
                        or 总数结果.get("总数", 0)
                    )
                elif isinstance(总数结果, (tuple, list)) and len(总数结果) > 0:
                    # 如果是元组或列表，取第一个元素
                    总数 = 总数结果[0]
                elif hasattr(总数结果, "__getitem__"):
                    # 对于其他可索引对象，安全地尝试获取
                    try:
                        if hasattr(总数结果, "keys"):  # 类字典对象
                            总数 = next(iter(总数结果.values()), 0)
                        else:  # 类列表对象
                            总数 = 总数结果[0]
                    except (IndexError, KeyError, TypeError, StopIteration):
                        print(
                            f"无法从查询结果中提取总数: {总数结果}, 类型: {type(总数结果)}"
                        )
                        总数 = 0

            print(f"用户id {用户id} 的产品总数 (筛选后): {总数}")

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            查询SQL = f"""
            SELECT id, 产品名称, 产品分类, 产品描述, 产品信息, 产品规格, 手卡文件本地路径, 创建时间, 更新时间, 状态
            FROM 用户产品表
            WHERE {where_clause}
            ORDER BY 更新时间 DESC
            LIMIT ${param_index} OFFSET ${param_index + 1}
            """
            query_params = list(params)  # 复制基础参数
            query_params.extend([每页数量, 偏移量])
            结果 = await 异步连接池实例.执行查询(查询SQL, tuple(query_params))

            # 构建返回数据
            产品列表 = []
            for 行 in 结果:
                if isinstance(行, dict):
                    # 处理产品规格JSON字段
                    产品规格_原始 = 行.get("产品规格")
                    产品规格_解析 = {}
                    if 产品规格_原始:
                        try:
                            if isinstance(产品规格_原始, str):
                                产品规格_解析 = json.loads(产品规格_原始)
                            elif isinstance(产品规格_原始, dict):
                                产品规格_解析 = 产品规格_原始
                        except (json.JSONDecodeError, TypeError):
                            产品规格_解析 = {}

                    产品项 = {
                        "产品id": 行.get("id"),
                        "产品名称": 行.get("产品名称", ""),
                        "产品分类": 行.get("产品分类", "").split(",")
                        if 行.get("产品分类")
                        else [],
                        "产品描述": 行.get("产品描述", ""),
                        "产品信息": json.loads(行.get("产品信息", "{}"))
                        if 行.get("产品信息")
                        else {},
                        "产品规格": 产品规格_解析,
                        "手卡文件本地路径": 行.get("手卡文件本地路径", ""),
                        "创建时间": 行.get("创建时间", "").strftime("%Y-%m-%d %H:%M:%S")
                        if 行.get("创建时间")
                        else "",
                        "更新时间": 行.get("更新时间", "").strftime("%Y-%m-%d %H:%M:%S")
                        if 行.get("更新时间")
                        else "",
                        "状态": 行.get("状态"),
                    }
                else:
                    # 处理产品规格JSON字段（数组格式）
                    产品规格_原始 = 行[5] if len(行) > 5 else None
                    产品规格_解析 = {}
                    if 产品规格_原始:
                        try:
                            if isinstance(产品规格_原始, str):
                                产品规格_解析 = json.loads(产品规格_原始)
                            elif isinstance(产品规格_原始, dict):
                                产品规格_解析 = 产品规格_原始
                        except (json.JSONDecodeError, TypeError):
                            产品规格_解析 = {}

                    产品项 = {
                        "产品id": 行[0] if len(行) > 0 else None,
                        "产品名称": 行[1] if len(行) > 1 else "",
                        "产品分类": 行[2].split(",") if len(行) > 2 and 行[2] else [],
                        "产品描述": 行[3] if len(行) > 3 else "",
                        "产品信息": json.loads(行[4]) if len(行) > 4 and 行[4] else {},
                        "产品规格": 产品规格_解析,
                        "手卡文件本地路径": 行[6] if len(行) > 6 else "",
                        "创建时间": 行[7].strftime("%Y-%m-%d %H:%M:%S")
                        if len(行) > 7 and 行[7]
                        else "",
                        "更新时间": 行[8].strftime("%Y-%m-%d %H:%M:%S")
                        if len(行) > 8 and 行[8]
                        else "",
                        "状态": 行[9] if len(行) > 9 else None,
                    }
                产品列表.append(产品项)

            返回数据 = {
                "总数": 总数,
                "总页数": (总数 + 每页数量 - 1) // 每页数量 if 总数 > 0 else 1,
                "当前页": 页码,
                "每页数量": 每页数量,
                "列表": 产品列表,
            }

            return True, 返回数据, "获取产品列表成功"

        except Exception as e:
            # 增加错误日志
            traceback.print_exc()
            错误详情 = traceback.format_exc()
            print(f"获取产品列表失败: {str(e)}")
            print(f"详细错误: {错误详情}")
            return False, {}, f"获取产品列表失败: {str(e)}"

    @staticmethod
    async def 获取产品详情(
        产品id: int, 用户id: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any], str]:
        """
        获取产品详情信息
        :param 产品id: 产品ID
        :param 用户id: 用户id（可选，用于权限验证）
        :return: (成功标志, 产品详情, 消息)
        """
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                查询SQL = """
                SELECT id, 用户id, 产品名称, 产品分类, 产品描述, 手卡文件本地路径, 产品信息, 产品规格, 缺失信息, 状态, 创建时间, 更新时间
                FROM 用户产品表
                WHERE id = $1 AND 状态 = 1
                """
                结果 = await 连接.fetchrow(查询SQL, 产品id)

                if not 结果:
                    return False, {}, "产品不存在或已删除"

                # 添加调试日志
                print(f"数据库查询结果 - 产品ID: {产品id}")
                print(f"产品信息原始数据: {结果.get('产品信息')}")
                print(f"产品信息数据类型: {type(结果.get('产品信息'))}")
                print(f"产品规格原始数据: {结果.get('产品规格')}")
                print(f"产品规格数据类型: {type(结果.get('产品规格'))}")
                print(f"缺失信息原始数据: {结果.get('缺失信息')}")
                print(f"缺失信息数据类型: {type(结果.get('缺失信息'))}")

                # 确保结果是字典类型
                if not isinstance(结果, dict):
                    print(f"获取产品详情失败：数据库返回格式非字典类型 - {type(结果)}")
                    return False, {}, "获取产品详情失败：数据库返回格式异常"

                # 如果提供了用户id，验证所有权 (使用字典键)
                if 用户id is not None and 结果.get("用户id") != 用户id:
                    return False, {}, "无权访问此产品信息"

                # 使用字典键访问结果
                # 处理产品信息JSON字段
                产品信息_原始 = 结果.get("产品信息")
                产品信息_解析 = {}
                if 产品信息_原始:
                    try:
                        if isinstance(产品信息_原始, str):
                            产品信息_解析 = json.loads(产品信息_原始)
                        elif isinstance(产品信息_原始, dict):
                            产品信息_解析 = 产品信息_原始
                    except (json.JSONDecodeError, TypeError) as e:
                        print(f"解析产品信息JSON失败: {e}, 原始数据: {产品信息_原始}")
                        产品信息_解析 = {}

                # 处理产品规格JSON字段
                产品规格_原始 = 结果.get("产品规格")
                产品规格_解析 = {}
                if 产品规格_原始:
                    try:
                        if isinstance(产品规格_原始, str):
                            产品规格_解析 = json.loads(产品规格_原始)
                        elif isinstance(产品规格_原始, dict):
                            产品规格_解析 = 产品规格_原始
                    except (json.JSONDecodeError, TypeError) as e:
                        print(f"解析产品规格JSON失败: {e}, 原始数据: {产品规格_原始}")
                        产品规格_解析 = {}

                # 处理缺失信息JSON字段
                缺失信息_原始 = 结果.get("缺失信息")
                缺失信息_解析 = []
                if 缺失信息_原始:
                    try:
                        if isinstance(缺失信息_原始, str):
                            缺失信息_解析 = json.loads(缺失信息_原始)
                        elif isinstance(缺失信息_原始, list):
                            缺失信息_解析 = 缺失信息_原始
                    except (json.JSONDecodeError, TypeError) as e:
                        print(f"解析缺失信息JSON失败: {e}, 原始数据: {缺失信息_原始}")
                        缺失信息_解析 = []

                产品详情 = {
                    "产品id": 结果.get("id"),
                    "用户id": 结果.get("用户id"),
                    "产品名称": 结果.get("产品名称", ""),
                    "产品分类": 结果.get("产品分类", "").split(",")
                    if 结果.get("产品分类")
                    else [],
                    "产品描述": 结果.get("产品描述", ""),
                    "手卡文件本地路径": 结果.get("手卡文件本地路径", ""),
                    "产品信息": 产品信息_解析,
                    "产品规格": 产品规格_解析,
                    "缺失信息": 缺失信息_解析,
                    "状态": 结果.get("状态", 1),
                    "创建时间": 结果["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 结果.get("创建时间")
                    else "",
                    "更新时间": 结果["更新时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 结果.get("更新时间")
                    else "",
                }

                return True, 产品详情, "获取产品详情成功"
        except Exception as e:
            # 增加错误日志
            错误详情 = traceback.format_exc()
            print(f"获取产品详情失败: {str(e)}")
            print(f"详细错误: {错误详情}")
            return False, {}, f"获取产品详情失败: {str(e)}"

    @staticmethod
    def _深度合并字典(字典1: Dict[str, Any], 字典2: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并两个字典，如果有冲突，字典2的值优先
        :param 字典1: 原始字典
        :param 字典2: 新字典，其值优先级更高
        :return: 合并后的字典
        """
        结果 = 字典1.copy()

        for 键, 值 in 字典2.items():
            if 键 in 结果 and isinstance(结果[键], dict) and isinstance(值, dict):
                结果[键] = 异步用户产品数据._深度合并字典(结果[键], 值)
            else:
                结果[键] = 值

        return 结果

    @staticmethod
    async def 删除产品(产品id: int, 用户id: int) -> Dict[str, Any]:
        """
        逻辑删除产品（将状态设置为0）

        参数:
        - 产品id: 要删除的产品ID
        - 用户id: 用户id（用于验证所有权）

        返回:
        - 包含操作结果的字典: {"status": int, "message": str, "data": Dict}
        """
        try:
            系统日志器.info(f"开始删除产品, 用户id: {用户id}, 产品ID: {产品id}")

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 检查产品是否存在且属于该用户
                检查SQL = "SELECT id FROM 用户产品表 WHERE id = $1 AND 用户id = $2 AND 状态 = 1"
                记录 = await 连接.fetchrow(检查SQL, 产品id, 用户id)

                if not 记录:
                    系统日志器.warning(
                        f"未找到ID为{产品id}的产品或产品不属于用户{用户id}"
                    )
                    return {
                        "status": 404,
                        "message": "未找到要删除的产品或权限不足",
                        "data": {},
                    }

                # 逻辑删除产品（将状态设置为0）
                删除SQL = """
                UPDATE 用户产品表 SET
                状态 = 0,
                更新时间 = NOW()
                WHERE id = $1 AND 用户id = $2
                """
                删除结果 = await 连接.execute(删除SQL, 产品id, 用户id)
                影响行数 = (
                    int(删除结果.split()[-1]) if 删除结果.startswith("UPDATE") else 0
                )

                if 影响行数 > 0:
                    系统日志器.info(f"成功删除产品，产品ID: {产品id}")
                    return {
                        "status": 100,
                        "message": "产品删除成功",
                        "data": {"产品id": 产品id},
                    }
                else:
                    系统日志器.warning(f"删除产品失败，产品ID: {产品id}")
                    return {
                        "status": 500,
                        "message": "产品删除失败",
                        "data": {"产品id": 产品id},
                    }

        except Exception as e:
            错误信息 = f"删除产品时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return {"status": 500, "message": 错误信息, "data": {"产品id": 产品id}}

    @staticmethod
    async def 验证产品所有权(用户id: int, 产品id: int) -> bool:
        """
        验证产品是否属于指定用户

        参数:
        - 用户id: 用户id
        - 产品id: 产品ID

        返回:
        - 验证结果: 如果产品属于该用户，返回True；否则返回False
        """
        try:
            系统日志器.info(f"验证产品所有权, 用户id: {用户id}, 产品ID: {产品id}")

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 检查产品是否存在且属于该用户
                检查SQL = "SELECT id FROM 用户产品表 WHERE id = $1 AND 用户id = $2 AND 状态 = 1"
                记录 = await 连接.fetchrow(检查SQL, 产品id, 用户id)

                if 记录:
                    系统日志器.debug(
                        f"产品所有权验证成功，产品ID: {产品id}, 用户id: {用户id}"
                    )
                    return True
                else:
                    系统日志器.warning(
                        f"产品所有权验证失败，产品ID: {产品id}, 用户id: {用户id}"
                    )
                    return False

        except Exception as e:
            错误信息 = f"验证产品所有权时发生错误: {str(e)}"
            错误日志器.error(错误信息, exc_info=True)
            return False


# 单例实例
异步用户产品数据实例 = 异步用户产品数据()
