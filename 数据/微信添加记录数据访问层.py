"""
微信添加记录数据访问层

负责用户_联系方式_微信添加记录表的时间控制相关数据库操作
包括时间控制字段的查询、更新和统计功能
"""


from typing import Dict, Any, Optional
from datetime import datetime, date
import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 系统日志器


async def 异步验证用户微信关联数据访问服务(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    验证用户是否拥有指定的微信账号
    
    Args:
        用户id: 用户id
        微信信息表id: 微信信息表id
        
    Returns:
        Dict: 包含验证结果的响应数据
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 查询用户微信关联表，验证用户是否拥有该微信账号
            查询SQL = """
            SELECT 
                id,
                用户id,
                微信id,
                状态,
                绑定时间
            FROM "用户微信关联表"
            WHERE "用户id" = $1 AND "微信id" = $2 AND "状态" = 1
            """
            
            关联记录 = await 异步连接.fetchrow(查询SQL, 用户id, 微信信息表id)
                
            if 关联记录:
                系统日志器.info(f"用户微信关联验证成功: 用户id={用户id}, 微信id={微信信息表id}")
                return {
                        "status": 状态.通用.成功,
                        "message": "微信账号验证通过",
                        "data": {
                            "关联id": 关联记录["id"],
                            "用户id": 关联记录["用户id"], 
                            "微信id": 关联记录["微信id"],
                            "状态": 关联记录["状态"],
                            "绑定时间": 关联记录["绑定时间"].strftime('%Y-%m-%d %H:%M:%S') if 关联记录["绑定时间"] else None,
                            "是否关联": True
                        }
                    }
            else:
                系统日志器.warning(f"用户微信关联验证失败: 用户id={用户id}, 微信id={微信信息表id}")
                return {
                        "status": 状态.通用.失败,
                        "message": "该微信账号未绑定到您的账户，请先绑定微信账号",
                        "data": {
                            "是否关联": False
                        }
                    }
            
    except Exception as e:
        错误信息 = f"验证用户微信关联时发生异常: 用户id={用户id}, 微信id={微信信息表id}, 错误={str(e)}"
        错误日志器.error(错误信息)
        return {
            "status": 状态.通用.服务器错误,
            "message": "微信账号验证失败，请稍后重试",
            "data": {"错误详情": 错误信息, "是否关联": False}
        }


async def 异步获取微信添加时间控制记录数据访问服务(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    获取微信添加时间控制记录数据访问服务
    
    功能说明：
    - 获取指定微信号的时间控制相关字段
    - 包括计划时间、实际时间、计数器等信息
    - 用于时间控制算法的基础数据
    
    参数：
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表的ID
    
    返回：
        Dict[str, Any]: 包含时间控制记录的响应
    """
    try:
        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:**********). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        # 查询时间控制相关字段，同时获取记录ID以备后续更新使用
        查询时间控制SQL = """
            SELECT
                "id",
                "计划添加时间",
                "实际添加时间",
                "当日添加计数",
                "连续添加计数",
                "最后长休息时间",
                "时间控制状态",
                "创建时间",
                "更新时间"
            FROM "用户_联系方式_微信添加记录表"
            WHERE "用户表id" = $1 AND "我方添加的微信信息表id" = $2
            ORDER BY "更新时间" DESC
            LIMIT 1
        """

        # 执行查询获取时间控制记录
        时间控制记录 = await 异步连接池实例.执行单行查询(查询时间控制SQL, (用户id, 微信信息表id))

        if 时间控制记录:
            # 检查是否跨日期，如果跨日期需要重置当日计数
            当前日期 = date.today()
            最后更新日期 = 时间控制记录["更新时间"].date() if 时间控制记录["更新时间"] else None

            if 最后更新日期 and 最后更新日期 < 当前日期:
                # 跨日期，重置当日计数
                当日添加计数 = 0
                需要更新跨日计数 = True
            else:
                当日添加计数 = 时间控制记录["当日添加计数"]
            需要更新跨日计数 = False

            时间控制数据 = {
                "记录id": 时间控制记录["id"],  # 添加记录ID，方便后续更新
                "计划添加时间": 时间控制记录["计划添加时间"],
                "实际添加时间": 时间控制记录["实际添加时间"],
                "当日添加计数": 当日添加计数,
                "连续添加计数": 时间控制记录["连续添加计数"],
                "最后长休息时间": 时间控制记录["最后长休息时间"],
                "时间控制状态": 时间控制记录["时间控制状态"],
                "最后更新时间": 时间控制记录["更新时间"],
                "数据来源": "数据库记录",
                "需要更新跨日计数": 需要更新跨日计数
            }

            # 如果需要，更新跨日计数
            if 需要更新跨日计数:
                await 异步重置微信添加跨日计数数据访问服务(用户id, 微信信息表id)
                时间控制数据["当日添加计数"] = 0

        else:
            # 没有记录，返回初始状态
            时间控制数据 = {
                        "计划添加时间": None,
                        "实际添加时间": None,
                        "当日添加计数": 0,
                        "连续添加计数": 0,
                        "最后长休息时间": None,
                        "时间控制状态": "normal",
                        "最后更新时间": None,
                        "数据来源": "初始状态",
                        "需要更新跨日计数": False
                    }
                
        系统日志器.info(f"获取微信添加时间控制记录成功: 用户id={用户id}, 微信信息表id={微信信息表id}, 数据来源={时间控制数据['数据来源']}")
                
        return {
                    "status": 状态.通用.成功,
                    "message": "获取微信添加时间控制记录成功",
                    "data": 时间控制数据
                }
                
    except Exception as e:
        错误日志器.error(f"获取微信添加时间控制记录数据访问异常: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"获取微信添加时间控制记录失败: {str(e)}",
            "data": None
        }


async def 异步更新微信添加时间控制记录数据访问服务(
    记录id: int,
    用户id: int,
    计划添加时间: Optional[datetime] = None,
    实际添加时间: Optional[datetime] = None,
    当日添加计数增量: int = 0,
    连续添加计数增量: int = 0,
    最后长休息时间: Optional[datetime] = None,
    时间控制状态: Optional[str] = None,
    是否重置连续计数: bool = False
) -> Dict[str, Any]:
    """
    更新微信添加时间控制记录数据访问服务
    
    功能说明：
    - 更新指定记录的时间控制字段
    - 支持增量更新和绝对值更新
    - 处理计数器的增减和重置
    
    参数：
        记录id (int): 要更新的记录ID
        用户id (int): 当前用户的ID
        计划添加时间 (Optional[datetime]): 新的计划添加时间
        实际添加时间 (Optional[datetime]): 实际添加时间
        当日添加计数增量 (int): 当日计数的增量（默认0）
        连续添加计数增量 (int): 连续计数的增量（默认0）
        最后长休息时间 (Optional[datetime]): 最后长休息时间
        时间控制状态 (Optional[str]): 新的时间控制状态
        是否重置连续计数 (bool): 是否重置连续添加计数
    
    返回：
        Dict[str, Any]: 包含更新结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 验证记录归属
            验证记录SQL = """
                SELECT "当日添加计数", "连续添加计数" 
                FROM "用户_联系方式_微信添加记录表"
                WHERE "id" = $1 AND "用户表id" = $2
            """
            
            记录信息 = await 异步连接.fetchrow(验证记录SQL, 记录id, 用户id)
                
            if not 记录信息:
                    return {
                        "status": 状态.微信.获取对接进度列表失败,
                        "message": "记录不存在或无权限操作",
                        "data": None
                    }
                
                # 计算新的计数值
            if 是否重置连续计数:
                    新连续计数 = 0
            else:
                    新连续计数 = 记录信息["连续添加计数"] + 连续添加计数增量
                
            新当日计数 = 记录信息["当日添加计数"] + 当日添加计数增量
                
                # 构建更新SQL和参数
            更新字段列表 = []
            更新参数列表 = []
                
            参数索引 = 1

            if 计划添加时间 is not None:
                    更新字段列表.append(f'"计划添加时间" = ${参数索引}')
                    更新参数列表.append(计划添加时间)
                    参数索引 += 1

            if 实际添加时间 is not None:
                    更新字段列表.append(f'"实际添加时间" = ${参数索引}')
                    更新参数列表.append(实际添加时间)
                    参数索引 += 1

            if 当日添加计数增量 != 0:
                    更新字段列表.append(f'"当日添加计数" = ${参数索引}')
                    更新参数列表.append(新当日计数)
                    参数索引 += 1

            if 连续添加计数增量 != 0 or 是否重置连续计数:
                    更新字段列表.append(f'"连续添加计数" = ${参数索引}')
                    更新参数列表.append(新连续计数)
                    参数索引 += 1

            if 最后长休息时间 is not None:
                    更新字段列表.append(f'"最后长休息时间" = ${参数索引}')
                    更新参数列表.append(最后长休息时间)
                    参数索引 += 1

            if 时间控制状态 is not None:
                    更新字段列表.append(f'"时间控制状态" = ${参数索引}')
                    更新参数列表.append(时间控制状态)
                    参数索引 += 1

                # 总是更新修改时间
            更新字段列表.append('"更新时间" = NOW()')
                
            if not 更新字段列表:
                    return {
                        "status": 状态.通用.成功,
                        "message": "没有需要更新的字段",
                        "data": {"记录id": 记录id}
                    }
                
                # 执行更新
            更新SQL = f"""
                    UPDATE "用户_联系方式_微信添加记录表" 
                    SET {', '.join(更新字段列表)}
                    WHERE "id" = $1 AND "用户表id" = $2
                """
            更新参数列表.extend([记录id, 用户id])

            await 异步连接.execute(更新SQL, *更新参数列表)
                
            系统日志器.info(f"更新微信添加时间控制记录成功: 记录id={记录id}, 用户id={用户id}, 更新字段数={len(更新字段列表)-1}")
                
            return {
                    "status": 状态.通用.成功,
                    "message": "更新微信添加时间控制记录成功",
                    "data": {
                        "记录id": 记录id,
                        "新当日计数": 新当日计数,
                        "新连续计数": 新连续计数,
                        "更新字段数": len(更新字段列表) - 1
                    }
                }
                
    except Exception as e:
        错误日志器.error(f"更新微信添加时间控制记录数据访问异常: 记录id={记录id}, 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"更新微信添加时间控制记录失败: {str(e)}",
            "data": None
        }


async def 异步统计微信添加操作计数数据访问服务(用户id: int, 微信信息表id: int, 统计时间范围: str = "today") -> Dict[str, Any]:
    """
    统计微信添加操作计数数据访问服务
    
    功能说明：
    - 统计指定微信号在特定时间范围内的添加操作次数
    - 支持今日、本小时、最近N次等不同统计维度
    - 用于时间控制算法的限制判断
    
    参数：
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表的ID
        统计时间范围 (str): 统计范围 today|current_hour|recent_operations
    
    返回：
        Dict[str, Any]: 包含统计结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            统计结果 = {}
            
            if 统计时间范围 == "today" or 统计时间范围 == "all":
                # 统计今日添加次数
                今日统计SQL = """
                    SELECT
                        COUNT(*) as 今日总次数,
                        MAX("实际添加时间") as 最后添加时间
                    FROM "用户_联系方式_微信添加记录表"
                    WHERE "用户表id" = $1 AND "我方添加的微信信息表id" = $2
                    AND "实际添加时间"::date = CURRENT_DATE
                    AND "实际添加时间" IS NOT NULL
                """

                今日结果_原始 = await 异步连接.fetchrow(今日统计SQL, 用户id, 微信信息表id)
                今日结果 = dict(今日结果_原始) if 今日结果_原始 else {}

                统计结果["今日添加次数"] = 今日结果.get("今日总次数", 0)
                统计结果["最后添加时间"] = 今日结果.get("最后添加时间")

            if 统计时间范围 == "current_hour" or 统计时间范围 == "all":
                # 统计当前小时添加次数
                当前小时统计SQL = """
                    SELECT COUNT(*) as 当前小时次数
                    FROM "用户_联系方式_微信添加记录表"
                    WHERE "用户表id" = $1 AND "我方添加的微信信息表id" = $2
                    AND "实际添加时间" >= DATE_TRUNC('hour', CURRENT_TIMESTAMP)
                    AND "实际添加时间" IS NOT NULL
                """

                小时结果_原始 = await 异步连接.fetchrow(当前小时统计SQL, 用户id, 微信信息表id)
                小时结果 = dict(小时结果_原始) if 小时结果_原始 else {}

                统计结果["当前小时添加次数"] = 小时结果.get("当前小时次数", 0)
                
            if 统计时间范围 == "recent_operations" or 统计时间范围 == "all":
                # 统计最近连续添加次数
                最近连续统计SQL = """
                    SELECT "连续添加计数", "最后长休息时间"
                    FROM "用户_联系方式_微信添加记录表"
                    WHERE "用户表id" = $1 AND "我方添加的微信信息表id" = $2
                    ORDER BY "更新时间" DESC
                    LIMIT 1
                """

                连续结果_原始 = await 异步连接.fetchrow(最近连续统计SQL, 用户id, 微信信息表id)
                连续结果 = dict(连续结果_原始) if 连续结果_原始 else {}

                统计结果["连续添加次数"] = 连续结果.get("连续添加计数", 0)
                统计结果["最后长休息时间"] = 连续结果.get("最后长休息时间")
                
                系统日志器.info(f"统计微信添加操作计数成功: 用户id={用户id}, 微信信息表id={微信信息表id}, 统计范围={统计时间范围}")
                
                return {
                    "status": 状态.通用.成功,
                    "message": "统计微信添加操作计数成功",
                    "data": 统计结果
                }
                
    except Exception as e:
        错误日志器.error(f"统计微信添加操作计数数据访问异常: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"统计微信添加操作计数失败: {str(e)}",
            "data": None
        }


async def 异步重置微信添加跨日计数数据访问服务(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    重置微信添加跨日计数数据访问服务
    
    功能说明：
    - 重置指定微信号的当日添加计数
    - 用于跨日期时自动重置计数器
    - 保持连续添加计数不变
    
    参数：
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表的ID
    
    返回：
        Dict[str, Any]: 包含重置结果的响应
    """
    try:
        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:**********). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        # 重置当日计数，保持其他字段不变
        重置计数SQL = """
            UPDATE "用户_联系方式_微信添加记录表"
            SET "当日添加计数" = 0, "更新时间" = CURRENT_TIMESTAMP
            WHERE "用户表id" = $1 AND "我方添加的微信信息表id" = $2
        """

        影响行数 = await 异步连接池实例.执行更新(重置计数SQL, (用户id, 微信信息表id))

        系统日志器.info(f"重置微信添加跨日计数成功: 用户id={用户id}, 微信信息表id={微信信息表id}, 影响行数={影响行数}")

        return {
            "status": 状态.通用.成功,
            "message": "重置微信添加跨日计数成功",
            "data": {
                "用户id": 用户id,
                "微信信息表id": 微信信息表id,
                "影响行数": 影响行数
            }
        }
                
    except Exception as e:
        错误日志器.error(f"重置微信添加跨日计数数据访问异常: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"重置微信添加跨日计数失败: {str(e)}",
            "data": None
        }


async def 异步更新微信添加记录状态数据访问服务(
    记录id: int,
    用户id: int,
    新状态: str,
    备注信息: Optional[str] = None
) -> Dict[str, Any]:
    """
    更新微信添加记录状态数据访问服务
    
    功能说明：
    - 更新指定记录的好友请求状态
    - 仅更新状态相关字段，不涉及时间控制
    - 用于状态更新的基础操作
    
    参数：
        记录id (int): 要更新的记录ID
        用户id (int): 当前用户的ID
        新状态 (str): 新的好友请求状态
        备注信息 (Optional[str]): 备注信息
    
    返回：
        Dict[str, Any]: 包含更新结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 验证记录归属并获取当前状态
            验证记录SQL = """
                SELECT "好友请求状态", "我方添加的微信信息表id"
                FROM "用户_联系方式_微信添加记录表"
                WHERE "id" = $1 AND "用户表id" = $2
            """
            
            记录信息 = await 异步连接.fetchrow(验证记录SQL, 记录id, 用户id)

            if not 记录信息:
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "记录不存在或无权限操作",
                    "data": None
                }

            # 更新状态和备注
            if 备注信息 is not None:
                更新状态SQL = """
                    UPDATE "用户_联系方式_微信添加记录表"
                    SET "好友请求状态" = $1, "备注" = $2, "更新时间" = CURRENT_TIMESTAMP
                    WHERE "id" = $3 AND "用户表id" = $4
                """
                await 异步连接.execute(更新状态SQL, 新状态, 备注信息, 记录id, 用户id)
            else:
                更新状态SQL = """
                    UPDATE "用户_联系方式_微信添加记录表"
                    SET "好友请求状态" = $1, "更新时间" = CURRENT_TIMESTAMP
                    WHERE "id" = $2 AND "用户表id" = $3
                """
                await 异步连接.execute(更新状态SQL, 新状态, 记录id, 用户id)
                
                系统日志器.info(f"更新微信添加记录状态成功: 记录id={记录id}, 用户id={用户id}, 原状态={记录信息['好友请求状态']}, 新状态={新状态}")
                
                return {
                    "status": 状态.通用.成功,
                    "message": "更新微信添加记录状态成功",
                    "data": {
                        "记录id": 记录id,
                        "微信信息表id": 记录信息["微信信息表id"],
                        "原状态": 记录信息["好友请求状态"],
                        "新状态": 新状态,
                        "备注信息": 备注信息
                    }
                }
                
    except Exception as e:
        错误日志器.error(f"更新微信添加记录状态数据访问异常: 记录id={记录id}, 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"更新微信添加记录状态失败: {str(e)}",
            "data": None
        }


async def 异步获取用户添加记录列表数据访问服务(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    搜索关键词: Optional[str] = None,
    状态筛选: Optional[int] = None,
    微信账号筛选: Optional[int] = None,
    时间范围: Optional[list] = None
) -> Dict[str, Any]:
    """
    获取用户添加记录列表数据访问服务

    功能说明：
    - 分页获取用户的微信添加记录列表
    - 支持关键词搜索、状态筛选、微信账号筛选
    - 支持时间范围筛选

    参数：
        用户id (int): 当前用户的ID
        页码 (int): 页码，从1开始
        每页数量 (int): 每页记录数量
        搜索关键词 (Optional[str]): 搜索关键词（联系方式）
        状态筛选 (Optional[str]): 状态筛选
        微信账号筛选 (Optional[int]): 微信账号筛选
        时间范围 (Optional[str]): 时间范围筛选

    返回：
        Dict[str, Any]: 包含记录列表和分页信息的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 构建查询条件
            查询条件列表 = ["r.用户表id = $1"]
            查询参数: list = [用户id]
            参数索引 = 2

            if 搜索关键词:
                查询条件列表.append(f"r.联系方式 LIKE $1")
                查询参数.append(f"%{搜索关键词}%")
                参数索引 += 1

            if 状态筛选:
                查询条件列表.append(f"r.好友请求状态 = $1")
                查询参数.append(状态筛选)
                参数索引 += 1

            if 微信账号筛选:
                查询条件列表.append(f"r.我方添加的微信信息表id = $1")
                查询参数.append(微信账号筛选)
                参数索引 += 1

            if 时间范围:
                if isinstance(时间范围, list) and len(时间范围) == 2:
                    查询条件列表.append(f"r.创建时间 BETWEEN ${参数索引} AND ${参数索引 + 1}")
                    查询参数.extend(时间范围)
                    参数索引 += 2
                elif isinstance(时间范围, str):
                    if 时间范围 == "today":
                        查询条件列表.append("r.创建时间::date = CURRENT_DATE")
                    elif 时间范围 == "week":
                        查询条件列表.append("r.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'")
                    elif 时间范围 == "month":
                        查询条件列表.append("r.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'")

            查询条件 = " AND ".join(查询条件列表)

            # 查询总数
            总数查询SQL = f"""
                SELECT COUNT(*) as total
                FROM 用户_联系方式_微信添加记录表 r
                LEFT JOIN 微信信息表 w ON r.我方添加的微信信息表id = w.id
                WHERE {查询条件}
            """

            总数结果_原始 = await 异步连接.fetchrow(总数查询SQL, *查询参数)
            总数结果 = dict(总数结果_原始) if 总数结果_原始 else {}
            总数 = 总数结果.get("total", 0)

            # 查询列表数据
            偏移量 = (页码 - 1) * 每页数量
            列表查询SQL = f"""
                SELECT
                    r.id,
                    COALESCE(r.联系方式, kc.联系方式) as 联系方式,
                    r.联系方式类型,
                    r.好友请求状态,
                    r.计划添加时间,
                    r.实际添加时间,
                    r.当日添加计数,
                    r.连续添加计数,
                    r.时间控制状态,
                    r.创建时间,
                    r.更新时间,
                    w.微信号 as 我方微信号,
                    w.昵称 as 我方微信昵称,
                    COALESCE(r.联系方式, kc.联系方式) as 目标联系方式,
                    COALESCE(k.昵称, '未知达人') as 达人昵称,
                    k.avatar as 达人头像,
                    k.粉丝数,
                    k.account_douyin as 抖音账号,
                    ui.个人备注,
                    ui.个人标签,
                    kc.类型 as 联系方式来源类型
                FROM 用户_联系方式_微信添加记录表 r
                LEFT JOIN 微信信息表 w ON r.我方添加的微信信息表id = w.id
                LEFT JOIN 用户达人补充信息表 ui ON r.用户达人补充信息表id = ui.id
                LEFT JOIN 联系方式表 kc ON ui.联系方式表id = kc.id
                LEFT JOIN 用户达人关联表 ur ON ui.用户达人关联表id = ur.id
                LEFT JOIN 达人表 k ON ur.达人id = k.id
                WHERE {查询条件}
                ORDER BY r.创建时间 DESC
                LIMIT ${len(查询参数) + 1} OFFSET ${len(查询参数) + 2}
            """

            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:**********). }}
            # {{ Source: PostgreSQL参数占位符文档 }}

            列表查询参数 = 查询参数 + [每页数量, 偏移量]
            记录列表 = await 异步连接.fetch(列表查询SQL, *列表查询参数)

            # 转换为字典列表以避免序列化问题
            记录列表_字典 = [dict(记录) for 记录 in 记录列表]

            # 计算分页信息
            总页数 = (总数 + 每页数量 - 1) // 每页数量 if 总数 > 0 else 0

            系统日志器.info(f"获取用户添加记录列表成功: 用户id={用户id}, 页码={页码}, 总数={总数}")

            return {
                    "status": 状态.通用.成功,
                    "message": "获取添加记录列表成功",
                    "data": {
                        "列表": 记录列表_字典,
                        "总数": 总数,
                        "页码": 页码,
                        "每页数量": 每页数量,
                        "总页数": 总页数
                    }
                }

    except Exception as e:
        错误日志器.error(f"获取用户添加记录列表数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"获取添加记录列表失败: {str(e)}",
            "data": None
        }


async def 异步创建手动添加记录数据访问服务(
    用户id: int,
    记录数据: Dict[str, Any]
) -> Dict[str, Any]:
    """
    创建手动添加记录数据访问服务

    功能说明：
    - 手动创建微信添加好友记录
    - 支持设置计划添加时间和验证消息
    - 自动关联到指定的微信账号

    参数：
        用户id (int): 当前用户的ID
        记录数据 (Dict[str, Any]): 记录数据

    返回：
        Dict[str, Any]: 包含创建结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 插入新记录
            插入SQL = """
                INSERT INTO 用户_联系方式_微信添加记录表 (
                    用户表id,
                    我方添加的微信信息表id,
                    联系方式,
                    联系方式类型,
                    计划添加时间,
                    好友请求状态,
                    验证消息,
                    创建时间,
                    更新时间
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                RETURNING id
            """

            插入参数 = [
                用户id,
                记录数据.get("微信信息表id"),
                记录数据.get("目标联系方式", 记录数据.get("联系方式")),
                记录数据.get("联系方式类型", "微信号"),
                记录数据.get("计划添加时间"),
                记录数据.get("好友请求状态", 0),
                记录数据.get("验证消息", "")
            ]

            记录ID = await 异步连接.fetchval(插入SQL, *插入参数)

            系统日志器.info(f"创建手动添加记录成功: 用户id={用户id}, 记录ID={记录ID}")

            return {
                    "status": 状态.通用.成功,
                    "message": "创建手动添加记录成功",
                    "data": {
                        "记录ID": 记录ID,
                        "联系方式": 记录数据.get("目标联系方式", 记录数据.get("联系方式")),
                        "微信信息表id": 记录数据.get("微信信息表id")
                    }
                }

    except Exception as e:
        错误日志器.error(f"创建手动添加记录数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"创建手动添加记录失败: {str(e)}",
            "data": None
        }





async def 异步批量删除添加记录数据访问服务(
    用户id: int,
    记录IDs: list
) -> Dict[str, Any]:
    """
    批量删除添加记录数据访问服务

    功能说明：
    - 批量删除多个添加记录
    - 批量验证记录归属权限
    - 执行批量硬删除

    参数：
        用户id (int): 当前用户的ID
        记录IDs (list): 要删除的记录ID列表

    返回：
        Dict[str, Any]: 包含批量删除结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            成功计数 = 0
            失败计数 = 0

            for 记录ID in 记录IDs:
                try:
                    # 验证记录归属
                    验证SQL = """
                        SELECT id FROM 用户_联系方式_微信添加记录表
                        WHERE id = $1 AND 用户表id = $2
                    """
                    记录 = await 异步连接.fetchrow(验证SQL, 记录ID, 用户id)

                    if not 记录:
                        失败计数 += 1
                        continue

                    # 执行删除
                    删除SQL = """
                        DELETE FROM 用户_联系方式_微信添加记录表
                        WHERE id = $1 AND 用户表id = $2
                    """
                    await 异步连接.execute(删除SQL, 记录ID, 用户id)
                    成功计数 += 1

                except Exception as e:
                    错误日志器.error(f"删除记录失败: 记录ID={记录ID}, 错误={str(e)}")
                    失败计数 += 1

                系统日志器.info(f"批量删除添加记录完成: 用户id={用户id}, 成功={成功计数}, 失败={失败计数}")

                return {
                    "status": 状态.通用.成功,
                    "message": "批量删除添加记录完成",
                    "data": {
                        "总数": len(记录IDs),
                        "成功数": 成功计数,
                        "失败数": 失败计数
                    }
                }

    except Exception as e:
        错误日志器.error(f"批量删除添加记录数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"批量删除添加记录失败: {str(e)}",
            "data": None
        }


async def 异步导出添加记录数据访问服务(
    用户id: int,
    导出参数: Dict[str, Any]
) -> Dict[str, Any]:
    """
    导出添加记录数据访问服务

    功能说明：
    - 导出用户的微信添加记录为Excel文件
    - 支持筛选条件导出
    - 生成Excel二进制数据

    参数：
        用户id (int): 当前用户的ID
        导出参数 (Dict[str, Any]): 导出参数

    返回：
        Dict[str, Any]: 包含Excel文件数据的响应
    """
    try:
        # 构建查询条件
        查询条件列表 = ["r.用户表id = $1"]
        查询参数: list = [用户id]
        参数索引 = 2

        # 应用筛选条件
        if 导出参数.get("搜索关键词"):
            查询条件列表.append(f"r.联系方式 LIKE ${参数索引}")
            查询参数.append(f"%{导出参数['搜索关键词']}%")
            参数索引 += 1

        if 导出参数.get("状态筛选"):
            查询条件列表.append(f"r.好友请求状态 = ${参数索引}")
            查询参数.append(导出参数["状态筛选"])
            参数索引 += 1

        if 导出参数.get("微信账号筛选"):
            查询条件列表.append(f"r.我方添加的微信信息表id = ${参数索引}")
            查询参数.append(导出参数["微信账号筛选"])
            参数索引 += 1

        查询条件 = " AND ".join(查询条件列表)

        # 查询导出数据
        导出查询SQL = f"""
            SELECT
                r.id as 记录ID,
                r.联系方式,
                r.联系方式类型,
                r.好友请求状态,
                r.计划添加时间,
                r.实际添加时间,
                r.验证消息,
                r.创建时间,
                w.微信号 as 我方微信号,
                w.昵称 as 我方微信昵称
            FROM 用户_联系方式_微信添加记录表 r
            LEFT JOIN 微信信息表 w ON r.我方添加的微信信息表id = w.id
            WHERE {查询条件}
            ORDER BY r.创建时间 DESC
        """

        导出数据 = await 异步连接池实例.执行查询(导出查询SQL, 查询参数)

        # 生成Excel文件
        try:
            import pandas as pd
            import io

            # 转换数据为DataFrame
            df = pd.DataFrame(导出数据)

            # 创建Excel文件
            excel_buffer = io.BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='微信添加记录', index=False)

            excel_buffer.seek(0)
            文件数据 = excel_buffer.getvalue()

        except ImportError:
            # 如果pandas不可用，返回CSV格式
            import csv
            import io

            csv_buffer = io.StringIO()
            if 导出数据:
                fieldnames = 导出数据[0].keys()
                writer = csv.DictWriter(csv_buffer, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(导出数据)

            文件数据 = csv_buffer.getvalue().encode('utf-8-sig')  # 使用BOM确保Excel正确显示中文

            # 生成文件名
            from datetime import datetime
            当前时间 = datetime.now().strftime("%Y%m%d_%H%M%S")
            文件名 = f"微信添加记录_{当前时间}.xlsx"

            系统日志器.info(f"导出添加记录成功: 用户id={用户id}, 记录数={len(导出数据)}")

            return {
                "status": 状态.通用.成功,
                "message": "导出添加记录成功",
                "data": {
                    "文件数据": 文件数据,
                    "文件名": 文件名,
                    "记录数": len(导出数据)
                }
            }

    except Exception as e:
        错误日志器.error(f"导出添加记录数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"导出添加记录失败: {str(e)}",
            "data": None
        }


async def 异步获取用户添加记录概览统计数据访问服务(
    用户id: int
) -> Dict[str, Any]:
    """
    获取用户添加记录概览统计数据访问服务

    功能说明：
    - 获取用户的微信添加记录概览统计数据
    - 包括总添加次数、成功添加数、待处理数、今日添加数等

    参数：
        用户id (int): 当前用户的ID

    返回：
        Dict[str, Any]: 包含概览统计数据的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 查询总添加次数
            总添加次数查询SQL = """
                SELECT COUNT(*) as total
                FROM 用户_联系方式_微信添加记录表
                WHERE 用户表id = $1
            """
            总添加次数结果_原始 = await 异步连接.fetchrow(总添加次数查询SQL, 用户id)
            总添加次数结果 = dict(总添加次数结果_原始) if 总添加次数结果_原始 else {}
            总添加次数 = 总添加次数结果.get("total", 0)

            # 查询成功添加数
            成功添加数查询SQL = """
                SELECT COUNT(*) as total
                FROM 用户_联系方式_微信添加记录表
                WHERE 用户表id = $1 AND 好友请求状态 = 1
            """
            成功添加数结果_原始 = await 异步连接.fetchrow(成功添加数查询SQL, 用户id)
            成功添加数结果 = dict(成功添加数结果_原始) if 成功添加数结果_原始 else {}
            成功添加数 = 成功添加数结果.get("total", 0)

            # 查询待处理数（包括待处理和进行中）
            待处理数查询SQL = """
                SELECT COUNT(*) as total
                FROM 用户_联系方式_微信添加记录表
                WHERE 用户表id = $1 AND (好友请求状态 = 0 OR 好友请求状态 = 2 OR 好友请求状态 IS NULL)
            """
            待处理数结果_原始 = await 异步连接.fetchrow(待处理数查询SQL, 用户id)
            待处理数结果 = dict(待处理数结果_原始) if 待处理数结果_原始 else {}
            待处理数 = 待处理数结果.get("total", 0)

            # 查询今日添加数
            今日添加数查询SQL = """
                SELECT COUNT(*) as total
                FROM 用户_联系方式_微信添加记录表
                WHERE 用户表id = $1 AND 创建时间::date = CURRENT_DATE
            """
            今日添加数结果_原始 = await 异步连接.fetchrow(今日添加数查询SQL, 用户id)
            今日添加数结果 = dict(今日添加数结果_原始) if 今日添加数结果_原始 else {}
            今日添加数 = 今日添加数结果.get("total", 0)

            # 构建概览统计数据
            概览统计数据 = {
                "总添加次数": 总添加次数,
                "成功添加数": 成功添加数,
                "待处理数": 待处理数,
                "今日添加数": 今日添加数
            }

            系统日志器.info(f"获取用户添加记录概览统计成功: 用户id={用户id}")

            return {
                "status": 状态.通用.成功,
                "message": "获取添加记录概览统计成功",
                "data": 概览统计数据
            }

    except Exception as e:
        错误日志器.error(f"获取用户添加记录概览统计数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"获取添加记录概览统计失败: {str(e)}",
            "data": None
        }


async def 异步删除添加记录数据访问服务(
    用户id: int,
    记录ID: int
) -> Dict[str, Any]:
    """
    删除添加记录数据访问服务

    功能说明：
    - 删除单个添加记录
    - 验证记录归属权限
    - 执行软删除或硬删除

    参数：
        用户id (int): 当前用户的ID
        记录ID (int): 要删除的记录ID

    返回：
        Dict[str, Any]: 包含删除结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 验证记录归属并获取记录信息
            验证SQL = """
                SELECT id, 联系方式, 我方添加的微信信息表id
                FROM 用户_联系方式_微信添加记录表
                WHERE id = $1 AND 用户表id = $2
            """
            记录信息 = await 异步连接.fetchrow(验证SQL, 记录ID, 用户id)

            if not 记录信息:
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "记录不存在或无权限操作",
                    "data": None
                }

            # 执行硬删除
            删除SQL = """
                DELETE FROM 用户_联系方式_微信添加记录表
                WHERE id = $1 AND 用户表id = $2
            """
            await 异步连接.execute(删除SQL, 记录ID, 用户id)

            系统日志器.info(f"删除添加记录成功: 用户id={用户id}, 记录ID={记录ID}")

            return {
                    "status": 状态.通用.成功,
                    "message": "删除添加记录成功",
                    "data": {
                        "记录ID": 记录ID,
                        "联系方式": 记录信息["联系方式"],
                        "微信信息表id": 记录信息["我方添加的微信信息表id"]
                    }
                }

    except Exception as e:
        错误日志器.error(f"删除添加记录数据访问异常: 用户id={用户id}, 记录ID={记录ID}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": f"删除添加记录失败: {str(e)}",
            "data": None
        }


async def 异步获取添加记录详情数据访问服务(
    用户id: int,
    记录ID: int
) -> Dict[str, Any]:
    """
    获取添加记录详情数据访问服务

    功能说明：
    - 获取单个添加记录的详细信息
    - 验证记录归属权限
    - 返回完整的记录数据和关联信息

    参数：
        用户id (int): 当前用户的ID
        记录ID (int): 记录ID

    返回：
        Dict[str, Any]: 包含记录详情的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 异步连接:
            # 查询记录详情
            详情查询SQL = """
                SELECT
                    r.id,
                    r.联系方式,
                    r.联系方式类型,
                    r.好友请求状态,
                    r.计划添加时间,
                    r.实际添加时间,
                    r.当日添加计数,
                    r.连续添加计数,
                    r.最后长休息时间,
                    r.时间控制状态,
                    r.验证消息,
                    r.创建时间,
                    r.更新时间,
                    w.微信号 as 我方微信号,
                    w.昵称 as 我方微信昵称,
                    w.头像 as 我方微信头像
                FROM 用户_联系方式_微信添加记录表 r
                LEFT JOIN 微信信息表 w ON r.我方添加的微信信息表id = w.id
                WHERE r.id = $1 AND r.用户表id = $2
            """

            记录详情 = await 异步连接.fetchrow(详情查询SQL, 记录ID, 用户id)

            if not 记录详情:
                    return {
                        "status": 状态.微信.获取对接进度列表失败,
                        "message": "记录不存在或无权限访问",
                        "data": None
                    }

            # 转换为字典以避免序列化问题
            记录详情_字典 = dict(记录详情)

            系统日志器.info(f"获取添加记录详情成功: 用户id={用户id}, 记录ID={记录ID}")

            return {
                    "status": 状态.通用.成功,
                    "message": "获取添加记录详情成功",
                    "data": {
                        "记录详情": 记录详情_字典
                    }
                }

    except Exception as e:
        错误日志器.error(f"获取添加记录详情数据访问异常: 用户id={用户id}, 记录ID={记录ID}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": f"获取添加记录详情失败: {str(e)}",
            "data": None
        }