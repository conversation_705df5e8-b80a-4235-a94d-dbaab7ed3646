"""
团队达人数据处理模块
负责处理团队维度的达人数据查询和统计业务
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Optional, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 应用日志器 as 数据库日志器


async def 异步获取团队达人统计(团队id: int, 时间范围: str = '30d', 包含非活跃: bool = False) -> Dict[str, Any]:
    """
    获取团队达人统计数据
    
    参数:
        团队id: 团队id
        时间范围: 时间范围，如 '7d', '30d', '90d', 'all'
        包含非活跃: 是否包含非活跃达人
        
    返回:
        包含团队达人统计数据的字典
    """
    try:
        # 解析时间范围
        时间条件 = ""
        查询参数 = [团队id]
        
        if 时间范围 != 'all':
            天数 = int(时间范围.replace('d', ''))
            开始时间 = datetime.now() - timedelta(days=天数)
            时间条件 = "AND uda.认领时间 >= $2"
            查询参数.append(开始时间)
        
        # 账号状态条件（如果需要可以在查询中直接使用）
        
        # 优化的团队达人统计查询 - 使用索引友好的查询方式
        # 建议索引：
        # - 用户团队关联表(团队id, 状态, 用户id)
        # - 用户达人关联表(用户id, 状态, 认领时间, 达人id)
        # {{ AURA-X: Fix - 使用中文字段名避免大小写问题. Source: PostgreSQL中文字段名最佳实践 }}
        统计SQL = f"""
        SELECT
            COUNT(DISTINCT uda.达人id) as 总达人数,
            COUNT(DISTINCT uda.用户id) as 活跃成员数,
            COUNT(DISTINCT CASE WHEN uda.认领时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
                                THEN uda.达人id END) as 月新增数
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uda ON utg.用户id = uda.用户id
        WHERE utg.团队id = $1
        AND utg.状态 = '正常'
        AND uda.状态 = 1
        {时间条件}
        """
        
        数据库日志器.debug(f"执行团队达人统计SQL: {统计SQL}, 参数: {查询参数}")
        统计结果 = await 异步连接池实例.执行查询(统计SQL, tuple(查询参数))
        
        if not 统计结果:
            return {
                "totalTalents": 0,
                "activeMembers": 0,
                "monthlyNew": 0,
                "avgPerMember": 0,
                "activeTalents": 0,
                "totalMembers": 0,
                "memberStats": []
            }
        
        基础统计 = 统计结果[0]
        总达人数 = 基础统计["总达人数"] or 0
        活跃成员数 = 基础统计["活跃成员数"] or 0
        
        # 计算平均每人达人数
        平均达人数 = float(总达人数 / 活跃成员数) if 活跃成员数 > 0 else 0
        
        # {{ AURA-X: Fix - 修复PostgreSQL字段名大小写问题. Source: PostgreSQL字段名处理最佳实践 }}
        # 查询团队总成员数
        成员数SQL = """
        SELECT COUNT(*) as 总成员数
        FROM 用户团队关联表
        WHERE 团队id = $1 AND 状态 = '正常'
        """
        成员数结果 = await 异步连接池实例.执行查询(成员数SQL, (团队id,))
        总成员数 = 成员数结果[0]['总成员数'] if 成员数结果 else 0
        
        # 简化的成员统计查询 - 修复字段名问题
        成员统计SQL = """
        SELECT
            u.id as 用户id,
            COALESCE(u.昵称, CONCAT('用户', u.id)) as 用户名,
            NULL as 头像,
            COUNT(DISTINCT uda.达人id) as 达人数量
        FROM 用户团队关联表 utg
        INNER JOIN 用户表 u ON utg.用户id = u.id
        LEFT JOIN 用户达人关联表 uda ON u.id = uda.用户id AND uda.状态 = 1
        WHERE utg.团队id = $1
        AND utg.状态 = '正常'
        GROUP BY u.id, u.昵称
        ORDER BY 达人数量 DESC
        LIMIT 10
        """
        
        成员统计结果 = await 异步连接池实例.执行查询(成员统计SQL, (团队id,))
        
        # 处理成员统计数据
        成员统计列表 = []
        for 成员 in 成员统计结果:
            成员统计列表.append({
                "用户id": 成员["用户id"],
                "用户名": 成员["用户名"],
                "头像": 成员["头像"] or "https://api.dicebear.com/7.x/personas/svg?seed=default",
                "达人数量": 成员["达人数量"] or 0
            })
        
        return {
            "总达人数": 总达人数,
            "活跃成员数": 活跃成员数,
            "月新增数": 基础统计["月新增数"] or 0,
            "平均每人达人数": round(平均达人数, 1),
            "活跃达人数": 总达人数,  # 暂时使用总达人数
            "总成员数": 总成员数,
            "成员统计": 成员统计列表
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队达人统计失败: {e}")
        raise


async def 异步获取团队达人列表(团队id: int, 页码: int = 1, 每页数量: int = 20,
                              成员id: Optional[int] = None, 关键词: Optional[str] = None,
                              排序字段: str = '认领时间', 排序方式: str = 'desc') -> Dict[str, Any]:
    """
    获取团队达人列表
    
    参数:
        团队id: 团队id
        页码: 当前页码
        每页数量: 每页显示数量
        成员id: 按成员筛选（可选）
        关键词: 搜索关键词（可选）
        状态筛选: 达人状态筛选（可选）
        排序字段: 排序字段
        排序方式: 排序方式
        
    返回:
        包含团队达人列表的字典
    """
    try:
        # 构建查询条件 - 优化抖音平台条件
        # 注意：很多历史数据可能没有平台字段，所以包含NULL值
        条件列表 = ["utg.团队id = $1", "utg.状态 = '正常'", "uda.状态 = 1"]
        查询参数 = [团队id]
        参数索引 = 2

        # 添加平台条件 - 兼容历史数据
        条件列表.append("(uda.平台 = '抖音' OR uda.平台 IS NULL OR uda.平台 = '' )")


        # {{ AURA-X: Fix - 修复PostgreSQL参数占位符重复使用问题. Source: PostgreSQL参数绑定最佳实践 }}
        # 成员筛选
        if 成员id:
            条件列表.append(f"uda.用户id = ${参数索引}")
            查询参数.append(成员id)
            参数索引 += 1

        # 简化的关键词搜索 - 只搜索用户名
        if 关键词:
            条件列表.append(f"u.昵称 LIKE ${参数索引}")
            关键词模式 = f"%{关键词}%"
            查询参数.append(关键词模式)
            参数索引 += 1
        
        # 暂时忽略状态筛选避免跨库查询问题
        
        # 简化排序 - 只支持按认领时间排序
        排序方向 = "DESC" if 排序方式.lower() == "desc" else "ASC"
        
        # 构建WHERE条件
        where_条件 = " AND ".join(条件列表)
        
        # 简化的总数查询
        总数SQL = f"""
        SELECT COUNT(DISTINCT uda.达人id) as total
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uda ON utg.用户id = uda.用户id
        INNER JOIN 用户表 u ON uda.用户id = u.id
        WHERE {where_条件}
        """
        
        数据库日志器.info(f"执行团队达人总数查询: {总数SQL}")
        数据库日志器.info(f"查询参数: {查询参数}")
        总数结果 = await 异步连接池实例.执行查询(总数SQL, tuple(查询参数))
        总数 = 总数结果[0]['total'] if 总数结果 else 0
        数据库日志器.info(f"抖音达人总数查询结果: {总数}")
        
        # 优化的抖音达人列表查询 - 从达人表获取真实数据
        偏移量 = (页码 - 1) * 每页数量
        列表SQL = f"""
        SELECT
            uda.达人id as 达人id,
            uda.认领时间 as 认领时间,
            uda.用户id as 认领人id,
            COALESCE(u.昵称, CONCAT('用户', u.id)) as 认领人姓名,
            NULL as 认领人头像,
            dt.昵称 as 达人昵称,
            dt.account_douyin as 抖音号,
            dt.avatar as 头像,
            dt.粉丝数 as 粉丝数,
            dt.关注数 as 关注数,
            dt.video_count_30 as 视频数,
            dt.promotion_sum_30 as 推广数,
            dt.city as 城市,
            dt.性别 as 性别,
            dt.企业认证 as 是否认证,
            dt.fans_sum_str as 粉丝数文本
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uda ON utg.用户id = uda.用户id
        INNER JOIN 用户表 u ON uda.用户id = u.id
        LEFT JOIN 达人表 dt ON uda.达人id = dt.id
        WHERE {where_条件}
        ORDER BY uda.认领时间 {排序方向}
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        查询参数.extend([每页数量, 偏移量])
        数据库日志器.info(f"执行团队达人列表查询: {列表SQL}")
        数据库日志器.info(f"列表查询参数: {查询参数}")
        列表结果 = await 异步连接池实例.执行查询(列表SQL, tuple(查询参数))

        # 添加调试信息
        数据库日志器.info(f"抖音达人查询结果: 总数={总数}, 当前页数据={len(列表结果) if 列表结果 else 0}")
        if 列表结果:
            数据库日志器.info(f"第一条数据示例: {列表结果[0] if 列表结果 else 'None'}")

        # 处理列表数据 - 返回完整的抖音达人信息
        处理后列表 = []
        for 达人 in 列表结果:
            处理后达人 = {
                "达人id": 达人["达人id"],
                "昵称": 达人["达人昵称"] or f"达人_{达人['达人id']}",
                "抖音号": 达人["抖音号"] or "",
                "头像": 达人["头像"] or "",
                "粉丝数": 达人["粉丝数"] or 0,
                "关注数": 达人["关注数"] or 0,
                "作品数": 达人["视频数"] or 0,
                "推广数": 达人["推广数"] or 0,
                "粉丝数文本": 达人["粉丝数文本"] or str(达人["粉丝数"] or 0),
                "城市": 达人["城市"] or "",
                "性别": 达人["性别"] or "",
                "企业认证": bool(达人["是否认证"]),
                "认领时间": 达人["认领时间"].isoformat() if 达人["认领时间"] else None,
                "认领人id": 达人["认领人id"],
                "认领人姓名": 达人["认领人姓名"],
                "认领人头像": 达人["认领人头像"] or "https://api.dicebear.com/7.x/personas/svg?seed=default",
                "平台": "抖音"
            }
            处理后列表.append(处理后达人)
        
        # {{ AURA-X: Optimize - 统一使用中文字段名，前后端直接对接. Source: 中文API设计最佳实践 }}
        return {
            "达人列表": 处理后列表,
            "总数": 总数,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": (总数 + 每页数量 - 1) // 每页数量 if 总数 > 0 else 0
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队达人列表失败: {e}")
        raise





async def 异步获取团队达人详细分析(团队id: int, 时间范围: str = '30d') -> Dict[str, Any]:
    """
    获取团队达人详细分析数据
    
    产品功能：为前端详细分析模态框提供完整的图表数据和统计信息
    
    参数:
        团队id: 团队id
        时间范围: 分析时间范围，如 '7d', '30d', '90d', '1y'
        
    返回:
        包含详细分析数据的字典，支持多种图表展示
    """
    try:
        from datetime import datetime, timedelta
        
        # 解析时间范围，计算查询时间点
        时间点映射 = {
            '7d': 7,
            '30d': 30, 
            '90d': 90,
            '1y': 365
        }
        
        天数 = 时间点映射.get(时间范围, 30)
        开始时间 = datetime.now() - timedelta(days=天数)
        
        # 1. 获取核心指标数据
        核心指标SQL = """
        SELECT
            COUNT(DISTINCT uda.达人id) as 总达人数,
            COUNT(DISTINCT uda.用户id) as 活跃成员数,
            COUNT(DISTINCT CASE WHEN uda.认领时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
                                THEN uda.达人id END) as 月新增数,
            COUNT(DISTINCT utg.用户id) as 总成员数
        FROM 用户团队关联表 utg
        LEFT JOIN 用户达人关联表 uda ON utg.用户id = uda.用户id AND uda.状态 = 1
        WHERE utg.团队id = $1
        AND utg.状态 = '正常'
        """
        
        数据库日志器.debug(f"执行核心指标查询: {核心指标SQL}")
        核心指标结果 = await 异步连接池实例.执行查询(核心指标SQL, (团队id,))
        
        if not 核心指标结果:
            raise Exception("无法获取团队核心指标数据")
            
        核心指标 = 核心指标结果[0]
        总达人数 = 核心指标["总达人数"] or 0
        活跃成员数 = 核心指标["活跃成员数"] or 0
        总成员数 = 核心指标["总成员数"] or 0
        
        # 2. 生成趋势分析数据
        # 根据时间范围确定数据点数量
        数据点数 = min(天数, 30) if 天数 <= 30 else 30
        
        趋势数据 = []
        for i in range(数据点数):
            当前日期 = datetime.now() - timedelta(days=数据点数-1-i)
            日期字符串 = 当前日期.strftime('%m-%d')
            
            # 查询当日累计达人数 - 简化查询避免性能问题
            当日达人数SQL = """
            SELECT COUNT(DISTINCT uda.达人id) as count
            FROM 用户团队关联表 utg
            INNER JOIN 用户达人关联表 uda ON utg.用户id = uda.用户id
            WHERE utg.团队id = $1
            AND utg.状态 = '正常'
            AND uda.状态 = 1
            AND uda.认领时间 <= $2
            """
            
            当日结果 = await 异步连接池实例.执行查询(当日达人数SQL, (团队id, 当前日期))
            当日达人数 = 当日结果[0]['count'] if 当日结果 else 0
            
            # 模拟粉丝总数计算（实际应该查询kol库的达人表）
            模拟粉丝数 = 当日达人数 * 18000 + (i * 1000)
            
            趋势数据.append({
                "date": 日期字符串,
                "talentCount": 当日达人数,
                "totalFans": 模拟粉丝数
            })
        
        # 3. 获取成员详细数据（包含绩效分析）
        成员详细SQL = """
        SELECT 
            u.id as userId,
            COALESCE(u.昵称, CONCAT('用户', u.id)) as memberName,
            COUNT(DISTINCT uda.达人id) as talentCount,
            utg.加入时间 as joinTime
        FROM 用户团队关联表 utg
        INNER JOIN 用户表 u ON utg.用户id = u.id
        LEFT JOIN 用户达人关联表 uda ON u.id = uda.用户id 
            AND uda.状态 = 1 
            AND uda.认领时间 >= $1
        WHERE utg.团队id = $2
        AND utg.状态 = '正常'
        GROUP BY u.id, u.昵称, utg.加入时间
        ORDER BY talentCount DESC
        """
        
        成员详细结果 = await 异步连接池实例.执行查询(成员详细SQL, (开始时间, 团队id))
        
        # 处理成员详细数据，添加分析指标
        成员详细数据 = []
        平均达人数 = 总达人数 / 活跃成员数 if 活跃成员数 > 0 else 0
        
        for i, 成员 in enumerate(成员详细结果):
            # 计算绩效评分（基于达人数量和平均值的比较）
            达人数量 = 成员["talentCount"] or 0
            绩效评分 = min(95, max(60, int(60 + (达人数量 / (平均达人数 + 1)) * 35)))
            
            # 计算增长率（模拟数据）
            增长率 = round((达人数量 - 平均达人数) / (平均达人数 + 1) * 100, 1)
            
            # 模拟粉丝总数
            模拟总粉丝 = 达人数量 * 18500 + (i * 5000)
            
            成员详细数据.append({
                "key": str(成员["userId"]),
                "memberName": 成员["memberName"],
                "talentCount": 达人数量,
                "totalFans": 模拟总粉丝,
                "performanceScore": 绩效评分,
                "growthRate": 增长率,
                "fansQuality": min(95, max(70, 绩效评分 - 5)),
                "activityLevel": min(95, max(75, 绩效评分 + 5)),
                "conversionRate": min(85, max(60, 绩效评分 - 10))
            })
        
        # 4. 生成类别分布数据（模拟，实际需要查询kol库）
        类别数据 = [
            {"name": "美妆时尚", "value": max(1, int(总达人数 * 0.3))},
            {"name": "生活方式", "value": max(1, int(总达人数 * 0.25))},
            {"name": "科技数码", "value": max(1, int(总达人数 * 0.15))},
            {"name": "美食探店", "value": max(1, int(总达人数 * 0.12))},
            {"name": "其他", "value": max(1, int(总达人数 * 0.18))}
        ]
        
        # 5. 生成粉丝分布数据（模拟）
        粉丝分布 = [
            max(1, int(总达人数 * 0.15)),   # <1万
            max(1, int(总达人数 * 0.35)),   # 1-5万
            max(1, int(总达人数 * 0.25)),   # 5-10万
            max(1, int(总达人数 * 0.15)),   # 10-50万
            max(0, int(总达人数 * 0.08)),   # 50-100万
            max(0, int(总达人数 * 0.02))    # >100万
        ]
        
        # 计算衍生指标
        参与率 = (活跃成员数 / 总成员数 * 100) if 总成员数 > 0 else 0
        效率指数 = min(100, max(60, int(60 + (总达人数 / (总成员数 + 1)) * 40)))
        活跃度评分 = min(100, max(70, int(70 + (活跃成员数 / (总成员数 + 1)) * 30)))
        
        # 计算总价值（模拟）
        总价值 = 总达人数 * 1.8 + (活跃成员数 * 5.2)
        价值增长 = max(0, min(30, (核心指标["monthlyNew"] or 0) / max(1, 总达人数) * 100))
        
        return {
            "总价值": round(总价值, 1),
            "价值增长": round(价值增长, 1),
            "效率指数": 效率指数,
            "活跃度评分": round(活跃度评分, 1),
            "参与率": round(参与率, 1),
            "活跃成员数": 活跃成员数,
            "总成员数": 总成员数,
            "趋势数据": 趋势数据,
            "类别数据": 类别数据,
            "粉丝分布": 粉丝分布,
            "成员详情": 成员详细数据
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队达人详细分析失败: {e}")
        raise


async def 异步获取团队达人详情(团队id: int, 达人id: int) -> Optional[Dict[str, Any]]:
    """
    获取团队达人详情信息
    
    产品功能：为前端详情模态框提供完整的达人详细信息
    
    核心业务逻辑：
    1. 验证达人是否属于该团队（通过用户团队关联和达人认领关联验证）
    2. 跨库查询达人基本信息（从kol库获取抖音数据）
    3. 查询认领关系和团队信息（从invite库获取业务数据）
    4. 计算商业价值指标（粉丝数、互动率、活跃度等）
    5. 生成合作建议（基于算法评估达人商业价值）
    
    数据来源：
    - 达人表：达人基础信息（昵称、粉丝数、简介等）
    - 用户达人关联表：认领关系信息
    - 用户团队关联表：团队成员关系
    - 用户表：认领人信息
    
    参数:
        团队id: 团队id - 用于验证达人归属
        达人id: 达人id - 主键，用于查询达人信息
        
    返回:
        达人详情字典或None（如果不存在）
        
    异常:
        - 数据库连接异常：记录错误日志并抛出异常
        - 查询参数无效：记录警告日志并返回None
    """
    try:
        # 1. 验证达人是否属于该团队并获取认领信息
        认领验证SQL = """
        SELECT 
            uda.用户id as 认领人id,
            uda.认领时间,
            uda.备注 as 认领备注,
            u.昵称 as 认领人昵称,
            utg.团队id,
            utg.职位 as 认领人角色
        FROM 用户达人关联表 uda
        INNER JOIN 用户团队关联表 utg ON uda.用户id = utg.用户id
        INNER JOIN 用户表 u ON uda.用户id = u.id
        WHERE uda.达人id = $1
        AND utg.团队id = $2
        AND uda.状态 = 1 
        AND utg.状态 = '正常'
        """
        
        数据库日志器.debug(f"验证达人 {达人id} 是否属于团队 {团队id}")
        认领结果 = await 异步连接池实例.执行查询(认领验证SQL, (达人id, 团队id))
        
        if not 认领结果:
            数据库日志器.warning(f"达人 {达人id} 不属于团队 {团队id} 或未被认领")
            return None
            
        认领信息 = 认领结果[0]
        
        # 2. 从kol库获取达人基本信息
        达人基本信息SQL = """
        SELECT 
            uid_number as 达人UID,
            昵称 as 达人昵称,
            account_douyin as 抖音号,
            粉丝数,
            关注数,
            COALESCE(video_count_30, 0) as 获赞数,
            COALESCE(promotion_sum_30, 0) as 作品数,
            avatar as 头像链接,
            introduction as 简介,
            update_time as 账号创建时间,
            douyin_info_update_time as 数据更新时间,
            city as 城市,
            性别,
            企业认证,
            账号状态
        FROM 达人表
        WHERE id = $1
        """
        
        数据库日志器.debug(f"查询达人 {达人id} 基本信息")
        达人基本结果 = await 异步连接池实例.执行查询(达人基本信息SQL, (达人id,))
        
        if not 达人基本结果:
            错误日志器.warning(f"kol库中未找到达人 {达人id} 的基本信息")
            # 提供默认信息确保接口可用
            达人基本信息 = {
                "达人UID": f"unknown_{达人id}",
                "达人昵称": f"达人_{达人id}",
                "抖音号": "未知",
                "粉丝数": 0,
                "关注数": 0,
                "获赞数": 0,
                "作品数": 0,
                "头像链接": "",
                "简介": "暂无简介",
                "账号创建时间": None,
                "数据更新时间": None,
                "城市": "",
                "性别": "",
                "企业认证": "",
                "账号状态": 1
            }
        else:
            达人基本信息 = 达人基本结果[0]
        
        # 3. 获取个人管理信息
        个人管理SQL = """
        SELECT 
            COUNT(*) as 总认领数,
            COUNT(CASE WHEN uda.认领时间 >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                       THEN 1 END) as 近期认领数,
            MIN(uda.认领时间) as 首次认领时间,
            MAX(uda.认领时间) as 最新认领时间
        FROM 用户达人关联表 uda
        WHERE uda.用户id = $1 AND uda.状态 = 1
        """
        
        个人管理结果 = await 异步连接池实例.执行查询(个人管理SQL, (认领信息["认领人id"],))
        个人管理信息 = 个人管理结果[0] if 个人管理结果 else {
            "总认领数": 0,
            "近期认领数": 0,
            "首次认领时间": None,
            "最新认领时间": None
        }
        
        # 4. 计算商业价值指标
        粉丝数 = 达人基本信息["粉丝数"] or 0
        获赞数 = 达人基本信息["获赞数"] or 0
        作品数 = 达人基本信息["作品数"] or 0
        
        # 商业价值评级算法（1-5星）
        if 粉丝数 >= 1000000:
            商业价值星级 = 5
        elif 粉丝数 >= 500000:
            商业价值星级 = 4
        elif 粉丝数 >= 100000:
            商业价值星级 = 3
        elif 粉丝数 >= 10000:
            商业价值星级 = 2
        else:
            商业价值星级 = 1
            
        # 互动率计算（获赞数/粉丝数，简化算法）
        互动率 = min(15.0, (获赞数 / max(粉丝数, 1) * 100)) if 粉丝数 > 0 else 0.0
        
        # 活跃度评估
        活跃度评分 = min(100, max(60, int(60 + (作品数 / 100) * 40)))
        
        # 合作建议算法
        合作建议 = []
        if 粉丝数 >= 100000:
            合作建议.append("适合品牌代言")
        if 粉丝数 >= 50000:
            合作建议.append("适合产品推广")
        if 互动率 >= 5.0:
            合作建议.append("用户互动活跃")
        if 作品数 >= 100:
            合作建议.append("内容产出稳定")
        if not 合作建议:
            合作建议.append("建议观察发展")
        
        # 5. 格式化数据格式
        def 格式化数字(数值):
            """将大数字格式化为易读格式"""
            if 数值 >= 100000000:  # 1亿
                return f"{数值/100000000:.1f}亿"
            elif 数值 >= 10000:  # 1万
                return f"{数值/10000:.1f}w"
            elif 数值 >= 1000:  # 1千
                return f"{数值/1000:.1f}k"
            else:
                return str(数值)
        
        # 6. 组装返回数据 - 使用与前端期望一致的字段名
        详情数据 = {
            # 基本信息
            "id": 达人id,
            "昵称": 达人基本信息["达人昵称"] or f"达人_{达人id}",
            "抖音号": 达人基本信息["抖音号"] or "",
            "UID": 达人基本信息["达人UID"] or "",
            "头像": 达人基本信息["头像链接"] or "",
            "性别": 达人基本信息["性别"] or "",
            "城市": 达人基本信息["城市"] or "",
            "简介": 达人基本信息["简介"] or "",
            "企业认证": 达人基本信息["企业认证"] or "",
            
            # 数据统计
            "粉丝数": 粉丝数,
            "关注数": 达人基本信息["关注数"] or 0,
            "获赞数": 获赞数,
            "作品数": 作品数,
            "账号状态": 达人基本信息.get("账号状态", 1),  # 使用实际账号状态
            
            # 认领信息
            "认领时间": 认领信息["认领时间"],
            "认领人id": 认领信息["认领人id"],
            "认领人昵称": 认领信息["认领人昵称"] or 认领信息["认领人英文名"] or f"用户{认领信息['认领人id']}",
            "认领人手机": "",  # 暂时为空，需要时可扩展
            "团队名称": "当前团队",  # 临时显示，需要时可查询team表
            "团队角色": 认领信息["认领人角色"] or "",
            "备注": 认领信息["认领备注"] or "",
            
            # 合作信息
            "合作状态": "",
            "联系方式": "",
            "个人备注": "",
            
            # 时间信息
            "数据更新时间": 达人基本信息["数据更新时间"],
            "个人信息更新时间": 认领信息["认领时间"],
            
            # 扩展信息（用于详情页高级功能）
            "商业价值星级": 商业价值星级,
            "互动率": round(互动率, 2),
            "活跃度评分": 活跃度评分,
            "合作建议": 合作建议,
            "总认领数": 个人管理信息["总认领数"],
            "近期认领数": 个人管理信息["近期认领数"]
        }
        
        数据库日志器.info(f"成功获取团队 {团队id} 达人 {达人id} 详情信息")
        return 详情数据
        
    except Exception as e:
        错误日志器.error(f"获取团队达人详情失败: {e}")
        raise


# ==================== 微信达人相关函数 ====================

async def 异步获取团队微信达人统计(团队id: int, 时间范围: str = '30d', 包含非活跃: bool = False) -> Dict[str, Any]:
    """
    获取团队微信达人统计数据

    参数:
        团队id: 团队id
        时间范围: 时间范围，如 '7d', '30d', '90d', 'all'
        包含非活跃: 是否包含非活跃达人

    返回:
        包含团队微信达人统计数据的字典
    """
    try:
        # 解析时间范围
        时间条件 = ""
        查询参数 = [团队id]

        if 时间范围 != 'all':
            天数 = int(时间范围.replace('d', ''))
            开始时间 = datetime.now() - timedelta(days=天数)
            时间条件 = "AND uwda.认领时间 >= $2"
            查询参数.append(开始时间)

        # 优化的团队微信达人统计查询
        # 建议索引：
        # - 用户团队关联表(团队id, 状态, 用户id)
        # - 用户达人关联表(用户id, 状态, 认领时间, 达人id, 平台)
        统计SQL = f"""
        SELECT
            COUNT(DISTINCT uwda.达人id) as 总达人数,
            COUNT(DISTINCT uwda.用户id) as 活跃成员数,
            COUNT(DISTINCT CASE WHEN uwda.认领时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
                                THEN uwda.达人id END) as 月新增数
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uwda ON utg.用户id = uwda.用户id
        WHERE utg.团队id = $1
        AND utg.状态 = '正常'
        AND uwda.状态 = 1
        AND uwda.平台 = '微信'
        {时间条件}
        """

        数据库日志器.debug(f"执行团队微信达人统计SQL: {统计SQL}, 参数: {查询参数}")
        统计结果 = await 异步连接池实例.执行查询(统计SQL, tuple(查询参数))

        if not 统计结果:
            return {
                "totalTalents": 0,
                "activeMembers": 0,
                "monthlyNew": 0,
                "avgPerMember": 0,
                "activeTalents": 0,
                "totalMembers": 0,
                "memberStats": []
            }

        基础统计 = 统计结果[0]
        总达人数 = 基础统计["总达人数"] or 0
        活跃成员数 = 基础统计["活跃成员数"] or 0

        # 计算平均每人达人数
        平均达人数 = float(总达人数 / 活跃成员数) if 活跃成员数 > 0 else 0

        # 查询团队总成员数
        成员数SQL = """
        SELECT COUNT(*) as 总成员数
        FROM 用户团队关联表
        WHERE 团队id = $1 AND 状态 = '正常'
        """
        成员数结果 = await 异步连接池实例.执行查询(成员数SQL, (团队id,))
        总成员数 = 成员数结果[0]['总成员数'] if 成员数结果 else 0

        # 成员统计查询
        成员统计SQL = """
        SELECT
            u.id as 用户id,
            COALESCE(u.昵称, '用户' || u.id) as 用户名,
            NULL as 头像,
            COUNT(DISTINCT uwda.达人id) as 达人数量
        FROM 用户团队关联表 utg
        INNER JOIN 用户表 u ON utg.用户id = u.id
        LEFT JOIN 用户达人关联表 uwda ON u.id = uwda.用户id AND uwda.状态 = 1 AND uwda.平台 = '微信'
        WHERE utg.团队id = $1
        AND utg.状态 = '正常'
        GROUP BY u.id, u.昵称
        ORDER BY 达人数量 DESC
        LIMIT 10
        """

        成员统计结果 = await 异步连接池实例.执行查询(成员统计SQL, (团队id,))

        # 处理成员统计数据
        成员统计列表 = []
        for 成员 in 成员统计结果:
            成员统计列表.append({
                "用户id": 成员["用户id"],
                "用户名": 成员["用户名"],
                "头像": 成员["头像"] or "https://api.dicebear.com/7.x/personas/svg?seed=default",
                "达人数量": 成员["达人数量"] or 0
            })

        return {
            "总达人数": 总达人数,
            "活跃成员数": 活跃成员数,
            "月新增数": 基础统计["月新增数"] or 0,
            "平均每人达人数": round(平均达人数, 1),
            "活跃达人数": 总达人数,
            "总成员数": 总成员数,
            "成员统计": 成员统计列表
        }

    except Exception as e:
        错误日志器.error(f"获取团队微信达人统计失败: {e}")
        raise


async def 异步获取团队微信达人列表(团队id: int, 页码: int = 1, 每页数量: int = 20,
                                成员id: Optional[int] = None, 关键词: Optional[str] = None,
                                排序字段: str = '认领时间', 排序方式: str = 'desc') -> Dict[str, Any]:
    """
    获取团队微信达人列表

    参数:
        团队id: 团队id
        页码: 当前页码
        每页数量: 每页显示数量
        成员id: 按成员筛选（可选）
        关键词: 搜索关键词（可选）
        状态筛选: 达人状态筛选（可选）
        排序字段: 排序字段
        排序方式: 排序方式

    返回:
        包含团队微信达人列表的字典
    """
    try:
        # 构建查询条件
        条件列表 = ["utg.团队id = $1", "utg.状态 = '正常'", "uwda.状态 = 1"]
        查询参数 = [团队id]
        参数索引 = 2

        # 成员筛选
        if 成员id:
            条件列表.append(f"uwda.用户id = ${参数索引}")
            查询参数.append(成员id)
            参数索引 += 1

        # 关键词搜索（搜索用户名和微信达人昵称）
        if 关键词:
            条件列表.append(f"(u.昵称 LIKE ${参数索引} OR wd.昵称 LIKE ${参数索引 + 1})")
            关键词模式 = f"%{关键词}%"
            查询参数.extend([关键词模式, 关键词模式])
            参数索引 += 2

        # 排序处理
        排序方向 = "DESC" if 排序方式.lower() == "desc" else "ASC"

        # 构建WHERE条件
        where_条件 = " AND ".join(条件列表)

        # 总数查询
        总数SQL = f"""
        SELECT COUNT(DISTINCT uwda.达人id) as total
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uwda ON utg.用户id = uwda.用户id
        INNER JOIN 用户表 u ON uwda.用户id = u.id
        LEFT JOIN 微信达人表 wd ON uwda.达人id = wd.id
        WHERE {where_条件} AND uwda.平台 = '微信'
        """

        数据库日志器.debug(f"执行团队微信达人总数查询: {总数SQL}, 参数: {查询参数}")
        总数结果 = await 异步连接池实例.执行查询(总数SQL, 查询参数)
        总数 = 总数结果[0]['total'] if 总数结果 else 0

        # 达人列表查询
        偏移量 = (页码 - 1) * 每页数量
        列表SQL = f"""
        SELECT
            uwda.达人id as 达人id,
            uwda.认领时间 as 认领时间,
            uwda.用户id as 认领人id,
            COALESCE(u.昵称, CONCAT('用户', u.id)) as 认领人姓名,
            NULL as 认领人头像,
            wd.昵称 as 达人昵称,
            wd.finderUsername as 视频号,
            wd.头像 as 头像,
            wd.粉丝数文本 as 粉丝数文本,
            wd.GMV文本 as GMV文本,
            wd.内容类型 as 内容类型,
            wd.带货类目 as 带货类目,
            wd.有无联系方式 as 有无联系方式
        FROM 用户团队关联表 utg
        INNER JOIN 用户达人关联表 uwda ON utg.用户id = uwda.用户id
        INNER JOIN 用户表 u ON uwda.用户id = u.id
        LEFT JOIN 微信达人表 wd ON uwda.达人id = wd.id
        WHERE {where_条件} AND uwda.平台 = '微信'
        ORDER BY uwda.认领时间 {排序方向}
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        查询参数.extend([每页数量, 偏移量])
        数据库日志器.debug(f"执行团队微信达人列表查询: {列表SQL}, 参数: {查询参数}")
        列表结果 = await 异步连接池实例.执行查询(列表SQL, 查询参数)

        # 处理列表数据
        处理后列表 = []
        for 达人 in 列表结果:
            # 处理JSON字段
            内容类型 = []
            带货类目 = []

            if 达人.get("contentTypes"):
                try:
                    内容类型 = json.loads(达人["contentTypes"]) if isinstance(达人["contentTypes"], str) else 达人["contentTypes"]
                except (json.JSONDecodeError, TypeError):
                    内容类型 = []

            if 达人.get("categories"):
                try:
                    带货类目 = json.loads(达人["categories"]) if isinstance(达人["categories"], str) else 达人["categories"]
                except (json.JSONDecodeError, TypeError):
                    带货类目 = []

            处理后达人 = {
                "达人id": 达人["达人id"],
                "昵称": 达人.get("达人昵称") or f"微信达人_{达人['达人id']}",
                "视频号": 达人.get("视频号", ""),
                "头像": 达人.get("头像", ""),
                "粉丝数文本": 达人.get("粉丝数文本", ""),
                "GMV文本": 达人.get("GMV文本", ""),
                "内容类型": 内容类型,
                "带货类目": 带货类目,
                "有联系方式": 达人.get("有无联系方式", 0),
                "认领时间": 达人["认领时间"].isoformat() if 达人["认领时间"] else None,
                "认领人id": 达人["认领人id"],
                "认领人姓名": 达人["认领人姓名"],
                "认领人头像": 达人["认领人头像"] or "https://api.dicebear.com/7.x/personas/svg?seed=default",
                "平台": "微信"
            }
            处理后列表.append(处理后达人)

        return {
            "达人列表": 处理后列表,
            "总数": 总数,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": (总数 + 每页数量 - 1) // 每页数量 if 总数 > 0 else 0
        }

    except Exception as e:
        错误日志器.error(f"获取团队微信达人列表失败: {e}")
        raise