"""
达人补充信息数据访问层
负责处理用户达人补充信息表的数据访问操作

特性：
1. 补充联系方式的增删改查
2. 权限验证和数据安全
3. 分页查询和搜索
4. 与联系方式表的关联管理
"""

from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 达人补充信息数据访问:
    """达人补充信息数据访问类"""

    @staticmethod
    async def 添加补充联系方式(
        用户达人关联表id: int,
        联系方式: str,
        联系方式类型: str,
        联系方式表id: Optional[int] = None,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None
    ) -> Optional[int]:
        """
        添加补充联系方式
        
        Args:
            用户达人关联表id: 用户达人关联表id
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            联系方式表id: 联系方式表ID（可选）
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息
            
        Returns:
            新创建的补充信息ID或None
        """
        try:
            插入SQL = """
            INSERT INTO 用户达人补充信息表
            (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 补充信息, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id
            """
            
            # 处理个人标签
            标签JSON = 个人标签 if 个人标签 else []
            
            结果 = await 异步连接池实例.执行查询(
                插入SQL, 
                (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 标签JSON, 补充信息)
            )
            
            if 结果:
                补充信息id = 结果[0]["id"]
                数据库日志器.info(f"添加补充联系方式成功: ID={补充信息id}")
                return 补充信息id
            else:
                return None
                
        except Exception as e:
            错误日志器.error(f"添加补充联系方式失败: 关联表id={用户达人关联表id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 检查补充信息权限(补充信息id: int, 用户id: int) -> Optional[Dict[str, Any]]:
        """
        检查补充信息权限
        
        Args:
            补充信息id: 补充信息ID
            用户id: 用户ID
            
        Returns:
            权限信息或None
        """
        try:
            检查权限SQL = """
            SELECT si.id, si.用户达人关联表id FROM 用户达人补充信息表 si
            JOIN 用户达人关联表 ur ON si.用户达人关联表id = ur.id
            WHERE si.id = $1 AND ur.用户id = $2 AND ur.状态 = 1
            """
            
            结果 = await 异步连接池实例.执行查询(检查权限SQL, (补充信息id, 用户id))
            
            if 结果:
                return 结果[0]
            else:
                return None
                
        except Exception as e:
            错误日志器.error(f"检查补充信息权限失败: 补充信息ID={补充信息id}, 用户ID={用户id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 更新补充联系方式(
        补充信息id: int,
        联系方式: Optional[str] = None,
        联系方式类型: Optional[str] = None,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None,
        用户联系人表id: Optional[UUID] = None
    ) -> bool:
        """
        更新补充联系方式
        
        Args:
            补充信息id: 补充信息ID
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息
            用户联系人表id: 用户联系人表ID
            
        Returns:
            是否更新成功
        """
        try:
            # 构建动态更新字段
            更新字段 = []
            参数列表 = []
            参数索引 = 1
            
            if 联系方式 is not None:
                更新字段.append(f"联系方式 = ${参数索引}")
                参数列表.append(联系方式)
                参数索引 += 1
                
            if 联系方式类型 is not None:
                更新字段.append(f"联系方式类型 = ${参数索引}")
                参数列表.append(联系方式类型)
                参数索引 += 1
                
            if 个人备注 is not None:
                更新字段.append(f"个人备注 = ${参数索引}")
                参数列表.append(个人备注)
                参数索引 += 1
                
            if 个人标签 is not None:
                更新字段.append(f"个人标签 = ${参数索引}")
                参数列表.append(个人标签)
                参数索引 += 1
                
            if 补充信息 is not None:
                更新字段.append(f"补充信息 = ${参数索引}")
                参数列表.append(补充信息)
                参数索引 += 1
                
            if 用户联系人表id is not None:
                更新字段.append(f"用户联系人表id = ${参数索引}")
                参数列表.append(用户联系人表id)
                参数索引 += 1
            
            if not 更新字段:
                return True  # 没有要更新的字段
            
            # 添加更新时间
            更新字段.append("更新时间 = NOW()")
            
            # 添加WHERE条件的参数
            参数列表.append(补充信息id)
            
            更新SQL = f"""
            UPDATE 用户达人补充信息表
            SET {", ".join(更新字段)}
            WHERE id = ${参数索引}
            """
            
            影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)
            
            if 影响行数 > 0:
                数据库日志器.info(f"更新补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"更新补充联系方式未影响任何行: ID={补充信息id}")
                return False
                
        except Exception as e:
            错误日志器.error(f"更新补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 删除补充联系方式(补充信息id: int) -> bool:
        """
        删除补充联系方式
        
        Args:
            补充信息id: 补充信息ID
            
        Returns:
            是否删除成功
        """
        try:
            删除SQL = """
            DELETE FROM 用户达人补充信息表
            WHERE id = $1
            """
            
            影响行数 = await 异步连接池实例.执行更新(删除SQL, (补充信息id,))
            
            if 影响行数 > 0:
                数据库日志器.info(f"删除补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"删除补充联系方式未影响任何行: ID={补充信息id}")
                return False
                
        except Exception as e:
            错误日志器.error(f"删除补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 查询补充信息列表(
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        关键词: Optional[str] = None,
        平台: Optional[str] = None,
        联系方式类型: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询补充信息列表
        
        Args:
            用户id: 用户ID
            页码: 页码
            每页数量: 每页数量
            关键词: 搜索关键词
            平台: 平台筛选
            联系方式类型: 联系方式类型筛选
            
        Returns:
            查询结果
        """
        try:
            # 构建查询条件
            where_conditions = ["u.用户id = $1", "u.状态 = 1"]
            params = [用户id]
            param_index = 2
            
            # 构建JOIN表
            join_tables = ""
            
            # 关键词搜索
            keyword_condition = ""
            if 关键词:
                keyword_condition = f"""
                AND (
                    s.联系方式 ILIKE ${{param_index}} OR
                    s.个人备注 ILIKE ${{param_index}} OR
                    s.补充信息 ILIKE ${{param_index}}
                """
                
                # 如果关键词可能是达人相关信息，需要JOIN达人表
                if 平台 == "抖音":
                    join_tables = "LEFT JOIN 达人表 t ON u.达人id = t.id"
                    keyword_condition += " OR t.昵称 ILIKE ${param_index} OR t.account_douyin ILIKE ${param_index}"
                elif 平台 == "微信":
                    join_tables = "LEFT JOIN 微信达人表 wt ON u.达人id = wt.id"
                    keyword_condition += " OR wt.昵称 ILIKE ${param_index} OR wt.finderUsername ILIKE ${param_index}"
                
                keyword_condition += ")"
                params.append(f"%{关键词}%")
                param_index += 1
            
            # 平台筛选
            if 平台:
                where_conditions.append(f"u.平台 = ${param_index}")
                params.append(平台)
                param_index += 1
            
            # 联系方式类型筛选
            if 联系方式类型:
                where_conditions.append(f"s.联系方式类型 = ${param_index}")
                params.append(联系方式类型)
                param_index += 1
            
            # 查询总数
            count_sql = f"""
            SELECT COUNT(DISTINCT s.id) as total
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            {join_tables}
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            """
            
            总数结果 = await 异步连接池实例.执行查询(count_sql, params)
            总数 = 总数结果[0]["total"] if 总数结果 else 0
            
            # 分页查询数据
            offset = (页码 - 1) * 每页数量
            params.extend([每页数量, offset])
            
            data_sql = f"""
            SELECT
                s.id as 补充信息id,
                u.id as 关联id,
                u.达人id,
                u.平台,
                u.平台账号,
                u.认领时间,
                s.联系方式,
                s.联系方式类型,
                s.个人备注,
                s.个人标签,
                s.补充信息,
                s.用户联系人表id,
                s.更新时间 as 联系方式更新时间
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            {join_tables}
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            ORDER BY s.更新时间 DESC, u.认领时间 DESC
            LIMIT ${len(params) - 1} OFFSET ${len(params)}
            """
            
            数据结果 = await 异步连接池实例.执行查询(data_sql, params)
            
            return {
                "列表": 数据结果,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量
            }
            
        except Exception as e:
            错误日志器.error(f"查询补充信息列表失败: 用户ID={用户id}, 错误={str(e)}")
            return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量}

    @staticmethod
    async def 查询或创建补充信息记录(用户达人关联表id: int) -> Optional[int]:
        """
        查询或创建用户达人补充信息记录
        
        Args:
            用户达人关联表id: 用户达人关联表id
            
        Returns:
            补充信息ID或None
        """
        try:
            # 先查询是否存在
            查询补充信息SQL = """
            SELECT id FROM 用户达人补充信息表
            WHERE 用户达人关联表id = $1
            LIMIT 1
            """
            
            查询结果 = await 异步连接池实例.执行查询(查询补充信息SQL, (用户达人关联表id,))
            
            if 查询结果:
                return 查询结果[0]["id"]
            else:
                # 如果不存在补充信息记录，创建一个基础记录
                创建补充信息SQL = """
                INSERT INTO 用户达人补充信息表
                (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 创建时间, 更新时间)
                VALUES ($1, NULL, NULL, NULL, '通过寄样申请自动创建', '[]', NOW(), NOW())
                RETURNING id
                """
                
                创建结果 = await 异步连接池实例.执行查询(创建补充信息SQL, (用户达人关联表id,))
                
                if 创建结果:
                    补充信息id = 创建结果[0]["id"]
                    数据库日志器.info(f"自动创建补充信息记录: ID={补充信息id}")
                    return 补充信息id
                else:
                    return None
                    
        except Exception as e:
            错误日志器.error(f"查询或创建补充信息记录失败: 关联表id={用户达人关联表id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 查询关联记录_通过补充信息ID(补充信息id: int) -> Optional[Dict[str, Any]]:
        """
        通过补充信息ID查询关联记录
        
        Args:
            补充信息id: 补充信息ID
            
        Returns:
            关联记录信息或None
        """
        try:
            查询关联记录SQL = """
            SELECT 
                s.id as 补充信息id,
                s.用户达人关联表id,
                u.用户id,
                u.达人id,
                u.平台,
                u.平台账号,
                u.认领时间,
                u.状态
            FROM 用户达人补充信息表 s
            JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            WHERE s.id = $1
            """
            
            结果 = await 异步连接池实例.执行查询(查询关联记录SQL, (补充信息id,))
            
            if 结果:
                return 结果[0]
            else:
                return None
                
        except Exception as e:
            错误日志器.error(f"查询关联记录失败: 补充信息ID={补充信息id}, 错误={str(e)}")
            return None


    @staticmethod
    async def 查询或创建补充信息记录(关联表id: int) -> Optional[int]:
        """
        查询或创建用户达人补充信息记录

        Args:
            关联表id: 用户达人关联表id

        Returns:
            补充信息记录ID或None
        """
        try:
            # 先查询是否已存在
            查询SQL = """
            SELECT id FROM 用户达人补充信息表
            WHERE 用户达人关联表id = $1
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (关联表id,))

            if 结果:
                return 结果[0]["id"]
            else:
                # 创建新记录
                创建SQL = """
                INSERT INTO 用户达人补充信息表
                (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 创建时间, 更新时间)
                VALUES ($1, NULL, NULL, NULL, '通过寄样申请自动创建', '[]', NOW(), NOW())
                RETURNING id
                """

                创建结果 = await 异步连接池实例.执行查询(创建SQL, (关联表id,))

                if 创建结果:
                    数据库日志器.info(f"创建补充信息记录成功: 关联表id={关联表id}")
                    return 创建结果[0]["id"]
                else:
                    return None

        except Exception as e:
            错误日志器.error(f"查询或创建补充信息记录失败: 关联表id={关联表id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 检查用户达人关联权限(用户id: int, 达人id: int) -> Optional[Dict[str, Any]]:
        """
        检查用户是否认领了指定达人

        Args:
            用户id: 用户ID
            达人id: 达人ID

        Returns:
            关联信息字典或None
        """
        try:
            检查关联SQL = """
            SELECT id FROM 用户达人关联表
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            LIMIT 1
            """
            关联结果 = await 异步连接池实例.执行查询(检查关联SQL, (用户id, 达人id))

            if 关联结果:
                return {"关联表id": 关联结果[0]["id"], "有权限": True}
            else:
                return None

        except Exception as e:
            错误日志器.error(f"检查用户达人关联权限失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 查询关联记录_通过补充信息ID(补充信息id: int, 用户id: int) -> Optional[Dict[str, Any]]:
        """
        通过补充信息ID查询关联记录

        Args:
            补充信息id: 补充信息ID
            用户id: 用户ID

        Returns:
            关联记录信息或None
        """
        try:
            查询关联记录SQL = """
            SELECT
                s.id as 补充信息id,
                s.用户达人关联表id,
                r.id as 关联表id,
                r.用户id,
                r.达人id,
                r.平台,
                r.平台账号,
                r.状态
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 r ON s.用户达人关联表id = r.id
            WHERE s.id = $1 AND r.用户id = $2
            """

            关联记录 = await 异步连接池实例.执行查询(查询关联记录SQL, (补充信息id, 用户id))

            if 关联记录:
                return 关联记录[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(f"查询关联记录失败: 补充信息id={补充信息id}, 用户id={用户id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 更新关联表达人ID(关联表id: int, 达人id: int, 用户id: int) -> bool:
        """
        更新用户达人关联表的达人ID字段

        Args:
            关联表id: 关联表ID
            达人id: 达人ID
            用户id: 用户ID

        Returns:
            更新是否成功
        """
        try:
            更新关联表SQL = """
            UPDATE 用户达人关联表
            SET 达人id = $1
            WHERE id = $2 AND 用户id = $3 AND 达人id IS NULL
            """

            更新结果 = await 异步连接池实例.执行更新(更新关联表SQL, (达人id, 关联表id, 用户id))

            return 更新结果 > 0

        except Exception as e:
            错误日志器.error(f"更新关联表达人ID失败: 关联表id={关联表id}, 达人id={达人id}, 用户id={用户id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 更新补充联系方式(
        补充信息id: int,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新补充联系方式

        Args:
            补充信息id: 补充信息ID
            个人备注: 个人备注
            个人标签: 个人标签
            补充信息: 补充信息

        Returns:
            是否更新成功
        """
        try:
            更新字段 = []
            更新值 = []
            参数索引 = 1

            if 个人备注 is not None:
                更新字段.append(f"个人备注 = ${参数索引}")
                更新值.append(个人备注)
                参数索引 += 1

            if 个人标签 is not None:
                更新字段.append(f"个人标签 = ${参数索引}")
                更新值.append(json.dumps(个人标签))
                参数索引 += 1

            if 补充信息 is not None:
                更新字段.append(f"补充信息 = ${参数索引}")
                更新值.append(json.dumps(补充信息))
                参数索引 += 1

            if not 更新字段:
                return True  # 没有要更新的字段

            更新字段.append("更新时间 = NOW()")
            更新值.append(补充信息id)

            更新SQL = f"""
            UPDATE 用户达人补充信息表
            SET {", ".join(更新字段)}
            WHERE id = ${参数索引}
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, 更新值)

            if 影响行数 > 0:
                数据库日志器.info(f"更新补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"更新补充联系方式未影响任何行: ID={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 删除补充联系方式(补充信息id: int) -> bool:
        """
        删除补充联系方式

        Args:
            补充信息id: 补充信息ID

        Returns:
            是否删除成功
        """
        try:
            删除SQL = """
            DELETE FROM 用户达人补充信息表
            WHERE id = $1
            """

            影响行数 = await 异步连接池实例.执行更新(删除SQL, (补充信息id,))

            if 影响行数 > 0:
                数据库日志器.info(f"删除补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"删除补充联系方式未影响任何行: ID={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"删除补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False


# 创建数据访问层实例
达人补充信息数据访问实例 = 达人补充信息数据访问()
