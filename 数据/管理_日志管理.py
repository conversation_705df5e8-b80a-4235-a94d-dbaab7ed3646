"""
SuperAdmin 日志管理数据操作模块
负责处理日志文件操作、日志查询等相关的数据库操作
超级管理员专用 - 权限等级最高

功能包括：
- 接口调用用户列表查询
- 日志文件列表获取
- 日志文件内容读取
- 日志数据统计分析等高级日志管理功能
"""

import os
import pathlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 系统日志器, 错误日志器, 接口日志器

# 定义日志文件目录的基础路径 (相对于项目根目录) - 统一存放在系统日志目录
日志文件目录 = os.path.join(os.path.dirname(os.path.dirname(__file__)), "系统日志")


async def 异步获取接口调用用户列表(
    接口路径: str, 
    时间段: str = 'all', 
    开始日期: Optional[str] = None, 
    结束日期: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    异步获取指定接口的调用用户列表
    
    Args:
        接口路径 (str): 要查询的接口路径。
        时间段 (str): 预设时间范围，默认为 'all'。
        开始日期 (Optional[str]): 自定义时间段的开始日期 (YYYY-MM-DD)。
        结束日期 (Optional[str]): 自定义时间段的结束日期 (YYYY-MM-DD)。

    Returns:
        List[Dict[str, Any]]: 调用该接口的用户、IP 和调用次数信息列表 
                               [{'用户id': id, '用户名': 昵称, 'IP地址': ip, '调用次数': count}, ...]。
                               对于匿名调用，用户id 为 None，用户名为 '未知用户'。
    """
    try:
        # --- 添加日志：记录输入参数 ---
        系统日志器.info(f"[异步获取接口调用用户列表] 接收参数: 接口路径='{接口路径}', 时间段='{时间段}', 开始日期='{开始日期}', 结束日期='{结束日期}'")
        参数 = [接口路径] # 第一个参数是接口路径
        条件 = " WHERE ijl.请求路径 = $1 " # 基础条件

        now = datetime.now()

        # 根据时间段计算日期范围并添加到条件和参数中
        if 时间段 == 'today':
            起始 = now.replace(hour=0, minute=0, second=0, microsecond=0)
            结束 = now.replace(hour=23, minute=59, second=59, microsecond=999999)
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'yesterday':
            昨天 = now - timedelta(days=1)
            起始 = 昨天.replace(hour=0, minute=0, second=0, microsecond=0)
            结束 = 昨天.replace(hour=23, minute=59, second=59, microsecond=999999)
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'week':
            起始 = now - timedelta(days=now.weekday())
            起始 = 起始.replace(hour=0, minute=0, second=0, microsecond=0)
            结束 = 起始 + timedelta(days=6)
            结束 = 结束.replace(hour=23, minute=59, second=59, microsecond=999999)
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'month':
            起始 = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            下个月第一天 = (起始.replace(day=28) + timedelta(days=4)).replace(day=1)
            结束 = 下个月第一天 - timedelta(microseconds=1)
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'last_month':
            本月第一天 = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            上月最后一天 = 本月第一天 - timedelta(microseconds=1)
            上月第一天 = 上月最后一天.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            起始 = 上月第一天
            结束 = 上月最后一天
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'year':
            起始 = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            结束 = now.replace(month=12, day=31, hour=23, minute=59, second=59, microsecond=999999)
            条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
            参数.extend([起始, 结束])
        elif 时间段 == 'custom' and 开始日期 and 结束日期:
            try:
                起始 = datetime.strptime(开始日期, '%Y-%m-%d').replace(hour=0, minute=0, second=0, microsecond=0)
                结束 = datetime.strptime(结束日期, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
                条件 += " AND ijl.创建时间 BETWEEN $1 AND $2 "
                参数.extend([起始, 结束])
            except ValueError:
                系统日志器.warning(f"自定义日期格式错误: 开始={开始日期}, 结束={结束日期}")
                # 日期无效则不添加时间条件，查询该接口所有时间的调用用户

        # 构建完整查询，获取用户id、用户名、IP地址和调用次数
        查询 = f"""
        SELECT
            ijl.用户id,
            MAX(u.昵称) AS 用户名, -- 使用 MAX() 获取用户名，确保每个分组只有一行用户名
            ijl.ip地址,
            COUNT(*) AS 调用次数 -- 新增：计算调用次数
        FROM 接口日志表 ijl
        LEFT JOIN 用户表 u ON ijl.用户id = u.id
        {条件}
        GROUP BY ijl.用户id, ijl.ip地址 -- 按用户id和IP地址分组
        ORDER BY 调用次数 DESC, COALESCE(ijl.用户id, 999999999) ASC, ijl.ip地址 ASC -- 按次数、用户id(处理NULL)、IP排序
        """

        # --- 添加日志：记录最终执行的 SQL 和参数 ---
        系统日志器.debug(f"[异步获取接口调用用户列表] 执行 SQL: {查询}")
        系统日志器.debug(f"[异步获取接口调用用户列表] SQL 参数: {参数}")
        结果 = await 异步连接池实例.执行查询(查询, 参数)
        
        # --- 添加日志：记录数据库原始返回结果 ---
        系统日志器.info(f"[异步获取接口调用用户列表] 数据库原始结果: {结果}")
        
        if not 结果:
            系统日志器.info("[异步获取接口调用用户列表] 查询结果为空，返回空列表。")
            return []
        
        用户列表 = []
        for 行 in 结果:
            用户id = 行.get("用户id")
            用户名 = 行.get("用户名")
            # 如果用户id存在但用户名为空（理论上不应发生，除非用户表数据有问题），给个默认值
            if 用户id is not None and not 用户名:
                用户名 = f"用户 {用户id}"
            # 如果用户id不存在，标记为未知用户
            elif 用户id is None:
                用户名 = "未知用户"
            
            用户列表.append({
                "用户id": 用户id, # 可能为 None
                "用户名": 用户名,
                "IP地址": 行.get("IP地址") or "未知IP", # 获取IP地址，提供默认值
                "调用次数": 行.get("调用次数") or 0 # 新增：获取调用次数
            })
            
        # --- 添加日志：记录最终返回的用户列表 ---
        系统日志器.info(f"[异步获取接口调用用户列表] 处理后返回的用户列表: {用户列表}")
        return 用户列表

    except Exception as e:
        # --- 添加日志：记录捕获到的异常 ---
        接口日志器.error(f"异步获取接口 {接口路径} 调用用户列表失败 ({时间段}): {e}", exc_info=True)
        return [] # 出错时返回空列表


async def 异步获取日志文件列表() -> List[str]:
    """
    异步获取 日志/文件/ 目录下的 .log 文件列表
    
    Returns:
        List[str]: 日志文件名列表，按修改时间降序排序
    """
    try:
        # 使用 pathlib 进行更安全的路径操作
        log_dir = pathlib.Path(日志文件目录)
        if not log_dir.is_dir():
            系统日志器.debug(f"日志文件目录不存在: {日志文件目录}")
            return []
        
        # 使用 glob 查找所有 .log 文件，并只返回文件名
        log_files = [f.name for f in log_dir.glob('*.log') if f.is_file()]
        # 按修改时间降序排序，最新的在前面
        log_files.sort(key=lambda name: (log_dir / name).stat().st_mtime, reverse=True)
        return log_files
    except Exception as e:
        错误日志器.error(f"异步获取日志文件列表失败: {e}", exc_info=True)
        return []


async def 异步读取日志文件内容(文件名: str, 读取行数: int = 200) -> List[str]:
    """
    异步读取指定日志文件的最后 N 行内容
    
    Args:
        文件名 (str): 日志文件名
        读取行数 (int): 要读取的行数，默认200行
        
    Returns:
        List[str]: 日志文件的最后N行内容
    """
    try:
        # 安全性检查：确保文件名不包含路径遍历字符，并且是 .log 文件
        if '..' in 文件名 or not 文件名.endswith('.log'):
             系统日志器.debug(f"尝试读取无效的日志文件名: {文件名}")
             raise ValueError("无效的文件名")
             
        # 构造完整路径并再次验证
        目标文件路径 = pathlib.Path(日志文件目录) / 文件名
        # 确保解析后的路径仍在允许的目录下
        if not str(目标文件路径.resolve()).startswith(str(pathlib.Path(日志文件目录).resolve())):
             错误日志器.error(f"检测到潜在的目录遍历尝试: {文件名}")
             raise ValueError("非法的文件路径")

        if not 目标文件路径.is_file():
            raise FileNotFoundError(f"日志文件未找到: {文件名}")

        # 读取文件末尾 N 行 (简单实现，对超大文件可能效率不高)
        lines = []
        文件大小 = 目标文件路径.stat().st_size
        缓冲区大小 = 4096 # 每次读取的缓冲区大小
        已读取字节 = 0


        with open(目标文件路径, 'rb') as f:
            # 定位到文件末尾
            try:
                f.seek(0, os.SEEK_END)
            except OSError:
                 # 对于某些特殊文件（如管道）可能不支持 seek
                 # 简单读取最后一部分
                 f.seek(max(0, 文件大小 - 缓冲区大小 * 5)) # 尝试读取最后约 20KB
                 return f.read().decode('utf-8', errors='replace').splitlines()[-读取行数:]

            while len(lines) < 读取行数 and 文件大小 > 0:
                # 计算本次应该 seek 的位置
                本次seek偏移 = min(缓冲区大小, 文件大小 - 已读取字节)
                f.seek(-本次seek偏移 - 已读取字节, os.SEEK_END)
                
                # 读取数据块
                块 = f.read(本次seek偏移)
                已读取字节 += 本次seek偏移
                
                # 按行分割，并添加到结果列表前面
                块内行 = 块.decode('utf-8', errors='replace').splitlines()
                lines[0:0] = 块内行 # 插入到列表开头
                
                # 如果读取到文件开头，或者字节已读完，结束
                if f.tell() <= 0 or 已读取字节 >= 文件大小:
                    break

            # 返回最后 N 行
            return lines[-读取行数:]

    except FileNotFoundError:
         系统日志器.debug(f"请求的日志文件不存在: {文件名}")
         return [f"错误: 日志文件 '{文件名}' 未找到。"]
    except ValueError as ve:
         错误日志器.error(f"读取日志文件值错误: {ve}")
         return [f"错误: {ve}"]
    except Exception as e:
        错误日志器.error(f"异步读取日志文件内容失败 ({文件名}): {e}", exc_info=True)
        return [f"读取日志文件时发生内部错误: {e}"] 