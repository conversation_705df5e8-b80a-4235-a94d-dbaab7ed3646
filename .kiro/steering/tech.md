# 技术栈与构建系统

## 后端技术栈
- **Python 3.8+** - 主要编程语言
- **FastAPI 0.115.12** - 高性能Web API框架
- **Uvicorn** - ASGI服务器
- **MySQL 8.0** - 主数据库
- **PyMySQL/aiomysql** - 异步数据库连接器
- **Pydantic 2.x** - 数据验证和序列化
- **JWT (python-jose)** - 身份认证
- **Redis 7.x** - 缓存和会话存储

## 前端技术栈
- **Vue.js 3.4.0** - 渐进式前端框架 (Composition API)
- **Vite 5.2.0** - 现代化构建工具
- **Ant Design Vue 4.1.0** - 企业级UI组件库
- **Pinia 2.1.0** - 状态管理
- **Vue Router 4.3.0** - 路由管理
- **ECharts 5.x** - 数据可视化
- **Axios 1.6.0** - HTTP客户端

## 核心依赖
- **异步数据库**: 自研异步连接池，支持高并发
- **微信支付SDK**: 集成微信支付V3接口
- **阿里云SDK**: 短信服务、对象存储
- **腾讯云COS**: 文件存储和CDN加速
- **AI集成**: Coze、FastGPT、豆包等多种AI模型
- **WebSocket**: 实时日志推送和消息通知

## 常用命令

### 后端开发
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python main.py

# 创建管理员用户
python create_admin.py
```

### 前端开发
```bash
# 进入前端目录
cd admin-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 数据库操作
```bash
# 连接MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE limob_invitation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p limob_invitation_system < database/schema.sql
```

### Docker部署
```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 开发工具配置
- **IDE**: 支持Python和Vue.js开发
- **代码格式化**: PEP8 for Python, ESLint for JavaScript
- **版本控制**: Git with semantic commit messages
- **API文档**: FastAPI自动生成 (访问 /850483315-docs)
- **日志系统**: 五层日志体系 (系统、错误、接口、数据库、安全)