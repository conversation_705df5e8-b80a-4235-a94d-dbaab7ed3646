# 项目组织结构

## 架构模式
项目采用**严格的三层架构**分离模式：
- **路由层** (`路由/`) - 处理HTTP请求和响应，不包含业务逻辑
- **服务层** (`服务/`) - 业务逻辑处理和数据封装
- **数据层** (`数据/`) - 数据库操作和数据访问

## 核心目录结构

### 后端核心模块
```
├── 路由/                    # API路由定义 (FastAPI)
├── 服务/                    # 业务逻辑层
├── 数据/                    # 数据访问层
├── 数据模型/                # Pydantic数据模型
├── 日志/                    # 五层日志系统
├── 工具/                    # 通用工具函数
├── 中间件/                  # 请求中间件
├── 依赖项/                  # 项目依赖和认证
├── 核心/                    # 应用核心功能
```

### 前端管理界面
```
admin-frontend/
├── src/
│   ├── components/          # 可复用Vue组件
│   ├── layouts/             # 布局组件
│   ├── router/              # 前端路由配置
│   ├── services/            # API服务封装
│   ├── store/               # Pinia状态管理
│   ├── views/               # 页面视图组件
│   └── utils/               # 工具函数
```

### AI与智能化模块
```
├── AI/                      # AI相关模块和提示词
├── 服务/LangChain_*         # LangChain智能体服务
├── 数据/LangChain_*         # LangChain数据层
```

## 命名规范

### 中文命名约定
- **函数名**: 使用中文描述功能 (`创建用户`, `获取订单列表`)
- **变量名**: 使用中文描述内容 (`用户信息`, `订单数据`)
- **数据库字段**: 使用中文字段名 (`用户名称`, `创建时间`)
- **文件名**: 功能模块使用中文 (`用户管理.py`, `订单处理.py`)

### 响应格式标准
```python
# 成功响应
{"status": 100, "message": 数据内容}

# 错误响应  
{"status": 错误码, "message": 错误信息}
```

## 模块职责分工

### 路由层职责
- 接收HTTP请求
- 参数验证和解析
- 调用服务层方法
- 格式化响应数据
- **禁止**: 直接数据库操作、业务逻辑处理

### 服务层职责
- 业务逻辑实现
- 数据验证和处理
- 调用数据层方法
- 错误处理和日志记录
- **禁止**: 直接HTTP请求处理

### 数据层职责
- 数据库连接管理
- SQL查询执行
- 数据持久化操作
- **禁止**: 业务逻辑处理

## 关键配置文件
- `main.py` - FastAPI应用入口
- `config.py` - 应用配置管理
- `.env` - 环境变量配置
- `requirements.txt` - Python依赖
- `admin-frontend/package.json` - 前端依赖

## 特殊目录说明
- `静态/` - 静态文件服务
- `模板/` - Jinja2 HTML模板 (已废弃，使用Vue SPA)
- `测试/` - 测试文件和脚本
- `文档/` - 项目文档
- `手动执行/` - 维护脚本
- `系统日志/` - 日志文件存储

## 开发约定
1. **严格遵循三层架构** - 不允许跨层直接调用
2. **统一错误处理** - 使用统一的异常处理机制
3. **日志记录规范** - 关键操作必须记录日志
4. **代码复用** - 通用功能抽取到工具层或服务层
5. **API文档** - 所有接口必须有完整的文档注释