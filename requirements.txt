# ===== 核心Web框架 =====
fastapi
uvicorn
pydantic==2.12.0a1
pydantic-core==2.37.2
pydantic-extra-types==2.10.2
pydantic-settings==2.7.1

# ===== 异步和网络 =====
aiofiles

# aiohttp
# alibabacloud-tea - 阿里云SDK
# dashscope - 阿里云通义千问
# langchain-community - LangChain社区版
aiohttp


httpx
websockets

# ===== 数据库 =====
asyncpg
SQLAlchemy

# ===== 认证和安全 =====
PyJWT
bcrypt
passlib
cryptography
Authlib
python-multipart

# ===== LangChain生态 =====
langchain
langchain-community
langchain-core
langchain-openai
langchain-text-splitters
langgraph
langsmith

# ===== AI和机器学习 =====
openai
dashscope
tiktoken
numpy
onnxruntime

# ===== 文档处理（图片OCR已优化） =====
unstructured[all-docs]==0.18.11
rapidocr-onnxruntime==1.2.3
pymupdf
Pillow
pillow-heif
pandas
openpyxl
xlrd

# ===== 搜索和工具 =====
duckduckgo_search

# ===== 阿里云服务 =====
alibabacloud-dysmsapi20170525
aliyun-python-sdk-core
aliyun-python-sdk-dysmsapi

# ===== 腾讯云服务 =====
cos_python_sdk_v5

# ===== 微信支付 =====
wechatpayv3

# ===== 工具库 =====
requests
PyYAML
python-dotenv
python-dateutil
click
rich
coloredlogs
psutil

# ===== 数据处理 =====
orjson
xmltodict
marshmallow

# ===== 其他依赖 =====
PyPika
email_validator