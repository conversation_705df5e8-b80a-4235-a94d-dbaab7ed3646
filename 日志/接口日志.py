# 已使用统一日志系统替代
from datetime import datetime
from typing import Any, Dict, Optional

from .日志配置 import 应用日志器 as 接口日志器, 错误日志器


# 删除循环导入
# from 日志 import 系统日志器, 错误日志器, 安全日志器, 接口日志器, 数据库日志器

# 直接导出函数，不使用装饰器
async def 异步数据库记录接口日志(用户id: Optional[int] = None, ip: str = "", 路径: str = "", 方法: str = "",
                                 状态码: int = 0, 耗时: float = 0.0, 错误详情: Optional[str] = None,
                                 堆栈跟踪: Optional[str] = None, 请求头: str = "", 请求参数: str = "", 请求体: str = "",
                                 数据库连接池实例=None):
    """将API接口调用记录到数据库"""
    if not 数据库连接池实例:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
        数据库连接池实例 = 异步连接池实例

    try:
        # 简单截断
        def 截断(文本, 长度=5000):
            if not 文本:
                return ""
            return 文本[:长度] if len(str(文本)) <= 长度 else str(文本)[:长度] + "..."

        # 确保连接池已初始化
        if not hasattr(数据库连接池实例, '_连接池') or not 数据库连接池实例._连接池:
            return

        # 暂时禁用接口日志插入，避免异步生成器问题影响主要功能
        # TODO: 修复异步生成器问题后重新启用
        return

        await 数据库连接池实例.执行插入(
            """INSERT INTO 接口日志表 (用户id, IP地址, 请求路径, 请求方法, 状态码, 耗时, 创建时间, 错误信息, 堆栈跟踪, 请求头, 请求参数, 请求体)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)""",
            (
                用户id, ip, 路径, 方法, 状态码, round(耗时, 3), datetime.now(),
                截断(错误详情), 截断(堆栈跟踪), 截断(请求头), 截断(请求参数), 截断(请求体)
            )
        )
    except Exception as e:
        # 失败不影响主流程
        try:
            错误日志器.error(f"插入接口日志失败: {e}")
        except Exception:
            pass


# 移除这个复杂的装饰器应用，暂时先直接导出函数
# 异步数据库记录接口日志 = 数据库函数装饰器(_异步数据库记录接口日志)

async def 记录接口调用(请求路径: str, 方法: str, 用户id: Optional[int] = None,
                       状态码: int = 200, 耗时: float = 0, 错误信息: Optional[str] = None,
                       请求参数: Dict[str, Any] = None, 响应数据: Dict[str, Any] = None):
    """记录接口调用信息到日志文件"""
    try:
        # 屏蔽敏感信息
        安全请求参数 = {}
        if 请求参数:
            for 键, 值 in 请求参数.items():
                安全请求参数[键] = "******" if any(敏感词 in 键.lower() for 敏感词 in ["password", "密码"]) else 值

        # 构建日志消息
        日志消息 = f"接口调用: {请求路径} [{方法}] 用户:{用户id or 'None'} 状态:{状态码} 耗时:{耗时:.3f}s"
        
        if 错误信息:
            错误日志器.error(f"{日志消息} 错误:{错误信息}")
        else:
            接口日志器.info(日志消息)
            if 耗时 > 1.0:  # 慢接口警告
                接口日志器.warning(f"慢接口: {请求路径} 耗时:{耗时:.3f}s")
    except Exception:
        pass  # 日志记录失败不影响主流程