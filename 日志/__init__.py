# 日志系统统一入口 - 优化版本
"""
统一的日志系统入口模块

提供两个核心日志器：
- 应用日志器：记录正常业务操作和信息
- 错误日志器：记录错误、异常和警告

以及完整的日志功能：
- 实时WebSocket日志服务
- 接口调用记录
- 安全事件记录
- 系统生命周期记录
"""

# =============================================================================
# 所有导入 - 统一在文件顶部
# =============================================================================
from .日志配置 import (
    # 核心日志器
    应用日志器, 错误日志器,
    # 全局配置
    配置全局异常捕获
)

# 实时日志服务导入
from .实时日志服务 import (
    处理日志WebSocket连接,     # WebSocket连接处理函数
    获取日志类型和级别,       # 日志类型和级别验证
    初始化实时日志系统,       # 实时日志系统初始化
    实时日志管理器           # 日志管理器实例（供资源清理使用）
)

# 专用日志记录功能导入
from .接口日志 import (
    异步数据库记录接口日志,   # 接口调用数据库记录
    记录接口调用            # 接口调用文件记录
)

from .系统日志 import (
    记录安全事件,           # 安全相关事件记录
    记录系统启动,           # 系统启动记录  
    记录系统关闭            # 系统关闭记录
)

from .数据库日志 import (
    记录数据库操作,         # 数据库操作记录
    记录连接池事件,         # 连接池事件记录
    数据库函数装饰器        # 数据库函数装饰器
)

# =============================================================================
# 兼容性别名 - 保持向后兼容，但实际都使用核心日志器
# =============================================================================
# 说明：为了保持向后兼容性，保留这些别名，但实际都指向核心日志器
系统日志器 = 应用日志器      # 系统操作 -> 应用日志器
数据库日志器 = 应用日志器    # 数据库操作 -> 应用日志器  
接口日志器 = 应用日志器      # 接口调用 -> 应用日志器
安全日志器 = 应用日志器      # 安全事件 -> 应用日志器
连接池日志器 = 应用日志器    # 连接池操作 -> 应用日志器
日志记录器 = 应用日志器      # 通用日志记录 -> 应用日志器



# =============================================================================
# 统一导出列表 - 明确对外接口
# =============================================================================
__all__ = [
    # 核心日志器
    '应用日志器', '错误日志器',
    
    # 兼容性别名（建议使用核心日志器）
    '系统日志器', '数据库日志器', '接口日志器', '安全日志器', '连接池日志器', '日志记录器',
    
    # 系统配置
    '配置全局异常捕获',
    
    # 实时日志服务
    '处理日志WebSocket连接', '获取日志类型和级别', '初始化实时日志系统', '实时日志管理器',
    
    # 专用记录功能
    '异步数据库记录接口日志', '记录接口调用',
    '记录安全事件', '记录系统启动', '记录系统关闭',
    '记录数据库操作', '记录连接池事件', '数据库函数装饰器'
]

# =============================================================================
# 自动初始化
# =============================================================================
# 配置全局异常捕获
配置全局异常捕获() 

# =============================================================================
# 使用指南（注释说明）
# =============================================================================
"""
推荐使用模式：

1. 普通业务日志：
   from 日志 import 应用日志器
   应用日志器.info("用户登录成功")

2. 错误日志：
   from 日志 import 错误日志器  
   错误日志器.error("数据库连接失败", exc_info=True)

3. 兼容性使用（不推荐新代码使用）：
   from 日志 import 系统日志器, 数据库日志器
   # 实际都是应用日志器的别名

4. 专用功能：
   from 日志 import 记录安全事件, 异步数据库记录接口日志
   记录安全事件("登录失败", 用户id=123, 成功=False)
""" 