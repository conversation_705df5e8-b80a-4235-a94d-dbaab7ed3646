import asyncio
import httpx
import json
import os
import sys
from typing import Any, Dict, Optional

# 设置项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 从项目中导入真实的异步连接池实例
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例  # noqa: E402

# 尝试从项目中导入真实的异步更新达人信息函数
try:
    from 数据.抖音达人数据 import 异步更新达人信息
except ImportError:
    print("错误: 未能从 数据.达人数据 导入 异步更新达人信息. 将使用占位函数.")
    async def 异步更新达人信息(达人id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
        print(f"信息: (占位符)模拟 异步更新达人信息 调用. 达人id: {达人id}, 更新数据: {更新数据}")
        # 模拟成功更新
        return {"状态": "成功", "消息": f"(占位符)模拟更新达人id {达人id} 成功", "达人id": 达人id}

# 定义状态码
抖音API错误_UID问题 = 10033
记录处理中状态 = 99998
记录处理成功状态 = 0
# 可以定义一个通用错误状态，如果处理失败但不是UID问题，例如 99997
记录处理通用错误状态 = None # 或者一个特定的错误码，如 99997. None表示恢复为可重试

async def 异步获取并标记单个待处理记录(处理中状态码: int) -> Optional[Dict[str, Any]]:
    '''
    获取一条可处理的达人记录，并将其状态标记为"处理中"。
    使用乐观锁尝试标记，重试几次。
    '''
    for attempt in range(3): # 最多尝试3次
        查询SQL = f'''
            SELECT id, uid_number 
            FROM kol.达人表 
            WHERE uid_number IS NOT NULL AND uid_number != '' 
              AND (sec_uid IS NULL OR sec_uid = '')
              AND (接口状态_UID IS NULL OR (接口状态_UID != {抖音API错误_UID问题} AND 接口状态_UID != {处理中状态码}))
            ORDER BY id ASC
            LIMIT 1
        '''
        try:
            # print(f"调试 (尝试 {attempt + 1}): 执行获取SQL: {查询SQL}")
            符合条件的达人列表 = await 异步连接池实例.执行查询(查询SQL)
            
            if not 符合条件的达人列表:
                # print(f"调试 (尝试 {attempt + 1}): 未找到符合条件的达人。")
                return None # 没有可处理的记录

            待处理达人 = 符合条件的达人列表[0]
            达人id_待标记 = 待处理达人['id']

            # 尝试标记为处理中
            标记SQL = f'''
                UPDATE kol.达人表
                SET 接口状态_UID = %s
                WHERE id = $1
                  AND (接口状态_UID IS NULL OR (接口状态_UID != {抖音API错误_UID问题} AND 接口状态_UID != {处理中状态码})) 
            '''
            # print(f"调试 (尝试 {attempt + 1}): 执行标记SQL: {标记SQL} 参数: ({处理中状态码}, {达人id_待标记})")
            影响行数 = await 异步连接池实例.执行更新(标记SQL, (处理中状态码, 达人id_待标记))

            if 影响行数 > 0:
                print(f"信息: 成功标记并获取到达人id {达人id_待标记} 进行处理。")
                return 待处理达人 # 成功获取并标记
            else:
                # print(f"调试 (尝试 {attempt + 1}): 标记达人id {达人id_待标记} 失败，可能已被其他并发任务获取。")
                if attempt < 2: # 如果不是最后一次尝试，则等待一小段时间
                    await asyncio.sleep(0.1 + attempt * 0.1) # 增加等待时间
                else:
                    print(f"信息: 多次尝试标记达人id {达人id_待标记} 失败。")
                    return None
        except Exception as e:
            print(f"错误: 在获取并标记记录过程中发生异常 (尝试 {attempt + 1}): {e}")
            return None # 发生异常，返回None
            
    return None # 所有尝试失败

async def 异步从抖音获取达人信息(uid_number: str) -> Optional[Dict[str, Any]]:
    '''
    根据uid_number异步从抖音API获取达人信息.
    如果成功. 返回API响应的JSON数据. 否则返回None.
    '''
    api_url = f"https://live.douyin.com/webcast/user/?aid=6383&live_id=1&device_platform=web&language=zh-CN&target_uid={uid_number}"
    print(f"信息: 开始请求抖音API: {api_url}")
    async with httpx.AsyncClient(timeout=10.0) as 客户端:
        try:
            响应 = await 客户端.get(api_url)
            响应.raise_for_status() 
            数据 = 响应.json()
            print(f"信息: 成功从抖音API获取到达人 {uid_number} 的信息.")
            return 数据
        except httpx.HTTPStatusError as exc:
            # 特别处理HTTP错误，如果响应体是JSON，也尝试解析并返回
            print(f"错误: 请求抖音API返回HTTP错误 (HTTP {exc.response.status_code}): {exc.request.url}")
            try:
                错误数据 = exc.response.json()
                print(f"错误: API响应内容 (JSON): {错误数据}")
                return 错误数据 # 返回包含status_code的错误JSON
            except json.JSONDecodeError:
                print(f"错误: API响应内容 (非JSON): {exc.response.text}")
                return {"status_code": exc.response.status_code, "error_message": "非JSON响应", "raw_content": exc.response.text}
        except httpx.RequestError as exc:
            print(f"错误: 请求抖音API时发生网络错误: {exc.request.url} - {exc}")
            return None
        except json.JSONDecodeError: # 重命名避免与外部json冲突
            # 这种情况是成功获取响应但无法解析为JSON
            print(f"错误: 解析抖音API响应JSON失败. URL: {api_url}, 响应状态码: {响应.status_code if '响应' in locals() else '未知'}, 响应内容: {响应.text if '响应' in locals() else '未知'}")
            return None


async def 异步执行单次更新达人抖音信息() -> Optional[bool]: # 返回值可以是 True, False, 或 None
    '''
    处理单个达人记录。
    - 返回 True: 成功获取、处理并成功更新数据库最终状态。
    - 返回 False: 获取记录后，在处理或更新数据库最终状态时失败。
    - 返回 None: 未能获取到可供处理的记录。
    '''
    print("信息: Worker开始执行单次更新任务...")
    待更新达人 = await 异步获取并标记单个待处理记录(记录处理中状态)

    if not 待更新达人:
        # print("信息: Worker未能获取到待处理记录.") # 获取函数会打印信息，这里不再重复
        return None # 未获取到记录，不是错误，而是此worker无事可做

    达人id = 待更新达人.get("id")
    uid_number = 待更新达人.get("uid_number")
    最终接口状态 = 记录处理通用错误状态 
    操作是否成功 = False
    更新详情 = {} # 确保更新详情被初始化

    try:
        print(f"信息: Worker正在处理已标记的达人id: {达人id}, UID: {uid_number}")
        抖音数据 = await 异步从抖音获取达人信息(uid_number)

        if not 抖音数据:
            print(f"错误: Worker未能从抖音获取UID {uid_number} 的信息。最终状态将设为通用错误。")
            最终接口状态 = 记录处理通用错误状态
            更新详情 = {"接口状态_UID": 最终接口状态} # 必须有这个才能更新状态
        else:
            api_status_code = 抖音数据.get("status_code")
            if api_status_code == 抖音API错误_UID问题:
                print(f"信息: Worker发现UID {uid_number} API返回10033。")
                更新详情 = {"接口状态_UID": 抖音API错误_UID问题}
                最终接口状态 = 抖音API错误_UID问题 
            elif api_status_code == 0 and "data" in 抖音数据:
                数据体 = 抖音数据["data"]
                if "sec_uid" in 数据体:
                    更新详情["sec_uid"] = 数据体["sec_uid"]
                if "display_id" in 数据体:
                    更新详情["account_douyin"] = 数据体["display_id"]
                if "昵称" in 数据体:
                    更新详情["昵称"] = 数据体["昵称"]
                if "signature" in 数据体:
                    更新详情["introduction"] = 数据体["signature"]
                if "avatar_thumb" in 数据体 and isinstance(数据体["avatar_thumb"], dict) and \
                   "url_list" in 数据体["avatar_thumb"] and isinstance(数据体["avatar_thumb"]["url_list"], list) and \
                   len(数据体["avatar_thumb"]["url_list"]) > 0:
                    更新详情["avatar"] = 数据体["avatar_thumb"]["url_list"][0]
                if "follow_info" in 数据体 and isinstance(数据体["follow_info"], dict):
                    fi = 数据体["follow_info"]
                    if "following_count" in fi:
                        更新详情["关注数"] = fi["following_count"]
                    if "follower_count" in fi:
                        更新详情["粉丝数"] = fi["follower_count"]
                if "authentication_info" in 数据体 and isinstance(数据体["authentication_info"], dict):
                    ai = 数据体["authentication_info"]
                    if "enterprise_verify_reason" in ai and ai["enterprise_verify_reason"]:
                        更新详情["企业认证"] = ai["enterprise_verify_reason"]
                
                更新详情["接口状态_UID"] = 记录处理成功状态
                最终接口状态 = 记录处理成功状态 
            else:
                print(f"错误: Worker UID {uid_number} API返回失败或格式不正确 (status_code: {api_status_code})。最终状态将设为通用错误。")
                最终接口状态 = 记录处理通用错误状态
                更新详情 = {"接口状态_UID": 最终接口状态} # 必须有这个才能更新状态
        
        # 确保至少有接口状态_UID在更新详情中，以释放记录的"处理中"状态
        if not 更新详情 or "接口状态_UID" not in 更新详情:
             更新详情["接口状态_UID"] = 最终接口状态 # 如果上面逻辑没覆盖到，确保状态被设置

        print(f"信息: Worker准备最终更新数据库ID {达人id}。 更新内容: {更新详情}")
        更新结果 = await 异步更新达人信息(达人id=达人id, 更新数据=更新详情)
        if 更新结果 and 更新结果.get("状态") == "成功":
            print(f"信息: Worker成功最终更新达人id {达人id}: {更新结果.get('消息')}")
            操作是否成功 = True 
        else:
            print(f"错误: Worker最终更新达人id {达人id} 失败: {更新结果.get('消息') if 更新结果 else '无更新结果返回'}。接口状态可能未正确从处理中更新。")
            # 尝试再次确保状态被重置，避免卡死
            await 异步更新达人信息(达人id=达人id, 更新数据={"接口状态_UID": 记录处理通用错误状态})
            操作是否成功 = False
        
        print(f"信息: Worker完成处理达人id {达人id}。操作成功: {操作是否成功}")
        return 操作是否成功

    except Exception as e:
        print(f"错误: Worker处理达人id {达人id} (UID {uid_number}) 时发生未捕获异常: {e}。尝试重置接口状态。")
        try:
            await 异步更新达人信息(达人id=达人id, 更新数据={"接口状态_UID": 记录处理通用错误状态}) 
        except Exception as er:
            print(f"错误: Worker重置达人id {达人id} 状态时再次发生错误: {er}")
        return False # 指示此工作单元因异常失败

async def 异步并发处理达人信息(总目标处理记录数: int, 并发数: int, 任务启动间隔毫秒数: int = 10):
    '''
    并发处理达人信息，直到达到目标处理记录数或无更多可处理记录。
    任何工作单元的失败（逻辑失败或异常）将导致停止派发新任务。
    '''
    if 并发数 <= 0:
        并发数 = 1  # 确保至少有一个并发单元
    semaphore = asyncio.Semaphore(并发数)
    stop_event = asyncio.Event() # 用于通知所有任务停止
    tasks = []
    实际执行的任务数 = 0
    数据库更新成功数 = 0
    无可用记录的worker数 = 0 # 新增计数器

    print(f"信息: 并发处理启动。目标处理记录数: {总目标处理记录数}, 最大并发数: {并发数}")

    async def 单个工作单元(任务编号: int):
        nonlocal 数据库更新成功数, 无可用记录的worker数 
        if stop_event.is_set():
            print(f"信息: 工作单元 {任务编号} 未启动，已收到停止信号。")
            return

        async with semaphore:
            if stop_event.is_set():
                return

            print(f"信息: 工作单元 {任务编号} 开始执行...")
            try:
                处理结果 = await 异步执行单次更新达人抖音信息() # 返回 True, False, 或 None
                
                if 处理结果 is True:
                    数据库更新成功数 += 1
                elif 处理结果 is False: # 明确的处理失败
                    print(f"信息: 工作单元 {任务编号} 报告处理失败，设置停止事件。")
                    stop_event.set()
                elif 处理结果 is None: # 未获取到记录
                    无可用记录的worker数 +=1
                    print(f"信息: 工作单元 {任务编号} 未获取到记录。当前无记录worker数: {无可用记录的worker数}")
                    # 如果连续多个worker都获取不到记录，也可能意味着没数据了，可以考虑停止
                    # 例如: if 无可用记录的worker数 >= 并发数 * 2: stop_event.set()
            except Exception as e:
                print(f"错误: 工作单元 {任务编号} 发生未捕获异常: {e}，设置停止事件。")
                stop_event.set() 
            finally:
                print(f"信息: 工作单元 {任务编号} 执行完毕。")
    
    for i in range(总目标处理记录数):
        if stop_event.is_set():
            print(f"信息: 检测到停止信号，不再派发新的工作单元 (已派发 {实际执行的任务数} 个)。")
            break
        
        实际执行的任务数 += 1
        task = asyncio.create_task(单个工作单元(i + 1))
        tasks.append(task)
        
        if 任务启动间隔毫秒数 > 0 and i < 总目标处理记录数 - 1: 
            await asyncio.sleep(任务启动间隔毫秒数 / 1000.0)

    if tasks:
        print(f"信息: 等待已派发的 {len(tasks)} 个工作单元完成...")
        await asyncio.gather(*tasks, return_exceptions=True) 
    
    print(f"信息: 并发处理结束。总共尝试派发任务: {实际执行的任务数}，数据库更新成功数: {数据库更新成功数}，无可用记录的worker数: {无可用记录的worker数}。")
    if stop_event.is_set() and 实际执行的任务数 < 总目标处理记录数 :
        print("信息: 处理因错误或逻辑失败提前中止。")

async def 主任务():
    print("信息: 脚本启动...")
    希望处理的总记录数 = 100000
    希望的并发任务数 = 1 # 例如，同时运行10个更新任务
    希望的任务启动间隔毫秒数 = 20 # 快速派发任务，由信号量控制并发执行
    
    await 异步并发处理达人信息(
        总目标处理记录数=希望处理的总记录数, 
        并发数=希望的并发任务数, 
        任务启动间隔毫秒数=希望的任务启动间隔毫秒数
    )
    
    if hasattr(异步连接池实例, '关闭') and callable(getattr(异步连接池实例, '关闭')):
        print("信息: 正在关闭数据库连接池...")
        await 异步连接池实例.关闭数据库连接池()
        print("信息: 数据库连接池已关闭.")
    print("信息: 脚本执行完毕.")

if __name__ == "__main__":
    asyncio.run(主任务()) 