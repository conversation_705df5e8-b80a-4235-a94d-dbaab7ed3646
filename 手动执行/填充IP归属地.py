"""
临时脚本：填充用户登录记录表中IP归属地为null的记录
一次性执行脚本，用于补充历史数据的IP归属地信息
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 工具.IP归属地查询 import 查询IP归属地
from 日志 import 系统日志器, 错误日志器


async def 填充IP归属地():
    """填充用户登录记录表中IP归属地为null的记录"""
    try:
        # 初始化数据库连接池
        await 异步连接池实例.初始化数据库连接池()
        系统日志器.info("数据库连接池初始化成功")
        
        # 查询IP归属地为null的记录
        查询SQL = """
        SELECT id, ip地址
        FROM 用户登陆记录表
        WHERE ip归属地 IS NULL OR ip归属地 = ''
        ORDER BY id
        """
        
        记录列表 = await 异步连接池实例.执行查询(查询SQL)
        总记录数 = len(记录列表)
        
        if 总记录数 == 0:
            系统日志器.info("没有需要填充IP归属地的记录")
            return
        
        系统日志器.info(f"找到 {总记录数} 条需要填充IP归属地的记录")
        
        成功计数 = 0
        失败计数 = 0
        
        # 批量处理记录
        for 索引, 记录 in enumerate(记录列表, 1):
            记录ID = 记录['id']
            IP地址 = 记录['ip地址']
            
            try:
                # 查询IP归属地
                IP归属地 = 查询IP归属地(IP地址)
                
                # 更新数据库记录
                更新SQL = """
                UPDATE 用户登陆记录表
                SET ip归属地 = $1
                WHERE id = $2
                """
                
                await 异步连接池实例.执行更新(更新SQL, (IP归属地, 记录ID))
                
                成功计数 += 1
                系统日志器.info(f"[{索引}/{总记录数}] 记录ID {记录ID}: {IP地址} -> {IP归属地}")
                
                # 每处理10条记录暂停一下，避免API请求过于频繁
                if 索引 % 10 == 0:
                    系统日志器.info(f"已处理 {索引}/{总记录数} 条记录，暂停1秒...")
                    await asyncio.sleep(1)
                
            except Exception as e:
                失败计数 += 1
                错误日志器.error(f"[{索引}/{总记录数}] 处理记录ID {记录ID} 失败: {e}")
                continue
        
        # 输出统计结果
        系统日志器.info("=" * 50)
        系统日志器.info("IP归属地填充完成！")
        系统日志器.info(f"总记录数: {总记录数}")
        系统日志器.info(f"成功处理: {成功计数}")
        系统日志器.info(f"失败记录: {失败计数}")
        系统日志器.info("=" * 50)
        
    except Exception as e:
        错误日志器.error(f"填充IP归属地过程中发生错误: {e}", exc_info=True)
    
    finally:
        # 关闭数据库连接池
        try:
            await 异步连接池实例.关闭数据库连接池()
            系统日志器.info("数据库连接池已关闭")
        except Exception as e:
            错误日志器.error(f"关闭数据库连接池失败: {e}")


async def 检查填充结果():
    """检查填充结果的统计信息"""
    try:
        await 异步连接池实例.初始化数据库连接池()
        
        # 统计各种IP归属地的数量
        统计SQL = """
        SELECT
            ip归属地,
            COUNT(*) as 数量
        FROM 用户登陆记录表
        GROUP BY ip归属地
        ORDER BY 数量 DESC
        """
        
        统计结果 = await 异步连接池实例.执行查询(统计SQL)
        
        系统日志器.info("IP归属地统计结果:")
        系统日志器.info("-" * 30)
        for 记录 in 统计结果:
            归属地 = 记录['ip归属地'] or 'NULL'
            数量 = 记录['数量']
            系统日志器.info(f"{归属地}: {数量} 条")
        
        # 检查是否还有null记录
        null记录SQL = """
        SELECT COUNT(*) as null_count
        FROM 用户登陆记录表
        WHERE ip归属地 IS NULL OR ip归属地 = ''
        """
        
        null结果 = await 异步连接池实例.执行查询(null记录SQL)
        null数量 = null结果[0]['null_count']
        
        if null数量 > 0:
            系统日志器.warning(f"仍有 {null数量} 条记录的IP归属地为空")
        else:
            系统日志器.info("✅ 所有记录的IP归属地都已填充完成")
            
    except Exception as e:
        错误日志器.error(f"检查填充结果失败: {e}")
    finally:
        await 异步连接池实例.关闭数据库连接池()


async def main():
    """主函数"""
    print("🚀 开始填充用户登录记录表中的IP归属地...")
    print("=" * 60)
    
    # 执行填充
    await 填充IP归属地()
    
    print("\n" + "=" * 60)
    print("📊 检查填充结果...")
    
    # 检查结果
    await 检查填充结果()
    
    print("\n✅ 脚本执行完成！")


if __name__ == "__main__":
    # 运行脚本
    asyncio.run(main())
