# CRM通知系统功能说明

## 功能概述

CRM通知系统为用户提供完整的消息通知功能，包括系统更新通知和业务相关通知。

## 主要特性

### 1. 通知类型

- **系统更新通知**：来自管理员发布的系统通告，所有用户默认可见
- **业务通知**：与用户业务相关的提醒，如邀约状态变更、样品申请结果等

### 2. 通知状态管理

- **未读状态**：新通知默认为未读状态
- **已读状态**：用户查看或手动标记后变为已读
- **智能关联**：通告表中的数据自动作为系统通知，无需预先创建关联

### 3. 前端功能

#### 通知图标
- 位置：导航栏右侧，用户信息旁边
- 功能：显示未读通知数量，点击查看最新通知
- 轮询：每30秒自动更新未读数量

#### 通知中心页面
- 路径：`/notifications`
- 功能：
  - 通知列表展示（分页）
  - 筛选功能（按类型、已读状态）
  - 标记已读（单个、批量、全部）
  - 通知详情查看
  - 响应式设计

### 4. 后端API接口

#### 用户端接口
- `POST /user/notifications/list` - 获取通知列表
- `POST /user/notifications/unread-count` - 获取未读数量
- `POST /user/notifications/mark-read` - 标记单个已读
- `POST /user/notifications/batch-mark-read` - 批量标记已读
- `POST /user/notifications/mark-all-read` - 标记全部已读
- `POST /user/notifications/detail` - 获取通知详情
- `POST /user/notifications/center-data` - 获取通知中心数据

#### 管理员接口
- `POST /admin/notifications/create-system` - 基于通告创建系统通知

### 5. 数据库设计

#### 用户通知表
```sql
CREATE TABLE 用户通知表 (
  id int(11) NOT NULL AUTO_INCREMENT,
  用户id int(11) NOT NULL,
  通知类型 varchar(50) NOT NULL,
  标题 varchar(200) NOT NULL,
  内容 longtext,
  重要性 int(11) DEFAULT '1',
  是否已读 tinyint(1) DEFAULT '0',
  阅读时间 datetime DEFAULT NULL,
  来源通告ID int(11) DEFAULT NULL,
  业务关联id varchar(100) DEFAULT NULL,
  业务类型 varchar(50) DEFAULT NULL,
  创建时间 datetime DEFAULT CURRENT_TIMESTAMP,
  更新时间 datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);
```

#### 通知模板表
```sql
CREATE TABLE 通知模板表 (
  id int(11) NOT NULL AUTO_INCREMENT,
  模板名称 varchar(100) NOT NULL,
  模板类型 varchar(50) NOT NULL,
  业务类型 varchar(50) NOT NULL,
  标题模板 varchar(200) NOT NULL,
  内容模板 longtext NOT NULL,
  重要性 int(11) DEFAULT '1',
  是否启用 tinyint(1) DEFAULT '1',
  创建时间 datetime DEFAULT CURRENT_TIMESTAMP,
  更新时间 datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);
```

## 使用说明

### 管理员使用

1. **发布系统通告**
   - 在管理后台创建通告
   - 设置为"已发布"状态
   - 通告自动作为系统通知对所有用户可见

2. **创建系统通知**
   - 可选择基于已发布的通告创建系统通知
   - 系统会为所有活跃用户创建通知记录

### 用户使用

1. **查看通知**
   - 点击导航栏通知图标查看最新通知
   - 访问通知中心查看完整通知列表

2. **管理通知**
   - 点击通知可标记为已读
   - 支持批量操作和全部标记已读
   - 可按类型和状态筛选通知

3. **通知交互**
   - 点击业务通知可跳转到相关页面
   - 系统通知提供详细信息查看

## 技术特点

### 1. 智能数据整合
- 通告表数据自动作为系统通知
- 无需预先创建关联，减少数据冗余
- 支持动态已读状态管理

### 2. 高性能设计
- 使用UNION查询整合多数据源
- 支持分页和筛选
- 前端轮询优化，避免频繁请求

### 3. 用户体验优化
- 响应式设计，支持移动端
- 友好的时间显示
- 直观的未读状态指示

### 4. 扩展性强
- 支持多种通知类型
- 模板化通知内容
- 灵活的业务关联机制

## 注意事项

1. **数据一致性**
   - 通告删除不影响已创建的通知记录
   - 用户删除不会影响通告数据

2. **性能考虑**
   - 建议定期清理过期通知记录
   - 大量用户时考虑异步创建通知

3. **权限控制**
   - 用户只能查看和操作自己的通知
   - 管理员可以管理系统通知

## 后续优化建议

1. **推送功能**
   - 集成WebSocket实现实时推送
   - 支持邮件/短信通知

2. **高级筛选**
   - 按时间范围筛选
   - 按重要性级别筛选

3. **统计分析**
   - 通知阅读率统计
   - 用户行为分析

4. **批量操作**
   - 管理员批量发送通知
   - 定时通知功能
