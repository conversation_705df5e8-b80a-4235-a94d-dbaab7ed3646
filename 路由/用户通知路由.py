"""
用户通知路由
处理用户端通知相关的API接口
"""

from fastapi import APIRouter, Body, Depends

# 导入认证依赖
from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 数据模型.用户通知模型 import (
    批量标记已读请求,
    标记已读请求,
    格式化通知时间,
    生成通知摘要,
    获取通知列表请求,
    获取通知详情请求,
)

# 导入日志系统
from 日志 import 应用日志器 as 接口日志器
from 日志 import 错误日志器
from 服务.异步用户通知服务 import 异步批量标记通知已读 as 服务_异步批量标记通知已读
from 服务.异步用户通知服务 import 异步标记所有通知已读 as 服务_异步标记所有通知已读
from 服务.异步用户通知服务 import 异步标记通知已读 as 服务_异步标记通知已读
from 服务.异步用户通知服务 import (
    异步获取用户未读通知数量 as 服务_异步获取用户未读通知数量,
)

# 导入服务层
from 服务.异步用户通知服务 import 异步获取用户通知列表 as 服务_异步获取用户通知列表
from 服务.异步用户通知服务 import 异步获取通知详情 as 服务_异步获取通知详情

# 导入状态码
from 状态 import 状态

# 创建路由器
用户通知路由 = APIRouter(tags=["用户通知"])


# =============== 获取通知列表 ===============


@用户通知路由.post("/notifications/list", response_model=统一响应模型)
async def 获取通知列表接口(
    请求参数: 获取通知列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取用户通知列表

    支持分页、筛选和排序
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(
            f"用户 {用户id} 请求通知列表: 页码={请求参数.页码}, 类型={请求参数.通知类型}"
        )

        # 调用服务层获取通知列表
        结果 = await 服务_异步获取用户通知列表(
            用户id=用户id,
            页码=请求参数.页码,
            每页数量=请求参数.每页数量,
            通知类型=请求参数.通知类型,
            是否已读=请求参数.是否已读,
            排序字段=请求参数.排序字段,
            排序顺序=请求参数.排序顺序,
        )

        # 处理通知列表，添加前端需要的字段
        处理后的列表 = []
        for 通知 in 结果["列表"]:
            # 生成摘要和时间显示
            摘要 = 生成通知摘要(通知.get("内容", []))
            时间显示 = 格式化通知时间(通知["创建时间"])

            处理后的通知 = {**通知, "摘要": 摘要, "时间显示": 时间显示}
            处理后的列表.append(处理后的通知)

        响应数据 = {
            "列表": 处理后的列表,
            "总数": 结果["总数"],
            "总页数": 结果["总页数"],
            "当前页": 结果["当前页"],
        }

        return 统一响应模型.成功(数据=响应数据, 消息="获取通知列表成功")

    except Exception as e:
        错误日志器.error(f"获取通知列表失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="获取通知列表失败")


# =============== 获取未读通知数量 ===============


@用户通知路由.post("/notifications/unread-count", response_model=统一响应模型)
async def 获取未读通知数量接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取用户未读通知数量
    """
    try:
        用户id = 当前用户["id"]

        # 调用服务层获取未读数量
        未读数量 = await 服务_异步获取用户未读通知数量(用户id)

        响应数据 = {"未读数量": 未读数量}

        return 统一响应模型.成功(数据=响应数据, 消息="获取未读数量成功")

    except Exception as e:
        错误日志器.error(f"获取未读通知数量失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="获取未读数量失败")


# =============== 标记单个通知已读 ===============


@用户通知路由.post("/notifications/mark-read", response_model=统一响应模型)
async def 标记通知已读接口(
    请求参数: 标记已读请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    标记单个通知为已读
    """
    try:
        用户id = 当前用户["id"]
        通知id = 请求参数.通知id

        接口日志器.info(f"用户 {用户id} 标记通知 {通知id} 为已读")

        # 调用服务层标记已读
        结果 = await 服务_异步标记通知已读(通知id, 用户id)

        if 结果["成功"]:
            return 统一响应模型.成功(消息=结果["消息"])
        else:
            return 统一响应模型.失败(状态码=状态.通用.参数错误, 消息=结果["消息"])

    except Exception as e:
        错误日志器.error(f"标记通知已读失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="标记已读失败")


# =============== 批量标记通知已读 ===============


@用户通知路由.post("/notifications/batch-mark-read", response_model=统一响应模型)
async def 批量标记通知已读接口(
    请求参数: 批量标记已读请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    批量标记通知为已读
    """
    try:
        用户id = 当前用户["id"]
        通知id列表 = 请求参数.通知id列表

        接口日志器.info(f"用户 {用户id} 批量标记 {len(通知id列表)} 个通知为已读")

        # 调用服务层批量标记已读
        结果 = await 服务_异步批量标记通知已读(通知id列表, 用户id)

        if 结果["成功"]:
            响应数据 = {"影响行数": 结果.get("影响行数", 0)}
            return 统一响应模型.成功(数据=响应数据, 消息=结果["消息"])
        else:
            return 统一响应模型.失败(状态码=状态.通用.参数错误, 消息=结果["消息"])

    except Exception as e:
        错误日志器.error(f"批量标记通知已读失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="批量标记已读失败")


# =============== 标记所有通知已读 ===============


@用户通知路由.post("/notifications/mark-all-read", response_model=统一响应模型)
async def 标记所有通知已读接口(当前用户: dict = Depends(获取当前用户)):
    """
    标记用户所有通知为已读
    """
    try:
        用户id = 当前用户["id"]

        接口日志器.info(f"用户 {用户id} 标记所有通知为已读")

        # 调用服务层标记所有已读
        结果 = await 服务_异步标记所有通知已读(用户id)

        if 结果["成功"]:
            响应数据 = {"影响行数": 结果.get("影响行数", 0)}
            return 统一响应模型.成功(数据=响应数据, 消息=结果["消息"])
        else:
            return 统一响应模型.失败(状态码=状态.通用.参数错误, 消息=结果["消息"])

    except Exception as e:
        错误日志器.error(f"标记所有通知已读失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="标记所有已读失败")


# =============== 获取通知详情 ===============


@用户通知路由.post("/notifications/detail", response_model=统一响应模型)
async def 获取通知详情接口(
    请求参数: 获取通知详情请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取通知详情
    """
    try:
        用户id = 当前用户["id"]
        通知id = 请求参数.通知id

        接口日志器.info(f"用户 {用户id} 获取通知 {通知id} 详情")

        # 调用服务层获取通知详情
        通知详情 = await 服务_异步获取通知详情(通知id, 用户id)

        if 通知详情:
            # 添加时间显示
            通知详情["时间显示"] = 格式化通知时间(通知详情["创建时间"])

            return 统一响应模型.成功(数据=通知详情, 消息="获取通知详情成功")
        else:
            return 统一响应模型.失败(
                状态码=状态.通用.无数据, 消息="通知不存在或无权限访问"
            )

    except Exception as e:
        错误日志器.error(f"获取通知详情失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.通用.服务器错误, 消息="获取通知详情失败")


# =============== 获取通知中心数据 ===============


@用户通知路由.post("/notifications/center-data", response_model=统一响应模型)
async def 获取通知中心数据接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取通知中心页面所需的完整数据
    包括未读数量、最新通知列表等
    """
    try:
        用户id = 当前用户["id"]

        接口日志器.info(f"用户 {用户id} 获取通知中心数据")

        # 并发获取未读数量和通知列表
        import asyncio

        未读数量任务 = 服务_异步获取用户未读通知数量(用户id)
        通知列表任务 = 服务_异步获取用户通知列表(
            用户id=用户id, 页码=1, 每页数量=20, 排序字段="创建时间", 排序顺序="DESC"
        )

        未读数量, 通知列表结果 = await asyncio.gather(未读数量任务, 通知列表任务)

        # 处理通知列表
        处理后的列表 = []
        for 通知 in 通知列表结果["列表"]:
            摘要 = 生成通知摘要(通知.get("内容", []))
            时间显示 = 格式化通知时间(通知["创建时间"])

            通知卡片 = {
                "id": 通知["id"],
                "标题": 通知["标题"],
                "摘要": 摘要,
                "通知类型": 通知["通知类型"],
                "重要性": 通知["重要性"],
                "是否已读": 通知["是否已读"],
                "创建时间": 通知["创建时间"],
                "时间显示": 时间显示,
            }
            处理后的列表.append(通知卡片)

        响应数据 = {
            "未读数量": 未读数量,
            "通知列表": 处理后的列表,
            "分页信息": {
                "总数": 通知列表结果["总数"],
                "总页数": 通知列表结果["总页数"],
                "当前页": 通知列表结果["当前页"],
            },
            "筛选选项": {
                "通知类型": [
                    {"值": "system_update", "标签": "系统更新"},
                    {"值": "business", "标签": "业务通知"},
                ],
                "已读状态": [
                    {"值": True, "标签": "已读"},
                    {"值": False, "标签": "未读"},
                ],
            },
        }

        return 统一响应模型.成功(数据=响应数据, 消息="获取通知中心数据成功")

    except Exception as e:
        错误日志器.error(f"获取通知中心数据失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="获取通知中心数据失败"
        )
