"""
工作台路由
基于真实业务数据的工作台API接口
"""
import traceback
from fastapi import APIRouter, Depends

from 依赖项.认证 import 获取当前用户
from 数据模型.工作台模型 import (
    工作台数据请求模型,
    个人数据请求模型,
    团队数据请求模型,
    趋势数据请求模型,
    微信运营指标请求模型,
    多指标趋势数据请求模型
)
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器, 错误日志器
from 服务.异步工作台服务 import 异步工作台服务

# 创建服务实例
工作台服务实例 = 异步工作台服务()

工作台路由 = APIRouter(tags=["工作台"])


def 获取用户id(请求对象, 当前用户: dict) -> int:
    """统一获取用户id的逻辑"""
    return 请求对象.用户id if hasattr(请求对象, '用户id') and 请求对象.用户id else 当前用户["id"]


# 使用现有的异常处理和日志系统
def 处理接口异常(异常, 接口名称, 用户id=None):
    """简化的异常处理函数"""
    错误信息 = f"{接口名称}失败: {str(异常)}"
    错误日志器.error(f"[{接口名称}] 用户id: {用户id} - {错误信息}")
    return 统一响应模型.失败(500, 错误信息)


@工作台路由.post("/wechat-operations", summary="获取个人微信运营数据", description="获取用户的微信账号、好友数量等运营数据")
async def 获取个人微信运营数据(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取个人微信运营数据

    参数:
    - **用户id**: 用户id (可选，不传则使用当前登录用户)
    - **时间范围**: 时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度、自定义）
    - **开始日期**: 开始日期 (时间范围为custom时使用)
    - **结束日期**: 结束日期 (时间范围为custom时使用)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 微信运营数据，包含:
        - 微信账号数: 微信账号数量
        - 好友总数: 好友总数
        - 今日新增好友: 今日新增好友数
        - 昨日新增好友: 昨日新增好友数
        - 本周新增好友: 本周新增好友数
        - 本月新增好友: 本月新增好友数
        - 当前新增好友: 当前选择时间范围的新增好友数
        - 新增好友标题: 新增好友指标的标题
        - 平均好友数: 平均每账号好友数
        - 指标卡片: 格式化的指标卡片数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取个人微信运营数据"
    
    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        应用日志器.debug(f"[{接口名称}] 请求参数: 时间范围={请求.时间范围}")
        
        微信运营数据 = await 工作台服务实例.获取个人微信运营数据(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )
        
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取微信运营数据")
        return 统一响应模型.成功(微信运营数据, "获取微信运营数据成功")
        
    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/dashboard/wechat-metrics", summary="获取微信运营核心指标", description="获取微信运营7个核心业务指标数据")
async def 获取微信运营核心指标(请求: 微信运营指标请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取微信运营核心指标数据

    7个关键指标：
    1. 微信账号数量 - 用户绑定的有效微信账号总数
    2. 好友总数 - 当前用户微信账号的好友总数量
    3. 今日新增 - 今日新增的好友数量
    4. 发送好友请求数 - 主动发送的好友请求总数
    5. 入库好友数 - 已成功添加并入库有好友入库时间的好友数量
    6. 沟通好友数 - 我方最后一条消息发送时间有数据，但是对方最后一条消息发送时间无数据的好友数量
    7. 互动好友数 - 我方最后一条消息发送时间与对方最后一条消息发送时间都有的好友数量

    参数:
    - **时间范围**: 时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）
    - **开始日期**: 自定义开始日期 (时间范围为custom时使用)
    - **结束日期**: 自定义结束日期 (时间范围为custom时使用)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 微信运营核心指标数据，包含7个关键指标和格式化的指标卡片
    """
    用户id = 用户["id"]
    接口名称 = "获取微信运营核心指标"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        应用日志器.debug(f"[{接口名称}] 请求参数: 时间范围={请求.时间范围}")

        # 参数验证
        有效时间范围 = ["昨日", "今日", "本周", "上周", "本月", "上月", "本季度", "上季度", "自定义"]
        if 请求.时间范围 not in 有效时间范围:
            return 统一响应模型.失败(400, f"无效的时间范围参数，支持的值: {', '.join(有效时间范围)}")

        # 自定义时间范围验证
        if 请求.时间范围 == "自定义" and (not 请求.开始日期 or not 请求.结束日期):
            return 统一响应模型.失败(400, "自定义时间范围时必须提供开始日期和结束日期")

        # 调用服务层获取核心指标数据
        核心指标数据 = await 工作台服务实例.获取微信运营核心指标(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取微信运营核心指标")
        return 统一响应模型.成功(核心指标数据, "获取微信运营核心指标成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/invitation-business", summary="获取邀约业务数据", description="获取用户的邀约统计、成功率等业务数据")
async def 获取邀约业务数据(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取邀约业务数据
    
    参数:
    - **用户id**: 用户id (可选)
    - **时间范围**: 时间范围
    
    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 邀约业务数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取邀约业务数据"
    
    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        
        邀约业务数据 = await 工作台服务实例.获取邀约业务数据(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )
        
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取邀约业务数据")
        return 统一响应模型.成功(邀约业务数据, "获取邀约业务数据成功")
        
    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/talent-management", summary="获取达人管理数据", description="获取用户的达人认领、活跃度等管理数据")
async def 获取达人管理数据(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取达人管理数据

    参数:
    - **用户id**: 用户id (可选)
    - **时间范围**: 时间范围

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 达人管理数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取达人管理数据"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        达人管理数据 = await 工作台服务实例.获取达人管理数据(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取达人管理数据")
        return 统一响应模型.成功(达人管理数据, "获取达人管理数据成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/talent-management-by-platform", summary="获取分平台达人管理统计", description="获取用户按平台分组的达人认领统计数据")
async def 获取分平台达人管理统计(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取分平台达人管理统计数据

    按平台（微信、抖音）分别统计用户认领的达人数量，用于工作台核心业务指标展示。

    参数:
    - **用户id**: 用户id (可选)
    - **时间范围**: 时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度、自定义）
    - **开始日期**: 开始日期 (时间范围为custom时使用)
    - **结束日期**: 结束日期 (时间范围为custom时使用)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 分平台达人管理统计数据，包含微信和抖音平台的指标卡片
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取分平台达人管理统计"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        应用日志器.debug(f"[{接口名称}] 请求参数: 时间范围={请求.时间范围}")

        # 调用服务层获取分平台达人统计数据
        分平台统计数据 = await 工作台服务实例.获取分平台达人管理统计(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取分平台达人管理统计")
        return 统一响应模型.成功(分平台统计数据, "获取分平台达人管理统计成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/cooperation-projects", summary="获取合作项目数据", description="获取用户的合作项目、销售额等数据")
async def 获取合作项目数据(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取合作项目数据
    
    参数:
    - **用户id**: 用户id (可选)
    - **时间范围**: 时间范围
    
    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 合作项目数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取合作项目数据"
    
    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        
        合作项目数据 = await 工作台服务实例.获取合作项目数据(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )
        
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取合作项目数据")
        return 统一响应模型.成功(合作项目数据, "获取合作项目数据成功")
        
    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/team-overview", summary="获取团队数据概览", description="获取用户的团队参与、成员、绩效等概览数据")
async def 获取团队数据概览(请求: 团队数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取团队数据概览

    参数:
    - **用户id**: 用户id (可选)
    - **团队id**: 团队id (可选，为空则获取用户所有团队)
    - **时间范围**: 时间范围

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 团队数据概览
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取团队数据概览"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        团队数据 = await 工作台服务实例.获取团队数据概览(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取团队数据概览")
        return 统一响应模型.成功(团队数据, "获取团队数据概览成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/trends", summary="获取趋势数据", description="获取邀约、合作、销售等趋势分析数据")
async def 获取趋势数据(请求: 趋势数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取趋势数据
    
    参数:
    - **用户id**: 用户id (可选)
    - **数据类型**: 数据类型 ('invitation', 'cooperation', 'sales')
    - **时间范围**: 时间范围
    
    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 趋势数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取趋势数据"
    
    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求，数据类型: {请求.数据类型}")
        
        # 参数验证
        有效数据类型 = ['invitation', 'cooperation', 'sales']
        if 请求.数据类型 not in 有效数据类型:
            return 统一响应模型.失败(400, f"无效的数据类型，支持的类型: {', '.join(有效数据类型)}")
        
        趋势数据 = await 工作台服务实例.获取趋势数据(
            用户id=用户id,
            数据类型=请求.数据类型,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )
        
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取趋势数据")
        return 统一响应模型.成功(趋势数据, "获取趋势数据成功")
        
    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/todos", summary="获取待办事项", description="获取用户的待办事项列表")
async def 获取待办事项(请求: 个人数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取待办事项

    参数:
    - **用户id**: 用户id (可选)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 待办事项列表
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取待办事项"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        待办事项 = await 工作台服务实例.获取待办事项列表(用户id=用户id)

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取待办事项")
        return 统一响应模型.成功(待办事项, "获取待办事项成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/invitation-batch-update", summary="批量更新邀约状态", description="批量更新用户的邀约记录状态")
async def 批量更新邀约状态(请求: dict, 用户: dict = Depends(获取当前用户)):
    """
    批量更新邀约状态

    参数:
    - **邀约IDs**: 邀约记录ID列表
    - **新状态**: 新的邀约状态码
    - **备注**: 更新备注 (可选)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 更新结果统计
    """
    用户id = 用户["id"]
    接口名称 = "批量更新邀约状态"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        # 参数验证
        邀约IDs = 请求.get('邀约IDs', [])
        新状态 = 请求.get('新状态')
        备注 = 请求.get('备注', '')

        if not 邀约IDs or not isinstance(邀约IDs, list):
            return 统一响应模型.失败(400, "邀约IDs参数无效")

        if not 新状态:
            return 统一响应模型.失败(400, "新状态参数不能为空")

        # 调用服务层批量更新邀约状态
        更新结果 = await 工作台服务实例.批量更新邀约状态(
            用户id=用户id,
            邀约IDs=邀约IDs,
            新状态=新状态,
            备注=备注
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功批量更新邀约状态")
        return 统一响应模型.成功(更新结果, "批量更新邀约状态成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/invitation-delete", summary="删除邀约记录", description="删除指定的邀约记录")
async def 删除邀约记录(请求: dict, 用户: dict = Depends(获取当前用户)):
    """
    删除邀约记录

    参数:
    - **邀约ID**: 邀约记录ID
    - **删除原因**: 删除原因 (可选)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 删除结果
    """
    用户id = 用户["id"]
    接口名称 = "删除邀约记录"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        # 参数验证
        邀约ID = 请求.get('邀约ID')
        删除原因 = 请求.get('删除原因', '')

        if not 邀约ID:
            return 统一响应模型.失败(400, "邀约ID参数不能为空")

        # 调用服务层删除邀约记录
        删除结果 = await 工作台服务实例.删除邀约记录(
            用户id=用户id,
            邀约ID=邀约ID,
            删除原因=删除原因
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功删除邀约记录")
        return 统一响应模型.成功(删除结果, "删除邀约记录成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/talent-contact-batch-update", summary="批量更新达人联系方式", description="批量更新达人的联系方式信息")
async def 批量更新达人联系方式(请求: dict, 用户: dict = Depends(获取当前用户)):
    """
    批量更新达人联系方式

    参数:
    - **达人关联ids**: 达人关联记录ID列表
    - **联系方式**: 新的联系方式
    - **联系方式类型**: 联系方式类型 (微信/电话/邮箱等)
    - **备注**: 更新备注 (可选)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 更新结果统计
    """
    用户id = 用户["id"]
    接口名称 = "批量更新达人联系方式"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        # 参数验证
        达人关联ids = 请求.get('达人关联ids', [])
        联系方式 = 请求.get('联系方式')
        联系方式类型 = 请求.get('联系方式类型', '微信')
        备注 = 请求.get('备注', '')

        if not 达人关联ids or not isinstance(达人关联ids, list):
            return 统一响应模型.失败(400, "达人关联ids参数无效")

        if not 联系方式:
            return 统一响应模型.失败(400, "联系方式参数不能为空")

        # 调用服务层批量更新联系方式
        更新结果 = await 工作台服务实例.批量更新达人联系方式(
            用户id=用户id,
            达人关联ids=达人关联ids,
            联系方式=联系方式,
            联系方式类型=联系方式类型,
            备注=备注
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功批量更新达人联系方式")
        return 统一响应模型.成功(更新结果, "批量更新达人联系方式成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/talent-unbind", summary="解除达人关联", description="解除用户与达人的关联关系")
async def 解除达人关联(请求: dict, 用户: dict = Depends(获取当前用户)):
    """
    解除达人关联

    参数:
    - **关联id**: 达人关联记录ID
    - **解除原因**: 解除原因 (可选)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 解除结果
    """
    用户id = 用户["id"]
    接口名称 = "解除达人关联"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")

        # 参数验证
        关联id = 请求.get('关联id')
        解除原因 = 请求.get('解除原因', '')

        if not 关联id:
            return 统一响应模型.失败(400, "关联id参数不能为空")

        # 调用服务层解除达人关联
        解除结果 = await 工作台服务实例.解除达人关联(
            用户id=用户id,
            关联id=关联id,
            解除原因=解除原因
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功解除达人关联")
        return 统一响应模型.成功(解除结果, "解除达人关联成功")

    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/dashboard", summary="获取工作台完整数据", description="获取工作台所有模块的数据（聚合接口）")
async def 获取工作台完整数据(请求: 工作台数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取工作台完整数据（聚合接口）

    参数:
    - **用户id**: 用户id (可选)
    - **时间范围**: 时间范围
    - **开始日期**: 开始日期 (时间范围为custom时使用)
    - **结束日期**: 结束日期 (时间范围为custom时使用)
    - **包含模块**: 包含的模块列表

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 包含所有模块数据的字典
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取工作台完整数据"
    
    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理聚合请求")
        应用日志器.debug(f"[{接口名称}] 包含模块: {请求.包含模块}")
        
        # 参数验证
        if 请求.时间范围 == '自定义' and (not 请求.开始日期 or not 请求.结束日期):
            return 统一响应模型.失败(400, "自定义时间范围时必须提供开始日期和结束日期")
        
        if 请求.包含模块 and len(请求.包含模块) == 0:
            return 统一响应模型.失败(400, "包含模块列表不能为空")
        
        完整数据 = await 工作台服务实例.获取工作台完整数据(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期,
            包含模块=请求.包含模块
        )
        
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取工作台完整数据")
        return 统一响应模型.成功(完整数据, "获取工作台完整数据成功")
        
    except Exception as e:
        return 处理接口异常(e, 接口名称, 用户id)


@工作台路由.post("/multi-metric-trends", summary="获取多指标趋势数据", description="获取多个业务指标的趋势分析数据，支持不同时间维度")
async def 获取多指标趋势数据(请求: 多指标趋势数据请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取多指标趋势数据

    支持功能：
    - 多个业务指标同时显示
    - 不同时间维度（日/周/月/季度）
    - 不同数据间隔（1天/3天/7天）
    - 指标选择性显示/隐藏

    参数:
    - **业务模块**: 业务模块 ('wechat', 'invitation', 'talent', 'sample')
    - **指标列表**: 要查询的指标列表
    - **时间维度**: 时间维度 ('day', 'week', 'month', 'quarter')
    - **时间范围**: 时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 多指标趋势数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取多指标趋势数据"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求，业务模块: {请求.业务模块}, 时间维度: {请求.时间维度}")

        # 参数验证
        有效业务模块 = ['wechat', 'invitation', 'talent', 'sample']
        if 请求.业务模块 not in 有效业务模块:
            return 统一响应模型.失败(400, f"无效的业务模块，支持的模块: {', '.join(有效业务模块)}")

        有效时间维度 = ['day', 'week', 'month', 'quarter']
        if 请求.时间维度 not in 有效时间维度:
            return 统一响应模型.失败(400, f"无效的时间维度，支持的维度: {', '.join(有效时间维度)}")

        多指标趋势数据 = await 工作台服务实例.获取多指标趋势数据(
            用户id=用户id,
            业务模块=请求.业务模块,
            指标列表=请求.指标列表,
            时间维度=请求.时间维度,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取多指标趋势数据")
        return 统一响应模型.成功(多指标趋势数据, "获取多指标趋势数据成功")

    except Exception as e:
        错误日志器.error(f"[{接口名称}] 用户id: {用户id} - 获取多指标趋势数据失败: {str(e)}")
        错误日志器.error(f"[{接口名称}] 错误详情: {traceback.format_exc()}")
        return 处理接口异常(e, 接口名称, 用户id)
