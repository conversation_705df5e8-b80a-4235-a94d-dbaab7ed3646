from fastapi import APIRouter, HTTPException, status, Depends

from 依赖项.认证 import 获取当前用户  # 新增：导入认证依赖  # 未使用的导入
from 数据模型.响应模型 import 统一响应模型  # 项目统一的响应包装器  # 未使用的导入
from 数据模型.线索模型 import (
    线索上传请求模型,
    线索更新请求模型,
    获取线索详情请求模型,
    获取线索列表请求模型  # 用于列表接口的响应数据部分类型
)
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from 日志 import 接口日志器, 错误日志器  # 未使用的导入
from 服务 import 线索服务  # 导入整个服务模块  # 未使用的导入
from 服务.线索搜索限制服务 import 线索搜索限制服务  # 新增：导入搜索限制服务
from 状态 import 状态  # 新增：导入状态码

# 将 router 实例重命名为 线索路由 以便在 main.py 中统一导入风格
线索路由 = APIRouter(
    tags=["线索管理"],
    dependencies=[Depends(获取当前用户)], # 修改：启用全局认证依赖
)

@线索路由.post("/upload", response_model=统一响应模型, summary="上传新线索") # 修改：移除参数化
async def 接口_上传线索(请求: 线索上传请求模型, 当前用户: dict = Depends(获取当前用户)) -> 统一响应模型: # 修改：返回类型也对应调整
    """
    接收线索数据并创建新的线索记录。
    操作用户将从当前登录用户自动获取。

    - **联系方式**: 包含联系方式内容、类型和记录来源。
    - **线索来源**: 这条线索本身的来源。
    - **更新用户id**: 操作者ID。
    - **额外信息**: 其他动态信息将以JSON格式存储。
    """
    接口日志器.info(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 接收到请求: {请求.model_dump_json(indent=2, exclude_none=True)}")
    try:
        服务层处理结果_字典 = await 线索服务.服务_上传新线索(请求, 操作用户id=当前用户["id"])
        
        action_type = 服务层处理结果_字典.get("action_type")
        lead_details_model = 服务层处理结果_字典.get("lead_details") # 这是 线索详情响应模型 的实例

        if not lead_details_model: # 防御性编程，理论上服务层应总是返回它
            错误日志器.error(f"[接口 /leads/upload] 服务层未能返回有效的线索详情。Action: {action_type}")
            return 统一响应模型.失败(
                状态码=状态.通用.服务器错误,
                消息="上传线索处理异常，未能获取线索详情"
            )

        message = "操作成功完成。"
        lead_id_for_log = lead_details_model.id

        if action_type == "created":
            message = "新线索创建成功"
            接口日志器.info(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 新线索创建成功，ID: {lead_id_for_log}")
        elif action_type == "updated_by_name_match":
            message = "线索已存在并通过名称匹配，信息更新成功"
            接口日志器.info(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 线索信息更新成功 (名称匹配)，ID: {lead_id_for_log}")
        else:
            # 未知的 action_type，记录警告但仍视为一般成功，使用默认消息和状态码
            接口日志器.warning(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 处理完成，但收到未知的action_type: '{action_type}'。线索ID: {lead_id_for_log}")
        
        return 统一响应模型.成功(数据=lead_details_model.model_dump(), 消息=message, 状态码=100)
        
    except HTTPException as http_exc: # 捕获服务层主动抛出的HTTPException
        错误日志器.warning(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 服务层校验失败或发生已知错误: {http_exc.status_code} - {http_exc.detail}")
        raise http_exc # 直接向上抛出，由FastAPI框架处理
    except Exception as e:
        错误日志器.error(f"[接口 /leads/upload] 用户 {当前用户.get('id')} 未知错误: {str(e)}", exc_info=True)
        # 对于未能被服务层捕获的未知错误，返回统一的500错误
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"上传线索时发生服务器内部错误: {str(e)}"
        )

@线索路由.post("/detail", response_model=统一响应模型, summary="获取线索详情") # 修改：移除参数化
async def 接口_获取线索详情(请求: 获取线索详情请求模型, 当前用户: dict = Depends(获取当前用户)) -> 统一响应模型: # 修改：返回类型也对应调整
    """
    根据线索ID获取单个线索的详细信息。
    需要用户登录。
    """
    接口日志器.info(f"[接口 /leads/detail] 用户 {当前用户.get('id')} 接收到请求: {请求.model_dump_json(indent=2)}")
    try:
        处理结果 = await 线索服务.服务_获取线索详情(请求.线索id)
        接口日志器.info(f"[接口 /leads/detail] 用户 {当前用户.get('id')} 成功获取线索ID {请求.线索id} 的详情。")
        return 统一响应模型.成功(数据=处理结果, 消息="线索详情获取成功")
    except HTTPException as http_exc:
        错误日志器.warning(f"[接口 /leads/detail] 用户 {当前用户.get('id')} 服务层校验失败或发生已知错误: {http_exc.status_code} - {http_exc.detail}")
        raise http_exc
    except Exception as e:
        错误日志器.error(f"[接口 /leads/detail] 用户 {当前用户.get('id')} 未知错误: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"获取线索详情时发生服务器内部错误: {str(e)}"
        )

@线索路由.post("/update", response_model=统一响应模型, summary="更新指定线索") # 修改：移除参数化
async def 接口_更新线索(请求: 线索更新请求模型, 当前用户: dict = Depends(获取当前用户)) -> 统一响应模型: # 修改：返回类型也对应调整
    """
    根据线索ID更新已存在的线索记录。
    允许部分更新，仅更新请求中提供的字段。
    操作用户将从当前登录用户自动获取。
    """
    接口日志器.info(f"[接口 /leads/update] 用户 {当前用户.get('id')} 接收到更新请求: {请求.model_dump_json(indent=2, exclude_none=True)}")
    try:
        处理结果 = await 线索服务.服务_更新指定线索(请求, 操作用户id=当前用户["id"])
        接口日志器.info(f"[接口 /leads/update] 用户 {当前用户.get('id')} 成功更新线索ID {请求.线索id}。")
        return 统一响应模型.成功(数据=处理结果, 消息="线索更新成功")
    except HTTPException as http_exc:
        错误日志器.warning(f"[接口 /leads/update] 用户 {当前用户.get('id')} 服务层校验失败或发生已知错误: {http_exc.status_code} - {http_exc.detail}")
        raise http_exc
    except Exception as e:
        错误日志器.error(f"[接口 /leads/update] 用户 {当前用户.get('id')} 未知错误: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"更新线索时发生服务器内部错误: {str(e)}"
        )

@线索路由.post("/list", response_model=统一响应模型, summary="获取线索列表（分页）") # 修改：移除参数化
async def 接口_获取线索列表(请求: 获取线索列表请求模型, 当前用户: dict = Depends(获取当前用户)) -> 统一响应模型: # 修改：返回类型也对应调整
    """
    获取线索列表，支持分页和筛选参数。
    需要用户登录。

    **搜索次数限制**：
    - 非会员用户：每日最多搜索10次
    - 会员用户：每日最多搜索100次

    - **页码**: 当前页码，从1开始。
    - **每页数量**: 每页希望获取的记录数量。
    - **筛选_联系方式**: (可选) 按联系方式内容模糊查询。
    - **筛选_线索来源**: (可选) 按线索来源精确查询。
    - **起始id**: (可选) 筛选ID大于此值的记录，默认为0表示不限制起始ID，用于配合分页进一步筛选。
    - **筛选_信息值**: (可选) 在线索的'信息'JSON字段的任意层级字符串值中进行模糊搜索。请注意，此搜索效率可能不高。
    """
    用户id = 当前用户.get('id')
    if not 用户id:
        错误日志器.error("[接口 /leads/list] 当前用户信息中缺少用户ID")
        return 统一响应模型.失败(
            状态码=429,  # 登录认证失败
            消息="用户认证信息无效"
        )

    接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 接收到列表请求: {请求.model_dump_json(indent=2, exclude_none=True)}")

    try:
        # 1. 初始化搜索限制服务（用于搜索和翻页限制检查）
        搜索限制服务 = 线索搜索限制服务()

        # 2. 检查用户搜索权限（仅在有筛选条件时进行检查）
        起始id = getattr(请求, '起始id', None)
        是否为搜索请求 = any([
            请求.筛选_联系方式,
            请求.筛选_线索来源,
            请求.筛选_信息值,
            起始id is not None and 起始id > 0
        ])

        if 是否为搜索请求:
            # 检查搜索权限
            权限检查结果 = await 搜索限制服务.检查用户搜索权限(用户id)

            if not 权限检查结果['can_search']:
                # 搜索次数已达上限
                状态码 = 状态.线索搜索.非会员搜索次数已达上限 if not 权限检查结果['is_member'] else 状态.线索搜索.会员搜索次数已达上限
                接口日志器.warning(f"[接口 /leads/list] 用户 {用户id} 搜索次数已达上限: {权限检查结果['message']}")
                return 统一响应模型.失败(
                    状态码=状态码,
                    消息=权限检查结果['message'],
                    数据={
                        "search_limit_info": {
                            "used_count": 权限检查结果['used_count'],
                            "limit_count": 权限检查结果['limit_count'],
                            "is_member": 权限检查结果['is_member']
                        }
                    }
                )

            # 记录搜索行为
            记录成功 = await 搜索限制服务.记录搜索行为(用户id)
            if not 记录成功:
                错误日志器.warning(f"[接口 /leads/list] 用户 {用户id} 搜索记录更新失败")

            接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 搜索权限检查通过，剩余次数: {权限检查结果['remaining_count'] - 1}")

        # 3. 检查翻页权限和记录翻页行为
        # 每次翻页操作（包括上一页、下一页、点击指定页数）都算一次查询
        # 每个用户每日限制翻页100次，超过100次就提示
        翻页权限检查结果 = None

        # 判断是否为翻页请求的逻辑：
        # 只依赖前端明确标识的翻页操作（is_pagination=True）
        # 这样可以准确区分搜索操作和翻页操作，避免误记录
        是否为翻页请求 = getattr(请求, 'is_pagination', False)

        接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 翻页检查: 当前页码={请求.页码}, 是否为翻页请求={是否为翻页请求}")

        if 是否为翻页请求:
            try:
                接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 开始检查翻页权限，当前页码: {请求.页码}")
                翻页权限检查结果 = await 搜索限制服务.检查用户翻页权限(用户id)

                if not 翻页权限检查结果['can_paginate']:
                    # 翻页次数已达上限（10次）
                    接口日志器.warning(f"[接口 /leads/list] 用户 {用户id} 翻页次数已达上限: {翻页权限检查结果['message']}")
                    return 统一响应模型.失败(
                        状态码=状态.线索搜索.翻页次数已达上限,
                        消息=翻页权限检查结果['message'],
                        数据={
                            "pagination_limit_info": {
                                "used_count": 翻页权限检查结果['used_count'],
                                "limit_count": 翻页权限检查结果['limit_count'],
                                "warning_level": 翻页权限检查结果['warning_level']
                            }
                        }
                    )

                # 记录翻页行为（与搜索行为记录逻辑一致）
                接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 开始记录翻页行为")
                记录成功 = await 搜索限制服务.记录翻页行为(用户id)
                if not 记录成功:
                    错误日志器.warning(f"[接口 /leads/list] 用户 {用户id} 翻页记录更新失败")
                else:
                    接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 翻页记录更新成功")

                接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 翻页权限检查通过，剩余次数: {翻页权限检查结果['remaining_count'] - 1}")
            except Exception as e:
                错误日志器.warning(f"[接口 /leads/list] 用户 {用户id} 翻页权限检查失败: {str(e)}")
                # 继续执行，不阻止翻页，但记录错误

        # 4. 获取用户权限信息（用于数据隐私保护）
        用户权限信息 = {
            "is_member": 权限检查结果.get('is_member', False) if 是否为搜索请求 else False,
            "user_id": 用户id
        }

        # 5. 执行线索列表查询
        处理结果_字典 = await 线索服务.服务_获取线索列表(请求, 用户权限信息)
        接口日志器.info(f"[接口 /leads/list] 用户 {用户id} 成功获取线索列表，返回 {len(处理结果_字典['列表'])} 条记录，显示总数 {处理结果_字典.get('显示总数', '未知')}。")

        # 6. 在响应中包含搜索次数信息（如果是搜索请求）
        响应数据 = 处理结果_字典
        if 是否为搜索请求:
            响应数据['search_info'] = {
                "used_count": 权限检查结果['used_count'] + 1,  # 已包含本次搜索
                "limit_count": 权限检查结果['limit_count'],
                "remaining_count": 权限检查结果['remaining_count'] - 1,
                "is_member": 权限检查结果['is_member']
            }

        # 7. 添加翻页限制信息（如果进行了翻页）
        # 每个用户每日限制翻页100页，在响应中返回限制信息
        if 翻页权限检查结果:
            响应数据['pagination_limit_info'] = {
                "can_paginate": 翻页权限检查结果['can_paginate'],
                "used_count": 翻页权限检查结果['used_count'] + 1,  # 已包含本次翻页
                "limit_count": 翻页权限检查结果['limit_count'],
                "remaining_count": 翻页权限检查结果['remaining_count'] - 1,
                "warning_level": 翻页权限检查结果['warning_level'],
                "message": 翻页权限检查结果['message']
            }

        # 8. 添加数据隐私保护说明
        if '隐私信息' in 响应数据:
            响应数据['privacy_notice'] = {
                "message": "为保护数据隐私，部分统计信息已进行模糊化处理",
                "upgrade_hint": "升级会员可获得更详细的数据统计" if not 用户权限信息['is_member'] else None
            }

        return 统一响应模型.成功(数据=响应数据, 消息="线索列表获取成功")

    except HTTPException as http_exc:
        错误日志器.warning(f"[接口 /leads/list] 用户 {用户id} 服务层校验失败或发生已知错误: {http_exc.status_code} - {http_exc.detail}")
        raise http_exc
    except Exception as e:
        错误日志器.error(f"[接口 /leads/list] 用户 {用户id} 未知错误: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"获取线索列表时发生服务器内部错误: {str(e)}"
        )

# ==================== 翻页限制检查接口 ====================

@线索路由.post("/pagination-limit", response_model=统一响应模型, summary="检查用户翻页限制")
async def 接口_检查翻页限制(当前用户: dict = Depends(获取当前用户)) -> 统一响应模型:
    """
    检查用户当前的翻页次数使用情况
    每个用户每日限制翻页100页，与线索查询次数逻辑一致

    返回信息包括：
    - 是否可以翻页
    - 今日已翻页次数
    - 每日翻页限制（100次）
    - 剩余翻页次数
    - 警告级别
    """
    用户id = 当前用户.get('id')
    if not 用户id:
        错误日志器.error("[接口 /leads/pagination-limit] 当前用户信息中缺少用户ID")
        return 统一响应模型.失败(
            状态码=429,  # 登录认证失败
            消息="用户认证信息无效"
        )

    接口日志器.info(f"[接口 /leads/pagination-limit] 用户 {用户id} 检查翻页限制")

    try:
        # 初始化搜索限制服务
        搜索限制服务 = 线索搜索限制服务()

        # 获取用户翻页权限信息
        权限信息 = await 搜索限制服务.检查用户翻页权限(用户id)

        响应数据 = {
            "can_paginate": 权限信息['can_paginate'],
            "used_count": 权限信息['used_count'],
            "limit_count": 权限信息['limit_count'],
            "remaining_count": 权限信息['remaining_count'],
            "warning_level": 权限信息['warning_level'],
            "message": 权限信息['message']
        }

        接口日志器.info(f"[接口 /leads/pagination-limit] 用户 {用户id} 翻页限制检查成功")
        return 统一响应模型.成功(数据=响应数据, 消息="翻页限制检查成功")

    except Exception as e:
        错误日志器.error(f"[接口 /leads/pagination-limit] 用户 {用户id} 检查翻页限制失败: {str(e)}", exc_info=True)

        # 降级处理：返回默认允许翻页的响应
        响应数据 = {
            "can_paginate": True,
            "used_count": 0,
            "limit_count": 100,  # 使用正确的限制数量
            "remaining_count": 100,
            "warning_level": "none",
            "message": "翻页限制检查失败，使用默认设置"
        }

        return 统一响应模型.成功(数据=响应数据, 消息="翻页限制检查失败，使用默认设置")

# ==================== 搜索次数查询接口 ====================

@线索路由.post("/search-quota", response_model=统一响应模型, summary="查询用户搜索次数配额")
async def 接口_查询搜索配额(当前用户: dict = Depends(获取当前用户)) -> 统一响应模型:
    """
    查询用户当前的搜索次数使用情况

    返回信息包括：
    - 是否为会员
    - 今日已使用次数
    - 每日限制次数
    - 剩余次数
    - 会员信息
    """
    用户id = 当前用户.get('id')
    if not 用户id:
        错误日志器.error("[接口 /leads/search-quota] 当前用户信息中缺少用户ID")
        return 统一响应模型.失败(
            状态码=429,  # 登录认证失败
            消息="用户认证信息无效"
        )

    接口日志器.info(f"[接口 /leads/search-quota] 用户 {用户id} 查询搜索配额")

    try:
        # 初始化搜索限制服务
        搜索限制服务 = 线索搜索限制服务()

        # 获取用户搜索权限信息
        权限信息 = await 搜索限制服务.检查用户搜索权限(用户id)

        响应数据 = {
            "can_search": 权限信息['can_search'],
            "is_member": 权限信息['is_member'],
            "used_count": 权限信息['used_count'],
            "limit_count": 权限信息['limit_count'],
            "remaining_count": 权限信息['remaining_count'],
            "message": 权限信息['message'],
            "member_info": 权限信息['member_info']
        }

        接口日志器.info(f"[接口 /leads/search-quota] 用户 {用户id} 搜索配额查询成功")
        return 统一响应模型.成功(数据=响应数据, 消息="搜索配额查询成功")

    except Exception as e:
        错误日志器.error(f"[接口 /leads/search-quota] 用户 {用户id} 查询搜索配额失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.线索搜索.获取搜索次数失败,
            消息="查询搜索配额时发生错误，请稍后重试"
        )

# ==================== 新增接口模型 ====================

class 线索概览请求(BaseModel):
    """线索概览统计请求模型"""
    时间范围: str = Field(default="30d", description="统计时间范围")
    包含详细分析: bool = Field(default=True, description="是否包含详细分析")

class 批量操作请求(BaseModel):
    """批量操作请求模型"""
    线索ID列表: List[int] = Field(description="线索ID列表")
    操作类型: str = Field(description="操作类型")
    操作数据: Optional[Dict[str, Any]] = Field(default=None, description="操作数据")

class 导出请求(BaseModel):
    """导出请求模型"""
    导出格式: str = Field(default="excel", description="导出格式")
    导出字段: List[str] = Field(description="导出字段")
    筛选条件: Optional[Dict[str, Any]] = Field(default=None, description="筛选条件")

# ==================== 新增API接口 ====================

@线索路由.post("/overview", response_model=统一响应模型, summary="获取线索概览统计")
async def 线索概览统计接口(
    当前用户: dict = Depends(获取当前用户)
):
    """
    获取线索概览统计数据

    功能：
    - 提供线索总数、新增数量等基础统计
    - 提供来源分布、地域分布等分析数据
    - 支持不同时间范围的统计
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"用户 {用户id} 请求线索概览统计")

        # 调用服务层获取统计数据 - 使用模拟数据
        统计数据 = {
            "totalLeads": 154054,
            "dailyNew": 156,
            "weeklyNew": 1089,
            "monthlyNew": 4567,
            "hasContactCount": 45678,
            "highValueCount": 12345,
            "conversionRate": 23.5,
            "growthRate": 12.3,
            "formattedTotal": "15.4万",
            "trendAnalysis": "increasing",
            "sourceDistribution": [
                {"name": "全网达人Excel", "value": 68900, "percentage": 44.7},
                {"name": "主播数据", "value": 20000, "percentage": 13.0},
                {"name": "短视频达人", "value": 15000, "percentage": 9.7},
                {"name": "其他来源", "value": 50154, "percentage": 32.6}
            ],
            "locationDistribution": [
                {"name": "北京", "value": 15405},
                {"name": "上海", "value": 12340},
                {"name": "广州", "value": 9876},
                {"name": "深圳", "value": 8765},
                {"name": "杭州", "value": 6543}
            ],
            "categoryDistribution": [
                {"name": "美妆", "value": 23456},
                {"name": "时尚", "value": 18765},
                {"name": "美食", "value": 15432},
                {"name": "旅游", "value": 12345},
                {"name": "科技", "value": 9876}
            ]
        }

        接口日志器.info(f"线索概览统计获取成功: 总线索数={统计数据.get('totalLeads', 0)}")

        return 统一响应模型(
            status=100,
            message="获取线索概览统计成功",
            data=统计数据
        )

    except Exception as e:
        接口日志器.error(f"获取线索概览统计失败: {e}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"获取线索概览统计失败: {str(e)}"
        )

@线索路由.post("/batch-operate", response_model=统一响应模型, summary="批量操作线索")
async def 批量操作接口(
    请求: 批量操作请求,
    当前用户: dict = Depends(获取当前用户)
):
    """
    批量操作线索

    功能：
    - 支持批量删除
    - 支持批量更新
    - 支持批量导出
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"用户 {用户id} 批量操作线索: {请求.操作类型}, 数量={len(请求.线索ID列表)}")

        # 模拟批量操作结果
        操作结果 = {
            "影响数量": len(请求.线索ID列表),
            "成功数量": len(请求.线索ID列表),
            "失败数量": 0,
            "操作类型": 请求.操作类型
        }

        接口日志器.info(f"批量操作成功: 操作类型={请求.操作类型}, 影响数量={操作结果.get('影响数量', 0)}")

        return 统一响应模型(
            status=100,
            message="批量操作成功",
            data=操作结果
        )

    except Exception as e:
        接口日志器.error(f"批量操作失败: {e}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"批量操作失败: {str(e)}"
        )

@线索路由.post("/export", summary="导出线索数据")
async def 导出接口(
    请求: 导出请求,
    当前用户: dict = Depends(获取当前用户)
):
    """
    导出线索数据

    功能：
    - 支持Excel和CSV格式
    - 支持自定义字段
    - 支持筛选条件导出
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"用户 {用户id} 导出线索数据: 格式={请求.导出格式}")

        # 模拟导出成功
        from fastapi.responses import JSONResponse

        return JSONResponse(
            content={
                "status": 100,
                "message": "导出成功",
                "data": {
                    "downloadUrl": "/downloads/leads_export.xlsx",
                    "fileName": f"线索数据_{用户id}.xlsx",
                    "fileSize": "2.5MB"
                }
            }
        )

    except Exception as e:
        接口日志器.error(f"导出线索数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出线索数据失败: {str(e)}")

# ==================== 健康检查接口 ====================

@线索路由.post("/health", summary="线索模块健康检查")
async def 健康检查():
    """线索模块健康检查接口"""
    return {
        "status": 100,
        "message": "线索模块运行正常",
        "module": "线索管理",
        "version": "1.0.0"
    }