# 路由/订单.py
import traceback

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, Field

# 从依赖项模块导入获取当前用户函数
from 依赖项.认证 import 获取当前用户

# 导入统一日志系统
from 日志 import 安全日志器, 错误日志器
from 服务.会员服务 import 会员服务类
from 服务.支付服务 import 统一订单支付服务类
from 状态 import 状态

# 创建路由器
订单路由 = APIRouter()


# ========================= 请求模型定义 =========================


class 获取套餐列表请求(BaseModel):
    """获取套餐列表请求"""

    pass


class 创建订单请求(BaseModel):
    """创建订单请求"""

    会员id: int = Field(..., description="会员套餐ID")
    付费周期: str = Field(..., description="付费周期：monthly/yearly")
    支付类型: str = Field(default="NATIVE", description="支付类型：NATIVE/JSAPI")


class 查询订单请求(BaseModel):
    """查询订单请求"""

    订单号: str = Field(..., description="订单号")


class 取消订单请求(BaseModel):
    """取消订单请求"""

    订单号: str = Field(..., description="订单号")


# ========================= 套餐管理接口 =========================


@订单路由.post("/membership_plans", summary="获取会员套餐列表")
async def 获取会员套餐列表(请求: 获取套餐列表请求):
    """
    获取所有可用的会员套餐列表

    Returns:
        包含套餐信息的响应数据
    """
    try:
        会员服务 = 会员服务类()
        套餐列表 = await 会员服务.获取套餐列表()

        return {"status": 100, "message": "获取套餐列表成功", "data": 套餐列表}

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取套餐列表失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取套餐列表失败: {str(e)}",
                "data": None,
            },
        )


# ========================= 订单管理接口 =========================


@订单路由.post("/create", summary="创建支付订单")
async def 创建支付订单(订单信息: 创建订单请求, 用户: dict = Depends(获取当前用户)):
    """
    创建支付订单并生成微信支付二维码

    Args:
        订单信息: 包含会员id和付费周期的订单信息
        用户: 当前登录用户

    Returns:
        包含支付信息的响应数据
    """
    try:
        # 参数验证
        if 订单信息.付费周期 not in ["monthly", "yearly"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.订单.参数错误,
                    "message": "付费周期参数错误，只支持 monthly 或 yearly",
                    "data": None,
                },
            )

        if 订单信息.支付类型 not in ["NATIVE", "JSAPI"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.订单.参数错误,
                    "message": "支付类型参数错误，只支持 NATIVE 或 JSAPI",
                    "data": None,
                },
            )

        # 创建支付服务实例
        支付服务 = 统一订单支付服务类()

        # 创建订单
        订单结果 = await 支付服务.创建订单(
            用户id=用户["id"],
            会员id=订单信息.会员id,
            付费周期=订单信息.付费周期,
            支付类型=订单信息.支付类型,
        )

        # 检查订单创建结果
        if 订单结果.get("status") == 状态.通用.成功:
            安全日志器.info(
                f"用户 {用户['id']} 创建订单成功，订单号: {订单结果.get('data', {}).get('订单号')}"
            )
            return {
                "status": 100,
                "message": "订单创建成功",
                "data": 订单结果.get("data"),
            }
        else:
            # 订单创建失败，返回错误信息
            安全日志器.warning(
                f"用户 {用户['id']} 创建订单失败: {订单结果.get('message')}"
            )
            return 订单结果

    except HTTPException as he:
        raise he
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"创建订单失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"创建订单失败: {str(e)}",
                "data": None,
            },
        )


@订单路由.post("/query", summary="查询订单状态")
async def 查询订单状态(查询信息: 查询订单请求, 用户: dict = Depends(获取当前用户)):
    """
    查询订单支付状态

    Args:
        查询信息: 包含订单号的查询信息
        用户: 当前登录用户

    Returns:
        订单状态信息
    """
    try:
        支付服务 = 统一订单支付服务类()
        订单状态 = await 支付服务.查询订单状态(查询信息.订单号, 用户["id"])

        return {"status": 100, "message": "查询订单状态成功", "data": 订单状态}

    except HTTPException as he:
        raise he
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"查询订单状态失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"查询订单状态失败: {str(e)}",
                "data": None,
            },
        )


@订单路由.post("/cancel", summary="取消订单")
async def 取消订单(取消信息: 取消订单请求, 用户: dict = Depends(获取当前用户)):
    """
    取消未支付的订单

    Args:
        取消信息: 包含订单号的取消信息
        用户: 当前登录用户

    Returns:
        取消结果
    """
    try:
        支付服务 = 统一订单支付服务类()
        取消结果 = await 支付服务.取消订单(取消信息.订单号, 用户["id"])

        安全日志器.info(f"用户 {用户['id']} 取消订单: {取消信息.订单号}")

        return {"status": 100, "message": "订单取消成功", "data": 取消结果}

    except HTTPException as he:
        raise he
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"取消订单失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"取消订单失败: {str(e)}",
                "data": None,
            },
        )


@订单路由.get("/user_orders", summary="获取用户订单列表")
async def 获取用户订单列表(用户: dict = Depends(获取当前用户)):
    """
    获取当前用户的所有订单

    Args:
        用户: 当前登录用户

    Returns:
        用户订单列表
    """
    try:
        支付服务 = 统一订单支付服务类()
        订单列表 = await 支付服务.获取用户订单列表(用户["id"])

        return {"status": 100, "message": "获取订单列表成功", "data": 订单列表}

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取用户订单列表失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取订单列表失败: {str(e)}",
                "data": None,
            },
        )


@订单路由.get("/user_membership", summary="获取用户会员信息")
async def 获取用户会员信息(用户: dict = Depends(获取当前用户)):
    """
    获取用户当前的会员信息

    Args:
        用户: 当前登录用户

    Returns:
        用户会员信息
    """
    try:
        会员服务 = 会员服务类()
        会员信息 = await 会员服务.获取用户会员信息(用户["id"])

        return {"status": 100, "message": "获取会员信息成功", "data": 会员信息}

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取用户会员信息失败: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取会员信息失败: {str(e)}",
                "data": None,
            },
        )


# ========================= 微信支付回调接口 =========================


@订单路由.post("/wechat_pay_notify", summary="微信支付回调通知")
async def 微信支付回调通知(request: Request):
    """
    处理微信支付回调通知

    Args:
        request: 请求对象

    Returns:
        符合微信支付要求的响应格式
    """
    try:
        # 获取微信支付通知的原始数据
        微信通知头信息 = dict(request.headers)
        微信通知体数据 = await request.body()

        安全日志器.info(f"收到微信支付回调通知，Headers: {微信通知头信息}")

        # 创建支付服务实例处理回调
        支付服务 = 统一订单支付服务类()
        处理结果 = await 支付服务.处理微信支付回调(微信通知头信息, 微信通知体数据)

        if 处理结果.get("code") == "SUCCESS":
            安全日志器.info(f"微信支付回调处理成功: {处理结果.get('message')}")
            return {"code": "SUCCESS", "message": "成功"}
        else:
            错误日志器.error(f"微信支付回调处理失败: {处理结果.get('message')}")
            return {"code": "FAIL", "message": 处理结果.get("message", "处理失败")}

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"处理微信支付回调通知发生异常: {str(e)}\n{错误详情}")
        安全日志器.error(f"微信支付回调异常: {str(e)}")

        # 返回符合微信支付要求的失败响应
        return {"code": "FAIL", "message": "系统处理异常"}


@订单路由.get("/payment_status", summary="检查支付功能状态")
async def 检查支付功能状态():
    """
    检查微信支付功能是否可用

    Returns:
        支付功能状态信息
    """
    try:
        # 创建支付服务实例
        支付服务 = 统一订单支付服务类()

        # 检查支付功能状态
        if 支付服务.支付功能可用:
            return {
                "status": 100,
                "message": "支付功能正常",
                "data": {
                    "支付功能可用": True,
                    "支持的支付方式": ["NATIVE", "JSAPI"],
                    "配置状态": "正常",
                    "微信支付配置": {
                        "APPID": bool(支付服务.微信支付配置.get("APPID")),
                        "MCHID": bool(支付服务.微信支付配置.get("MCHID")),
                        "PRIVATE_KEY": bool(支付服务.微信支付配置.get("PRIVATE_KEY")),
                        "CERT_SERIAL_NO": bool(
                            支付服务.微信支付配置.get("CERT_SERIAL_NO")
                        ),
                        "APIV3_KEY": bool(支付服务.微信支付配置.get("APIV3_KEY")),
                        "NOTIFY_URL": bool(支付服务.微信支付配置.get("NOTIFY_URL")),
                    },
                },
            }
        else:
            return {
                "status": 503,
                "message": f"支付功能不可用: {支付服务.配置错误信息}",
                "data": {
                    "支付功能可用": False,
                    "错误信息": 支付服务.配置错误信息,
                    "建议": "请检查微信支付配置或联系管理员",
                    "配置检查": "请运行 python wechat/setup.py 检查配置",
                },
            }

    except Exception as e:
        错误日志器.error(f"检查支付功能状态失败: {str(e)}")
        return {
            "status": 500,
            "message": f"检查支付功能状态失败: {str(e)}",
            "data": {"支付功能可用": False, "错误信息": str(e)},
        }
