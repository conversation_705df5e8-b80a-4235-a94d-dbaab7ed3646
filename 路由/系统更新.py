from typing import List, Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from 服务.腾讯云存储服务 import 腾讯云存储服务实例
from 日志 import 接口日志器, 错误日志器
import 状态

# 系统更新相关路由
系统更新路由 = APIRouter()

class 批量下载链接请求(BaseModel):
    """
    批量下载链接请求模型
    
    用于批量获取文件下载链接的请求参数
    
    Attributes:
        文件路径列表 (List[str]): 需要获取下载链接的文件路径列表
        过期时间 (Optional[int]): 下载链接的过期时间（秒），默认3600秒（1小时）
    """
    文件路径列表: List[str]
    过期时间: Optional[int] = 3600

@系统更新路由.get("/file_list")
async def 获取文件列表(前缀: str = ""):
    """
    获取腾讯云COS中的文件列表
    
    Args:
        前缀 (str): 文件路径前缀，用于筛选特定目录下的文件，默认为空（获取所有文件）
        
    Returns:
        dict: 包含文件列表和下载链接的响应数据
            - status: 状态码
            - message: 响应消息
            - data: 文件列表，每个文件包含路径和下载链接
            
    Raises:
        HTTPException: 当获取文件列表失败时抛出500错误
    """
    try:
        接口日志器.info(f"开始获取文件列表，前缀: {前缀}")
        
        文件列表 = await 腾讯云存储服务实例.获取文件列表(前缀)
        
        # 为每个文件添加下载链接
        for 文件 in 文件列表:
            文件['下载链接'] = await 腾讯云存储服务实例.获取文件下载链接(文件['路径'])
        
        接口日志器.info(f"成功获取文件列表，共 {len(文件列表)} 个文件")
        
        return {
            "status": 状态.通用.成功_旧,
            "message": "获取文件列表成功",
            "data": 文件列表
        }
    except Exception as e:
        错误日志器.error(f"获取文件列表异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取文件列表失败: {str(e)}",
                "data": None
            }
        )

@系统更新路由.get("/download_url")
async def 获取文件下载链接(文件路径: str, 过期时间: int = 3600):
    """
    获取单个文件的临时下载链接
    
    Args:
        文件路径 (str): 需要获取下载链接的文件路径
        过期时间 (int): 下载链接的过期时间（秒），默认3600秒（1小时）
        
    Returns:
        dict: 包含下载链接的响应数据
            - status: 状态码
            - message: 响应消息
            - data: 包含文件路径、下载链接和过期时间的数据
            
    Raises:
        HTTPException: 当获取下载链接失败时抛出500错误
    """
    try:
        接口日志器.info(f"开始获取文件下载链接，文件路径: {文件路径}, 过期时间: {过期时间}秒")
        
        下载链接 = await 腾讯云存储服务实例.获取文件下载链接(文件路径, 过期时间)
        
        if 下载链接:
            接口日志器.info(f"成功获取文件下载链接: {文件路径}")
            return {
                "status": 状态.通用.成功_旧,
                "message": "获取下载链接成功",
                "data": {
                    "文件路径": 文件路径,
                    "下载链接": 下载链接,
                    "过期时间": 过期时间
                }
            }
        else:
            错误日志器.warning(f"获取文件下载链接失败: {文件路径}")
            return {
                "status": 状态.通用.操作失败,
                "message": "获取下载链接失败",
                "data": None
            }
    except Exception as e:
        错误日志器.error(f"获取下载链接异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取下载链接失败: {str(e)}",
                "data": None
            }
        )

@系统更新路由.post("/batch_download_urls")
async def 批量获取下载链接(请求: 批量下载链接请求):
    """
    批量获取多个文件的临时下载链接
    
    Args:
        请求 (批量下载链接请求): 包含文件路径列表和过期时间的请求对象
        
    Returns:
        dict: 包含批量下载链接的响应数据
            - status: 状态码
            - message: 响应消息
            - data: 包含下载链接映射和过期时间的数据
            
    Raises:
        HTTPException: 当批量获取下载链接失败时抛出500错误
    """
    try:
        接口日志器.info(f"开始批量获取下载链接，文件数量: {len(请求.文件路径列表)}, 过期时间: {请求.过期时间}秒")
        
        下载链接映射 = await 腾讯云存储服务实例.批量获取文件下载链接(
            请求.文件路径列表, 请求.过期时间
        )
        
        接口日志器.info(f"成功批量获取下载链接，共 {len(下载链接映射)} 个文件")
        
        return {
            "status": 状态.通用.成功_旧,
            "message": "批量获取下载链接成功",
            "data": {
                "下载链接映射": 下载链接映射,
                "过期时间": 请求.过期时间
            }
        }
    except Exception as e:
        错误日志器.error(f"批量获取下载链接异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"批量获取下载链接失败: {str(e)}",
                "data": None
            }
        )