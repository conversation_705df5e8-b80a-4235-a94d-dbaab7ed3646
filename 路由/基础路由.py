import sys

from fastapi import APIRouter, Response, status
from fastapi import __version__ as fastapi_version

# 如果需要配置项，例如服务器状态检查的token，可以从config导入
# import config 

基础路由 = APIRouter()

@基础路由.get("/")
async def root():
    """根路径响应"""
    return {"message": "邀请系统后端API服务运行中"}


@基础路由.get("/server-status", include_in_schema=True, tags=["服务器状态接口"])
async def 服务器状态(响应: Response, token: str | None = None): # token的依赖和验证逻辑保持原样
    """服务器状态检查接口"""
    # 实际项目中，这个token应该来自配置或更安全的验证方式
    if token == "Main": # 假设 "Main" 是一个示例token，实际应配置化或移除
        data = {
            "运行状态": "正常",
            "FastAPI版本": fastapi_version,
            "Python版本": sys.version
        }
        return data
    else:
        响应.status_code = status.HTTP_404_NOT_FOUND
        return {"message": "错误"}


@基础路由.get("/health", include_in_schema=False)
def 健康检查(响应: Response, 简洁模式: bool = True):
    """简单健康检查，无需身份验证"""
    响应.status_code = status.HTTP_200_OK
    if 简洁模式:
        return {"status": "healthy"}
    return {"status": "healthy", "message": "服务运行正常"} 