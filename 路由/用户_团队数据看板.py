"""
用户团队数据看板路由
专门处理团队数据看板相关的API接口
"""
import traceback
from fastapi import APIRouter, Depends, Body

from 依赖项.认证 import 获取当前用户
from 数据模型.团队管理模型 import (
    团队数据看板聚合请求,
    团队成员详细绩效请求,
)
from 数据模型.响应模型 import 统一响应模型
from 日志 import 接口日志器, 错误日志器

# 创建路由器，不设置prefix，在main.py中统一设置
用户团队数据看板路由 = APIRouter(tags=["用户团队数据看板"])


async def 检查团队访问权限(团队id: int, 用户id: int, 错误消息: str = "无权限访问该团队"):
    """统一的团队访问权限检查函数"""
    try:
        from 数据.团队数据看板 import 获取团队详情
        
        团队详情 = await 获取团队详情(团队id, 用户id, True)
        
        if not 团队详情.get("success"):
            return False, 统一响应模型.失败(状态码=403, 消息=错误消息)
        
        return True, None
        
    except Exception as e:
        错误日志器.error(f"检查团队访问权限失败: 团队id={团队id}, 用户id={用户id}, 错误={e}")
        return False, 统一响应模型.失败(状态码=500, 消息="权限检查失败")



@用户团队数据看板路由.post(
    "/member-performance-detail",
    response_model=统一响应模型,
    summary="获取团队成员详细绩效数据",
    description="获取团队成员的详细绩效数据，包含个人数据详情展示和多指标排行榜功能",
)
async def 获取团队成员详细绩效接口(
    请求数据: 团队成员详细绩效请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队成员详细绩效数据接口 - 第二阶段功能增强
    
    功能特性：
    1. 成员个人数据详情展示 - 包含微信运营、达人管理、邀约业务、样品管理等各项指标
    2. 多指标排行榜功能 - 支持好友数、达人数、邀约数等多个维度的排行榜
    3. 绩效等级和排名计算 - 为每个成员计算绩效等级和在团队中的排名
    4. 团队平均值统计 - 提供团队各项指标的平均值数据
    5. 统一数据格式 - 与工作台数据格式保持一致
    
    返回数据结构：
    {
        "指标卡片": [],  # 团队级别指标卡片
        "汇总数据": {
            "成员绩效": [],  # 成员详细绩效列表
            "平均值": {},    # 团队平均值数据
            "排行榜数据": {  # 多指标排行榜
                "好友数排行榜": [],
                "达人数排行榜": [],
                "邀约数排行榜": []
            }
        }
    }
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围
        
        接口日志器.info(
            f"🚀 团队成员详细绩效：开始获取团队 {团队id} 成员详细绩效数据，用户: {用户id}，时间范围: {时间范围}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队成员详细绩效数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入优化后的团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取成员详细绩效数据（包含个人数据详情和排行榜）
        成员绩效数据 = await 团队数据看板服务实例.获取成员绩效数据(
            团队id=团队id, 时间范围=时间范围 or "本周"
        )

        if "error" in 成员绩效数据:
            return 统一响应模型.失败(状态码=404, 消息=成员绩效数据["error"])

        # 构建工作台兼容的响应格式
        响应数据 = {
            "指标卡片": [],  # 可以根据需要添加团队级别的指标卡片
            "汇总数据": {
                "成员绩效": 成员绩效数据.get("成员列表", []),
                "平均值": 成员绩效数据.get("平均值", {}),
                "排行榜数据": 成员绩效数据.get("排行榜数据", {}),
                "成员总数": 成员绩效数据.get("成员总数", 0),
                "时间范围": 时间范围,
                "更新时间": 成员绩效数据.get("更新时间"),
                "数据说明": "团队成员详细绩效数据，包含个人数据详情和多指标排行榜",
            },
        }

        接口日志器.info(
            f"✅ 团队成员详细绩效：用户 {用户id} 获取团队 {团队id} 成员详细绩效数据成功，共 {响应数据['汇总数据']['成员总数']} 名成员"
        )
        return 统一响应模型.成功(数据=响应数据, 消息="获取团队成员详细绩效数据成功")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队成员详细绩效接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队成员详细绩效数据失败")


@用户团队数据看板路由.post(
    "/core-metrics",
    response_model=统一响应模型,
    summary="获取团队核心业务指标聚合数据",
    description="获取团队核心业务指标聚合数据，与工作台核心指标完全对标",
)
async def 获取团队核心业务指标聚合接口(
    请求数据: 团队数据看板聚合请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队核心业务指标聚合数据接口

    功能特性：
    1. 与工作台核心指标完全对标 - 确保数据计算逻辑一致
    2. 并行聚合成员数据 - 通过并行调用各成员的工作台核心指标方法
    3. 智能数据聚合 - 区分累计性指标和增量指标的聚合方式
    4. 完整指标覆盖 - 包含微信运营、达人管理、寄样管理等所有核心指标

    返回数据结构：
    {
        "微信运营指标": {
            "微信账号数量": 0,
            "好友总数": 0,
            "今日新增": 0,
            "发送好友请求数": 0,
            "沟通好友数": 0,
            "互动好友数": 0
        },
        "达人管理指标": {
            "总邀约数": 0,
            "总认领达人": 0,
            "微信认领达人": 0,
            "抖音认领达人": 0,
            "联系方式获取": 0,
            "好友转化": 0
        },
        "寄样管理指标": {
            "申请通过数量": 0,
            "实际寄样数量": 0,
            "样品送达数量": 0
        },
        "团队汇总": {
            "参与成员数": 0,
            "活跃成员数": 0,
            "总成员数": 0
        },
        "指标卡片": []
    }
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围 or "今日"

        接口日志器.info(
            f"🚀 团队核心业务指标聚合：开始获取团队 {团队id} 核心业务指标聚合数据，用户: {用户id}，时间范围: {时间范围}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队核心业务指标数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取团队核心业务指标聚合数据
        核心指标数据 = await 团队数据看板服务实例.获取团队核心业务指标聚合(
            团队id=团队id, 时间范围=时间范围
        )

        if not 核心指标数据.get("success"):
            return 统一响应模型.失败(状态码=404, 消息=核心指标数据.get("error", "获取核心业务指标聚合数据失败"))

        接口日志器.info(
            f"✅ 团队核心业务指标聚合：用户 {用户id} 获取团队 {团队id} 核心业务指标聚合数据成功"
        )
        return 统一响应模型.成功(
            数据=核心指标数据.get("data", {}),
            消息="获取团队核心业务指标聚合数据成功"
        )

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队核心业务指标聚合接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队核心业务指标聚合数据失败")


@用户团队数据看板路由.post(
    "/verify-deduplication",
    response_model=统一响应模型,
    summary="验证团队数据去重逻辑",
    description="验证团队数据看板的去重逻辑是否在数据库层面正确实现，确保数据准确性",
)
async def 验证团队数据去重逻辑接口(
    请求数据: 团队数据看板聚合请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    验证团队数据去重逻辑接口 - 确保数据库层面去重正确实现
    
    功能特性：
    1. 验证微信账号去重 - 确保同一微信账号不被重复统计
    2. 验证好友去重 - 确保同一好友不被重复统计  
    3. 验证达人去重 - 确保同一达人不被重复统计
    4. 数据一致性检查 - 确保团队汇总与成员汇总一致
    5. 详细的验证报告 - 提供具体的重复记录数和问题分析
    
    返回数据结构：
    {
        "团队id": 4,
        "验证时间": "2024-01-01T12:00:00",
        "去重验证": {
            "微信": {"总记录数": 10, "去重后数量": 8, "重复记录数": 2, "去重正确": false},
            "好友": {"总记录数": 100, "去重后数量": 95, "重复记录数": 5, "去重正确": false},
            "达人": {"总记录数": 50, "去重后数量": 50, "重复记录数": 0, "去重正确": true}
        },
        "数据一致性": false,
        "问题报告": ["微信数据存在2条重复记录，需要优化去重逻辑"]
    }
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        
        接口日志器.info(
            f"🔍 验证团队数据去重逻辑：开始验证团队 {团队id} 的数据去重逻辑，用户: {用户id}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限验证该团队数据去重逻辑"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据查询工具
        from 数据.团队数据看板查询 import 团队数据查询工具实例

        # 执行数据去重逻辑验证
        验证结果 = await 团队数据查询工具实例.验证团队数据去重逻辑(团队id)

        # 根据验证结果返回相应的响应
        if 验证结果["数据一致性"]:
            消息 = f"团队 {团队id} 数据去重逻辑验证通过，所有数据在数据库层面正确去重"
            接口日志器.info(f"✅ 验证团队数据去重逻辑：{消息}")
            return 统一响应模型.成功(数据=验证结果, 消息=消息)
        else:
            问题数量 = len(验证结果["问题报告"])
            消息 = f"团队 {团队id} 数据去重逻辑验证失败，发现 {问题数量} 个问题"
            接口日志器.warning(f"⚠️ 验证团队数据去重逻辑：{消息}")
            return 统一响应模型.成功(数据=验证结果, 消息=消息)

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"验证团队数据去重逻辑接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="验证团队数据去重逻辑失败")
