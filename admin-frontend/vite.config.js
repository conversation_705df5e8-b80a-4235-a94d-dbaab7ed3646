import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
  },
  optimizeDeps: {
    include: ['jsoneditor', 'json-editor-vue3'],
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 8080,      // 指定端口号
    proxy: {
      // 代理所有以 /admin 开头的请求
      '/admin': {
        target: 'http://127.0.0.1:8000', // 后端API服务地址
        changeOrigin: true,              // 必须设置为 true，这样后端服务器才会认为请求是同源的
        rewrite: (path) => path // 保持原路径不变
      },
      // 代理所有以 /team 开头的请求（团队管理API）
      '/team': {
        target: 'http://127.0.0.1:8000', // 后端API服务地址
        changeOrigin: true,              // 必须设置为 true，这样后端服务器才会认为请求是同源的
        rewrite: (path) => path // 保持原路径不变
      },
      // WebSocket代理配置
      '/limob/admin/ws/logs': {
        target: 'ws://127.0.0.1:8000',
        ws: true,
        changeOrigin: true,
      },
    }
  }
});