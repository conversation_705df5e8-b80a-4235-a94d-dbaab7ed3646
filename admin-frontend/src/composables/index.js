/**
 * Composables统一导出
 * 优化后的统一API请求处理Hook导出
 *
 * 注意：useApi.js 和 useApiResponse.js 已被删除并合并到 useApiRequest.js
 * 所有功能现在通过 useApiRequest 及其变体提供
 */

// 导入统一的API请求Hook
import {
  useApiRequest,
  useSuperAdminRequest,
  useSilentRequest,
  useNotificationRequest,
  useApiEndpoint,
  useBatchApiRequest
} from './useApiRequest';

// 导入其他工具Hook
import { useFormValidation } from './useFormValidation';

// 统一导出API请求相关Hook
export {
  useApiRequest,
  useSuperAdminRequest,
  useSilentRequest,
  useNotificationRequest,
  useApiEndpoint,
  useBatchApiRequest
};

// 导出工具Hook
export {
  useFormValidation
};

// 兼容性导出（向后兼容旧的命名）
// 注意：这些都指向新的统一Hook实现
export {
  useApiRequest as useApi,
  useSuperAdminRequest as useApiResponse,
  useSuperAdminRequest as useSuperAdminResponse
};

// 默认导出
export default {
  // 统一的API请求Hook
  useApiRequest,
  useSuperAdminRequest,
  useSilentRequest,
  useNotificationRequest,
  useApiEndpoint,
  useBatchApiRequest,

  // 工具Hook
  useFormValidation,

  // 兼容性别名
  useApi: useApiRequest,
  useApiResponse: useSuperAdminRequest,
  useSuperAdminResponse: useSuperAdminRequest
};
