/* AgentEditor 基础布局样式 */

.agent-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 顶部操作栏 */
.editor-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 60px;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.agent-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 400px;
}

.agent-name-input {
  font-size: 16px;
  font-weight: 600;
  border: 1px solid transparent;
  background: transparent;
  transition: all 0.3s ease;
  color: #1890ff;
}

.agent-name-input::placeholder {
  color: #bfbfbf;
}

.agent-name-input:hover {
  border-color: #e8e8e8;
  background: #fafafa;
}

.agent-name-input:focus {
  border-color: #1890ff;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.id-tag {
  font-size: 11px;
  font-weight: normal;
}

.agent-description-section {
  flex: 1;
  max-width: 500px;
  margin-left: 16px;
}

.description-input {
  font-size: 12px;
  color: #666;
  border: 1px solid transparent;
  background: transparent;
  transition: all 0.3s ease;
}

.description-input::placeholder {
  color: #bfbfbf;
}

.description-input:hover {
  border-color: #e8e8e8;
  background: #fafafa;
}

.description-input:focus {
  border-color: #1890ff;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.status-tags {
  display: flex;
  align-items: center;
  gap: 4px;
}

.back-btn {
  color: #666;
  transition: color 0.3s ease;
}

.back-btn:hover {
  color: #1890ff;
  background: #f0f9ff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn, .reset-btn, .test-btn {
  color: #666;
  transition: all 0.3s ease;
  border: none;
  background: transparent;
  padding: 4px 8px;
  border-radius: 4px;
}

.tool-btn:hover, .reset-btn:hover, .test-btn:hover {
  color: #1890ff;
  background: #f0f9ff;
}

.save-btn {
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.save-btn:hover {
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

/* 三栏布局 */
.editor-content {
  display: grid;
  grid-template-columns: 350px minmax(400px, 1fr) minmax(350px, 400px);
  gap: 0;
  flex: 1;
  overflow: hidden;
  background: #f5f5f5;
}

.prompt-panel {
  background: white;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  padding: 0;
}

.config-panel {
  background: white;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  padding: 0;
}

.chat-panel {
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
/* 超宽屏优化 (3440px+ 带鱼屏) */
@media (min-width: 3440px) {
  .editor-content {
    grid-template-columns: 500px minmax(800px, 1fr) minmax(500px, 700px);
  }
}

/* 大带鱼屏优化 (2560px+) */
@media (min-width: 2560px) {
  .editor-content {
    grid-template-columns: 450px minmax(600px, 1fr) minmax(450px, 600px);
  }
}

/* 标准大屏优化 (1920px+) */
@media (min-width: 1920px) {
  .editor-content {
    grid-template-columns: 420px minmax(550px, 1fr) minmax(420px, 520px);
  }
}

/* 中大屏优化 (1600px+) */
@media (min-width: 1600px) {
  .editor-content {
    grid-template-columns: 400px minmax(500px, 1fr) minmax(400px, 450px);
  }
}

@media (max-width: 1400px) {
  .editor-content {
    grid-template-columns: 320px minmax(350px, 1fr) minmax(300px, 380px);
  }
}

@media (max-width: 1200px) {
  .editor-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }
  
  .prompt-panel {
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    max-height: 300px;
  }
  
  .config-panel {
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
    max-height: 400px;
  }
  
  .chat-panel {
    min-height: 300px;
  }
}

/* 全局状态栏 */
.global-status-bar {
  position: fixed;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 8px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  animation: slideDown 0.3s ease;
}

.global-status-bar.success {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 1px solid #b7eb8f;
  color: #389e0d;
}

.global-status-bar.error {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  border: 1px solid #ffccc7;
  color: #cf1322;
}

.global-status-bar.warning {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  border: 1px solid #ffe58f;
  color: #d48806;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
.prompt-panel::-webkit-scrollbar,
.config-panel::-webkit-scrollbar {
  width: 6px;
}

.prompt-panel::-webkit-scrollbar-track,
.config-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.prompt-panel::-webkit-scrollbar-thumb,
.config-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.prompt-panel::-webkit-scrollbar-thumb:hover,
.config-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
