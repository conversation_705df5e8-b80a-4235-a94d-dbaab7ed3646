/* 工具配置样式 - 统一版本，移除重复 */

.tool-status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-count {
  color: #666;
  font-size: 12px;
}

/* 工具配置容器 */
.tools-config-container {
  padding: 16px;
}

.tools-overview {
  margin-bottom: 24px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* 统一工具列表样式 */
.unified-tools-list {
  margin-bottom: 24px;
}

.tools-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tool-item {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
  transition: all 0.3s ease;
}

.tool-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.tool-item.tool-enabled {
  border-color: #52c41a;
  background: #f6ffed;
}

.tool-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.tool-info {
  flex: 1;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tool-name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
}

.tool-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.tool-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-config {
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-label {
  font-size: 12px;
  color: #666;
  min-width: 50px;
}

/* 批量测试区域 */
.batch-tool-test {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e8e8e8;
}

.batch-tool-test .test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.batch-tool-test .test-header h4 {
  margin: 0;
  color: #52c41a;
  font-size: 16px;
}

.batch-tool-test .preview-card {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.batch-tool-test .json-preview-content {
  background: #ffffff;
  border: 1px solid #d9f7be;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #262626;
  max-height: 300px;
  overflow-y: auto;
}

.batch-tool-test .preview-actions {
  margin-top: 12px;
  text-align: center;
}

/* 工具测试结果 */
.test-result {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.test-result.success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.test-result.error {
  background: #fff2f0;
  border-color: #ffccc7;
}

/* 工具测试输出 */
.tool-test-output {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

/* 智能体工具调用测试 */
.agent-tool-test-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.agent-tool-test-result {
  margin-top: 16px;
}

.agent-tool-test-output {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.agent-response {
  background: #f0f8ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.tool-calls-info {
  margin-top: 16px;
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.tool-call-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
}

.tool-call-result {
  margin-left: 12px;
  padding: 8px;
  background: #f6ffed;
  border: 1px solid #d9f7be;
  border-radius: 4px;
  font-size: 12px;
}

.performance-info {
  margin-top: 12px;
  padding: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

/* 可用工具区域 */
.available-tools {
  margin-bottom: 24px;
}

.available-tools h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #262626;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.tool-card {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.tool-card.selected {
  border-color: #52c41a;
  background: #f6ffed;
}

/* 已启用工具区域 */
.enabled-tools {
  margin-bottom: 24px;
}

.enabled-tools h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #262626;
}

.enabled-tools-list {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
}

.enabled-tool-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.enabled-tool-item:last-child {
  border-bottom: none;
}

.enabled-tool-item .tool-info {
  flex: 1;
}

.enabled-tool-item .tool-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
