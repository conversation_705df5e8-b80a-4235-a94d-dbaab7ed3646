<template>
  <a-card title="用户活跃度统计" :loading="loading" class="user-login-stats">
    <template #extra>
      <a-tooltip title="刷新数据">
        <a-button type="text" :icon="h(ReloadOutlined)" @click="fetchStats" :loading="loading" />
      </a-tooltip>
    </template>
    
    <div class="stats-grid">
      <!-- 总体统计 -->
      <div class="stat-card total-users">
        <div class="stat-icon">
          <UserOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      
      <!-- 在线用户 -->
      <div class="stat-card online-users">
        <div class="stat-icon online">
          <WifiOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.onlineUsers }}</div>
          <div class="stat-label">在线用户</div>
          <div class="stat-percent">{{ getPercentage(stats.onlineUsers, stats.totalUsers) }}%</div>
        </div>
      </div>
      
      <!-- 最近活跃 -->
      <div class="stat-card active-users">
        <div class="stat-icon active">
          <ClockCircleOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.recentlyActiveUsers }}</div>
          <div class="stat-label">最近活跃</div>
          <div class="stat-percent">{{ getPercentage(stats.recentlyActiveUsers, stats.totalUsers) }}%</div>
        </div>
      </div>
      
      <!-- 不活跃用户 -->
      <div class="stat-card inactive-users">
        <div class="stat-icon inactive">
          <ExclamationCircleOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.inactiveUsers }}</div>
          <div class="stat-label">不活跃用户</div>
          <div class="stat-percent">{{ getPercentage(stats.inactiveUsers, stats.totalUsers) }}%</div>
        </div>
      </div>
    </div>
    
    <!-- 活跃度分布图表 -->
    <a-divider />
    <div class="activity-chart">
      <h4>用户活跃度分布</h4>
      <div class="chart-container">
        <div class="chart-bar" v-for="(item, index) in chartData" :key="index">
          <div class="bar-container">
            <div 
              class="bar-fill" 
              :style="{ 
                height: `${item.percentage}%`, 
                backgroundColor: item.color 
              }"
            ></div>
          </div>
          <div class="bar-label">{{ item.label }}</div>
          <div class="bar-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
    
    <!-- 最近登录用户 -->
    <a-divider />
    <div class="recent-logins">
      <h4>最近登录用户</h4>
      <a-list 
        :data-source="recentLoginUsers" 
        size="small"
        :loading="recentLoginsLoading"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :src="item.avatar" :style="{ backgroundColor: getAvatarColor(item.昵称) }">
                  {{ item.昵称?.charAt(0)?.toUpperCase() }}
                </a-avatar>
              </template>
              <template #title>
                <span>{{ item.昵称 }}</span>
                <a-tag 
                  :color="getLoginStatusColor(item.login_status)" 
                  size="small" 
                  style="margin-left: 8px;"
                >
                  {{ getLoginStatusText(item.login_status) }}
                </a-tag>
              </template>
              <template #description>
                <div class="login-info">
                  <span>{{ formatDate(item.last_login_time) }}</span>
                  <span class="ip-address">{{ item.ip_address }}</span>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </a-card>
</template>

<script setup>
import {computed, h, onMounted, ref} from 'vue';
import {
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  UserOutlined,
  WifiOutlined
} from '@ant-design/icons-vue';
import {useApiService} from '../composables/useApiService';
import dayjs from 'dayjs';

const loading = ref(false);
const recentLoginsLoading = ref(false);

// 统计数据
const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  recentlyActiveUsers: 0,
  inactiveUsers: 0,
  longInactiveUsers: 0,
  neverLoginUsers: 0
});

// 最近登录用户
const recentLoginUsers = ref([]);

// 使用API服务
const { executeRequest } = useApiService('userManagement');

// 计算百分比
const getPercentage = (value, total) => {
  if (total === 0) return 0;
  return Math.round((value / total) * 100);
};

// 图表数据
const chartData = computed(() => [
  {
    label: '在线',
    value: stats.value.onlineUsers,
    percentage: getPercentage(stats.value.onlineUsers, stats.value.totalUsers),
    color: '#52c41a'
  },
  {
    label: '最近活跃',
    value: stats.value.recentlyActiveUsers,
    percentage: getPercentage(stats.value.recentlyActiveUsers, stats.value.totalUsers),
    color: '#1890ff'
  },
  {
    label: '不活跃',
    value: stats.value.inactiveUsers,
    percentage: getPercentage(stats.value.inactiveUsers, stats.value.totalUsers),
    color: '#faad14'
  },
  {
    label: '长期不活跃',
    value: stats.value.longInactiveUsers,
    percentage: getPercentage(stats.value.longInactiveUsers, stats.value.totalUsers),
    color: '#ff4d4f'
  },
  {
    label: '从未登录',
    value: stats.value.neverLoginUsers,
    percentage: getPercentage(stats.value.neverLoginUsers, stats.value.totalUsers),
    color: '#d9d9d9'
  }
]);

// 判断用户登录状态
const judgeLoginStatus = (lastLoginTime) => {
  if (!lastLoginTime) return 'never_login';
  
  const now = dayjs();
  const lastLogin = dayjs(lastLoginTime);
  const diffHours = now.diff(lastLogin, 'hour');
  
  if (diffHours <= 1) return 'online';
  if (diffHours <= 24) return 'recently_active';
  if (diffHours <= 168) return 'inactive'; // 7天内
  return 'long_inactive';
};

// 获取登录状态颜色
const getLoginStatusColor = (loginStatus) => {
  const colorMap = {
    'online': 'green',
    'recently_active': 'blue', 
    'inactive': 'orange',
    'long_inactive': 'red',
    'never_login': 'default'
  };
  return colorMap[loginStatus] || 'default';
};

// 获取登录状态文本
const getLoginStatusText = (loginStatus) => {
  const textMap = {
    'online': '在线',
    'recently_active': '最近活跃',
    'inactive': '不活跃',
    'long_inactive': '长期不活跃',
    'never_login': '从未登录'
  };
  return textMap[loginStatus] || '未知';
};

// 获取头像颜色
const getAvatarColor = (昵称) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068', '#108ee9'];
  const index = 昵称 ? 昵称.charCodeAt(0) % colors.length : 0;
  return colors[index];
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '从未登录';
  return dayjs(dateString).format('MM-DD HH:mm');
};

// 获取用户登录统计数据
const 获取用户登录统计 = async () => {
  loading.value = true;
  try {
    console.log('开始获取用户登录统计数据...');
    
    // 简化请求参数，只传递必要的参数
    const 请求参数 = {
      page: 1,
      size: 100  // 增加数量以获取更多数据用于统计
    };

    console.log('发送的请求参数:', 请求参数);

    // 获取用户列表数据来计算统计
    const 响应数据 = await executeRequest('getUserList', [请求参数]);

    console.log('用户列表API响应:', 响应数据);

    if (response && response.status === 100 && response.data) {
      const responsePayload = response.data;
      if (responsePayload && responsePayload.items && Array.isArray(responsePayload.items)) {
        const users = responsePayload.items;
        console.log(`获取到 ${users.length} 个用户数据`);
        
        // 计算各种状态的用户数量
        const statusCounts = {
          online: 0,
          recently_active: 0,
          inactive: 0,
          long_inactive: 0,
          never_login: 0
        };

        users.forEach(user => {
          // 使用last_login_time字段（后端返回的标准字段名）
          const lastLoginTime = user.last_login_time || user.last_login || user.上次登录时间;
          const status = judgeLoginStatus(lastLoginTime);
          statusCounts[status]++;
        });

        console.log('用户状态统计:', statusCounts);

        stats.value = {
          totalUsers: users.length,
          onlineUsers: statusCounts.online,
          recentlyActiveUsers: statusCounts.recently_active,
          inactiveUsers: statusCounts.inactive,
          longInactiveUsers: statusCounts.long_inactive,
          neverLoginUsers: statusCounts.never_login
        };

        // 获取最近登录的用户（前10个）
        const recentUsers = users
          .filter(user => {
            const lastLoginTime = user.last_login_time || user.last_login || user.上次登录时间;
            return lastLoginTime;
          })
          .sort((a, b) => {
            const timeA = a.last_login_time || a.last_login || a.上次登录时间;
            const timeB = b.last_login_time || b.last_login || b.上次登录时间;
            return new Date(timeB) - new Date(timeA);
          })
          .slice(0, 10)
          .map(user => ({
            ...user,
            login_status: judgeLoginStatus(user.last_login_time || user.last_login || user.上次登录时间)
          }));

        console.log('最近登录用户:', recentUsers);
        recentLoginUsers.value = recentUsers;

      } else {
        console.warn('用户列表API响应格式异常，items字段缺失或不是数组:', responsePayload);
        
        // 设置默认值
        stats.value = {
          totalUsers: 0,
          onlineUsers: 0,
          recentlyActiveUsers: 0,
          inactiveUsers: 0,
          longInactiveUsers: 0,
          neverLoginUsers: 0
        };
        recentLoginUsers.value = [];
      }
    } else {
      console.warn('用户列表API响应格式异常:', response);
      
      // 设置默认值
      stats.value = {
        totalUsers: 0,
        onlineUsers: 0,
        recentlyActiveUsers: 0,
        inactiveUsers: 0,
        longInactiveUsers: 0,
        neverLoginUsers: 0
      };
      recentLoginUsers.value = [];
    }
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    console.error('错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      statusText: error.response?.statusText
    });
    
    // 设置默认值，避免页面显示异常
    stats.value = {
      totalUsers: 0,
      onlineUsers: 0,
      recentlyActiveUsers: 0,
      inactiveUsers: 0,
      longInactiveUsers: 0,
      neverLoginUsers: 0
    };
    recentLoginUsers.value = [];
    
    // 显示用户友好的错误提示
    console.warn('用户统计数据获取失败，显示默认数据');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  获取用户登录统计();
});
</script>

<style scoped>
.user-login-stats {
  height: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
  padding: 12px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #666;
}

.stat-icon.online {
  background: #f6ffed;
  color: #52c41a;
}

.stat-icon.active {
  background: #e6f7ff;
  color: #1890ff;
}

.stat-icon.inactive {
  background: #fffbe6;
  color: #faad14;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-top: 4px;
}

.stat-percent {
  font-size: 12px;
  color: #1890ff;
  margin-top: 2px;
}

.activity-chart h4 {
  margin-bottom: 16px;
  color: #262626;
}

.chart-container {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 120px;
  padding: 0 16px;
  background: #fafafa;
  border-radius: 8px;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 60px;
}

.bar-container {
  height: 80px;
  width: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  margin-bottom: 8px;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-radius: 10px;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
  margin-bottom: 4px;
}

.bar-value {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  text-align: center;
}

.recent-logins h4 {
  margin-bottom: 16px;
  color: #262626;
}

.login-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #8c8c8c;
}

.ip-address {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}
</style> 