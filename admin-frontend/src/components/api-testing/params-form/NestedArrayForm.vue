<template>
  <div class="nested-array-form">
    <div v-if="arrayValidations && Object.keys(arrayValidations).length > 0" class="array-validation-summary">
      <small>数组整体校验: {{ formatArrayValidationSummary(arrayValidations) }}</small>
    </div>
    <a-form :model="arrayItems">
      <div class="array-items">
        <div v-for="(itemData, index) in arrayItems" :key="index" class="array-item">
          <div class="array-item-content">
            <a-tooltip placement="topLeft" class="array-item-label-tooltip">
              <template #title>
                <div style="white-space: pre-line;">{{ formatItemSchemaDetailsForTooltip(itemSchema) }}</div>
              </template>
              <span>项目 {{ index + 1 }} (类型: {{ itemSchema.oasType || itemSchema.type }})</span>
            </a-tooltip>
            
            <nested-object-form
              v-if="itemSchema.type === 'object' && itemSchema.children && itemSchema.children.length > 0"
              :fields="itemSchema.children"
              :value="itemData" 
              @change="(val) => updateItem(index, val)"
              class="nested-object-in-array"
            />
            <nested-array-form 
              v-else-if="itemSchema.type === 'array' && itemSchema.itemSchema"
              :item-schema="itemSchema.itemSchema" 
              :array-validations="itemSchema.validations"
              :value="itemData || []" 
              @change="(val) => updateItem(index, val)"
              class="nested-array-in-array"
            />
            <a-form-item 
              v-else 
              :name="index" 
              :rules="generateAntdValidationRulesForItem(itemSchema)"
              class="simple-item-form-item" 
            >
              <a-input 
                v-if="itemSchema.type === 'string' || itemSchema.type === 'url' || itemSchema.type === 'email'" 
                v-model:value="arrayItems[index]" 
                :placeholder="itemSchema.placeholder || '输入字符串值'"
                allow-clear
                @change="emitUpdate"
              />
              <a-input-number 
                v-else-if="itemSchema.type === 'number' || itemSchema.type === 'integer'" 
                v-model:value="arrayItems[index]" 
                :placeholder="itemSchema.placeholder || '输入数值'"
                :precision="itemSchema.oasType === 'integer' ? 0 : undefined"
                style="width: 100%;"
                @change="emitUpdate"
              />
              <a-switch 
                v-else-if="itemSchema.type === 'boolean'" 
                v-model:checked="arrayItems[index]" 
                @change="emitUpdate"
              />
              <a-date-picker
                v-else-if="itemSchema.type === 'date'"
                v-model:value="arrayItems[index]"
                style="width: 100%;"
                valueFormat="YYYY-MM-DD"
                @change="emitUpdate"
              />
              <a-date-picker
                v-else-if="itemSchema.type === 'datetime'"
                v-model:value="arrayItems[index]"
                show-time
                style="width: 100%;"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                @change="emitUpdate"
              />
              <a-select
                v-else-if="itemSchema.options && itemSchema.options.length > 0"
                v-model:value="arrayItems[index]"
                :placeholder="itemSchema.placeholder || '请选择'"
                style="width: 100%;"
                allow-clear
                @change="emitUpdate"
              >
                <a-select-option v-for="option in itemSchema.options" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
              <a-textarea 
                v-else
                v-model:value="arrayItems[index]" 
                :placeholder="itemSchema.placeholder || '输入值 (类型: ' + itemSchema.type + ')' "
                rows="3"
                allow-clear
                @change="emitUpdate"
              />
            </a-form-item>
          </div>
          <div class="array-item-actions">
            <a-button type="text" danger @click="removeItem(index)">删除</a-button>
          </div>
        </div>
      </div>
    </a-form>
    <div class="array-actions">
      <a-button type="dashed" block @click="addItem">
        <plus-outlined /> 添加项目
      </a-button>
    </div>
  </div>
</template>

<script setup>
import {ref, watch} from 'vue';
import {
  Button as AButton,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Select as ASelect,
  SelectOption as ASelectOption,
  Switch as ASwitch,
  Textarea as ATextarea,
  Tooltip as ATooltip,
} from 'ant-design-vue';
import {PlusOutlined} from '@ant-design/icons-vue';
import NestedObjectForm from './NestedObjectForm.vue';

const props = defineProps({
  itemSchema: {
    type: Object,
    required: true
  },
  arrayValidations: {
    type: Object,
    default: () => ({})
  },
  value: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['change']);

// 数组项列表
const arrayItems = ref([]);

// 监听value变化，更新本地数据
watch(() => props.value, (newValue) => {
  arrayItems.value = [...(newValue || [])];
}, { immediate: true, deep: true });

// 向父组件发送更新事件
const emitUpdate = () => {
  emit('change', [...arrayItems.value]);
};

// 添加新项目
const addItem = () => {
  let defaultValue;
  const schema = props.itemSchema;
  
  if (schema.type === 'object' && schema.children && schema.children.length > 0) {
    defaultValue = {};
  } else if (schema.type === 'array' && schema.itemSchema) {
    defaultValue = [];
  } else {
    if (schema.defaultValue !== undefined) {
      defaultValue = schema.defaultValue;
    } else {
      switch (schema.type) {
        case 'string':
        case 'url':
        case 'email':
        case 'date':
        case 'datetime':
          defaultValue = '';
          break;
        case 'number':
        case 'integer':
          defaultValue = null;
          break;
        case 'boolean':
          defaultValue = false;
          break;
        default:
          defaultValue = undefined;
      }
    }
  }
  
  arrayItems.value.push(defaultValue);
  emitUpdate();
};

// 更新特定项目
const updateItem = (index, newValue) => {
  arrayItems.value[index] = newValue;
  emitUpdate();
};

// 移除项目
const removeItem = (index) => {
  arrayItems.value.splice(index, 1);
  emitUpdate();
};

// Helper function to format item schema details for tooltip
const formatItemSchemaDetailsForTooltip = (schema) => {
  if (!schema) return '';
  const details = [];
  details.push('项定义');
  if (schema.oasType) {
    details.push(`类型: ${schema.oasType}${schema.oasFormat ? ` (${schema.oasFormat})` : ''}`);
  }
  if (schema.description) {
    details.push(`描述: ${schema.description}`);
  }
  if (schema.defaultValue !== undefined) {
    details.push(`默认值: ${JSON.stringify(schema.defaultValue)}`);
  }
  if (schema.validations) {
    const vRules = [];
    if (schema.validations.pattern) vRules.push(`模式: ${schema.validations.pattern}`);
    if (schema.validations.minLength !== undefined) vRules.push(`最小长度: ${schema.validations.minLength}`);
    if (schema.validations.maxLength !== undefined) vRules.push(`最大长度: ${schema.validations.maxLength}`);
    if (schema.validations.minimum !== undefined) vRules.push(`最小值: ${schema.validations.minimum}`);
    if (schema.validations.maximum !== undefined) vRules.push(`最大值: ${schema.validations.maximum}`);
    if (schema.validations.exclusiveMinimum !== undefined) vRules.push(`排他最小值: ${schema.validations.exclusiveMinimum}`);
    if (schema.validations.exclusiveMaximum !== undefined) vRules.push(`排他最大值: ${schema.validations.exclusiveMaximum}`);
    if (schema.validations.multipleOf !== undefined) vRules.push(`倍数: ${schema.validations.multipleOf}`);
    if (schema.validations.enum) vRules.push(`可选值: ${schema.validations.enum.join(', ')}`);
    if (schema.type === 'array') {
      if (schema.validations.minItems !== undefined) vRules.push(`项内最少数量: ${schema.validations.minItems}`);
      if (schema.validations.maxItems !== undefined) vRules.push(`项内最多数量: ${schema.validations.maxItems}`);
      if (schema.validations.uniqueItems) vRules.push(`项内元素唯一`);
    }
    if (vRules.length > 0) {
      details.push(`项校验规则:
  - ${vRules.join('\n  - ')}`);
    }
  }
  return details.join('\n\n');
};

// Helper function to generate Ant Design Vue validation rules for an array item
const generateAntdValidationRulesForItem = (itemSchema) => {
  if (!itemSchema) return [];
  const rules = [];

  // Note: itemSchema.required doesn't make sense for an array item itself,
  // as its presence is controlled by the array. Required fields *within* an object item
  // would be handled by the NestedObjectForm.
  // However, if an item *must* not be empty (e.g. non-empty string), that is a validation.

  if (itemSchema.type === 'email') {
    rules.push({ type: 'email', message: '请输入有效的邮箱地址' });
  }
  if (itemSchema.type === 'url') {
    rules.push({ type: 'url', message: '请输入有效的URL地址' });
  }

  if (itemSchema.validations) {
    const vals = itemSchema.validations;
    // Basic value presence for simple types if they are effectively required (e.g. cannot be empty string if minLength=1)
    if (vals.minLength === 1 && (itemSchema.type === 'string' || itemSchema.type === 'text')) {
        rules.push({ required: true, message: `此项不能为空字符串`, whitespace: true }); 
    } else if (vals.minLength !== undefined && vals.minLength > 0) {
        rules.push({ min: vals.minLength, message: `长度不能少于 ${vals.minLength} 个字符`, trigger: 'blur' });
    } else if (itemSchema.type === 'string' && vals.minLength === undefined && itemSchema.requiredInParentAsItem) { 
        // If the item schema itself represents a required string in an array of strings
        rules.push({ required: true, message: `此字符串项不能为空`, whitespace: true });
    } 

    if (vals.maxLength !== undefined) {
      rules.push({ max: vals.maxLength, message: `长度不能超过 ${vals.maxLength} 个字符`, trigger: 'blur' });
    }
    if (vals.pattern) {
      try {
        const regex = new RegExp(vals.pattern);
        rules.push({ pattern: regex, message: `必须符合模式: ${vals.pattern}`, trigger: 'blur' });
      } catch (e) {
        console.warn(`Invalid regex pattern for item: ${vals.pattern}`, e);
      }
    }
    if (vals.minimum !== undefined) {
      rules.push({ type: itemSchema.oasType === 'integer' ? 'integer' : 'number', min: vals.minimum, message: `数值不能小于 ${vals.minimum}`, trigger: 'blur' });
    }
    if (vals.maximum !== undefined) {
      rules.push({ type: itemSchema.oasType === 'integer' ? 'integer' : 'number', max: vals.maximum, message: `数值不能大于 ${vals.maximum}`, trigger: 'blur' });
    }
    if (vals.enum && Array.isArray(vals.enum)) {
      rules.push({
        validator: (rule, value) => {
          if (value === undefined || value === null || value === '') return Promise.resolve(); 
          const enumValues = vals.enum;
          if (!enumValues.includes(value)) {
            return Promise.reject(`${value} 不在允许的值列表中: [${enumValues.join(', ')}]`);
          }
          return Promise.resolve();
        },
        message: `值必须是 [${vals.enum.join(', ')}] 中的一个`,
        trigger: 'change',
      });
    }
    // itemSchema.validations for minItems, maxItems, uniqueItems apply if the item itself is an array (nested array).
    // These are passed to the recursive NestedArrayForm via :array-validations="itemSchema.validations"
  }
  return rules;
};

// Helper to display array's own validations (minItems, maxItems of the parent array)
const formatArrayValidationSummary = (validations) => {
  if (!validations || Object.keys(validations).length === 0) return '';
  const summary = [];
  if (validations.minItems !== undefined) summary.push(`最少 ${validations.minItems} 项`);
  if (validations.maxItems !== undefined) summary.push(`最多 ${validations.maxItems} 项`);
  if (validations.uniqueItems) summary.push('项目需唯一');
  return summary.join(', ');
};
</script>

<style scoped>
.nested-array-form {
  padding: 8px;
}

.array-items {
  margin-bottom: 16px;
}

.array-item {
  display: flex;
  margin-bottom: 16px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  position: relative;
}

.array-item-content {
  flex: 1;
}

.array-item-label-tooltip {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.simple-item-form-item {
  margin-bottom: 0;
}

.nested-object-in-array,
.nested-array-in-array {
  border: 1px solid #e8e8e8;
  padding: 10px;
  border-radius: 3px;
  background-color: #fff;
}

.array-item-actions {
  margin-left: 8px;
  display: flex;
  align-items: flex-start;
}

.array-actions {
  margin-top: 8px;
}

.array-validation-summary {
  margin-bottom: 10px;
  padding: 5px;
  background-color: #f0f8ff;
  border-radius: 3px;
  font-size: 0.9em;
}
</style> 