<template>
  <div class="request-history-panel">
    <div class="history-header">
      <h4><history-outlined /> 请求历史</h4>
      <a-button 
        size="small" 
        danger 
        type="link" 
        @click="handleClearHistory"
        v-if="history.length > 0"
      >
        <template #icon><delete-outlined /></template>
        清空历史
      </a-button>
    </div>
    <a-list 
      item-layout="horizontal" 
      :data-source="history"
      size="small"
      class="history-list-container"
      v-if="history.length > 0"
    >
      <template #renderItem="{ item }">
        <a-list-item @click="() => loadFromHistory(item.id)" class="history-item">
          <a-list-item-meta>
            <template #title>
              <span :class="['status-dot', getStatusDotClass(item.statusType)]"></span>
              <span class="history-item-name">{{ item.name }}</span>
            </template>
            <template #description>
              <div class="history-item-details">
                <span>{{ item.method }} {{ item.path }}</span>
                <span>{{ formatTimestamp(item.timestamp) }}</span>
                <span>HTTP: {{ item.httpStatus }} <span v-if="item.fastApiStatus !== null && item.fastApiStatus !== item.httpStatus">| Biz: {{ item.fastApiStatus }}</span></span>
              </div>
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>
    <a-empty v-else description="暂无请求历史" />
  </div>
</template>

<script setup>
import {computed, createVNode} from 'vue';
import {
  Button as AButton,
  Empty as AEmpty,
  List as AList,
  ListItem as AListItem,
  ListItemMeta as AListItemMeta,
  message,
  Modal
} from 'ant-design-vue';
import {DeleteOutlined, ExclamationCircleOutlined, HistoryOutlined} from '@ant-design/icons-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';

const store = useApiTestingModule();

const history = computed(() => store.requestHistory);

const loadFromHistory = (historyId) => {
  store.loadRequestFromHistory(historyId);
  message.success('历史记录已加载');
};

const handleClearHistory = () => {
  Modal.confirm({
    title: '确认清空历史记录?',
    icon: createVNode(ExclamationCircleOutlined),
    content: '此操作不可撤销。',
    okText: '确认清空',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      store.clearRequestHistory();
      message.success('请求历史已清空');
    },
    onCancel() {},
  });
};

const formatTimestamp = (isoString) => {
  if (!isoString) return '';
  const date = new Date(isoString);
  return date.toLocaleString('zh-CN', { hour12: false });
};

const getStatusDotClass = (statusType) => {
  if (statusType === 'success') return 'status-success';
  if (statusType === 'error') return 'status-error';
  return 'status-info';
};

</script>

<style scoped>
.request-history-panel {
  display: flex;
  flex-direction: column;
  height: 100%; /* 如果父容器限制了高度 */
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 4px; /* 微调内边距 */
}

.history-header h4 {
  margin-bottom: 0;
  font-size: 1em; /* 与Sider的h3协调 */
  color: rgba(0,0,0,.85);
  display: flex;
  align-items: center;
}
.history-header h4 .anticon {
  margin-right: 6px;
}

.history-list-container {
  flex-grow: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0; /* 给列表一个边框 */
  border-radius: 2px;
}

.history-item {
  cursor: pointer;
  padding: 6px 10px !important; /* 调整列表项内边距 */
  border-bottom: 1px solid #f0f0f0; /* 分隔线 */
  transition: background-color 0.2s;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:hover {
  background-color: #f7f7f7;
}

.history-item-name {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px; /* 根据Sider宽度调整 */
  display: inline-block;
  vertical-align: middle;
}

.history-item-details span {
  display: block;
  font-size: 11px;
  color: #888;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}
.status-success { background-color: #52c41a; }
.status-error { background-color: #f5222d; }
.status-info { background-color: #1890ff; }

.ant-empty {
    padding: 20px 0;
}
</style> 