// admin-frontend/src/config/apiDefinitions.js

export const apiCategories = [
  { key: '用户', name: '用户管理', icon: 'user' }, // Ant Design Icon 名称
  { key: '产品', name: '产品解析', icon: 'appstore' },
  { key: '订单', name: '订单管理', icon: 'shopping-cart' },
  { key: 'AI', name: 'AI功能', icon: 'robot' },
  { key: '系统', name: '系统管理', icon: 'setting' },
  { key: '微信', name: '微信相关', icon: 'wechat' },
  { key: '知识库', name: '知识库', icon: 'book' },
  { key: '其他', name: '其他接口', icon: 'ellipsis' },
];

export const apiEndpoints = [
  // --- 用户管理 ---
  {
    id: 'user_login',
    category: '用户',
    name: '用户登录',
    path: '/api/v1/用户/登录', // 实际被测接口路径
    method: 'POST', 
    requiresAuth: false, // 用户登录本身不需要预先认证
    description: '通过手机号和密码进行用户登录。成功返回用户信息及Token。',
    params: [
      { name: '手机号', key: '手机号', type: 'string', required: true, defaultValue: '13800138000', placeholder: '请输入手机号' },
      { name: '密码', key: '密码', type: 'password', required: true, defaultValue: 'password123', placeholder: '请输入密码' }
    ],
    headers: [
      // { name: 'Content-Type', key: 'Content-Type', value: 'application/json', editable: false }, // 会由store自动添加
    ]
  },
  {
    id: 'user_register',
    category: '用户',
    name: '用户注册',
    path: '/api/v1/用户/注册',
    method: 'POST',
    requiresAuth: false, // 用户注册不需要预先认证
    description: '注册新用户。需要提供手机号、密码和验证码。',
    params: [
      { name: '手机号', key: '手机号', type: 'string', required: true, placeholder: '请输入手机号' },
      { name: '密码', key: '密码', type: 'password', required: true, placeholder: '请输入密码' },
      { name: '确认密码', key: '确认密码', type: 'password', required: true, placeholder: '请再次输入密码' },
      { name: '验证码', key: '验证码', type: 'string', required: true, placeholder: '请输入短信验证码' }
    ],
    headers: []
  },
  {
    id: 'get_user_info',
    category: '用户',
    name: '获取用户信息',
    path: '/api/v1/用户/信息', // 假设获取当前登录用户的信息，可能不需要参数或通过Token
    method: 'POST', // 严格按照规范，所有接口POST
    requiresAuth: true, // 获取用户信息通常需要认证
    description: '获取当前登录用户的详细信息。请求体可为空。',
    params: [], // 如果需要特定用户id，可以在此添加 { name: '用户id', key: '用户id', type: 'string' }
    headers: [
      // { name: 'Authorization', key: 'Authorization', value: 'Bearer YOUR_TOKEN', editable: true, placeholder: '在此处粘贴Token' }
    ]
  },

  // --- 产品解析 ---
  {
    id: 'product_parse_douyin',
    category: '产品',
    name: '解析抖音商品',
    path: '/api/v1/产品/解析抖音',
    method: 'POST',
    requiresAuth: false, // 假设此接口为开放接口，或有其他认证方式
    description: '输入抖音商品链接，返回解析后的商品信息。',
    params: [
      { name: '商品链接', key: '链接', type: 'url', required: true, placeholder: '请输入抖音商品链接' },
      { name: '选项', key: '选项', type: 'json_string', required: false, defaultValue: '{}', placeholder: '可选解析参数，JSON格式' }
    ],
    headers: []
  },

  // --- AI功能 ---
  {
    id: 'ai_chat_completion',
    category: 'AI',
    name: 'AI对话补全',
    path: '/api/v1/ai/对话补全',
    method: 'POST',
    requiresAuth: true, // AI功能通常需要认证以追踪用量或权限
    description: '与AI进行对话，获取补全回复。',
    params: [
      { name: '消息列表', key: 'messages', type: 'json_array_object', required: true, defaultValue: '[{"role": "user", "content": "你好"}]', placeholder: '遵循OpenAI格式的消息数组' },
      { name: '模型名称', key: 'model', type: 'string', required: false, defaultValue: 'gpt-3.5-turbo', placeholder: '例如 gpt-4, gpt-3.5-turbo' },
      { name: '最大Token数', key: 'max_tokens', type: 'integer', required: false, defaultValue: 150, placeholder: '生成内容的最大长度' }
    ],
    headers: []
  },
  
  // 更多接口定义...
];

/**
 * 参数类型说明:
 * string: 普通字符串输入框 (a-input)
 * password: 密码输入框 (a-input-password)
 * number: 数字输入框 (a-input-number)
 * integer: 整数输入框 (a-input-number with precision 0)
 * boolean: 开关 (a-switch)
 * text: 多行文本框 (a-textarea)
 * json_string: JSON字符串输入，可用多行文本框 (a-textarea with validation)
 * json_object: (高级) 未来可支持更复杂的表单生成器来编辑对象，目前同json_string
 * json_array_object: (高级) 未来可支持更复杂的表单生成器来编辑对象数组，目前同json_string
 * url: URL输入框 (a-input with type url)
 * email: 邮箱输入框 (a-input with type email)
 * date: 日期选择器 (a-date-picker)
 * datetime: 日期时间选择器 (a-date-picker with showTime)
 * select: 下拉选择框 (a-select)，需要配合 options: [{label: '', value: ''}] 属性
 */ 