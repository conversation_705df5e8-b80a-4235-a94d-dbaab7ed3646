<template>
  <div class="api-call-log">
    <!-- 页面头部 -->
    <div class="log-header">
      <h1>接口调用日志</h1>
      <div class="header-actions">
        <a-space>
          <a-button @click="刷新日志数据" :loading="loading" type="primary">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportLogs" :loading="exporting">
            <template #icon><DownloadOutlined /></template>
            导出
          </a-button>
          <a-switch v-model:checked="autoRefresh" checked-children="自动刷新" un-checked-children="手动刷新" />
        </a-space>
      </div>
    </div>

    <a-card title="日志查询" class="search-card">
      <!-- 快速筛选 -->
      <div class="quick-filters">
        <a-space wrap>
          <a-tag 
            v-for="filter in quickFilters" 
            :key="filter.key"
            :color="activeQuickFilter === filter.key ? 'blue' : 'default'"
            @click="应用快速筛选(filter)"
            style="cursor: pointer; padding: 4px 12px;"
          >
            {{ filter.label }}
          </a-tag>
        </a-space>
      </div>
      
      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item label="请求路径">
            <a-input 
              v-model:value="searchFormState.path" 
              placeholder="请输入请求路径" 
              style="width: 200px"
              allow-clear
            >
              <template #prefix><ApiOutlined /></template>
            </a-input>
          </a-form-item>
          
          <a-form-item label="请求方法">
            <a-select 
              v-model:value="searchFormState.method" 
              placeholder="选择请求方法" 
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="GET">GET</a-select-option>
              <a-select-option value="POST">POST</a-select-option>
              <a-select-option value="PUT">PUT</a-select-option>
              <a-select-option value="DELETE">DELETE</a-select-option>
              <a-select-option value="PATCH">PATCH</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="状态码">
            <a-select 
              v-model:value="searchFormState.statusCode" 
              placeholder="选择状态码" 
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="200">200 成功</a-select-option>
              <a-select-option value="400">400 请求错误</a-select-option>
              <a-select-option value="401">401 未授权</a-select-option>
              <a-select-option value="403">403 禁止访问</a-select-option>
              <a-select-option value="404">404 未找到</a-select-option>
              <a-select-option value="500">500 服务器错误</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="用户">
            <a-input 
              v-model:value="searchFormState.user" 
              placeholder="请输入用户名" 
              style="width: 150px"
              allow-clear
            >
              <template #prefix><UserOutlined /></template>
            </a-input>
          </a-form-item>
          
          <a-form-item label="IP地址">
            <a-input 
              v-model:value="searchFormState.ip" 
              placeholder="请输入IP地址" 
              style="width: 150px"
              allow-clear
            />
          </a-form-item>
          
          <a-form-item label="时间范围">
            <a-range-picker 
              v-model:value="searchFormState.dateRange" 
              style="width: 300px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              :presets="datePresets"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="获取API调用日志" :loading="loading">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ClearOutlined /></template>
                重置
              </a-button>
              <a-button @click="toggleAdvancedSearch" type="dashed">
                <template #icon><SettingOutlined /></template>
                {{ showAdvancedSearch ? '收起' : '高级' }}
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
        
        <!-- 高级搜索 -->
        <div v-if="showAdvancedSearch" class="advanced-search">
          <a-divider>高级搜索</a-divider>
          <a-form layout="inline" :model="advancedSearchState">
            <a-form-item label="响应时间">
              <a-input-group compact>
                <a-input-number 
                  v-model:value="advancedSearchState.minResponseTime" 
                  placeholder="最小值" 
                  style="width: 80px"
                  :min="0"
                />
                <a-input style="width: 30px; text-align: center; pointer-events: none" placeholder="~" disabled />
                <a-input-number 
                  v-model:value="advancedSearchState.maxResponseTime" 
                  placeholder="最大值" 
                  style="width: 80px"
                  :min="0"
                />
              </a-input-group>
              <span style="margin-left: 8px; color: #666;">ms</span>
            </a-form-item>
            
            <a-form-item label="错误关键词">
              <a-input 
                v-model:value="advancedSearchState.errorKeyword" 
                placeholder="错误信息关键词" 
                style="width: 200px"
                allow-clear
              />
            </a-form-item>
            
            <a-form-item label="排序方式">
              <a-select 
                v-model:value="advancedSearchState.sortBy" 
                style="width: 150px"
              >
                <a-select-option value="timestamp_desc">时间倒序</a-select-option>
                <a-select-option value="timestamp_asc">时间正序</a-select-option>
                <a-select-option value="response_time_desc">响应时间倒序</a-select-option>
                <a-select-option value="response_time_asc">响应时间正序</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-card>
    
    <!-- 统计信息 -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic title="总请求数" :value="stats.totalRequests" :value-style="{ color: '#1890ff' }">
            <template #prefix><ApiOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic title="成功率" :value="stats.successRate" suffix="%" :value-style="{ color: '#52c41a' }">
            <template #prefix><CheckCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic title="平均响应时间" :value="stats.avgResponseTime" suffix="ms" :value-style="{ color: '#faad14' }">
            <template #prefix><ClockCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic title="错误数" :value="stats.errorCount" :value-style="{ color: '#ff4d4f' }">
            <template #prefix><ExclamationCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>
      
    <!-- 数据表格 -->
    <a-card title="日志列表" class="table-card">
      <template #extra>
        <a-space>
          <span class="result-count">共 {{ pagination.total }} 条记录</span>
          <a-select v-model:value="pagination.pageSize" @change="handlePageSizeChange" size="small">
            <a-select-option :value="10">10条/页</a-select-option>
            <a-select-option :value="20">20条/页</a-select-option>
            <a-select-option :value="50">50条/页</a-select-option>
            <a-select-option :value="100">100条/页</a-select-option>
          </a-select>
        </a-space>
      </template>
      
      <a-table 
        :columns="columns" 
        :data-source="dataSource" 
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
        size="middle"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'method'">
            <a-tag :color="getMethodColor(record.method)">{{ record.method }}</a-tag>
          </template>
          
          <template v-else-if="column.key === 'path'">
            <a-tooltip :title="record.path">
              <span class="path-text">{{ truncateText(record.path, 30) }}</span>
            </a-tooltip>
          </template>
          
          <template v-else-if="column.key === 'statusCode'">
            <a-tag :color="getStatusColor(record.statusCode)">
              {{ record.statusCode }}
              <span class="status-text">{{ getStatusText(record.statusCode) }}</span>
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'responseTime'">
            <span :class="getResponseTimeClass(record.responseTime)">
              {{ record.responseTime }}ms
              <a-progress 
                :percent="getResponseTimePercent(record.responseTime)" 
                :show-info="false" 
                size="small" 
                :stroke-color="getResponseTimeColor(record.responseTime)"
                style="width: 60px; margin-left: 8px;"
              />
            </span>
          </template>
          
          <template v-else-if="column.key === 'timestamp'">
            <a-tooltip :title="record.timestamp">
              {{ formatRelativeTime(record.timestamp) }}
            </a-tooltip>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showLogDetail(record)">
                <template #icon><EyeOutlined /></template>
                详情
              </a-button>
              <a-button type="link" size="small" @click="copyLogInfo(record)">
                <template #icon><CopyOutlined /></template>
                复制
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 日志详情弹窗 -->
    <a-modal 
      v-model:open="detailModalVisible" 
      title="接口调用详情" 
      width="900px"
      :footer="null"
      class="detail-modal"
    >
      <div v-if="selectedLog" class="log-detail">
        <a-tabs v-model:activeKey="detailActiveTab">
          <a-tab-pane key="basic" tab="基本信息">
            <a-descriptions :column="2" bordered size="small">
              <a-descriptions-item label="请求ID">{{ selectedLog.id }}</a-descriptions-item>
              <a-descriptions-item label="请求时间">{{ selectedLog.timestamp }}</a-descriptions-item>
              <a-descriptions-item label="请求路径">{{ selectedLog.path }}</a-descriptions-item>
              <a-descriptions-item label="请求方法">
                <a-tag :color="getMethodColor(selectedLog.method)">{{ selectedLog.method }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="状态码">
                <a-tag :color="getStatusColor(selectedLog.statusCode)">{{ selectedLog.statusCode }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="响应时间">{{ selectedLog.responseTime }}ms</a-descriptions-item>
              <a-descriptions-item label="用户">{{ selectedLog.user || '未知' }}</a-descriptions-item>
              <a-descriptions-item label="IP地址">{{ selectedLog.ip }}</a-descriptions-item>
              <a-descriptions-item label="User-Agent" :span="2">{{ selectedLog.userAgent }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          
          <a-tab-pane key="request" tab="请求信息">
            <div class="json-container">
              <h4>请求头</h4>
              <a-textarea 
                :value="formatJson(selectedLog.requestHeaders)" 
                :rows="6" 
                readonly 
                class="json-textarea"
              />
              
              <h4>请求参数</h4>
              <a-textarea 
                :value="formatJson(selectedLog.requestBody)" 
                :rows="8" 
                readonly 
                class="json-textarea"
              />
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="response" tab="响应信息">
            <div class="json-container">
              <h4>响应头</h4>
              <a-textarea 
                :value="formatJson(selectedLog.responseHeaders)" 
                :rows="6" 
                readonly 
                class="json-textarea"
              />
              
              <h4>响应数据</h4>
              <a-textarea 
                :value="formatJson(selectedLog.responseBody)" 
                :rows="10" 
                readonly 
                class="json-textarea"
              />
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="error" tab="错误信息" v-if="selectedLog.statusCode >= 400">
            <div class="error-container">
              <a-alert 
                :message="`HTTP ${selectedLog.statusCode} 错误`" 
                :description="selectedLog.errorMessage || '无详细错误信息'" 
                type="error" 
                show-icon 
              />
              
              <div v-if="selectedLog.stackTrace" class="stack-trace">
                <h4>堆栈跟踪</h4>
                <pre>{{ selectedLog.stackTrace }}</pre>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {computed, onMounted, onUnmounted, reactive, ref} from 'vue'
import {
  ApiOutlined,
  CheckCircleOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  CopyOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  ReloadOutlined,
  SearchOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import {message} from 'ant-design-vue'
import { useSuperAdminRequest } from '@/composables/useApiRequest'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '请求时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 120,
    sorter: true
  },
  {
    title: '请求路径',
    dataIndex: 'path',
    key: 'path',
    width: 200,
    ellipsis: true
  },
  {
    title: '方法',
    dataIndex: 'method',
    key: 'method',
    width: 80
  },
  {
    title: '状态码',
    dataIndex: 'statusCode',
    key: 'statusCode',
    width: 100,
    sorter: true
  },
  {
    title: '响应时间',
    dataIndex: 'responseTime',
    key: 'responseTime',
    width: 150,
    sorter: true
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
    width: 120
  },
  {
    title: '用户',
    dataIndex: 'user',
    key: 'user',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 搜索表单状态
const searchFormState = reactive({
  path: '',
  method: '',
  statusCode: '',
  user: '',
  ip: '',
  dateRange: []
})

// 高级搜索状态
const advancedSearchState = reactive({
  minResponseTime: null,
  maxResponseTime: null,
  errorKeyword: '',
  sortBy: 'timestamp_desc'
})

// 控制状态
const showAdvancedSearch = ref(false)
const autoRefresh = ref(false)
const exporting = ref(false)
const activeQuickFilter = ref('')

// 数据源和分页
const dataSource = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 统计数据
const stats = reactive({
  totalRequests: 0,
  successRate: 0,
  avgResponseTime: 0,
  errorCount: 0
})

// 加载状态
const loading = ref(false)

// 详情弹窗
const detailModalVisible = ref(false)
const selectedLog = ref(null)
const detailActiveTab = ref('basic')

// 自动刷新定时器
let refreshTimer = null

// API 调用 - 使用统一的API请求处理
const { 执行API请求: executeRequest } = useSuperAdminRequest()

// 快速筛选选项
const quickFilters = [
  { key: 'all', label: '全部', filter: {} },
  { key: 'success', label: '成功请求', filter: { statusCode: '200' } },
  { key: 'error', label: '错误请求', filter: { statusCode: '500' } },
  { key: 'slow', label: '慢请求', filter: { minResponseTime: 1000 } },
  { key: 'today', label: '今日', filter: { dateRange: [dayjs().startOf('day'), dayjs().endOf('day')] } },
  { key: 'hour', label: '最近1小时', filter: { dateRange: [dayjs().subtract(1, 'hour'), dayjs()] } }
]

// 日期预设
const datePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  { label: '上月', value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] }
]

// 获取API调用日志数据
const 获取API调用日志 = async () => {
  loading.value = true
  try {
    const 请求参数 = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchFormState,
      ...advancedSearchState
    }

    // 调用API获取日志数据
    const 响应数据 = await executeRequest(() => 
      // 这里需要调用实际的API服务方法
      // 暂时使用模拟数据
      Promise.resolve({
        status: 100,
        message: {
          items: generateMockData(),
          total: 1000
        }
      })
    )

    // 处理响应数据
    if (响应数据.status === 100) {
      const 日志结果 = 响应数据.message
      dataSource.value = 日志结果.items || []
      pagination.total = 日志结果.total || 0

      // 更新统计数据
      更新统计数据(dataSource.value)
    } else {
      message.error(响应数据.message || '获取日志失败')
    }
  } catch (错误) {
    console.error('获取日志失败:', 错误)
    message.error('获取日志失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
  const paths = ['/admin/users', '/admin/login', '/admin/announcements', '/admin/dashboard/stats', '/admin/api-stats']
  const statusCodes = [200, 201, 400, 401, 403, 404, 500]
  const users = ['admin', 'user1', 'user2', 'guest', null]
  
  return Array.from({ length: pagination.pageSize }, (_, index) => {
    const statusCode = statusCodes[Math.floor(Math.random() * statusCodes.length)]
    const responseTime = Math.floor(Math.random() * 2000) + 50
    
    return {
      id: (pagination.current - 1) * pagination.pageSize + index + 1,
      timestamp: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').format('YYYY-MM-DD HH:mm:ss'),
      path: paths[Math.floor(Math.random() * paths.length)] + (Math.random() > 0.5 ? '/list' : '/detail'),
      method: methods[Math.floor(Math.random() * methods.length)],
      statusCode,
      responseTime,
      ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
      user: users[Math.floor(Math.random() * users.length)],
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      requestHeaders: { 'Content-Type': 'application/json', 'Authorization': 'Bearer token' },
      requestBody: { page: 1, pageSize: 20 },
      responseHeaders: { 'Content-Type': 'application/json' },
      responseBody: statusCode === 200 ? { status: 100, data: [], message: 'success' } : { status: statusCode, message: 'error' },
      errorMessage: statusCode >= 400 ? '请求处理失败' : null,
      stackTrace: statusCode >= 500 ? 'Error: Internal server error\n    at handler.js:123\n    at process.js:456' : null
    }
  })
}

// 更新统计数据
const 更新统计数据 = (data) => {
  const total = pagination.total
  const successCount = data.filter(item => item.statusCode >= 200 && item.statusCode < 300).length
  const errorCount = data.filter(item => item.statusCode >= 400).length
  const avgTime = data.reduce((sum, item) => sum + item.responseTime, 0) / data.length
  
  stats.totalRequests = total
  stats.successRate = Math.round((successCount / data.length) * 100)
  stats.avgResponseTime = Math.round(avgTime)
  stats.errorCount = errorCount
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理排序
  if (sorter.field) {
    const order = sorter.order === 'ascend' ? 'asc' : 'desc'
    advancedSearchState.sortBy = `${sorter.field}_${order}`
  }
  
  获取API调用日志()
}

// 页面大小变化处理
const handlePageSizeChange = () => {
  pagination.current = 1
  获取API调用日志()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchFormState, {
    path: '',
    method: '',
    statusCode: '',
    user: '',
    ip: '',
    dateRange: []
  })
  Object.assign(advancedSearchState, {
    minResponseTime: null,
    maxResponseTime: null,
    errorKeyword: '',
    sortBy: 'timestamp_desc'
  })
  activeQuickFilter.value = ''
  pagination.current = 1
  获取API调用日志()
}

// 刷新日志数据
const 刷新日志数据 = () => {
  获取API调用日志()
}

// 导出日志
const exportLogs = async () => {
  exporting.value = true
  try {
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('日志导出成功')
  } catch (error) {
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 切换高级搜索
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

// 应用快速筛选条件
const 应用快速筛选 = (筛选条件) => {
  activeQuickFilter.value = 筛选条件.key
  Object.assign(searchFormState, 筛选条件.filter)
  pagination.current = 1
  获取API调用日志()
}

// 显示日志详情
const showLogDetail = (record) => {
  selectedLog.value = record
  detailModalVisible.value = true
  detailActiveTab.value = 'basic'
}

// 复制日志信息
const copyLogInfo = async (record) => {
  const info = `ID: ${record.id}\n时间: ${record.timestamp}\n路径: ${record.path}\n方法: ${record.method}\n状态: ${record.statusCode}\n响应时间: ${record.responseTime}ms`
  
  try {
    await navigator.clipboard.writeText(info)
    message.success('日志信息已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 获取方法颜色
const getMethodColor = (method) => {
  const colors = {
    GET: 'blue',
    POST: 'green',
    PUT: 'orange',
    DELETE: 'red',
    PATCH: 'purple'
  }
  return colors[method] || 'default'
}

// 获取状态码颜色
const getStatusColor = (statusCode) => {
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400 && statusCode < 500) return 'error'
  if (statusCode >= 500) return 'error'
  return 'default'
}

// 获取状态码文本
const getStatusText = (statusCode) => {
  const texts = {
    200: '成功',
    201: '已创建',
    400: '请求错误',
    401: '未授权',
    403: '禁止访问',
    404: '未找到',
    500: '服务器错误'
  }
  return texts[statusCode] || ''
}

// 获取响应时间样式类
const getResponseTimeClass = (time) => {
  if (time < 200) return 'response-time-fast'
  if (time < 1000) return 'response-time-normal'
  return 'response-time-slow'
}

// 获取响应时间颜色
const getResponseTimeColor = (time) => {
  if (time < 200) return '#52c41a'
  if (time < 1000) return '#faad14'
  return '#ff4d4f'
}

// 获取响应时间百分比
const getResponseTimePercent = (time) => {
  return Math.min((time / 2000) * 100, 100)
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 格式化相对时间
const formatRelativeTime = (timestamp) => {
  return dayjs(timestamp).fromNow()
}

// 格式化 JSON
const formatJson = (data) => {
  try {
    if (typeof data === 'string') {
      return JSON.stringify(JSON.parse(data), null, 2)
    }
    return JSON.stringify(data, null, 2)
  } catch (e) {
    return data || ''
  }
}

// 设置自动刷新功能
const 设置自动刷新 = () => {
  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      获取API调用日志()
    }, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 监听自动刷新状态变化
const stopAutoRefreshWatcher = computed(() => {
  设置自动刷新()
  return autoRefresh.value
})

// 组件挂载时初始化数据
onMounted(() => {
  获取API调用日志()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.api-call-log {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-header h1 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.search-form {
  padding: 16px 0;
}

.advanced-search {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-count {
  color: #666;
  font-size: 14px;
}

.path-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1890ff;
}

.status-text {
  margin-left: 4px;
  font-size: 12px;
}

.response-time-fast {
  color: #52c41a;
  font-weight: 500;
}

.response-time-normal {
  color: #faad14;
  font-weight: 500;
}

.response-time-slow {
  color: #ff4d4f;
  font-weight: 500;
}

.detail-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-modal-body {
    padding: 24px;
  }
}

.log-detail {
  .ant-tabs-content-holder {
    padding: 16px 0;
  }
}

.json-container {
  h4 {
    margin: 16px 0 8px 0;
    color: #1890ff;
    font-size: 14px;
    font-weight: 600;
  }
  
  .json-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.error-container {
  .ant-alert {
    margin-bottom: 16px;
  }
  
  .stack-trace {
    h4 {
      margin: 16px 0 8px 0;
      color: #ff4d4f;
      font-size: 14px;
      font-weight: 600;
    }
    
    pre {
      background: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      padding: 12px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #a8071a;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-call-log {
    padding: 12px;
  }
  
  .log-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .search-form {
    .ant-form {
      flex-direction: column;
      
      .ant-form-item {
        margin-bottom: 12px;
        width: 100%;
        
        .ant-input,
        .ant-select,
        .ant-picker {
          width: 100% !important;
        }
      }
    }
  }
  
  .stats-row {
    .ant-col {
      margin-bottom: 12px;
    }
  }
}

/* 动画效果 */
.ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

.ant-tag {
  transition: all 0.3s ease;
}

.ant-tag:hover {
  transform: scale(1.05);
}

.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

/* 自定义滚动条 */
.json-textarea::-webkit-scrollbar,
.stack-trace pre::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.json-textarea::-webkit-scrollbar-track,
.stack-trace pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.json-textarea::-webkit-scrollbar-thumb,
.stack-trace pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.json-textarea::-webkit-scrollbar-thumb:hover,
.stack-trace pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>

<style scoped>
/* 你可以在这里添加组件特定的样式 */
pre {
  white-space: pre-wrap; 
  word-break: break-all; 
  background-color: #f5f5f5; 
  padding: 8px; 
  border-radius: 4px;
}
</style>