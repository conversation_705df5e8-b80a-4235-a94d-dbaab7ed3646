<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">
          <DashboardOutlined />
          管理仪表盘
        </h1>
        <p class="page-subtitle">用户注册统计分析</p>
      </div>

      <div class="header-right">
        <!-- 时间范围选择 -->
        <a-select
          v-model:value="时间范围"
          style="width: 150px; margin-right: 16px"
          @change="切换时间范围"
        >
          <a-select-option value="today">今天24小时</a-select-option>
          <a-select-option value="week">本周7天</a-select-option>
          <a-select-option value="month">本月每天</a-select-option>
          <a-select-option value="lastMonth">上月每天</a-select-option>
          <a-select-option value="recent30">近30天</a-select-option>
        </a-select>

        <!-- 刷新按钮 -->
        <a-button
          type="primary"
          :loading="loading"
          @click="刷新数据"
          size="small"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </div>
    </div>
    <!-- 用户注册统计图表 -->
    <div class="dashboard-content">
      <a-card title="用户注册时间统计" :bordered="false" class="chart-card">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" tip="正在加载数据...">
            <div style="height: 400px;"></div>
          </a-spin>
        </div>

        <div v-else-if="error" class="error-container">
          <a-result
            status="error"
            title="数据加载失败"
            :sub-title="error"
          >
            <template #extra>
              <a-button type="primary" @click="刷新数据">
                <ReloadOutlined />
                重新加载
              </a-button>
            </template>
          </a-result>
        </div>

        <div v-if="!loading && !error">
          <!-- 图表容器 -->
          <div ref="chartContainer" style="height: 400px;"></div>
          <!-- 统计摘要 -->
          <div class="chart-summary">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总注册数"
                  :value="统计摘要.总注册数"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="日均注册"
                  :value="统计摘要.日均注册"
                  :precision="1"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="最高单日"
                  :value="统计摘要.最高单日"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="增长趋势"
                  :value="统计摘要.增长率"
                  suffix="%"
                  :precision="1"
                  :value-style="{ color: 统计摘要.增长率 >= 0 ? '#52c41a' : '#f5222d' }"
                />
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { DashboardOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import superAdminService from '../services/superAdminService'

// 响应式数据
const loading = ref(false)
const error = ref(null)
const 时间范围 = ref('recent30')
const chartContainer = ref(null)
const 图表实例 = ref(null)

// 统计摘要数据
const 统计摘要 = ref({
  总注册数: 0,
  日均注册: 0,
  最高单日: 0,
  增长率: 0
})

// 图表数据
const 图表数据 = ref({
  时间轴: [],
  注册数据: []
})

// 获取用户注册统计数据
const 获取注册统计数据 = async () => {
  try {
    loading.value = true
    error.value = null

    const response = await superAdminService.getUserRegistrationStats({
      时间范围: 时间范围.value
    })

    if (superAdminService.isSuccess(response)) {
      const data = superAdminService.getData(response)

      // 更新图表数据
      图表数据.value = {
        时间轴: data.时间轴 || [],
        注册数据: data.注册数据 || []
      }

      // 更新统计摘要
      统计摘要.value = {
        总注册数: data.总注册数 || 0,
        日均注册: data.日均注册 || 0,
        最高单日: data.最高单日 || 0,
        增长率: data.增长率 || 0
      }

    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (err) {
    error.value = err.message
    message.error('获取用户注册统计数据失败')
  } finally {
    loading.value = false

    // 在loading结束后渲染图表，确保DOM可见
    if (!error.value) {
      await nextTick()
      渲染图表()
    }
  }
}

// 渲染图表
const 渲染图表 = () => {
  if (!chartContainer.value) return

  // 销毁现有图表实例
  if (图表实例.value) {
    图表实例.value.dispose()
  }

  // 创建新的图表实例
  图表实例.value = echarts.init(chartContainer.value)

  const option = {
    title: {
      text: 获取图表标题(),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#1890ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 13
      },
      formatter: '{b}<br/>注册人数: {c} 人'
    },
    xAxis: {
      type: 'category',
      data: 图表数据.value.时间轴,
      axisLabel: {
        rotate: 时间范围.value === 'today' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      name: '注册人数',
      minInterval: 1
    },
    series: [{
      name: '注册人数',
      type: 'line',
      data: 图表数据.value.注册数据,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#1890ff'
      },
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(24, 144, 255, 0.1)'
          }]
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }

  图表实例.value.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    图表实例.value?.resize()
  })
}

// 获取图表标题
const 获取图表标题 = () => {
  const titles = {
    today: '今天24小时用户注册趋势',
    week: '本周7天用户注册趋势',
    month: '本月每天用户注册趋势',
    lastMonth: '上月每天用户注册趋势',
    recent30: '近30天用户注册趋势'
  }
  return titles[时间范围.value] || '用户注册趋势'
}

// 切换时间范围
const 切换时间范围 = () => {
  获取注册统计数据()
}

// 刷新数据
const 刷新数据 = () => {
  获取注册统计数据()
}


// 组件挂载
onMounted(() => {
  获取注册统计数据()
})

</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-subtitle {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.chart-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-summary {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
</style>