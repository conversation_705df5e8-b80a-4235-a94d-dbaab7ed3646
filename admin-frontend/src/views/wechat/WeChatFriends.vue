<template>
  <div class="wechat-friends-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>
            <WechatOutlined />
            微信好友管理
          </h1>
          <p class="header-subtitle">查看和管理微信好友的下次沟通时间信息，优化客户关系维护</p>
        </div>
        <div class="header-actions">
          <a-space size="large">
            <a-button
              type="primary"
              size="large"
              @click="刷新列表"
              :loading="加载中"
              class="refresh-btn"
            >
              <template #icon><ReloadOutlined /></template>
              刷新数据
            </a-button>
          </a-space>
        </div>
      </div>
    </div>


    <!-- 微信好友数据表格 -->
    <a-card class="table-section" title="微信好友列表">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button
              type="text"
              @click="刷新列表"
              :loading="加载中"
              size="small"
            >
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="表格设置">
            <a-button type="text" size="small">
              <template #icon><SettingOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <a-table
        :columns="表格列定义"
        :data-source="好友列表"
        :loading="加载中"
        :pagination="分页配置"
        @change="处理表格变化"
        row-key="识别ID"
        :scroll="{ x: 1400 }"
        size="middle"
        :bordered="false"
      >
        <template #bodyCell="{ column, record }">
          <!-- 好友信息列 -->
          <template v-if="column.key === 'friend_info'">
            <div class="friend-info">
              <a-avatar :size="40" style="background-color: #1890ff">
                {{ record.好友姓名?.charAt(0) || '?' }}
              </a-avatar>
              <div class="info-content">
                <div class="name">{{ record.好友姓名 || '未知' }}</div>
                <div class="wechat-id">{{ record.好友微信号 || '未知' }}</div>
              </div>
            </div>
          </template>

          <!-- 我方微信信息列 -->
          <template v-if="column.key === 'my_wechat_info'">
            <div class="my-wechat-info">
              <div class="wechat-id">{{ record.我方微信号 || '未知' }}</div>
              <div class="nickname">{{ record.我方昵称 || '无昵称' }}</div>
            </div>
          </template>

          <!-- 下次沟通时间列 -->
          <template v-if="column.key === 'next_communication_time'">
            <div class="communication-time">
              <a-tag 
                :color="获取时间标签颜色(record.下次沟通时间)"
                style="margin: 0"
              >
                {{ 格式化时间(record.下次沟通时间) }}
              </a-tag>
            </div>
          </template>

          <!-- 最后消息时间列 -->
          <template v-if="column.key === 'last_message_time'">
            <div class="message-time">
              <div class="time-item">
                <span class="label">我方:</span>
                <span class="time">{{ 格式化时间(record.我方最后消息时间) || '无' }}</span>
              </div>
              <div class="time-item">
                <span class="label">对方:</span>
                <span class="time">{{ 格式化时间(record.对方最后消息时间) || '无' }}</span>
              </div>
            </div>
          </template>

          <!-- 备注列 -->
          <template v-if="column.key === 'remark'">
            <div class="remark">
              <a-tooltip :title="record.备注" v-if="record.备注">
                <span class="remark-text">{{ record.备注 }}</span>
              </a-tooltip>
              <span v-else class="no-remark">无备注</span>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="查看详情(record)">
                <template #icon>
                  <EyeOutlined />
                </template>
                详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 好友详情弹窗 -->
    <a-modal
      v-model:open="详情弹窗可见"
      title="微信好友详情"
      :width="800"
      :footer="null"
    >
      <div v-if="当前好友详情" class="friend-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="好友姓名">
            {{ 当前好友详情.好友姓名 || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="好友微信号">
            {{ 当前好友详情.好友微信号 || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="我方微信号">
            {{ 当前好友详情.我方微信号 || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="我方昵称">
            {{ 当前好友详情.我方昵称 || '无昵称' }}
          </a-descriptions-item>
          <a-descriptions-item label="下次沟通时间">
            <a-tag :color="获取时间标签颜色(当前好友详情.下次沟通时间)">
              {{ 格式化时间(当前好友详情.下次沟通时间) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="识别ID">
            {{ 当前好友详情.识别ID }}
          </a-descriptions-item>
          <a-descriptions-item label="我方最后消息时间" :span="2">
            {{ 格式化时间(当前好友详情.我方最后消息时间) || '无记录' }}
          </a-descriptions-item>
          <a-descriptions-item label="对方最后消息时间" :span="2">
            {{ 格式化时间(当前好友详情.对方最后消息时间) || '无记录' }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ 当前好友详情.备注 || '无备注' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { onMounted, reactive, ref } from 'vue'

// 响应式数据
const 加载中 = ref(false)
const 好友列表 = ref([])
const 详情弹窗可见 = ref(false)
const 当前好友详情 = ref(null)

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列定义
const 表格列定义 = [
  {
    title: '好友信息',
    key: 'friend_info',
    width: 200,
    fixed: 'left'
  },
  {
    title: '我方微信',
    key: 'my_wechat_info',
    width: 150
  },
  {
    title: '下次沟通时间',
    key: 'next_communication_time',
    width: 180,
    sorter: true
  },
  {
    title: '最后消息时间',
    key: 'last_message_time',
    width: 200
  },
  {
    title: '备注',
    key: 'remark',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right'
  }
]

// 工具函数
const 格式化时间 = (timeStr) => {
  if (!timeStr) return null
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

const 获取时间标签颜色 = (timeStr) => {
  if (!timeStr) return 'default'
  
  const now = dayjs()
  const targetTime = dayjs(timeStr)
  const diffHours = targetTime.diff(now, 'hour')
  
  if (diffHours < 0) return 'red'      // 已过期
  if (diffHours < 24) return 'orange'  // 24小时内
  if (diffHours < 72) return 'blue'    // 3天内
  return 'green'                       // 3天后
}

// API调用函数
const 获取好友列表 = async () => {
  try {
    加载中.value = true

    const 请求参数 = {
      页码: 分页配置.current,
      每页条数: 分页配置.pageSize
    }

    console.log('🔍 获取微信好友列表，参数:', 请求参数)

    // 调用后端API
    const authToken = localStorage.getItem('authToken')
    const response = await fetch('http://localhost:8000/wechat/friend-communication-schedule', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(请求参数)
    })

    const result = await response.json()
    console.log('📡 微信好友列表响应:', result)

    if (result.status === 100) {
      const data = result.data || {}
      好友列表.value = data.列表 || []
      分页配置.total = data.总数 || 0

      console.log('✅ 微信好友列表加载成功:', 好友列表.value.length, '条记录')
    } else {
      message.error(result.message || '获取微信好友列表失败')
      好友列表.value = []
      分页配置.total = 0
    }
  } catch (error) {
    console.error('❌ 获取微信好友列表异常:', error)
    message.error('获取微信好友列表失败')
    好友列表.value = []
    分页配置.total = 0
  } finally {
    加载中.value = false
  }
}

const 刷新列表 = () => {
  获取好友列表()
}

const 处理表格变化 = (pagination, filters, sorter) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  获取好友列表()
}

const 查看详情 = (record) => {
  当前好友详情.value = record
  详情弹窗可见.value = true
}

// 生命周期
onMounted(() => {
  获取好友列表()
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 表格样式 */
.table-section {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.friend-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content .name {
  font-weight: 500;
  color: #1d1d1d;
  margin-bottom: 4px;
}

.info-content .wechat-id {
  font-size: 12px;
  color: #666;
}

.my-wechat-info .wechat-id {
  font-weight: 500;
  color: #1d1d1d;
  margin-bottom: 4px;
}

.my-wechat-info .nickname {
  font-size: 12px;
  color: #666;
}

.message-time .time-item {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.message-time .label {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.message-time .time {
  font-size: 12px;
  color: #1d1d1d;
}

.remark-text {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.no-remark {
  color: #ccc;
  font-style: italic;
}

.friend-detail {
  padding: 16px 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .search-container .ant-row {
    gap: 8px;
  }

  .stats-cards .ant-col {
    margin-bottom: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .page-header {
    padding: 16px;
  }

  .header-left h1 {
    font-size: 24px;
  }
}

/* 表格内容样式优化 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 卡片悬停效果 */
.stats-cards .ant-card:hover,
.search-section:hover,
.table-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #9254de 100%);
}
</style>
