<template>
  <div class="edit-announcement">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header
        :title="announcementId ? '编辑通告' : '新建通告'"
        :sub-title="announcementId ? `ID: ${announcementId}` : '创建新的系统通告'"
        @back="goBack"
        class="header-content"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" :loading="isSaving" @click="handleSave">
              <template #icon><SaveOutlined /></template>
              保存通告
            </a-button>
            <a-button @click="goBack">
              <template #icon><CloseOutlined /></template>
              取消
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 主体内容区域 -->
    <a-spin :spinning="isLoading" tip="加载通告数据中...">
      <a-card v-if="!isLoading" class="form-container" :bordered="false">
        <a-form
          ref="formRef"
          :model="formState"
          :rules="formRules"
          layout="vertical"
          name="announcement_form"
          @finish="handleSave"
        >
          <!-- 基本信息区域 -->
          <a-card title="基本信息" size="small" class="section-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item name="标题" label="通告标题" required>
                  <a-input 
                    v-model:value="formState.标题" 
                    placeholder="请输入通告标题" 
                    show-count
                    :maxlength="200"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item name="类型" label="通告类型" required>
                  <a-select v-model:value="formState.类型" placeholder="请选择通告类型">
                    <a-select-option value="通知">通知</a-select-option>
                    <a-select-option value="公告">公告</a-select-option>
                    <a-select-option value="警告">警告</a-select-option>
                    <a-select-option value="系统维护">系统维护</a-select-option>
                    <a-select-option value="版本更新">版本更新</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item name="重要性" label="重要性级别" required>
                  <a-select v-model:value="formState.重要性" placeholder="请选择重要性">
                    <a-select-option :value="1">
                      <a-tag color="default">普通</a-tag>
                    </a-select-option>
                    <a-select-option :value="2">
                      <a-tag color="orange">重要</a-tag>
                    </a-select-option>
                    <a-select-option :value="3">
                      <a-tag color="red">紧急</a-tag>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              
              <a-col :span="8">
                <a-form-item name="排序" label="显示排序">
                  <a-input-number 
                    v-model:value="formState.排序" 
                    :min="0" 
                    :max="9999"
                    style="width: 100%"
                    placeholder="数字越大排序越靠前" 
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="8">
                <a-form-item name="操作标识" label="操作标识">
                  <a-input-number 
                    v-model:value="formState.操作标识" 
                    :min="0" 
                    style="width: 100%"
                    placeholder="默认为0" 
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item name="开始时间" label="生效开始时间">
                  <a-date-picker 
                    v-model:value="formState.开始时间" 
                    style="width: 100%" 
                    :show-time="true" 
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="通告生效开始时间（可选）"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item name="结束时间" label="生效结束时间">
                  <a-date-picker 
                    v-model:value="formState.结束时间" 
                    style="width: 100%" 
                    :show-time="true" 
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="通告生效结束时间（可选）"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item name="已发布" label="发布状态">
              <a-switch 
                v-model:checked="formState.已发布" 
                checked-children="已发布" 
                un-checked-children="草稿"
              />
              <span class="status-hint">
                {{ formState.已发布 ? '通告将立即对用户可见' : '通告保存为草稿，不会对用户显示' }}
              </span>
            </a-form-item>
          </a-card>

          <!-- 内容编辑区域 -->
          <a-card title="内容编辑" size="small" class="section-card">
            <a-form-item name="内容" label="通告内容" required>
              <a-form-item-rest>
                <div class="content-editor">
                <!-- 内容项列表 -->
                <div class="content-items">
                  <a-empty 
                    v-if="contentItems.length === 0" 
                    description="暂无内容项，请添加内容"
                    :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  />
                  
                  <!-- 内容项卡片 -->
                  <div v-for="(item, index) in contentItems" :key="item.id" class="content-item">
                    <a-card 
                      size="small" 
                      :title="getContentItemTitle(item, index)"
                      :class="{ 'image-item': item.类型 === '图片', 'text-item': item.类型 === '文本' }"
                    >
                      <template #extra>
                        <a-space>
                          <!-- 上移按钮 -->
                          <a-tooltip title="上移">
                            <a-button 
                              v-if="index > 0" 
                              type="text" 
                              size="small"
                              @click="moveItemUp(index)"
                            >
                              <template #icon><UpOutlined /></template>
                            </a-button>
                          </a-tooltip>
                          
                          <!-- 下移按钮 -->
                          <a-tooltip title="下移">
                            <a-button 
                              v-if="index < contentItems.length - 1" 
                              type="text" 
                              size="small"
                              @click="moveItemDown(index)"
                            >
                              <template #icon><DownOutlined /></template>
                            </a-button>
                          </a-tooltip>
                          
                          <!-- 删除按钮 -->
                          <a-popconfirm
                            title="确定要删除这个内容项吗？"
                            ok-text="确定"
                            cancel-text="取消"
                            @confirm="removeItem(index)"
                          >
                            <a-tooltip title="删除">
                              <a-button 
                                type="text" 
                                danger 
                                size="small"
                              >
                                <template #icon><DeleteOutlined /></template>
                              </a-button>
                            </a-tooltip>
                          </a-popconfirm>
                        </a-space>
                      </template>

                      <!-- 内容编辑区域 -->
                      <a-row :gutter="16">
                        <a-col :span="item.类型 === '图片' ? 14 : 18">
                          <!-- 文本内容输入 -->
                          <a-textarea
                            v-if="item.类型 === '文本'"
                            v-model:value="item.内容"
                            placeholder="请输入文本内容"
                            :rows="3"
                            show-count
                            :maxlength="1000"
                            @change="updateFormContent"
                          />

                          <!-- 图片URL输入 -->
                          <a-input
                            v-else
                            v-model:value="item.内容"
                            placeholder="请输入图片URL地址"
                            @change="updateFormContent"
                          />
                        </a-col>

                        <a-col :span="4">
                          <a-form-item-rest>
                            <a-input-number
                              v-model:value="item.操作类型"
                              style="width: 100%"
                              placeholder="操作类型"
                              :min="0"
                              @change="updateFormContent"
                            />
                          </a-form-item-rest>
                        </a-col>
                        
                        <!-- 图片预览按钮 -->
                        <a-col v-if="item.类型 === '图片'" :span="6">
                          <a-space>
                            <a-button 
                              type="link" 
                              @click="previewImage(item.内容)" 
                              :disabled="!item.内容"
                            >
                              <template #icon><EyeOutlined /></template>
                              预览
                            </a-button>
                          </a-space>
                        </a-col>
                      </a-row>
                      
                      <!-- 图片预览区域 -->
                      <div v-if="item.类型 === '图片' && item.内容" class="image-preview">
                        <img 
                          :src="item.内容" 
                          alt="图片预览" 
                          @error="handleImageError"
                          @load="handleImageLoad"
                        />
                      </div>
                    </a-card>
                  </div>
                </div>
                
                <!-- 添加内容按钮 -->
                <div class="content-actions">
                  <a-space>
                    <a-button type="dashed" @click="addTextItem">
                      <template #icon><PlusOutlined /></template>
                      添加文本段落
                    </a-button>
                    
                    <a-button type="dashed" @click="addImageItem">
                      <template #icon><PictureOutlined /></template>
                      添加图片
                    </a-button>
                  </a-space>
                </div>
              </div>
              </a-form-item-rest>
            </a-form-item>
          </a-card>
        </a-form>
      </a-card>
    </a-spin>

    <!-- 悬浮保存按钮 -->
    <div class="floating-actions">
      <a-affix :offset-bottom="20">
        <a-space direction="vertical">
          <a-button 
            type="primary" 
            size="large" 
            :loading="isSaving" 
            @click="handleSave"
            class="save-button"
          >
            <template #icon><SaveOutlined /></template>
            保存通告
          </a-button>
          
          <a-button size="large" @click="goBack" class="cancel-button">
            <template #icon><CloseOutlined /></template>
            取消
          </a-button>
        </a-space>
      </a-affix>
    </div>

    <!-- 图片预览弹窗 -->
    <a-modal 
      v-model:open="previewVisible" 
      title="图片预览" 
      :footer="null" 
      width="800px"
      centered
    >
      <div class="preview-container">
        <img 
          :src="previewImageSrc" 
          alt="图片预览" 
          class="preview-image"
          @error="handlePreviewError"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
/**
 * 编辑通告页面
 * 功能：创建和编辑系统通告，支持富文本内容编辑
 * 
 * 主要特性：
 * 1. 支持通告基本信息编辑（标题、类型、重要性等）
 * 2. 支持多种内容类型（文本、图片）
 * 3. 支持内容项的排序和管理
 * 4. 支持图片预览功能
 * 5. 支持发布状态控制
 * 6. 响应式设计，适配不同屏幕尺寸
 */

import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Empty } from 'ant-design-vue';
import {
  DeleteOutlined,
  DownOutlined,
  EyeOutlined,
  PictureOutlined,
  PlusOutlined,
  UpOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import superAdminService from '../../services/superAdminService';
import { useSuperAdminRequest } from '../../composables/useApiRequest';

// ==================== 路由和基础状态 ====================

const route = useRoute();
const router = useRouter();
const formRef = ref(null);

// 页面状态
const isLoading = ref(true);
const isSaving = ref(false);
const announcementId = ref(null);

// 使用统一的API响应处理
const { loading, execute: 执行API请求 } = useSuperAdminRequest();

// ==================== 表单数据和验证 ====================

// 表单状态
const formState = reactive({
  标题: '',
  类型: '通知',     // 默认值
  重要性: 1,        // 默认值：普通
  操作标识: 0,      // 默认值
  排序: 0,         // 默认值
  开始时间: null,
  结束时间: null,
  已发布: false,
  内容: [],        // 内容项数组 [{类型, 内容, 操作类型}]
});

// 表单验证规则
const formRules = {
  标题: [
    { required: true, message: '请输入通告标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度应在2-200个字符之间', trigger: 'blur' },
  ],
  类型: [
    { required: true, message: '请选择通告类型', trigger: 'change' },
  ],
  重要性: [
    { required: true, message: '请选择重要性级别', trigger: 'change' },
  ],
  内容: [
    { 
      validator: (_, value) => {
        if (!value || value.length === 0) {
          return Promise.reject('请至少添加一项内容');
        }
        // 检查是否有空内容项
        const hasEmptyContent = value.some(item => !item.内容 || item.内容.trim() === '');
        if (hasEmptyContent) {
          return Promise.reject('请填写所有内容项的内容');
        }
        return Promise.resolve();
      }, 
      trigger: 'change' 
    },
  ],
};

// ==================== 内容编辑器状态 ====================

// 内容项列表（用于编辑器显示）
const contentItems = ref([]);

// 图片预览状态
const previewVisible = ref(false);
const previewImageSrc = ref('');

// ==================== 工具函数 ====================

/**
 * 获取内容项标题
 * @param {Object} item - 内容项
 * @param {number} index - 索引
 * @returns {string} 标题文本
 */
const getContentItemTitle = (item, index) => {
  const typeMap = {
    '文本': '文本段落',
    '图片': '图片内容'
  };
  return `${typeMap[item.类型] || '内容'} ${index + 1}`;
};

/**
 * 处理图片加载错误
 * @param {Event} event - 错误事件
 */
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src);
  event.target.style.display = 'none';
};

/**
 * 处理图片加载成功
 * @param {Event} event - 加载事件
 */
const handleImageLoad = (event) => {
  event.target.style.display = 'block';
};

/**
 * 处理预览图片错误
 * @param {Event} event - 错误事件
 */
const handlePreviewError = (event) => {
  message.error('图片加载失败，请检查URL是否正确');
};

// ==================== 内容编辑器方法 ====================

/**
 * 添加文本内容项
 */
const addTextItem = () => {
  contentItems.value.push({
    id: Date.now() + Math.random(),
    类型: '文本',
    内容: '',
    操作类型: 0
  });
  updateFormContent();
  message.success('已添加文本段落');
};

/**
 * 添加图片内容项
 */
const addImageItem = () => {
  contentItems.value.push({
    id: Date.now() + Math.random(),
    类型: '图片',
    内容: '',
    操作类型: 0
  });
  updateFormContent();
  message.success('已添加图片内容');
};

/**
 * 删除内容项
 * @param {number} index - 要删除的索引
 */
const removeItem = (index) => {
  const item = contentItems.value[index];
  contentItems.value.splice(index, 1);
  updateFormContent();
  message.success(`已删除${item.类型}内容`);
};

/**
 * 上移内容项
 * @param {number} index - 当前索引
 */
const moveItemUp = (index) => {
  if (index > 0) {
    const item = contentItems.value.splice(index, 1)[0];
    contentItems.value.splice(index - 1, 0, item);
    updateFormContent();
    message.success('内容项已上移');
  }
};

/**
 * 下移内容项
 * @param {number} index - 当前索引
 */
const moveItemDown = (index) => {
  if (index < contentItems.value.length - 1) {
    const item = contentItems.value.splice(index, 1)[0];
    contentItems.value.splice(index + 1, 0, item);
    updateFormContent();
    message.success('内容项已下移');
  }
};

/**
 * 更新表单内容字段
 */
const updateFormContent = () => {
  formState.内容 = contentItems.value.map(item => ({
    类型: item.类型,
    内容: item.内容,
    操作类型: item.操作类型
  }));
};

/**
 * 预览图片
 * @param {string} url - 图片URL
 */
const previewImage = (url) => {
  if (!url || url.trim() === '') {
    message.warning('请先输入图片URL');
    return;
  }
  
  previewImageSrc.value = url;
  previewVisible.value = true;
};

// ==================== 数据加载和保存 ====================

/**
 * 加载通告数据
 */
const loadAnnouncementData = async () => {
  if (!announcementId.value) {
    isLoading.value = false;
    return;
  }

  try {
    const result = await 执行API请求(
      () => superAdminService.getAnnouncementDetail(announcementId.value),
      '通告数据加载成功'
    );
    
    if (result && result.data) {
      const announcementData = result.data;

      // 填充表单数据
      formState.标题 = announcementData.标题 || '';
      formState.类型 = announcementData.类型 || '通知';
      formState.重要性 = announcementData.重要性 || 1;
      formState.操作标识 = announcementData.操作标识 || 0;
      formState.排序 = announcementData.排序 || 0;
      formState.已发布 = announcementData.已发布 || false;

      // 处理日期字段
      if (announcementData.开始时间) {
        formState.开始时间 = dayjs(announcementData.开始时间);
      }

      if (announcementData.结束时间) {
        formState.结束时间 = dayjs(announcementData.结束时间);
      }

      // 初始化内容项
      const contentData = announcementData.内容 || [];
      formState.内容 = contentData;

      // 为每个内容项添加唯一ID用于v-for
      contentItems.value = contentData.map(item => ({
        ...item,
        id: Date.now() + Math.random(),
      }));
    }
  } catch (error) {
    console.error('通告数据加载失败:', error);
    message.error('通告数据加载失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};

/**
 * 保存通告
 */
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    isSaving.value = true;
    
    // 准备提交数据
    const payload = {
      标题: formState.标题,
      类型: formState.类型,
      重要性: formState.重要性,
      操作标识: formState.操作标识,
      排序: formState.排序,
      已发布: formState.已发布,
      内容: formState.内容,
      开始时间: formState.开始时间 ? formState.开始时间.format('YYYY-MM-DD HH:mm:ss') : null,
      结束时间: formState.结束时间 ? formState.结束时间.format('YYYY-MM-DD HH:mm:ss') : null,
    };

    // 验证编辑模式的ID
    if (announcementId.value) {
      const id = parseInt(announcementId.value);
      if (isNaN(id)) {
        throw new Error('缺少通告ID');
      }
      // ID作为参数传递，不需要在payload中设置
    }
    
    console.log('提交数据:', payload);
    
    // 调用API
    let result;
    const actionText = announcementId.value ? '通告更新成功！' : '通告创建成功！';
    
    if (announcementId.value) {
      // 更新通告
      result = await 执行API请求(
        () => superAdminService.updateAnnouncement(announcementId.value, payload),
        actionText
      );
    } else {
      // 创建通告
      result = await 执行API请求(
        () => superAdminService.createAnnouncement(payload),
        actionText
      );
    }
    
    if (result) {
      // 保存成功，返回列表页
      router.push('/notifications');
    }
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入项！');
    } else {
      console.error('保存失败:', error);
      message.error('保存失败，请稍后重试');
    }
  } finally {
    isSaving.value = false;
  }
};

/**
 * 返回列表页
 */
const goBack = () => {
  router.push('/notifications');
};

// ==================== 生命周期和监听器 ====================

/**
 * 组件挂载时初始化
 */
onMounted(async () => {
  announcementId.value = route.params.announcementId || null;
  if (announcementId.value && !announcementId.value.trim()) {
    announcementId.value = null;
  }
  await loadAnnouncementData();
});

/**
 * 监听内容项变化，自动更新表单内容
 */
watch(contentItems, () => {
  updateFormContent();
}, { deep: true });

</script>

<style scoped>
/**
 * 编辑通告页面样式
 * 采用现代化设计风格，注重编辑体验
 */

.edit-announcement {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  padding: 16px 24px;
}

/* 表单容器样式 */
.form-container {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 60px;
}

/* 区域卡片样式 */
.section-card {
  margin-bottom: 24px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.section-card .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

/* 状态提示样式 */
.status-hint {
  margin-left: 12px;
  color: #666;
  font-size: 12px;
}

/* 内容编辑器样式 */
.content-editor {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  min-height: 200px;
}

.content-items {
  margin-bottom: 16px;
  min-height: 120px;
}

.content-item {
  margin-bottom: 16px;
}

/* 内容项卡片样式 */
.text-item {
  border-left: 3px solid #1890ff;
}

.image-item {
  border-left: 3px solid #52c41a;
}

.content-item .ant-card-head {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

/* 内容操作按钮样式 */
.content-actions {
  display: flex;
  justify-content: center;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s;
}

.content-actions:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

/* 图片预览样式 */
.image-preview {
  margin-top: 12px;
  text-align: center;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 预览弹窗样式 */
.preview-container {
  text-align: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 悬浮操作按钮样式 */
.floating-actions {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
}

.save-button {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.cancel-button {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.cancel-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-announcement {
    padding: 0 12px 80px;
  }
  
  .header-content {
    padding: 12px 16px;
  }
  
  .content-editor {
    padding: 12px;
  }
  
  .floating-actions {
    right: 16px;
    bottom: 16px;
  }
  
  .floating-actions .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 表单项样式优化 */
.ant-form-vertical .ant-form-item-label {
  padding: 0 0 4px;
  font-weight: 500;
}

.ant-form-item-required::before {
  color: #ff4d4f;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 输入框样式优化 */
.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: 6px;
  transition: all 0.3s;
}

.ant-input:focus,
.ant-input-number:focus,
.ant-select-focused .ant-select-selector,
.ant-picker:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>