<template>
  <div class="internal-function-tools">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>🔧 简化工具管理</h1>
      <p class="page-description">管理智能体可调用的内部函数</p>
    </div>

    <!-- 工具列表 -->
    <a-card title="内部函数工具列表" :bordered="false">
      <template #extra>
        <a-button @click="刷新工具列表" :loading="工具列表加载中">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </template>

      <a-table
        :columns="工具配置列定义"
        :data-source="工具配置列表"
        :loading="工具列表加载中"
        row-key="工具名称"
        :pagination="{ pageSize: 20, showSizeChanger: false, showQuickJumper: false }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === '启用状态'">
            <a-switch
              :checked="record.启用状态"
              @change="(checked) => 切换工具启用状态(record, checked)"
              :loading="record.切换中"
            />
          </template>

          <template v-else-if="column.key === 'action'">
            <a-button size="small" @click="查看工具详情(record)">
              详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 简化的工具详情模态框 -->
    <a-modal
      v-model:open="工具详情模态框可见"
      title="工具详情"
      width="500px"
      :footer="null"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="工具名称">
          {{ 当前工具详情.工具名称 }}
        </a-descriptions-item>
        <a-descriptions-item label="工具描述">
          {{ 当前工具详情.工具描述 || '暂无描述' }}
        </a-descriptions-item>
        <a-descriptions-item label="权限要求">
          {{ 当前工具详情.权限要求 || '无特殊要求' }}
        </a-descriptions-item>
        <a-descriptions-item label="启用状态">
          <a-tag :color="当前工具详情.启用状态 ? 'green' : 'red'">
            {{ 当前工具详情.启用状态 ? '已启用' : '已禁用' }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { 管理员LangChain服务 } from '@/services'

// 响应式数据
const 工具配置列表 = ref([])
const 工具列表加载中 = ref(false)
const 工具详情模态框可见 = ref(false)
const 当前工具详情 = ref({})

// 简化的表格列定义
const 工具配置列定义 = [
  { title: '工具名称', dataIndex: '工具名称', key: '工具名称', width: 200 },
  { title: '工具描述', dataIndex: '工具描述', key: '工具描述', ellipsis: true },
  { title: '权限要求', dataIndex: '权限要求', key: '权限要求', width: 150 },
  { title: '启用状态', dataIndex: '启用状态', key: '启用状态', width: 100 },
  { title: '操作', key: 'action', width: 100 }
]

// 页面初始化
onMounted(() => {
  刷新工具列表()
})

// 刷新工具列表 - 简化版本
const 刷新工具列表 = async () => {
  try {
    工具列表加载中.value = true
    const 响应 = await 管理员LangChain服务.获取内部函数工具列表()

    if (响应.success) {
      工具配置列表.value = 响应.data || []
      message.success(`已加载 ${工具配置列表.value.length} 个工具`)
    } else {
      message.error('获取工具列表失败')
    }
  } catch (error) {
    console.error('获取工具列表失败:', error)
    message.error('获取工具列表失败')
  } finally {
    工具列表加载中.value = false
  }
}

// 切换工具启用状态 - 简化版本
const 切换工具启用状态 = async (工具记录, 启用状态) => {
  try {
    工具记录.切换中 = true
    // 调用简化的切换接口
    const 响应 = await 管理员LangChain服务.切换工具启用状态(工具记录.工具名称, 启用状态)

    if (响应.success) {
      工具记录.启用状态 = 启用状态
      message.success(`工具${启用状态 ? '启用' : '禁用'}成功`)
    } else {
      message.error('切换工具状态失败')
      工具记录.启用状态 = !启用状态
    }
  } catch (error) {
    console.error('切换工具状态失败:', error)
    message.error('切换工具状态失败')
    工具记录.启用状态 = !启用状态
  } finally {
    工具记录.切换中 = false
  }
}

// 查看工具详情 - 简化版本
const 查看工具详情 = (工具记录) => {
  当前工具详情.value = {
    工具名称: 工具记录.工具名称,
    工具描述: 工具记录.工具描述,
    权限要求: 工具记录.权限要求,
    启用状态: 工具记录.启用状态
  }
  工具详情模态框可见.value = true
}
</script>

<style scoped>
.internal-function-tools {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  color: #666;
  margin: 0;
}

.json-display {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

/* 表格优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}
</style>
