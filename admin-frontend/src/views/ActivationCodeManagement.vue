<template>
  <div class="activation-code-management-container">
    <ErrorBoundary>
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>
              <KeyOutlined />
              激活码管理系统
            </h1>
            <p class="header-subtitle">管理和维护系统激活码</p>
          </div>
          <div class="header-actions">
            <a-space size="large">
              <div class="stats-summary">
                <a-statistic 
                  title="激活码总数" 
                  :value="统计数据.基础统计?.总数 || 0" 
                  :value-style="{ color: '#1890ff', fontSize: '20px', fontWeight: '600' }"
                />
              </div>
              <a-button
                v-if="当前标签页 === 'codes'"
                type="primary"
                size="large"
                @click="显示生成模态框"
                class="add-code-btn"
              >
                <template #icon><PlusOutlined /></template>
                生成激活码
              </a-button>
              <a-button
                v-if="当前标签页 === 'types'"
                type="primary"
                size="large"
                @click="显示类型创建模态框"
                class="add-type-btn"
              >
                <template #icon><PlusOutlined /></template>
                创建类型
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <a-tabs v-model:activeKey="当前标签页" class="main-tabs">
        <a-tab-pane key="codes" tab="激活码管理">
          <!-- 激活码管理内容 -->
          <div class="tab-content">
            <!-- 统计卡片区域 -->
            <div class="stats-cards">
              <a-row :gutter="16">
                <a-col :xs="24" :sm="12" :md="6">
                  <a-card>
                    <a-statistic
                      title="总激活码数"
                      :value="统计数据.基础统计?.总数 || 0"
                      :value-style="{ color: '#1890ff' }"
                    >
                      <template #prefix>
                        <KeyOutlined />
                      </template>
                    </a-statistic>
                  </a-card>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <a-card>
                    <a-statistic
                      title="未使用"
                      :value="统计数据.基础统计?.未使用数量 || 0"
                      :value-style="{ color: '#52c41a' }"
                    >
                      <template #prefix>
                        <CheckCircleOutlined />
                      </template>
                    </a-statistic>
                  </a-card>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <a-card>
                    <a-statistic
                      title="已使用"
                      :value="统计数据.基础统计?.已使用数量 || 0"
                      :value-style="{ color: '#faad14' }"
                    >
                      <template #prefix>
                        <UserOutlined />
                      </template>
                    </a-statistic>
                  </a-card>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <a-card>
                    <a-statistic
                      title="使用率"
                      :value="使用率"
                      suffix="%"
                      :value-style="{ color: '#722ed1' }"
                    >
                      <template #prefix>
                        <BarChartOutlined />
                      </template>
                    </a-statistic>
                  </a-card>
                </a-col>
              </a-row>
            </div>
            
            <!-- 搜索筛选区域 -->
            <a-card class="search-section" title="激活码搜索">
              <div class="search-container">
                <a-row :gutter="16" align="middle">
                  <a-col :xs="24" :sm="16" :md="8" :lg="6">
                    <a-input-search
                      v-model:value="搜索表单.搜索关键词"
                      placeholder="搜索激活码或备注"
                      enter-button="搜索"
                      size="large"
                      allow-clear
                      @search="处理搜索"
                      @press-enter="处理搜索"
                      :loading="loading"
                    >
                      <template #prefix>
                        <SearchOutlined />
                      </template>
                    </a-input-search>
                  </a-col>
                  <a-col :xs="24" :sm="8" :md="4" :lg="3">
                    <a-select
                      v-model:value="搜索表单.类型id"
                      placeholder="选择类型"
                      size="large"
                      allow-clear
                      @change="处理搜索"
                      style="width: 100%"
                    >
                      <a-select-option 
                        v-for="类型 in 激活码类型列表" 
                        :key="类型.id" 
                        :value="类型.id"
                      >
                        {{ 类型.名称 }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :xs="24" :sm="8" :md="4" :lg="3">
                    <a-select
                      v-model:value="搜索表单.状态"
                      placeholder="使用状态"
                      size="large"
                      allow-clear
                      @change="处理搜索"
                      style="width: 100%"
                    >
                      <a-select-option value="unused">未使用</a-select-option>
                      <a-select-option value="used">已使用</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :xs="24" :sm="12" :md="8" :lg="6">
                    <a-range-picker
                      v-model:value="日期范围"
                      size="large"
                      @change="处理日期范围变化"
                      style="width: 100%"
                    />
                  </a-col>
                  <a-col :xs="24" :sm="12" :md="24" :lg="6">
                    <a-space>
                      <a-button
                        @click="重置搜索表单"
                        size="large"
                      >
                        <template #icon><ReloadOutlined /></template>
                        重置
                      </a-button>
                      <a-button
                        @click="导出激活码数据"
                        size="large"
                        :loading="导出loading"
                      >
                        <template #icon><ExportOutlined /></template>
                        导出
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </a-card>

            <!-- 激活码数据表格 -->
            <a-card class="table-section" title="激活码列表">
              <template #extra>
                <a-space>
                  <a-tooltip title="刷新数据">
                    <a-button
                      type="text"
                      @click="获取激活码列表"
                      :loading="loading"
                    >
                      <template #icon><ReloadOutlined /></template>
                    </a-button>
                  </a-tooltip>
                  <a-dropdown v-if="选中行键.length > 0">
                    <template #overlay>
                      <a-menu @click="处理批量操作菜单">
                        <a-menu-item key="batch-delete" danger>
                          <DeleteOutlined /> 批量删除 ({{ 选中行键.length }})
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <a-button>
                      批量操作 <DownOutlined />
                    </a-button>
                  </a-dropdown>
                </a-space>
              </template>
              
              <!-- 批量操作栏 -->
              <div v-if="选中行键.length > 0" class="batch-actions mb-4">
                <a-alert
                  message="批量操作"
                  :description="`已选择 ${选中行键.length} 个激活码`"
                  type="info"
                  show-icon
                  action
                >
                  <template #action>
                    <a-space>
                      <a-button 
                        size="small" 
                        type="primary" 
                        danger
                        @click="批量删除激活码"
                        :loading="批量删除loading"
                      >
                        批量删除
                      </a-button>
                      <a-button size="small" @click="选中行键 = []">取消选择</a-button>
                    </a-space>
                  </template>
                </a-alert>
              </div>

              <a-table
                :columns="表格列配置"
                :data-source="数据源"
                :loading="loading"
                row-key="id"
                :pagination="分页配置"
                @change="处理表格变化"
                :row-selection="行选择配置"
                :scroll="{ x: 1400 }"
                class="activation-code-table"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'activation_code'">
                    <div class="code-cell">
                      <a-typography-text 
                        :copyable="{ text: record.激活码 }"
                        code
                        class="activation-code-text"
                      >
                        {{ record.激活码 }}
                      </a-typography-text>
                    </div>
                  </template>

                  <template v-else-if="column.key === 'type_info'">
                    <div class="type-info">
                      <div class="type-name">{{ record.类型名称 }}</div>
                      <div class="type-desc">{{ record.类型描述 }}</div>
                      <div class="type-tags">
                        <a-tag v-if="record.会员天数" color="blue">
                          {{ record.会员天数 }}天会员
                        </a-tag>
                        <a-tag
                          :color="record.是否为一次性激活 === 0 ? 'orange' : 'green'"
                        >
                          <template #icon>
                            <ExclamationCircleOutlined v-if="record.是否为一次性激活 === 0" />
                            <ReloadOutlined v-else />
                          </template>
                          {{ record.是否为一次性激活 === 0 ? '一次性' : '永久' }}
                        </a-tag>
                      </div>
                    </div>
                  </template>
                  
                  <template v-else-if="column.key === 'status'">
                    <a-tag 
                      :color="record.状态 === '未使用' ? 'green' : 'orange'"
                    >
                      {{ record.状态 }}
                    </a-tag>
                  </template>

                  <template v-else-if="column.key === 'user_info'">
                    <div v-if="record.用户昵称" class="user-info">
                      <div class="user-name">{{ record.用户昵称 }}</div>
                      <div class="use-time">{{ 格式化时间(record.使用时间) }}</div>
                    </div>
                    <span v-else class="text-gray">未使用</span>
                  </template>

                  <template v-else-if="column.key === 'create_time'">
                    {{ 格式化时间(record.创建时间) }}
                  </template>

                  <template v-else-if="column.key === 'actions'">
                    <a-space>
                      <a-button 
                        type="link" 
                        size="small"
                        @click="查看激活码详情(record)"
                      >
                        详情
                      </a-button>
                      <a-popconfirm
                        v-if="record.状态 === '未使用'"
                        title="确定要删除这个激活码吗？"
                        @confirm="删除激活码(record.id)"
                      >
                        <a-button 
                          type="link" 
                          size="small"
                          danger
                        >
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </div>
        </a-tab-pane>
        <a-tab-pane key="types" tab="激活码类型管理">
          <ActivationCodeTypeManagement ref="类型管理组件引用" />
        </a-tab-pane>
      </a-tabs>

      <!-- 生成激活码模态框 -->
      <a-modal
        v-model:open="生成模态框可见"
        title="生成激活码"
        @ok="确认生成激活码"
        @cancel="取消生成激活码"
        :confirm-loading="生成loading"
        width="600px"
      >
        <a-form
          :model="生成表单"
          :rules="生成表单规则"
          ref="生成表单引用"
          layout="vertical"
        >
          <a-form-item label="激活码类型" name="类型id">
            <a-select
              v-model:value="生成表单.类型id"
              placeholder="请选择激活码类型"
              size="large"
            >
              <a-select-option 
                v-for="类型 in 激活码类型列表" 
                :key="类型.id" 
                :value="类型.id"
              >
                <div class="type-option">
                  <div class="type-name">{{ 类型.名称 }}</div>
                  <div class="type-desc">{{ 类型.描述 }} - {{ 类型.会员天数 }}天会员</div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- 激活码类型选择 -->
          <a-form-item label="激活码类型" name="是否为一次性激活">
            <a-radio-group
              v-model:value="生成表单.是否为一次性激活"
              size="large"
              class="activation-type-radio"
            >
              <a-radio :value="0" class="radio-option">
                <div class="radio-content">
                  <div class="radio-title">
                    <ExclamationCircleOutlined style="color: #faad14; margin-right: 8px;" />
                    一次性激活码
                  </div>
                  <div class="radio-desc">激活码只能使用一次，使用后即失效</div>
                </div>
              </a-radio>
              <a-radio :value="1" class="radio-option">
                <div class="radio-content">
                  <div class="radio-title">
                    <ReloadOutlined style="color: #52c41a; margin-right: 8px;" />
                    永久激活码
                  </div>
                  <div class="radio-desc">激活码可重复使用，每个用户只能使用一次</div>
                </div>
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item
            label="生成数量"
            name="数量"
            :validate-status="显示永久激活码数量警告 ? 'warning' : ''"
            :help="显示永久激活码数量警告 ? '建议永久激活码一次生成不超过10个，避免管理混乱' : ''"
          >
            <a-input-number
              v-model:value="生成表单.数量"
              :min="1"
              :max="1000"
              size="large"
              style="width: 100%"
              placeholder="请输入生成数量"
              :class="{ 'warning-input': 显示永久激活码数量警告 }"
            />
          </a-form-item>

          <a-form-item label="备注信息" name="备注">
            <a-textarea
              v-model:value="生成表单.备注"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              :maxlength="200"
              show-count
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 激活码详情模态框 -->
      <a-modal
        v-model:open="详情模态框可见"
        title="激活码详情"
        :footer="null"
        width="700px"
      >
        <div v-if="当前激活码详情" class="activation-code-detail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="激活码">
              <a-typography-text 
                :copyable="{ text: 当前激活码详情.激活码 }"
                code
              >
                {{ 当前激活码详情.激活码 }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag 
                :color="当前激活码详情.状态 === '未使用' ? 'green' : 'orange'"
              >
                {{ 当前激活码详情.状态 }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="类型名称">
              {{ 当前激活码详情.类型名称 }}
            </a-descriptions-item>
            <a-descriptions-item label="类型描述">
              {{ 当前激活码详情.类型描述 }}
            </a-descriptions-item>
            <a-descriptions-item label="会员天数">
              {{ 当前激活码详情.会员天数 }}天
            </a-descriptions-item>
            <a-descriptions-item label="类型价格">
              ¥{{ 当前激活码详情.类型价格 }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ 格式化时间(当前激活码详情.创建时间) }}
            </a-descriptions-item>
            <a-descriptions-item label="使用时间">
              {{ 当前激活码详情.使用时间 ? 格式化时间(当前激活码详情.使用时间) : '未使用' }}
            </a-descriptions-item>
            <a-descriptions-item label="使用用户" v-if="当前激活码详情.用户昵称">
              <div class="user-detail">
                <div>{{ 当前激活码详情.用户昵称 }}</div>
                <div class="user-email">{{ 当前激活码详情.用户邮箱 }}</div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="备注信息" :span="2">
              {{ 当前激活码详情.备注 || '无' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-modal>
    </ErrorBoundary>
  </div>
</template>

<script setup>
import {
  BarChartOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  ExportOutlined,
  KeyOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import ErrorBoundary from '../components/common/ErrorBoundary.vue'
import activationCodeService from '../services/activationCodeService.js'
import ActivationCodeTypeManagement from './ActivationCodeTypeManagement.vue'

// ==================== 响应式数据 ====================

const loading = ref(false)
const 生成loading = ref(false)
const 批量删除loading = ref(false)
const 导出loading = ref(false)

// 数据源
const 数据源 = ref([])
const 激活码类型列表 = ref([])
const 统计数据 = ref({
  基础统计: {},
  类型统计: [],
  近期使用: []
})

// 搜索表单
const 搜索表单 = reactive({
  搜索关键词: '',
  类型id: null,
  状态: null,
  开始日期: null,
  结束日期: null
})

// 日期范围
const 日期范围 = ref([])

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`
})

// 表格行选择
const 选中行键 = ref([])
const 行选择配置 = {
  selectedRowKeys: 选中行键,
  onChange: (selectedRowKeys) => {
    选中行键.value = selectedRowKeys
  },
  getCheckboxProps: (record) => ({
    disabled: record.状态 === '已使用' // 已使用的激活码不能删除
  })
}

// 生成激活码相关
const 生成模态框可见 = ref(false)
const 生成表单 = reactive({
  类型id: null,
  是否为一次性激活: 0, // 默认选择一次性激活码（数据库中0表示一次性）
  数量: 1,
  备注: ''
})

const 生成表单规则 = {
  类型id: [{ required: true, message: '请选择激活码类型' }],
  是否为一次性激活: [{ required: true, message: '请选择激活码类型' }],
  数量: [{ required: true, message: '请输入生成数量' }]
}

const 生成表单引用 = ref()

// 详情模态框
const 详情模态框可见 = ref(false)
const 当前激活码详情 = ref(null)

// 类型管理组件引用
const 类型管理组件引用 = ref()

// 标签页
const 当前标签页 = ref('codes')

// ==================== 计算属性 ====================

const 使用率 = computed(() => {
  const 总数 = 统计数据.value.基础统计?.总数 || 0
  const 已使用数量 = 统计数据.value.基础统计?.已使用数量 || 0
  return 总数 > 0 ? Math.round((已使用数量 / 总数) * 100) : 0
})

// 永久激活码数量警告
const 显示永久激活码数量警告 = computed(() => {
  return 生成表单.是否为一次性激活 === 1 && // 永久激活码
         生成表单.数量 > 10 // 数量超过10个
})

// ==================== 表格列配置 ====================

const 表格列配置 = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true
  },
  {
    title: '激活码',
    key: 'activation_code',
    width: 200,
    ellipsis: true
  },
  {
    title: '类型信息',
    key: 'type_info',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    filters: [
      { text: '未使用', value: '未使用' },
      { text: '已使用', value: '已使用' }
    ]
  },
  {
    title: '使用用户',
    key: 'user_info',
    width: 150
  },
  {
    title: '备注',
    dataIndex: '备注',
    key: 'remark',
    width: 150,
    ellipsis: true
  },
  {
    title: '创建时间',
    key: 'create_time',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// ==================== 方法 ====================

/**
 * 获取激活码列表
 */
const 获取激活码列表 = async () => {
  try {
    loading.value = true
    
    const 查询参数 = {
      page: 分页配置.current,
      size: 分页配置.pageSize,
      搜索关键词: 搜索表单.搜索关键词,
      类型id: 搜索表单.类型id,
      状态: 搜索表单.状态,
      开始日期: 搜索表单.开始日期,
      结束日期: 搜索表单.结束日期
    }
    
    const 响应 = await activationCodeService.获取激活码列表(查询参数)
    
    if (响应.status === 100) {
      数据源.value = 响应.data.list || []
      分页配置.total = 响应.data.total || 0
    } else {
      message.error(响应.message || '获取激活码列表失败')
    }
  } catch (error) {
    console.error('获取激活码列表失败:', error)
    message.error('获取激活码列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 获取激活码类型列表
 */
const 获取激活码类型列表 = async () => {
  try {
    const 响应 = await activationCodeService.获取激活码类型列表()
    
    if (响应.status === 100) {
      激活码类型列表.value = 响应.data || []
    } else {
      message.error(响应.message || '获取激活码类型列表失败')
    }
  } catch (error) {
    console.error('获取激活码类型列表失败:', error)
    message.error('获取激活码类型列表失败')
  }
}

/**
 * 获取激活码统计
 */
const 获取激活码统计 = async () => {
  try {
    const 响应 = await activationCodeService.获取激活码统计()
    
    if (响应.status === 100) {
      统计数据.value = 响应.data || {}
    } else {
      console.error('获取激活码统计失败:', 响应.message)
    }
  } catch (error) {
    console.error('获取激活码统计失败:', error)
  }
}

/**
 * 处理搜索
 */
const 处理搜索 = () => {
  分页配置.current = 1
  获取激活码列表()
}

/**
 * 重置搜索表单
 */
const 重置搜索表单 = () => {
  搜索表单.搜索关键词 = ''
  搜索表单.类型id = null
  搜索表单.状态 = null
  搜索表单.开始日期 = null
  搜索表单.结束日期 = null
  日期范围.value = []
  分页配置.current = 1
  获取激活码列表()
}

/**
 * 处理日期范围变化
 */
const 处理日期范围变化 = (_, dateStrings) => {
  搜索表单.开始日期 = dateStrings[0] || null
  搜索表单.结束日期 = dateStrings[1] || null
  处理搜索()
}

/**
 * 处理表格变化
 */
const 处理表格变化 = (pagination) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  获取激活码列表()
}

/**
 * 显示生成模态框
 */
const 显示生成模态框 = () => {
  生成模态框可见.value = true
  // 重置表单
  生成表单.类型id = null
  生成表单.是否为一次性激活 = 0 // 默认选择一次性激活码（数据库中0表示一次性）
  生成表单.数量 = 1
  生成表单.备注 = ''
}

/**
 * 取消生成激活码
 */
const 取消生成激活码 = () => {
  生成模态框可见.value = false
}

/**
 * 确认生成激活码
 */
const 确认生成激活码 = async () => {
  try {
    await 生成表单引用.value.validate()
    
    生成loading.value = true
    
    const 响应 = await activationCodeService.生成激活码(生成表单)
    
    if (响应.status === 100) {
      message.success(响应.message || '激活码生成成功')
      生成模态框可见.value = false
      // 刷新列表和统计
      获取激活码列表()
      获取激活码统计()
    } else {
      message.error(响应.message || '生成激活码失败')
    }
  } catch (error) {
    console.error('生成激活码失败:', error)
    message.error('生成激活码失败')
  } finally {
    生成loading.value = false
  }
}

/**
 * 查看激活码详情
 */
const 查看激活码详情 = async (record) => {
  try {
    const 响应 = await activationCodeService.获取激活码详情(record.id)
    
    if (响应.status === 100) {
      当前激活码详情.value = 响应.data
      详情模态框可见.value = true
    } else {
      message.error(响应.message || '获取激活码详情失败')
    }
  } catch (error) {
    console.error('获取激活码详情失败:', error)
    message.error('获取激活码详情失败')
  }
}

/**
 * 删除激活码
 */
const 删除激活码 = async (激活码id) => {
  try {
    const 响应 = await activationCodeService.删除激活码(激活码id)
    
    if (响应.status === 100) {
      message.success('激活码删除成功')
      获取激活码列表()
      获取激活码统计()
    } else {
      message.error(响应.message || '删除激活码失败')
    }
  } catch (error) {
    console.error('删除激活码失败:', error)
    message.error('删除激活码失败')
  }
}

/**
 * 批量删除激活码
 */
const 批量删除激活码 = async () => {
  if (选中行键.value.length === 0) {
    message.warning('请选择要删除的激活码')
    return
  }
  
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${选中行键.value.length} 个激活码吗？`,
    onOk: async () => {
      try {
        批量删除loading.value = true
        
        const 响应 = await activationCodeService.批量删除激活码(选中行键.value)
        
        if (响应.status === 100) {
          message.success('批量删除成功')
          选中行键.value = []
          获取激活码列表()
          获取激活码统计()
        } else {
          message.error(响应.message || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败')
      } finally {
        批量删除loading.value = false
      }
    }
  })
}

/**
 * 处理批量操作菜单
 */
const 处理批量操作菜单 = ({ key }) => {
  switch (key) {
    case 'batch-delete':
      批量删除激活码()
      break
  }
}

/**
 * 导出激活码数据
 */
const 导出激活码数据 = async () => {
  try {
    导出loading.value = true
    
    const 查询参数 = {
      搜索关键词: 搜索表单.搜索关键词,
      类型id: 搜索表单.类型id,
      状态: 搜索表单.状态,
      开始日期: 搜索表单.开始日期,
      结束日期: 搜索表单.结束日期
    }
    
    const blob = await activationCodeService.导出激活码数据(查询参数)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `激活码数据_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    
    // 清理URL对象
    window.URL.revokeObjectURL(url)
    
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    导出loading.value = false
  }
}

/**
 * 格式化时间
 */
const 格式化时间 = (时间) => {
  if (!时间) return '-'
  return new Date(时间).toLocaleString('zh-CN')
}

/**
 * 显示类型创建模态框
 */
const 显示类型创建模态框 = () => {
  类型管理组件引用.value.显示类型创建模态框()
}

// ==================== 生命周期 ====================

onMounted(async () => {
  try {
    await Promise.all([
      获取激活码列表(),
      获取激活码类型列表(),
      获取激活码统计()
    ])
  } catch (error) {
    console.error('激活码管理页面初始化失败:', error)
    message.error('页面初始化失败，请刷新重试')
  }
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  color: white;
  margin: 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 0 0 0;
  font-size: 14px;
}

.stats-summary {
  background: rgba(255, 255, 255, 0.1);
  padding: 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.add-code-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.add-code-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 标签页样式 */
.main-tabs {
  margin-bottom: 24px;
}

.main-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.main-tabs .ant-tabs-content-holder {
  padding-top: 16px;
}

.tab-content {
  min-height: 400px;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 24px;
}

.stats-cards .ant-card {
  text-align: center;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 24px;
}

.search-container {
  padding: 16px 0;
}

/* 表格样式 */
.table-section {
  margin-bottom: 24px;
}

.batch-actions {
  margin-bottom: 16px;
}

.activation-code-table .code-cell {
  font-family: 'Courier New', monospace;
}

.activation-code-text {
  font-size: 12px;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.type-info .type-name {
  font-weight: 600;
  color: #1890ff;
}

.type-info .type-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.user-info .user-name {
  font-weight: 500;
}

.user-info .use-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.text-gray {
  color: #999;
}

/* 模态框样式 */
.type-option .type-name {
  font-weight: 600;
  color: #1890ff;
}

.type-option .type-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.activation-code-detail .user-detail .user-email {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

/* 类型信息样式 */
.type-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-name {
  font-weight: 600;
  color: #262626;
}

.type-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.type-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 4px;
}

/* 激活码类型选择样式 */
.activation-type-radio {
  width: 100%;
}

.activation-type-radio .radio-option {
  display: block;
  width: 100%;
  margin-bottom: 16px;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activation-type-radio .radio-option:hover {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.activation-type-radio .radio-option.ant-radio-wrapper-checked {
  border-color: #1890ff;
  background-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.radio-content {
  margin-left: 8px;
}

.radio-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 永久激活码数量警告样式 */
.warning-input {
  border-color: #faad14 !important;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2) !important;
}

.warning-input:hover {
  border-color: #faad14 !important;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.3) !important;
}

.warning-input:focus {
  border-color: #faad14 !important;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.4) !important;
}

/* 警告提示文字样式 */
.ant-form-item-explain-warning {
  color: #faad14;
  font-size: 13px;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.ant-form-item-explain-warning::before {
  content: "⚠️";
  margin-right: 4px;
  font-size: 12px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .search-container .ant-row {
    gap: 8px;
  }

  .stats-cards .ant-col {
    margin-bottom: 16px;
  }

  .activation-type-radio .radio-option {
    padding: 12px;
    margin-bottom: 12px;
  }

  .radio-title {
    font-size: 14px;
  }

  .radio-desc {
    font-size: 12px;
  }
}
</style> 