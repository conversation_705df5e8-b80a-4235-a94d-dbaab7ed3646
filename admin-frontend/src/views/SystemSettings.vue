<template>
  <div>
    <a-card title="系统设置" :bordered="false">
      <a-spin :spinning="loadingSettings">
        <template v-if="errorMsg">
          <a-alert :message="errorMsg" type="error" show-icon style="margin-bottom: 16px;" />
        </template>
        <a-form
          :model="settingsFormState"
          layout="vertical"
          @finish="handleSaveSettings"
          ref="formRef"
        >
          <a-tabs v-model:activeKey="activeTabKey">
            <a-tab-pane key="general" tab="通用设置">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item
                    label="站点名称"
                    name="siteName"
                    :rules="[{ required: true, message: '请输入站点名称' }]"
                  >
                    <a-input v-model:value="settingsFormState.siteName" placeholder="例如：我的管理后台" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="站点Logo URL"
                    name="siteLogoUrl"
                  >
                    <a-input v-model:value="settingsFormState.siteLogoUrl" placeholder="例如：https://example.com/logo.png" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="版权信息" name="copyrightInfo">
                <a-textarea v-model:value="settingsFormState.copyrightInfo" placeholder="例如：© 2024 Your Company. All rights reserved." :rows="2" />
              </a-form-item>
              <a-form-item label="维护模式" name="maintenanceMode">
                <a-switch v-model:checked="settingsFormState.maintenanceMode" />
                <span style="margin-left: 8px;">{{ settingsFormState.maintenanceMode ? '已开启' : '已关闭' }}</span>
              </a-form-item>
               <a-form-item label="维护模式提示信息" name="maintenanceMessage" v-if="settingsFormState.maintenanceMode">
                <a-textarea v-model:value="settingsFormState.maintenanceMessage" placeholder="系统正在维护中，请稍后访问。" :rows="2" />
              </a-form-item>
            </a-tab-pane>

            <a-tab-pane key="api" tab="API设置">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="API请求速率限制 (次/分钟)" name="apiRateLimitPerMinute">
                    <a-input-number v-model:value="settingsFormState.apiRateLimitPerMinute" :min="0" style="width: 100%;" placeholder="0 表示无限制"/>
                  </a-form-item>
                </a-col>
                 <a-col :span="12">
                  <a-form-item label="API默认超时时间 (毫秒)" name="apiDefaultTimeoutMs">
                    <a-input-number v-model:value="settingsFormState.apiDefaultTimeoutMs" :min="500" style="width: 100%;" placeholder="例如: 30000"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-tab-pane>

            <a-tab-pane key="logging" tab="日志设置">
                <a-form-item label="默认日志级别" name="defaultLogLevel">
                    <a-select v-model:value="settingsFormState.defaultLogLevel" placeholder="选择默认日志级别">
                        <a-select-option value="DEBUG">DEBUG</a-select-option>
                        <a-select-option value="INFO">INFO</a-select-option>
                        <a-select-option value="WARNING">WARNING</a-select-option>
                        <a-select-option value="ERROR">ERROR</a-select-option>
                        <a-select-option value="CRITICAL">CRITICAL</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="日志保留天数" name="logRetentionDays">
                    <a-input-number v-model:value="settingsFormState.logRetentionDays" :min="0" style="width: 100%;" placeholder="0 表示永久保留"/>
                </a-form-item>
            </a-tab-pane>
            
            <a-tab-pane key="mail" tab="邮件服务 (SMTP)">
              <a-alert message="邮件服务设置更改后可能需要重启应用或服务才能生效。" type="info" show-icon style="margin-bottom:16px;"/>
              <a-form-item label="启用邮件服务" name="mailServiceEnabled">
                <a-switch v-model:checked="settingsFormState.mailServiceEnabled" />
              </a-form-item>
              <div v-if="settingsFormState.mailServiceEnabled">
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="SMTP 服务器地址" name="smtpHost" :rules="[{ required: settingsFormState.mailServiceEnabled, message: '请输入SMTP服务器地址' }]">
                            <a-input v-model:value="settingsFormState.smtpHost" placeholder="例如：smtp.example.com" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="SMTP 服务器端口" name="smtpPort" :rules="[{ required: settingsFormState.mailServiceEnabled, message: '请输入SMTP端口' }]">
                            <a-input-number v-model:value="settingsFormState.smtpPort" :min="1" :max="65535" style="width: 100%;" placeholder="例如：587 或 465"/>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="SMTP 用户名" name="smtpUser" :rules="[{ required: settingsFormState.mailServiceEnabled, message: '请输入SMTP用户名' }]">
                            <a-input v-model:value="settingsFormState.smtpUser" placeholder="例如：<EMAIL>" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="SMTP 密码/授权码" name="smtpPassword">
                            <a-input-password v-model:value="settingsFormState.smtpPassword" placeholder="输入密码或授权码" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-form-item label="发件人邮箱地址" name="smtpSenderEmail" :rules="[{ required: settingsFormState.mailServiceEnabled, type: 'email', message: '请输入有效的发件人邮箱' }]">
                  <a-input v-model:value="settingsFormState.smtpSenderEmail" placeholder="例如：<EMAIL>" />
                </a-form-item>
                 <a-form-item label="发件人名称" name="smtpSenderName">
                  <a-input v-model:value="settingsFormState.smtpSenderName" placeholder="例如：系统通知" />
                </a-form-item>
                <a-form-item label="使用SSL/TLS加密" name="smtpUseSsl">
                  <a-radio-group v-model:value="settingsFormState.smtpEncryption">
                    <a-radio value="none">无加密</a-radio>
                    <a-radio value="ssl">SSL</a-radio>
                    <a-radio value="tls">TLS/STARTTLS</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-button @click="testSmtpSettings" :loading="testingSmtp">测试邮件发送</a-button>
              </div>
            </a-tab-pane>

            <!-- 可以根据需要添加更多设置页签 -->

          </a-tabs>
          
          <a-divider />
          <a-form-item style="text-align: right;">
            <a-button @click="fetchSettings" :disabled="loadingSettings" style="margin-right: 8px;">重置/刷新</a-button>
            <a-button type="primary" html-type="submit" :loading="savingSettings">
              保存设置
            </a-button>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import superAdminService from '../services/superAdminService'; // 使用统一的API服务
import { useSuperAdminRequest } from '../composables/useApiRequest'; // 使用统一的响应处理

const loadingSettings = ref(false);
const savingSettings = ref(false);
const testingSmtp = ref(false);
const activeTabKey = ref('general');
const formRef = ref();
const errorMsg = ref('');

const initialSettingsFormState = () => ({
  siteName: '',
  siteLogoUrl: '',
  copyrightInfo: '',
  maintenanceMode: false,
  maintenanceMessage: '系统正在维护中，请稍后访问。',
  apiRateLimitPerMinute: 0,
  apiDefaultTimeoutMs: 30000,
  defaultLogLevel: 'INFO',
  logRetentionDays: 30,
  mailServiceEnabled: false,
  smtpHost: '',
  smtpPort: 587,
  smtpUser: '',
  smtpPassword: '',
  smtpSenderEmail: '',
  smtpSenderName: '',
  smtpEncryption: 'tls', // 'none', 'ssl', 'tls'
});

const settingsFormState = reactive({ ...initialSettingsFormState() });

// 使用统一的API响应处理
const { loading, 执行API请求 } = useSuperAdminRequest();

const fetchSettings = async () => {
  loadingSettings.value = true;
  // 使用统一的API调用，自动处理错误和加载状态
  const result = await 执行API请求(() => superAdminService.getSystemConfig());
  
  if (result) {
    // 成功获取设置数据
    Object.assign(settingsFormState, { ...initialSettingsFormState(), ...result });
    errorMsg.value = '';
  } else {
    // 失败时的处理（错误已在executeWithResponse中处理）
    errorMsg.value = '获取系统设置失败';
  }
  loadingSettings.value = false;
};

const handleSaveSettings = async () => {
  try {
    await formRef.value.validate();
    savingSettings.value = true;
    
    // 使用统一的API调用，自动处理错误和成功提示
    const result = await 执行API请求(() => superAdminService.updateSystemConfig(settingsFormState), '设置保存成功');
    
    if (result) {
      fetchSettings(); // 保存成功后重新加载以确认
    }
  } catch (errorInfo) {
    console.error('保存设置错误:', errorInfo);
    // 表单验证错误由 Ant Design 自动处理
    if (!(errorInfo && errorInfo.errorFields)) {
      message.error('保存设置时发生错误');
    }
  }
  savingSettings.value = false;
};

const testSmtpSettings = async () => {
  if (!settingsFormState.smtpHost || !settingsFormState.smtpPort || !settingsFormState.smtpUser) {
      message.warn('请先填写SMTP服务器地址、端口和用户名。');
      return;
  }
  testingSmtp.value = true;
  
  const smtpConfig = {
      host: settingsFormState.smtpHost,
      port: settingsFormState.smtpPort,
      user: settingsFormState.smtpUser,
      password: settingsFormState.smtpPassword,
      senderEmail: settingsFormState.smtpSenderEmail || settingsFormState.smtpUser,
      senderName: settingsFormState.smtpSenderName,
      encryption: settingsFormState.smtpEncryption,
  };
  
  message.loading('正在发送测试邮件...', 0);
  
  // 使用统一的API调用，自动处理错误
  const result = await 执行API请求(() => superAdminService.testSmtpConfig(smtpConfig), 'SMTP测试邮件发送成功！请检查测试邮箱。');
  
  message.destroy(); // 清除加载提示
  testingSmtp.value = false;
};

onMounted(() => {
  fetchSettings();
});

</script>

<style scoped>
/* 你可以在这里添加组件特定的样式 */
.ant-form-item {
  margin-bottom: 16px;
}
</style>