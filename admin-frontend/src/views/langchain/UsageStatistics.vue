<template>
  <div class="usage-statistics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>对话记录管理</h2>
      <p>查看和管理智能体对话记录，支持高级筛选和详情查看</p>

      <!-- 快速导航 -->
      <div class="quick-nav">
        <a-space>
          <a-button type="link" @click="$router.push('/langchain/overview')">
            返回概览
          </a-button>
          <a-button type="link" @click="$router.push('/langchain/agents')">
            智能体管理
          </a-button>
          <a-divider type="vertical" />
          <a-button type="primary" @click="导出记录" :loading="导出中">
            <template #icon><DownloadOutlined /></template>
            导出记录
          </a-button>
          <a-button @click="刷新数据" :loading="记录加载中">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>



    <!-- 筛选条件 -->
    <div class="filter-section">
      <a-card title="筛选条件" class="filter-card">
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form layout="inline" :model="查询条件" @finish="加载对话记录">
              <a-form-item label="时间范围">
                <a-range-picker
                  v-model:value="时间范围"
                  :placeholder="['开始时间', '结束时间']"
                  format="YYYY-MM-DD"
                />
              </a-form-item>

              <a-form-item label="智能体">
                <a-select
                  v-model:value="查询条件.智能体id"
                  placeholder="选择智能体"
                  style="width: 200px"
                  allowClear
                  show-search
                  :filter-option="智能体筛选"
                >
                  <a-select-option value="">全部智能体</a-select-option>
                  <a-select-option
                    v-for="agent in 智能体列表"
                    :key="agent.id"
                    :value="agent.id"
                  >
                    {{ agent.智能体名称 }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="用户搜索">
                <a-input
                  v-model:value="查询条件.用户关键词"
                  placeholder="用户名/手机号"
                  style="width: 150px"
                  allowClear
                />
              </a-form-item>

              <a-form-item label="消息搜索">
                <a-input
                  v-model:value="查询条件.消息关键词"
                  placeholder="搜索消息内容"
                  style="width: 150px"
                  allowClear
                />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit" :loading="记录加载中">
                    <template #icon><SearchOutlined /></template>
                    查询记录
                  </a-button>
                  <a-button @click="重置筛选">
                    <template #icon><ReloadOutlined /></template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-col>
        </a-row>

      </a-card>
    </div>

    <!-- 记录列表 -->
    <div class="records-section">
      <a-card title="对话记录">
        <template #extra>
          <a-button @click="导出记录" :loading="导出中">
            <template #icon><DownloadOutlined /></template>
            导出记录
          </a-button>
        </template>
        
        <a-table
          :columns="记录表格列"
          :data-source="对话记录列表"
          :pagination="分页配置"
          :loading="记录加载中"
          @change="处理表格变化"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-button type="link" size="small" @click="查看对话详情(record)">
                查看详情
              </a-button>
            </template>
            <template v-if="column.key === 'user_message'">
              <a-tooltip :title="record.用户消息">
                <span class="message-preview">{{ 截断文本(record.用户消息, 50) }}</span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'agent_reply'">
              <a-tooltip :title="record.智能体回复">
                <span class="message-preview">{{ 截断文本(record.智能体回复, 50) }}</span>
              </a-tooltip>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 对话详情弹窗 -->
    <a-modal
      v-model:open="显示对话详情"
      title="对话详情"
      width="800px"
      :footer="null"
    >
      <div v-if="当前对话记录" class="conversation-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="用户">{{ 当前对话记录.用户名 || '未知用户' }}</a-descriptions-item>
          <a-descriptions-item label="智能体">{{ 当前对话记录.智能体名称 || '未知智能体' }}</a-descriptions-item>
          <a-descriptions-item label="模型">{{ 当前对话记录.模型名称 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="处理时长">{{ 当前对话记录.处理时长 || 0 }}ms</a-descriptions-item>
          <a-descriptions-item label="算力消耗">{{ 当前对话记录.算力消耗 || 0 }}</a-descriptions-item>
          <a-descriptions-item label="令牌消耗">{{ 当前对话记录.令牌消耗 || 0 }}</a-descriptions-item>
          <a-descriptions-item label="对话时间" :span="2">{{ dayjs(当前对话记录.创建时间).format('YYYY-MM-DD HH:mm:ss') }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="conversation-messages">
          <div class="message-item user-message">
            <div class="message-label">用户消息：</div>
            <div class="message-content">{{ 当前对话记录.用户消息 }}</div>
          </div>
          <div class="message-item agent-message">
            <div class="message-label">智能体回复：</div>
            <div class="message-content">{{ 当前对话记录.智能体回复 }}</div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

import langchainService from '@/services/langchainService'

// 响应式数据
const 对话记录列表 = ref([])
const 智能体列表 = ref([])
const 当前对话记录 = ref(null)

// 状态控制
const 记录加载中 = ref(false)
const 导出中 = ref(false)
const 显示对话详情 = ref(false)

// 查询条件
const 查询条件 = reactive({
  开始日期: dayjs().subtract(7, 'day'),
  结束日期: dayjs(),
  智能体id: null,
  用户关键词: '',
  消息关键词: ''
})

// 时间范围计算属性
const 时间范围 = computed({
  get() {
    return [查询条件.开始日期, 查询条件.结束日期]
  },
  set(value) {
    if (value && value.length === 2) {
      查询条件.开始日期 = value[0]
      查询条件.结束日期 = value[1]
    }
  }
})

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列定义
const 记录表格列 = [
  {
    title: '对话时间',
    dataIndex: '创建时间',
    key: 'create_time',
    width: 150,
    sorter: true,
    customRender: ({ text }) => {
      if (!text) return '-'
      try {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      } catch (e) {
        return text
      }
    }
  },
  {
    title: '用户',
    dataIndex: '用户名',
    key: 'user_name',
    width: 100,
    customRender: ({ text, record }) => {
      return text || record.用户名称 || record.昵称 || record.手机号 || '未知用户'
    }
  },
  {
    title: '智能体',
    dataIndex: '智能体名称',
    key: 'agent_name',
    width: 120,
    customRender: ({ text }) => text || '未知智能体'
  },
  {
    title: '用户消息',
    dataIndex: '用户消息',
    key: 'user_message',
    width: 200,
    customRender: ({ text }) => {
      if (!text) return '-'
      return text.length > 50 ? text.substring(0, 50) + '...' : text
    }
  },
  {
    title: '智能体回复',
    dataIndex: '智能体回复',
    key: 'agent_reply',
    width: 200,
    customRender: ({ text }) => {
      if (!text) return '-'
      return text.length > 50 ? text.substring(0, 50) + '...' : text
    }
  },
  {
    title: '处理时长',
    dataIndex: '处理时长',
    key: 'process_time',
    width: 100,
    customRender: ({ text }) => {
      if (!text || text === 0) return '-'
      return `${text}ms`
    }
  },
  {
    title: '令牌消耗',
    dataIndex: '令牌消耗',
    key: 'token_cost',
    width: 100,
    customRender: ({ text }) => text || '-'
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 方法定义



// 新增筛选方法
const 智能体筛选 = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}



const 重置筛选 = () => {
  Object.assign(查询条件, {
    开始日期: dayjs().subtract(7, 'day'),
    结束日期: dayjs(),
    智能体id: null,
    用户关键词: '',
    消息关键词: ''
  })
  加载对话记录()
}

// 导出记录
const 导出记录 = async () => {
  try {
    导出中.value = true

    // 构建CSV内容
    const csvRows = [
      ['时间', '用户', '智能体', '用户消息', '智能体回复', '处理时长(ms)', '令牌消耗'],
      ...对话记录列表.value.map(record => [
        dayjs(record.创建时间).format('YYYY-MM-DD HH:mm:ss'),
        record.用户名 || '未知用户',
        record.智能体名称 || '未知智能体',
        record.用户消息 || '',
        record.智能体回复 || '',
        record.处理时长 || 0,
        record.令牌消耗 || 0
      ])
    ]

    const csvContent = csvRows.map(row =>
      row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    ).join('\n')

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `对话记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    link.click()

    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败: ' + error.message)
  } finally {
    导出中.value = false
  }
}









const 加载智能体列表 = async () => {
  try {
    const response = await langchainService.获取智能体列表({
      页码: 1,
      每页数量: 100,
      状态: 'running'
    })
    if (response.status === 100) {
      智能体列表.value = response.data.智能体列表 || []
    }
  } catch (error) {
    console.error('加载智能体列表失败:', error)
  }
}

const 刷新数据 = async () => {
  await Promise.all([
    加载智能体列表(),
    加载对话记录()
  ])
}

const 加载对话记录 = async (查询参数 = null) => {
  try {
    记录加载中.value = true

    // 如果没有传入查询参数，则根据当前查询条件构建
    if (!查询参数) {
      查询参数 = {
        开始日期: 查询条件.开始日期?.format('YYYY-MM-DD'),
        结束日期: 查询条件.结束日期?.format('YYYY-MM-DD'),
        智能体id: 查询条件.智能体id,
        用户关键词: 查询条件.用户关键词,
        消息关键词: 查询条件.消息关键词,
        页码: 分页配置.current,
        每页数量: 分页配置.pageSize
      }
    }

    console.log('🔍 发送对话记录查询请求:', 查询参数)
    const response = await langchainService.获取对话记录列表(查询参数)
    console.log('📋 对话记录响应:', response)

    if (response && response.status === 100) {
      // 确保数据格式正确
      const 记录数据 = response.data.记录列表 || []
      对话记录列表.value = 记录数据.map(record => ({
        ...record,
        // 确保时间字段格式正确
        创建时间: record.创建时间 || record.对话时间 || record.时间,
        // 确保用户名显示正确 - 后端已经通过COALESCE处理了
        用户名: record.用户名称 || record.用户名 || record.昵称 || record.手机号 || '未知用户',
        // 确保智能体名称显示正确
        智能体名称: record.智能体名称 || '未知智能体',
        // 确保消息字段存在
        用户消息: record.用户消息 || '',
        智能体回复: record.智能体回复 || '',
        // 确保数值字段格式正确
        处理时长: record.处理时长 || 0,
        令牌消耗: record.令牌消耗 || 0,
        算力消耗: record.算力消耗 || 0
      }))

      分页配置.total = response.data.总数量 || 0
      console.log('✅ 对话记录加载成功:', {
        记录数量: 对话记录列表.value.length,
        总数量: 分页配置.total,
        第一条记录: 对话记录列表.value[0]
      })
    } else {
      message.warning(response?.message || '获取对话记录失败')
    }
  } catch (error) {
    console.error('❌ 加载对话记录失败:', error)
    message.error('加载对话记录失败，请稍后重试')
  } finally {
    记录加载中.value = false
  }
}











const 处理表格变化 = (pagination) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  加载对话记录()
}

const 查看对话详情 = (record) => {
  当前对话记录.value = record
  显示对话详情.value = true
}

const 截断文本 = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}





// 生命周期
onMounted(() => {
  刷新数据()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.usage-statistics {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}





.filter-section {
  margin-bottom: 24px;
}

.filter-section .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.records-section .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-preview {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-detail {
  padding: 16px 0;
}

.conversation-messages {
  margin-top: 24px;
}

.message-item {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.user-message {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.agent-message {
  background: #f0f5ff;
  border-color: #adc6ff;
}

.message-label {
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.message-content {
  color: #595959;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .usage-statistics {
    padding: 16px;
  }

  .statistics-cards .ant-col {
    margin-bottom: 16px;
  }

  .charts-section .ant-col {
    margin-bottom: 16px;
  }

  .analysis-section .ant-col {
    margin-bottom: 16px;
  }
}

/* 快速导航样式 */
.quick-nav {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 快速筛选标签样式 */
.quick-filters {
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
}

/* 深度分析样式 */
.analysis-section {
  margin-bottom: 24px;
}

.analysis-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analysis-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 质量指标样式 */
.quality-metrics {
  padding: 8px 0;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
}

.metric-value {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

/* 热门话题样式 */
.topic-list {
  max-height: 300px;
  overflow-y: auto;
}

.topic-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.topic-item:last-child {
  border-bottom: none;
}

.topic-rank {
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
}

.topic-content {
  flex: 1;
}

.topic-name {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.topic-count {
  font-size: 12px;
  color: #8c8c8c;
}

.topic-trend {
  margin-left: 8px;
}

/* 时间分析样式 */
.time-insights {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.insight-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-label {
  font-size: 12px;
  color: #8c8c8c;
}

.insight-value {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .quick-nav .ant-space {
    flex-wrap: wrap;
  }

  .quick-filters {
    margin: 0 -16px;
    border-radius: 0;
  }

  .metric-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .metric-value {
    margin-top: 4px;
  }

  .topic-item {
    flex-wrap: wrap;
  }

  .topic-trend {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
