<template>
  <div class="prompt-config">
    <div class="section-header">
      <h2><EditOutlined /> 提示词配置</h2>
      <p>配置智能体的各类提示词</p>
    </div>

    <!-- 提示词标签页 -->
    <a-tabs v-model:activeKey="activeTab" type="card" class="prompt-tabs">
      <!-- 系统提示词 -->
      <a-tab-pane key="system" tab="系统提示词">
        <PromptEditor
          v-model:value="localForm.系统提示词"
          :placeholder="'定义智能体的角色、行为规范和回答风格'"
          :label="'系统提示词'"
          :error="errors.系统提示词"
          @change="handleFormChange"
          @validate="(error) => emit('validate', { 系统提示词: error })"
        />
      </a-tab-pane>

      <!-- 用户提示词 -->
      <a-tab-pane key="user" tab="用户提示词">
        <PromptEditor
          v-model:value="localForm.用户提示词"
          :placeholder="'定义用户消息的处理方式和响应格式'"
          :label="'用户提示词'"
          :error="errors.用户提示词"
          @change="handleFormChange"
          @validate="(error) => emit('validate', { 用户提示词: error })"
        />
      </a-tab-pane>

      <!-- 角色设定 -->
      <a-tab-pane key="role" tab="角色设定">
        <PromptEditor
          v-model:value="localForm.角色设定"
          :placeholder="'详细描述智能体的角色定位和专业领域'"
          :label="'角色设定'"
          :error="errors.角色设定"
          @change="handleFormChange"
          @validate="(error) => emit('validate', { 角色设定: error })"
        />
      </a-tab-pane>

      <!-- 行为规范 -->
      <a-tab-pane key="behavior" tab="行为规范">
        <PromptEditor
          v-model:value="localForm.行为规范"
          :placeholder="'定义智能体的行为准则和限制条件'"
          :label="'行为规范'"
          :error="errors.行为规范"
          @change="handleFormChange"
          @validate="(error) => emit('validate', { 行为规范: error })"
        />
      </a-tab-pane>
    </a-tabs>

    <!-- 提示词工具栏 -->
    <div class="prompt-toolbar">
      <a-space>
        <a-select
          v-model:value="selectedTemplate"
          placeholder="选择模板"
          style="width: 150px"
          @change="applyTemplate"
        >
          <a-select-option 
            v-for="template in 提示词模板列表" 
            :key="template.key" 
            :value="template.key"
          >
            {{ template.name }}
          </a-select-option>
        </a-select>

        <a-dropdown>
          <a-button size="small">
            插入变量 <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu @click="insertVariable">
              <a-menu-item 
                v-for="variable in 常用变量列表" 
                :key="variable.key"
                :data-placeholder="variable.placeholder"
              >
                <span>{{ variable.name }}</span>
                <small style="color: #8c8c8c; margin-left: 8px;">
                  {{ variable.placeholder }}
                </small>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-button size="small" @click="showPreviewModal = true">
          <EyeOutlined /> 预览
        </a-button>

        <a-button size="small" @click="formatPrompt">
          <FormatPainterOutlined /> 格式化
        </a-button>
      </a-space>
    </div>

    <!-- 提示词预览弹窗 -->
    <a-modal
      v-model:open="showPreviewModal"
      title="提示词预览"
      width="800px"
      :footer="null"
    >
      <div class="preview-content">
        <a-tabs type="card">
          <a-tab-pane key="system" tab="系统提示词" v-if="localForm.系统提示词">
            <div class="preview-text">{{ 生成提示词预览(localForm.系统提示词) }}</div>
          </a-tab-pane>
          <a-tab-pane key="user" tab="用户提示词" v-if="localForm.用户提示词">
            <div class="preview-text">{{ 生成提示词预览(localForm.用户提示词) }}</div>
          </a-tab-pane>
          <a-tab-pane key="role" tab="角色设定" v-if="localForm.角色设定">
            <div class="preview-text">{{ 生成提示词预览(localForm.角色设定) }}</div>
          </a-tab-pane>
          <a-tab-pane key="behavior" tab="行为规范" v-if="localForm.行为规范">
            <div class="preview-text">{{ 生成提示词预览(localForm.行为规范) }}</div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { EditOutlined, DownOutlined, EyeOutlined, FormatPainterOutlined } from '@ant-design/icons-vue'
import { usePromptTemplates } from '../../composables/usePromptTemplates'
import PromptEditor from './PromptEditor.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 提示词模板管理
const {
  提示词模板列表,
  常用变量列表,
  获取模板内容,
  插入变量到文本,
  格式化提示词,
  生成提示词预览
} = usePromptTemplates()

// 本地状态
const activeTab = ref('system')
const selectedTemplate = ref(null)
const showPreviewModal = ref(false)

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 应用模板
const applyTemplate = (templateKey) => {
  if (!templateKey) return
  
  const templateContent = 获取模板内容(templateKey)
  const currentField = getCurrentPromptField()
  
  if (currentField) {
    localForm.value[currentField] = templateContent
    handleFormChange()
  }
  
  selectedTemplate.value = null
}

// 获取当前提示词字段
const getCurrentPromptField = () => {
  const fieldMap = {
    'system': '系统提示词',
    'user': '用户提示词', 
    'role': '角色设定',
    'behavior': '行为规范'
  }
  return fieldMap[activeTab.value]
}

// 插入变量
const insertVariable = ({ key }) => {
  const variable = 常用变量列表.value.find(v => v.key === key)
  if (!variable) return
  
  const currentField = getCurrentPromptField()
  if (currentField) {
    const currentText = localForm.value[currentField] || ''
    localForm.value[currentField] = 插入变量到文本(currentText, variable.placeholder)
    handleFormChange()
  }
}

// 格式化提示词
const formatPrompt = () => {
  const currentField = getCurrentPromptField()
  if (currentField && localForm.value[currentField]) {
    localForm.value[currentField] = 格式化提示词(localForm.value[currentField])
    handleFormChange()
  }
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}
</script>

<style scoped>
.prompt-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.prompt-tabs {
  margin-bottom: 16px;
}

.prompt-tabs :deep(.ant-tabs-content-holder) {
  padding: 16px 0;
}

.prompt-toolbar {
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-top: 16px;
}

.preview-content {
  max-height: 500px;
  overflow-y: auto;
}

.preview-text {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}
</style>
