<template>
  <div class="schema-structure">
    <div v-if="!schema" class="empty-schema">
      <a-empty description="暂无Schema结构" />
    </div>
    <div v-else class="structure-tree">
      <SchemaNode :node="rootNode" :level="0" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import SchemaNode from './SchemaNode.vue'

// Props
const props = defineProps({
  schema: {
    type: Object,
    default: () => null
  }
})

// 构建根节点
const rootNode = computed(() => {
  if (!props.schema) return null
  
  return {
    name: 'root',
    type: props.schema.type || 'object',
    description: props.schema.description,
    required: false,
    properties: props.schema.properties || {},
    items: props.schema.items,
    schema: props.schema
  }
})
</script>

<style scoped>
.schema-structure {
  padding: 8px;
}

.empty-schema {
  text-align: center;
  padding: 40px 20px;
}

.structure-tree {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}
</style>
