<template>
  <div :class="$style.testPanel">
    <!-- 测试面板头部 -->
    <div :class="$style.header">
      <div :class="$style.title">
        <span>🤖 智能体测试</span>
        <a-badge :count="totalMessages" :offset="[10, 0]" />
      </div>
      <div :class="$style.controls">
        <a-select
          v-model:value="testMode"
          size="small"
          :class="$style.modeSelect"
          @change="handleModeChange"
        >
          <a-select-option value="admin">🔧 管理员</a-select-option>
          <a-select-option value="user">👤 用户</a-select-option>
          <a-select-option value="rag">📚 RAG</a-select-option>
          <a-select-option value="tools">🛠️ 工具</a-select-option>
        </a-select>
        <a-button
          size="small"
          type="text"
          @click="scrollToBottom"
          :class="$style.scrollBtn"
          title="滚动到底部"
        >
          ⬇️
        </a-button>
        <a-button
          size="small"
          type="text"
          @click="clearHistory"
          :class="$style.clearBtn"
        >
          清空
        </a-button>
      </div>
    </div>

    <!-- 统一的对话区域 -->
    <div :class="$style.chatContainer">
      <!-- 消息列表 -->
      <div :class="$style.messages" ref="messagesRef">
        <div v-if="currentMessages.length === 0" :class="$style.emptyState">
          <div :class="$style.emptyIcon">{{ getModeIcon(testMode) }}</div>
          <div :class="$style.emptyText">{{ getModeDescription(testMode) }}</div>
        </div>

        <div
          v-for="(msg, index) in currentMessages"
          :key="index"
          :class="[$style.message, $style[msg.role]]"
        >
          <div :class="$style.messageContent">
            <div :class="$style.content">{{ msg.content }}</div>
            <div :class="$style.meta">
              <span :class="$style.time">{{ msg.timestamp }}</span>
              <span v-if="msg.tokens" :class="$style.tokens">{{ msg.tokens }} tokens</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div :class="$style.inputArea">
        <!-- 状态异常提示 -->
        <div v-if="!isValidTestMode" :class="$style.errorState">
          <a-alert
            message="测试模式异常"
            :description="`当前模式: ${testMode}，正在自动修复...`"
            type="warning"
            show-icon
            :class="$style.errorAlert"
          />
          <a-button type="primary" @click="resetTestMode" size="small">
            重置为管理员模式
          </a-button>
        </div>

        <!-- 管理员对话输入 -->
        <div v-else-if="validatedTestMode === 'admin'" :class="$style.chatInput">
          <a-textarea
            v-model:value="inputMessage"
            :placeholder="getInputPlaceholder(validatedTestMode)"
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @press-enter="handleSendMessage"
            :disabled="isLoading"
            :class="$style.input"
          />
          <a-button
            type="primary"
            :loading="isLoading"
            @click="handleSendMessage"
            :class="$style.sendBtn"
          >
            发送
          </a-button>
        </div>

        <!-- 用户对话输入 -->
        <div v-else-if="validatedTestMode === 'user'" :class="$style.chatInput">
          <a-textarea
            v-model:value="inputMessage"
            :placeholder="getInputPlaceholder(validatedTestMode)"
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @press-enter="handleSendMessage"
            :disabled="isLoading"
            :class="$style.input"
          />
          <a-button
            type="primary"
            :loading="isLoading"
            @click="handleSendMessage"
            :class="$style.sendBtn"
          >
            发送
          </a-button>
        </div>

        <!-- RAG测试输入 -->
        <div v-else-if="validatedTestMode === 'rag'" :class="$style.ragInput">
          <a-input
            v-model:value="ragQuery"
            placeholder="输入查询内容测试RAG检索..."
            @press-enter="handleRagTest"
            :disabled="ragTesting"
          />
          <a-button
            type="primary"
            :loading="ragTesting"
            @click="handleRagTest"
          >
            检索
          </a-button>
        </div>

        <!-- 工具测试输入 -->
        <div v-else-if="validatedTestMode === 'tools'" :class="$style.toolsInput">
          <a-select
            v-model:value="selectedTool"
            placeholder="选择要测试的工具"
            :class="$style.toolSelect"
          >
            <a-select-option
              v-for="tool in availableTools"
              :key="tool.id"
              :value="tool.id"
            >
              {{ tool.工具名称 }}
            </a-select-option>
          </a-select>
          <a-button
            type="primary"
            :loading="toolTesting"
            @click="handleToolTest"
            :disabled="!selectedTool"
          >
            测试
          </a-button>
        </div>

        <!-- 默认输入（防护机制） -->
        <div v-else :class="$style.chatInput">
          <a-textarea
            v-model:value="inputMessage"
            placeholder="输入测试消息..."
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @press-enter="handleSendMessage"
            :disabled="isLoading"
            :class="$style.input"
          />
          <a-button
            type="primary"
            :loading="isLoading"
            @click="handleSendMessage"
            :class="$style.sendBtn"
          >
            发送
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import adminLangchainService from '@/services/adminLangchainService'
import { message } from 'ant-design-vue'
import { computed, nextTick, ref, watch } from 'vue'

// Props
const props = defineProps({
  agentId: {
    type: [String, Number],
    default: null
  },
  agentForm: {
    type: Object,
    default: () => ({})
  },
  availableTools: {
    type: Array,
    default: () => []
  }
})

// 测试模式
const testMode = ref('admin')
const inputMessage = ref('')
const ragQuery = ref('')
const selectedTool = ref('')

const isLoading = ref(false)
const ragTesting = ref(false)
const toolTesting = ref(false)

// 消息历史 - 统一管理
const adminMessages = ref([])
const userMessages = ref([])
const ragMessages = ref([])
const toolMessages = ref([])

// 消息容器引用
const messagesRef = ref(null)

// 状态验证和防护
const VALID_TEST_MODES = ['admin', 'user', 'rag', 'tools']

// 验证测试模式是否有效
const isValidTestMode = computed(() => {
  return VALID_TEST_MODES.includes(testMode.value)
})

// 获取验证后的测试模式（防护机制）
const validatedTestMode = computed(() => {
  return isValidTestMode.value ? testMode.value : 'admin'
})

// 计算属性
const currentMessages = computed(() => {
  switch (validatedTestMode.value) {
    case 'admin': return adminMessages.value
    case 'user': return userMessages.value
    case 'rag': return ragMessages.value
    case 'tools': return toolMessages.value
    default: return adminMessages.value // 默认返回管理员消息
  }
})

const totalMessages = computed(() => {
  return adminMessages.value.length + userMessages.value.length +
         ragMessages.value.length + toolMessages.value.length
})

// 状态监控和自动修复
// 监听测试模式变化，自动修复无效状态
watch(testMode, (newMode, oldMode) => {
  console.log(`[TestPanel] 测试模式变化: ${oldMode} -> ${newMode}`)

  // 如果模式无效，自动修复
  if (!VALID_TEST_MODES.includes(newMode)) {
    console.warn(`[TestPanel] 检测到无效测试模式: ${newMode}，自动修复为 admin`)
    nextTick(() => {
      testMode.value = 'admin'
      message.warning('测试模式异常，已自动修复')
    })
  }
}, { immediate: true })

// 重置测试模式
const resetTestMode = () => {
  console.log('[TestPanel] 手动重置测试模式为 admin')
  testMode.value = 'admin'
  inputMessage.value = ''
  ragQuery.value = ''
  selectedTool.value = ''
  message.success('测试模式已重置')
}

// 调试工具：获取组件状态信息
const getDebugInfo = () => {
  return {
    testMode: testMode.value,
    isValidTestMode: isValidTestMode.value,
    validatedTestMode: validatedTestMode.value,
    inputMessage: inputMessage.value,
    ragQuery: ragQuery.value,
    selectedTool: selectedTool.value,
    isLoading: isLoading.value,
    ragTesting: ragTesting.value,
    toolTesting: toolTesting.value,
    totalMessages: totalMessages.value,
    timestamp: new Date().toISOString()
  }
}

// 开发环境下暴露调试工具到全局
if (process.env.NODE_ENV === 'development') {
  window.TestPanelDebug = {
    getDebugInfo,
    resetTestMode,
    setTestMode: (mode) => {
      console.log(`[TestPanel] 调试工具设置模式: ${mode}`)
      testMode.value = mode
    }
  }
}

// 工具方法
const getModeIcon = (mode) => {
  const icons = {
    admin: '🔧',
    user: '👤',
    rag: '📚',
    tools: '🛠️'
  }
  return icons[mode] || '🤖'
}

const getModeDescription = (mode) => {
  const descriptions = {
    admin: '发送消息测试管理员接口',
    user: '发送消息测试用户对话接口',
    rag: '输入查询内容测试RAG检索',
    tools: '选择工具进行功能测试'
  }
  return descriptions[mode] || '开始测试'
}

const getInputPlaceholder = (mode) => {
  const placeholders = {
    admin: '输入消息测试管理员接口...',
    user: '输入消息测试用户接口...'
  }
  return placeholders[mode] || '输入测试内容...'
}

// 添加消息到历史
const addMessage = (content, role = 'user', tokens = null) => {
  const msg = {
    content,
    role,
    timestamp: new Date().toLocaleTimeString(),
    tokens
  }

  switch (testMode.value) {
    case 'admin':
      adminMessages.value.push(msg)
      break
    case 'user':
      userMessages.value.push(msg)
      break
    case 'rag':
      ragMessages.value.push(msg)
      break
    case 'tools':
      toolMessages.value.push(msg)
      break
  }

  // 平滑滚动到底部
  nextTick(() => {
    if (messagesRef.value) {
      // 使用平滑滚动
      messagesRef.value.scrollTo({
        top: messagesRef.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}

// 处理模式切换
const handleModeChange = (mode) => {
  console.log(`[TestPanel] 切换测试模式: ${testMode.value} -> ${mode}`)

  // 验证模式有效性
  if (!VALID_TEST_MODES.includes(mode)) {
    console.error(`[TestPanel] 尝试切换到无效模式: ${mode}`)
    message.error(`无效的测试模式: ${mode}`)
    return
  }

  testMode.value = mode
  inputMessage.value = ''
  ragQuery.value = ''
  selectedTool.value = ''
}

// 清空历史
const clearHistory = () => {
  switch (testMode.value) {
    case 'admin':
      adminMessages.value = []
      break
    case 'user':
      userMessages.value = []
      break
    case 'rag':
      ragMessages.value = []
      break
    case 'tools':
      toolMessages.value = []
      break
  }
}

// 手动滚动到底部的方法
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesRef.value) {
      messagesRef.value.scrollTo({
        top: messagesRef.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}

// 发送消息
const handleSendMessage = async () => {
  if (!inputMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  if (!props.agentId) {
    message.warning('请先保存智能体后再进行测试')
    return
  }



  try {
    isLoading.value = true

    // 添加用户消息
    addMessage(inputMessage.value, 'user')

    const messageContent = inputMessage.value
    inputMessage.value = ''

    // 根据模式调用不同接口
    let response
    if (testMode.value === 'admin') {
      response = await adminLangchainService.chatWithAgent(props.agentId, {
        消息: messageContent,
        会话id: null,
        上下文信息: null
      })
    } else if (testMode.value === 'user') {
      response = await adminLangchainService.chatWithUserAgent(props.agentId, {
        消息: messageContent,
        会话id: null,
        上下文信息: null
      })
    }

    // 添加助手回复
    if (response && response.success) {
      const replyContent = response.data?.智能体回复 || response.data?.回复内容 || '智能体回复异常'
      const tokens = response.data?.tokens || null
      addMessage(replyContent, 'assistant', tokens)
    } else {
      addMessage(`错误: ${response?.error || '接口调用失败'}`, 'system')
    }

  } catch (error) {
    console.error('[TestPanel] 消息发送失败:', error)
    addMessage(`测试失败: ${error.message}`, 'system')
    message.error('测试失败: ' + error.message)
  } finally {
    isLoading.value = false
    // 确保状态一致性
    if (!VALID_TEST_MODES.includes(testMode.value)) {
      console.warn('[TestPanel] 异步操作后检测到状态异常，重置模式')
      testMode.value = 'admin'
    }
  }
}

// RAG测试
const handleRagTest = async () => {
  if (!ragQuery.value.trim()) {
    message.warning('请输入查询内容')
    return
  }

  if (!props.agentId) {
    message.warning('请先保存智能体后再进行测试')
    return
  }

  try {
    ragTesting.value = true

    addMessage(`RAG查询: ${ragQuery.value}`, 'user')

    // 临时模拟RAG测试
    const response = {
      success: true,
      data: {
        query: ragQuery.value,
        results: ['检索结果1', '检索结果2'],
        relevance: 0.85
      }
    }

    if (response.success) {
      addMessage(`检索结果: ${JSON.stringify(response.data, null, 2)}`, 'assistant')
    } else {
      addMessage(`RAG测试失败: ${response.error}`, 'system')
    }

    ragQuery.value = ''

  } catch (error) {
    addMessage(`RAG测试异常: ${error.message}`, 'system')
    message.error('RAG测试失败: ' + error.message)
  } finally {
    ragTesting.value = false
  }
}

// 工具测试
const handleToolTest = async () => {
  // 参数验证
  if (!selectedTool.value) {
    message.warning('请选择要测试的工具')
    return
  }

  if (!props.agentId) {
    message.warning('请先保存智能体后再进行测试')
    return
  }

  // 获取工具信息
  const tool = props.availableTools?.find(t => t.id === selectedTool.value)
  const toolName = tool?.工具名称 || selectedTool.value

  if (!toolName) {
    message.error('无法获取工具信息')
    return
  }

  toolTesting.value = true
  addMessage(`测试工具: ${toolName}`, 'user')

  try {
    // 调用智能体工具验证接口
    const response = await adminLangchainService.验证智能体工具调用({
      智能体id: props.agentId,
      工具名称: toolName,
      参数: {}
    })

    // 处理响应结果
    if (response.success) {
      const formattedResult = {
        工具名称: toolName,
        测试状态: '成功',
        测试结果: response.data || {},
        测试时间: new Date().toLocaleString()
      }
      addMessage(`工具测试结果:\n${JSON.stringify(formattedResult, null, 2)}`, 'assistant')
      message.success('工具测试成功')
    } else {
      const errorMsg = response.message || '工具测试失败'
      addMessage(`工具测试失败: ${errorMsg}`, 'system')
      message.error(errorMsg)
    }

  } catch (error) {
    console.error('[TestPanel] 工具测试异常:', error)
    const errorMsg = error.message || '工具测试异常'
    addMessage(`工具测试异常: ${errorMsg}`, 'system')
    message.error(`工具测试失败: ${errorMsg}`)
  } finally {
    toolTesting.value = false
    // 确保状态一致性
    if (!VALID_TEST_MODES.includes(testMode.value)) {
      console.warn('[TestPanel] 工具测试后检测到状态异常，重置模式')
      testMode.value = 'tools'
    }
  }
}
</script>

<style module>
.testPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  position: relative;
}

.header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modeSelect {
  min-width: 120px;
}

.scrollBtn {
  color: #1890ff;
  font-size: 12px;
  transition: all 0.2s ease;
}

.scrollBtn:hover {
  color: #40a9ff;
  background-color: #f0f8ff;
}

.clearBtn {
  color: #8c8c8c;
}

.chatContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
  /* 平滑滚动 */
  scroll-behavior: smooth;
  /* 确保有最小高度以触发滚动 */
  min-height: 200px;
  /* 最大高度限制，确保不会无限增长 */
  max-height: calc(100vh - 300px);
}

/* Webkit浏览器滚动条样式 */
.messages::-webkit-scrollbar {
  width: 6px;
}

.messages::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.messages::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.messages::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 滚动阴影效果 */
.messages::before,
.messages::after {
  content: '';
  position: sticky;
  left: 0;
  right: 0;
  height: 10px;
  pointer-events: none;
  z-index: 1;
}

.messages::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
}

.messages::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8), transparent);
}

.emptyState {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8c8c8c;
  text-align: center;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
}

.message {
  display: flex;
  margin-bottom: 12px;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message.system {
  justify-content: center;
}

.messageContent {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
}

.message.user .messageContent {
  background: #1890ff;
  color: white;
}

.message.assistant .messageContent {
  background: #f6f6f6;
  color: #262626;
}

.message.system .messageContent {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.content {
  white-space: pre-wrap;
  line-height: 1.5;
}

.meta {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  opacity: 0.7;
}

.time {
  color: inherit;
}

.tokens {
  color: inherit;
  font-weight: 500;
}

.inputArea {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.errorState {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #fff2e8;
  border: 1px solid #ffd591;
  border-radius: 6px;
  margin-bottom: 16px;
}

.errorAlert {
  margin-bottom: 0;
}

.chatInput,
.ragInput,
.toolsInput {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}



.input {
  flex: 1;
}

.toolSelect {
  flex: 1;
}

.sendBtn {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testPanel {
    border-left: none;
    border-top: 1px solid #e8e8e8;
  }

  .header {
    padding: 8px 12px;
  }

  .title {
    font-size: 14px;
  }

  .modeSelect {
    min-width: 100px;
  }

  .messages {
    padding: 12px;
    /* 移动端调整最大高度 */
    max-height: calc(100vh - 250px);
    min-height: 150px;
  }

  .inputArea {
    padding: 12px;
  }

  /* 移动端滚动条样式调整 */
  .messages::-webkit-scrollbar {
    width: 4px;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .controls {
    justify-content: space-between;
  }



  .input {
    min-height: 60px;
  }
}
</style>