import { ref } from 'vue'

/**
 * 提示词模板管理
 */
export function usePromptTemplates() {
  
  // 预定义模板
  const 提示词模板列表 = ref([
    {
      key: 'assistant',
      name: '通用助手',
      description: '友好专业的AI助手',
      template: '你是一个友好、专业的AI助手。请根据用户的问题提供准确、有用的回答。保持礼貌和耐心，如果不确定答案，请诚实说明。'
    },
    {
      key: 'analyst',
      name: '数据分析师',
      description: '专业的数据分析专家',
      template: '你是一个专业的数据分析师，擅长数据处理、统计分析和可视化。请用专业但易懂的方式回答用户的数据相关问题，并在适当时提供具体的分析建议。'
    },
    {
      key: 'writer',
      name: '内容创作者',
      description: '创意写作和内容创作专家',
      template: '你是一个富有创意的内容创作者，擅长各种文体的写作。请根据用户需求创作高质量的内容，注意语言的流畅性和表达的准确性。'
    },
    {
      key: 'customer_service',
      name: '客服助手',
      description: '专业的客户服务代表',
      template: '你是一个专业的客服助手。请保持礼貌、耐心和专业的态度，认真倾听用户问题，提供准确的信息和解决方案。如果无法解决问题，请及时转接给相关专家。'
    },
    {
      key: 'teacher',
      name: '教学助手',
      description: '耐心的教育工作者',
      template: '你是一个耐心的教学助手，擅长用简单易懂的方式解释复杂概念。请根据用户的学习需求，提供循序渐进的指导和练习建议。'
    },
    {
      key: 'technical_expert',
      name: '技术专家',
      description: '专业的技术顾问',
      template: '你是一个技术专家，拥有丰富的技术知识和实践经验。请用专业但易懂的方式回答技术问题，提供最佳实践建议，并在必要时给出代码示例。'
    }
  ])

  // 常用变量
  const 常用变量列表 = ref([
    {
      key: 'user_name',
      name: '用户名',
      placeholder: '{用户名}',
      description: '当前用户的姓名'
    },
    {
      key: 'current_time',
      name: '当前时间',
      placeholder: '{当前时间}',
      description: '当前的日期和时间'
    },
    {
      key: 'company_name',
      name: '公司名称',
      placeholder: '{公司名称}',
      description: '公司或组织名称'
    },
    {
      key: 'assistant_name',
      name: '助手名称',
      placeholder: '{助手名称}',
      description: '智能体的名称'
    },
    {
      key: 'user_role',
      name: '用户角色',
      placeholder: '{用户角色}',
      description: '用户在系统中的角色'
    },
    {
      key: 'context',
      name: '上下文信息',
      placeholder: '{上下文信息}',
      description: '相关的背景信息'
    }
  ])

  // 获取模板内容
  const 获取模板内容 = (模板Key) => {
    const 模板 = 提示词模板列表.value.find(t => t.key === 模板Key)
    return 模板?.template || ''
  }

  // 插入变量到文本
  const 插入变量到文本 = (文本, 变量占位符, 光标位置 = null) => {
    if (光标位置 !== null) {
      return 文本.slice(0, 光标位置) + 变量占位符 + 文本.slice(光标位置)
    } else {
      return 文本 + 变量占位符
    }
  }

  // 替换模板变量
  const 替换模板变量 = (模板文本, 变量值对象) => {
    let 结果 = 模板文本
    
    Object.entries(变量值对象).forEach(([变量名, 值]) => {
      const 正则 = new RegExp(`\\{${变量名}\\}`, 'g')
      结果 = 结果.replace(正则, 值 || '')
    })
    
    return 结果
  }

  // 提取模板中的变量
  const 提取模板变量 = (模板文本) => {
    const 变量正则 = /\{([^}]+)\}/g
    const 变量集合 = new Set()
    let 匹配结果
    
    while ((匹配结果 = 变量正则.exec(模板文本)) !== null) {
      变量集合.add(匹配结果[1])
    }
    
    return Array.from(变量集合)
  }

  // 验证模板语法
  const 验证模板语法 = (模板文本) => {
    const 错误列表 = []
    
    // 检查括号匹配
    const 开括号数 = (模板文本.match(/\{/g) || []).length
    const 闭括号数 = (模板文本.match(/\}/g) || []).length
    
    if (开括号数 !== 闭括号数) {
      错误列表.push('括号不匹配')
    }
    
    // 检查空变量名
    const 空变量正则 = /\{\s*\}/g
    if (空变量正则.test(模板文本)) {
      错误列表.push('存在空的变量名')
    }
    
    // 检查嵌套括号
    const 嵌套括号正则 = /\{[^}]*\{/g
    if (嵌套括号正则.test(模板文本)) {
      错误列表.push('不支持嵌套的变量定义')
    }
    
    return 错误列表
  }

  // 计算文本统计信息
  const 计算文本统计 = (文本) => {
    if (!文本) {
      return {
        字符数: 0,
        行数: 0,
        预估Token数: 0,
        变量数: 0
      }
    }
    
    const 字符数 = 文本.length
    const 行数 = 文本.split('\n').length
    const 预估Token数 = Math.ceil(字符数 / 3) // 粗略估算
    const 变量数 = 提取模板变量(文本).length
    
    return {
      字符数,
      行数,
      预估Token数,
      变量数
    }
  }

  // 格式化提示词
  const 格式化提示词 = (文本) => {
    return 文本
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 移除多余空行
      .replace(/[ \t]+$/gm, '') // 移除行尾空格
      .trim() // 移除首尾空白
  }

  // 生成提示词预览
  const 生成提示词预览 = (模板文本, 变量值 = {}) => {
    const 默认变量值 = {
      用户名: '张三',
      当前时间: new Date().toLocaleString('zh-CN'),
      公司名称: '示例公司',
      助手名称: '智能助手',
      用户角色: '用户',
      上下文信息: '这是示例上下文信息'
    }
    
    const 合并变量值 = { ...默认变量值, ...变量值 }
    return 替换模板变量(模板文本, 合并变量值)
  }

  return {
    提示词模板列表,
    常用变量列表,
    获取模板内容,
    插入变量到文本,
    替换模板变量,
    提取模板变量,
    验证模板语法,
    计算文本统计,
    格式化提示词,
    生成提示词预览
  }
}
