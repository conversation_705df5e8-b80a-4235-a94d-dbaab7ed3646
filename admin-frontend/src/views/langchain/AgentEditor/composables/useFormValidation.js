import { message } from 'ant-design-vue'

/**
 * 表单验证逻辑
 */
export function useFormValidation() {
  
  // 验证智能体名称
  const 验证智能体名称 = (名称) => {
    if (!名称?.trim()) {
      return '请输入智能体名称'
    }
    if (名称.length < 2 || 名称.length > 50) {
      return '智能体名称长度应在2-50个字符之间'
    }
    return null
  }

  // 验证智能体描述
  const 验证智能体描述 = (描述) => {
    if (描述?.length > 200) {
      return '智能体描述不能超过200个字符'
    }
    return null
  }

  // 验证模型配置
  const 验证模型配置 = (模型id, 温度参数) => {
    const 错误 = {}
    
    if (!模型id) {
      错误.对话模型id = '请选择对话模型'
    }
    
    if (温度参数 < 0 || 温度参数 > 2) {
      错误.温度参数 = '温度参数应在0-2之间'
    }
    
    return Object.keys(错误).length > 0 ? 错误 : null
  }

  // 验证RAG配置
  const 验证RAG配置 = (启用rag, 知识库列表, 相似度阈值, 最大检索数量) => {
    const 错误 = {}
    
    if (启用rag) {
      if (!知识库列表 || 知识库列表.length === 0) {
        错误.知识库列表 = '启用rag时必须选择至少一个知识库'
      }
      
      if (相似度阈值 < 0 || 相似度阈值 > 1) {
        错误.相似度阈值 = '相似度阈值应在0-1之间'
      }
      
      if (最大检索数量 < 1 || 最大检索数量 > 20) {
        错误.最大检索数量 = '最大检索数量应在1-20之间'
      }
    }
    
    return Object.keys(错误).length > 0 ? 错误 : null
  }

  // 验证JSON Schema
  const 验证JSON_Schema = (json_schema) => {
    if (!json_schema) return null
    
    try {
      if (typeof json_schema === 'string') {
        JSON.parse(json_schema)
      }
      return null
    } catch (error) {
      return 'JSON Schema格式不正确'
    }
  }

  // 验证自定义变量
  const 验证自定义变量 = (自定义变量) => {
    if (!自定义变量 || 自定义变量.length === 0) return null
    
    const 变量名集合 = new Set()
    
    for (let i = 0; i < 自定义变量.length; i++) {
      const 变量 = 自定义变量[i]
      
      if (!变量.变量名?.trim()) {
        return `第${i + 1}个变量名不能为空`
      }
      
      if (变量名集合.has(变量.变量名)) {
        return `变量名"${变量.变量名}"重复`
      }
      
      变量名集合.add(变量.变量名)
      
      if (!变量.变量类型) {
        return `第${i + 1}个变量类型不能为空`
      }
    }
    
    return null
  }

  // 验证提示词长度
  const 验证提示词长度 = (提示词, 字段名 = '提示词') => {
    if (!提示词) return null
    
    if (提示词.length > 10000) {
      return `${字段名}长度不能超过10000个字符`
    }
    
    return null
  }

  // 综合验证表单
  const 验证完整表单 = (表单数据) => {
    const 所有错误 = {}
    
    // 验证基础信息
    const 名称错误 = 验证智能体名称(表单数据.智能体名称)
    if (名称错误) 所有错误.智能体名称 = 名称错误
    
    const 描述错误 = 验证智能体描述(表单数据.智能体描述)
    if (描述错误) 所有错误.智能体描述 = 描述错误
    
    // 验证模型配置
    const 模型错误 = 验证模型配置(表单数据.对话模型id, 表单数据.温度参数)
    if (模型错误) Object.assign(所有错误, 模型错误)
    
    // 验证RAG配置
    const RAG错误 = 验证RAG配置(
      表单数据.启用rag, 
      表单数据.知识库列表, 
      表单数据.相似度阈值, 
      表单数据.最大检索数量
    )
    if (RAG错误) Object.assign(所有错误, RAG错误)
    
    // 验证提示词
    const 系统提示词错误 = 验证提示词长度(表单数据.系统提示词, '系统提示词')
    if (系统提示词错误) 所有错误.系统提示词 = 系统提示词错误
    
    const 用户提示词错误 = 验证提示词长度(表单数据.用户提示词, '用户提示词')
    if (用户提示词错误) 所有错误.用户提示词 = 用户提示词错误
    
    // 验证JSON Schema
    const JSON错误 = 验证JSON_Schema(表单数据.json_schema)
    if (JSON错误) 所有错误.json_schema = JSON错误
    
    // 验证自定义变量
    const 变量错误 = 验证自定义变量(表单数据.自定义变量)
    if (变量错误) 所有错误.自定义变量 = 变量错误
    
    return Object.keys(所有错误).length > 0 ? 所有错误 : null
  }

  // 显示验证错误
  const 显示验证错误 = (错误对象) => {
    if (!错误对象) return
    
    const 错误信息 = Object.values(错误对象)[0]
    message.error(错误信息)
  }

  return {
    验证智能体名称,
    验证智能体描述,
    验证模型配置,
    验证RAG配置,
    验证JSON_Schema,
    验证自定义变量,
    验证提示词长度,
    验证完整表单,
    显示验证错误
  }
}
