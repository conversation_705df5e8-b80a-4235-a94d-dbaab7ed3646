<template>
  <div class="lead-bulk-import-container">
    <a-page-header title="批量导入线索" @back="() => $router.go(-1)" />
    <a-spin :spinning="isParsing || isImporting">
      <div class="content-area">
        <a-steps :current="effectiveCurrentStep" class="import-steps">
          <a-step title="上传文件" description="选择Excel或CSV文件" />
          <a-step title="字段映射" description="配置关键字段对应关系" />
          <a-step title="导入选项与预览" description="选择导入方式或编辑数据" />
          <a-step title="执行导入" description="系统正在处理您的数据" />
          <a-step title="完成" description="查看导入结果" />
        </a-steps>

        <div v-if="currentStep === 0" class="step-content step-upload">
          <a-upload-dragger
            name="file"
            :multiple="false"
            :before-upload="handleBeforeUpload"
            @change="handleFileChange"
            @drop="handleFileDrop"
            :showUploadList="false"
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          >
            <p class="ant-upload-drag-icon">
              <cloud-upload-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域以上传</p>
            <p class="ant-upload-hint">
              支持单个Excel (.xls, .xlsx) 或 CSV (.csv) 文件导入。
            </p>
          </a-upload-dragger>
          <div v-if="rawFileName" class="file-info">
             当前选择文件: <strong>{{ rawFileName }}</strong>
          </div>
        </div>

        <div v-if="currentStep === 1" class="step-content step-mapping">
          <a-alert type="info" show-icon class="mapping-alert">
            <template #message>请映射关键字段，并填写本次导入的来源信息。</template>
          </a-alert>
          <a-form layout="vertical" class="mapping-form">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="联系方式内容列" required>
                  <a-select
                    v-model:value="contactContentColumns" 
                    mode="multiple"
                    placeholder="选择一个或多个联系方式列"
                    allow-clear
                    show-search
                    :filter-option="(input, option) => 
                      option.children && 
                      option.children[0] && 
                      option.children[0].children && 
                      typeof option.children[0].children === 'string' && 
                      option.children[0].children.toLowerCase().includes(input.toLowerCase())
                    "
                  >
                    <a-select-option v-for="col in columnNames" :key="col" :value="col">{{ col }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系方式类型" class="disabled-form-item">
                  <a-alert
                    message="系统将自动识别联系方式类型"
                    description="基于内容智能识别为电话、邮箱或微信，不会使用Excel中的类型或平台字段"
                    type="info"
                    show-icon
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="线索来源" required>
                  <a-input 
                    v-model:value="leadSource" 
                    placeholder="例如：2024春季推广活动Excel表"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系方式记录来源" required>
                  <a-input 
                    v-model:value="contactRecordSource" 
                    placeholder="例如：管理员批量导入"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          
          <h4 class="preview-title">数据预览 (前 {{ previewRows.length }} 行)</h4>
          <a-table 
            :columns="tableColumnsForPreview" 
            :data-source="previewRows" 
            :scroll="{ x: true }" 
            size="small"
            bordered
            row-key="id"
          />
          
          <!-- 调试辅助按钮 -->
          <div class="debug-tools" style="margin-top: 10px; text-align: left;">
            <a-button size="small" @click="debugPreviewData">调试: 查看预览数据</a-button>
          </div>

          <div class="action-buttons">
            <a-button @click="reset" :disabled="isImporting || isParsing">重新上传</a-button>
            <a-button 
              type="primary" 
              @click="handleProceedToModeSelection" 
              :disabled="!canProceedToModeSelection || isImporting || isParsing"
            >
              下一步：选择导入模式
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2" class="step-content step-mode-selection">
          <a-alert 
            message="选择导入模式" 
            description="您可以选择直接批量导入所有数据，或者进入交互式模式逐条预览、编辑和导入数据。"
            type="info"
            show-icon 
            style="margin-bottom: 24px;"
          />
          <div class="action-buttons mode-selection-buttons">
            <a-button @click="handleBackToMapping" :disabled="isImporting">返回字段映射</a-button>
            <a-space>
              <a-button 
                type="default"
                @click="handleSelectInteractiveMode"
                :disabled="isImporting"
                size="large"
              >
                <template #icon><edit-outlined /></template>
                交互式导入 (预览与编辑)
              </a-button>
              <a-button 
                type="primary" 
                @click="handleSelectDirectImport"
                :disabled="isImporting || !isApiServiceReady"
                size="large"
              >
                <template #icon><play-circle-outlined /></template>
                直接批量导入 {{ totalRows > 0 ? `(${totalRows}条)` : '' }}
              </a-button>
            </a-space>
          </div>
        </div>

        <div v-if="currentStep === 3" class="step-content step-progress">
          <a-progress :percent="calculatedProgress" status="active" />
          <p class="progress-text">
            正在处理：{{ processedRows }} / {{ totalRows }} 条记录 (成功: {{ importProgress.success }}, 失败: {{ importProgress.failed }})
          </p>
        </div>

        <div v-if="currentStep === 4" class="step-content step-interactive-preview">
          <a-alert 
            message="交互式预览与导入"
            type="info" 
            show-icon 
            style="margin-bottom: 16px;"
          >
            <template #description>
              <p>在这里您可以预览所有已解析的数据，并进行修改。</p>
              <p>您可以选择逐条导入数据，或者在确认所有数据无误后，一次性导入所有剩余数据。</p>
              <p>已成功导入的条目将标记为绿色，失败的将标记为红色并显示错误信息。</p>
            </template>
          </a-alert>

          <div class="action-buttons interactive-top-actions" style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
            <a-button @click="handleBackToModeSelection" :disabled="isImporting">返回模式选择</a-button>
            <a-space>
                <span style="margin-right: 16px;">
                    总计: {{ totalRows }} 条 | 
                    已尝试: {{ interactiveProcessedRows }} 条 | 
                    成功: <a-tag color="success">{{ interactiveSuccessRows }}</a-tag> | 
                    失败: <a-tag color="error">{{ interactiveFailedRows }}</a-tag>
                </span>
                <a-button
                  @click="handleImportCurrentPage"
                  :disabled="isImporting || isImportingCurrentPage || paginatedInteractiveData.length === 0"
                  :loading="isImportingCurrentPage"
                  style="margin-left: 8px;"
                >
                  导入本页记录 ({{ paginatedInteractiveData.filter(row => !row._isImported).length }}条)
                </a-button>
                <a-button 
                    type="primary" 
                    @click="handleStartInteractiveBatchImport"
                    :disabled="isImporting || interactiveProcessedRows === totalRows || !isApiServiceReady" 
                    :loading="isImporting"
                >
                    <template #icon><play-circle-outlined /></template>
                    导入全部剩余记录
                </a-button>
            </a-space>
          </div>

          <a-table
            :columns="interactiveTableColumns"
            :data-source="paginatedInteractiveData"
            :scroll="{ x: 1300 }"
            size="small"
            bordered
            row-key="id" 
            :pagination="false"
          >
            <template #bodyCell="{ column, record, index, text }">
              <template v-if="column.key === '_interactiveIndex'">
                <span>{{ index + 1 + (interactiveCurrentPage - 1) * interactivePageSize }}</span>
              </template>
              <template v-else-if="editingRowKey === record.id && column.editable">
                <a-input 
                  v-model:value="editingRowData[column.dataIndex]" 
                  style="margin: -5px 0" 
                  @pressEnter="saveEdit"
                />
              </template>
              <template v-else-if="column.key === '_statusColumn'">
                <a-popconfirm
                  v-if="getStatusTagProps(record).hasErrorDetail"
                  title="错误详情"
                  placement="topLeft"
                  trigger="hover"
                  :overlayClassName="'error-detail-popconfirm'"
                >
                  <template #content>
                    <div style="max-width: 500px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; word-break: break-all;">
                      {{ String(record._error || '未知错误') }}
                    </div>
                  </template>
                  <a-tag :color="getStatusTagProps(record).color" style="cursor: pointer;">
                    <close-circle-outlined style="margin-right: 4px;" />
                    {{ getStatusTagProps(record).text }}
                  </a-tag>
                </a-popconfirm>
                <a-tag v-else :color="getStatusTagProps(record).color">
                  {{ getStatusTagProps(record).text }}
                </a-tag>
              </template>
              <template v-else-if="column.key === '_predictedType'">
                <span>{{ record._predictedType || '未知' }}</span>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <template v-if="editingRowKey === record.id">
                    <a-button type="link" size="small" @click="() => saveEdit(record.id)">保存</a-button>
                    <a-button type="link" size="small" danger @click="cancelEdit">取消</a-button>
                  </template>
                  <template v-else>
                    <a-button 
                      type="link" 
                      size="small"
                      @click="() => startEdit(record)" 
                      :disabled="(record._isImported && record._importStatus === 'success') || isImporting"
                    >
                      编辑
                    </a-button>
                    <a-button 
                      type="link" 
                      size="small" 
                      @click="() => handleCopyInsert(record)"
                      :disabled="isImporting"
                      style="margin-left: 8px;"
                    >
                      复制插入
                    </a-button>
                    <a-popconfirm
                      title="确定删除这条记录吗?"
                      ok-text="删除"
                      cancel-text="取消"
                      @confirm="() => handleDelete(record.id)"
                      :disabled="isImporting"
                    >
                      <a-button type="link" size="small" danger style="margin-left: 8px;">删除</a-button>
                    </a-popconfirm>
                    <a-button 
                      type="link" 
                      size="small" 
                      :danger="record._isImported && record._importStatus === 'failed'"
                      @click="() => handleImportSingleRow(record.id)"
                      :disabled="(record._isImported && record._importStatus === 'success') || isImporting || !isApiServiceReady"
                      :loading="isImportingSingleRow[record.id]"
                      style="margin-left: 8px;"
                    >
                      {{ record._isImported && record._importStatus === 'failed' ? '重新导入' : '导入此条' }}
                    </a-button>
                  </template>
                </a-space>
              </template>
              <template v-else>
                <span>{{ text }}</span>
              </template>
            </template>
          </a-table>

          <div class="pagination-container">
            <a-pagination
              v-model:current="interactiveCurrentPage"
              :total="interactiveDataWithStatus.length" 
              :page-size="interactivePageSize"
              @change="handlePageChange"
              show-size-changer 
              :page-size-options="['50', '10', '20', '100']" 
              @showSizeChange="onShowSizeChange"
            />
          </div>
        </div>

        <div v-if="currentStep === 5" class="step-content step-progress"> 
          <a-alert 
            message="正在执行交互式批量导入..." 
            type="info" 
            show-icon 
            style="margin-bottom: 16px;"
          />
          <a-progress :percent="calculatedProgress" status="active" />
          <p class="progress-text">
            正在处理 (本次批量)：{{ importProgress.success + importProgress.failed }} / {{ importProgress.total }} 条记录 
            (成功: {{ importProgress.success }}, 失败: {{ importProgress.failed }})
          </p>
          <p class="progress-text" style="margin-top: 8px;">
            全部数据进度：
            已尝试: {{ interactiveProcessedRows }} / {{ totalRows }} 条 | 
            总成功: {{ interactiveSuccessRows }} | 
            总失败: {{ interactiveFailedRows }}
          </p>
        </div>

        <div v-if="currentStep === 6" class="step-content step-complete">
          <a-result
            :status="importProgress.failed === 0 ? 'success' : (importProgress.success > 0 ? 'warning' : 'error')"
            :title="importProgress.failed === 0 ? '全部导入成功' : (importProgress.success > 0 ? '导入部分完成' : '导入失败')"
            :sub-title="`总计 ${totalRows} 条记录 (针对本次批量操作)。成功 ${importProgress.success} 条，失败 ${importProgress.failed} 条。`"
          >
            <template #extra>
              <a-button @click="downloadFailed" v-if="importProgress.failed > 0" danger>
                下载失败记录 ({{ importProgress.failed }}条)
              </a-button>
              <a-button type="primary" @click="reset">导入新文件</a-button>
            </template>
          </a-result>
          
          <div v-if="importProgress.failed > 0 && importProgress.errors.length > 0" class="failed-records-table-container" style="margin-top: 24px;">
            <h4>失败记录详情:</h4>
            <a-table
              :columns="failedRecordsTableColumns"
              :data-source="importProgress.errors"
              :scroll="{ x: 1200 }"
              size="small"
              bordered
              :row-key="(record, index) => `failed-${record.rowData.id || index}-${record.error ? record.error.slice(0,10) : 'noerror'}`"
            >
              <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'actions'">
                      <a-button
                          type="link"
                          size="small"
                          @click="() => handleReimportFailedRow(record)"
                          :loading="isReimportingFailedRow[record.rowData?.id]"
                          :disabled="isImporting || !isApiServiceReady"
                      >
                          重新导入
                      </a-button>
                  </template>
                  <template v-else-if="column.key === 'originalRowIndex' && record.rowData && record.rowData._originalRowIndex !== undefined">
                      <span>{{ record.rowData._originalRowIndex + 1 }}</span>
                  </template>
                  <template v-else-if="column.key === 'contactColumnName' && record.rowData">
                     <span>{{ record.rowData._contactColumnName || '(无来源列)' }}</span>
                  </template>
                  <template v-else-if="column.key === 'contactContent' && record.rowData">
                     <span>{{ record.rowData._contactContent || '(未提取)' }}</span>
                  </template>
                   <template v-else-if="column.dataIndex && Array.isArray(column.dataIndex) && column.dataIndex[0] === 'rowData' && record.rowData && record.rowData[column.dataIndex[1]] !== undefined">
                     <span>{{ record.rowData[column.dataIndex[1]] }}</span>
                   </template>
                   <template v-else-if="column.dataIndex === 'error'">
                     <a-popconfirm
                       v-if="record.error && record.error.length > 50" 
                       title="错误详情"
                       placement="topLeft"
                       trigger="hover"
                     >
                       <template #content>
                         <div style="max-width: 500px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; word-break: break-all;">
                           {{ record.error }}
                         </div>
                       </template>
                       <span style="cursor: pointer; color: #ff4d4f;">{{ record.error.substring(0, 50) }}...</span>
                     </a-popconfirm>
                     <span v-else>{{ record.error }}</span>
                   </template>
                  <!-- Existing customRender logic for index might be needed if not covered by dataIndex -->
                  <template v-else-if="column.key === 'index'"> <!-- Assuming 'index' is the key for the simple index column -->
                     <span>{{ importProgress.errors.indexOf(record) + 1 }}</span>
                  </template>
                  <!-- Fallback for any other text, ensure 'text' prop is available or handle based on record and column -->
                  <template v-else>
                    <span v-if="record[column.dataIndex]">{{ record[column.dataIndex] }}</span>
                    <span v-else-if="record.rowData && record.rowData[column.dataIndex]">{{ record.rowData[column.dataIndex] }}</span>
                  </template>
              </template>
            </a-table>
          </div>

        </div>

      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { useLeadImportStore } from '@/store/leadImportStore';
import { CloseCircleOutlined, CloudUploadOutlined, EditOutlined, PlayCircleOutlined } from '@ant-design/icons-vue';
import { message, notification } from 'ant-design-vue';
import { storeToRefs } from 'pinia';
import { computed, nextTick, onUnmounted, ref, watch } from 'vue';

// 定义交互模式表格的显示字段白名单
const INTERACTIVE_TABLE_VISIBLE_FIELDS = [
  '_contactContent',  // 联系方式内容
  '_contactColumnName', // 来源列名
  'id',  // ID
];

// 更新内部字段的显示名称映射
const INTERNAL_FIELD_TITLE_MAP = {
  _contactContent: '联系方式内容',
  _contactColumnName: '来源列名',
  _originalRowIndex: '原始表格行号',
  id: '唯一ID',
  _predictedType: '预测类型' // 添加映射
};

const isApiServiceReady = ref(false); // 新增状态

// +++ getStatusTagProps HELPER FUNCTION +++
const getStatusTagProps = (record) => {
  if (record._isImported) {
    if (record._importStatus === 'success') return { color: 'success', text: '成功' };
    if (record._importStatus === 'failed') return { color: 'error', text: record._error ? '失败 (详情)' : '失败', hasErrorDetail: !!record._error };
    if (record._importStatus === 'pending') return { color: 'processing', text: '处理中' }; // 'processing' is an AntD color
  }
  if (record._isEdited) return { color: 'warning', text: '已修改' };
  return { color: 'default', text: '待处理' }; // 'default' for AntD Tag
};
// +++ HELPER FUNCTION END +++

// +++ 添加预测联系方式类型的函数 +++ (此函数现在不再需要，将由 store 的 _predictedType 字段替代)
/*
const predictContactType = (content, columnName = '') => {
  // ... (旧的组件内预测逻辑) ...
};
*/
// +++ 函数定义结束 +++

// 使用统一的API服务，简化初始化
import superAdminService from '../services/superAdminService';
const apiService = superAdminService;
isApiServiceReady.value = true; // SuperAdminService总是可用

const leadImportStore = useLeadImportStore();

// 编辑状态 ref
const editingRowKey = ref(null); // 存储正在编辑的行的 id
const editingRowData = ref({});  // 存储正在编辑的行的数据副本

const interactiveCurrentPage = ref(1);
const interactivePageSize = ref(50); // 每页显示50条
const isImportingCurrentPage = ref(false); // 用于"导入本页记录"按钮的加载状态

const {
  file,
  rawFileName,
  columnNames,
  contactContentColumns,
  contactTypeColumn,
  leadSource,
  contactRecordSource,
  currentStep,
  previewRows,
  isParsing,
  isImporting,
  importProgress,
  canProceedToMapping,
  canProceedToModeSelection,
  totalRows,
  processedRows,
  interactiveDataWithStatus,
  interactiveProcessedRows,
  interactiveSuccessRows,
  interactiveFailedRows,
} = storeToRefs(leadImportStore);

const totalInteractivePages = computed(() => {
  if (!interactiveDataWithStatus.value) return 1; // 防止在数据加载前出现除零或NaN
  return Math.ceil(interactiveDataWithStatus.value.length / interactivePageSize.value) || 1; // 确保至少有1页
});

const paginatedInteractiveData = computed(() => {
  if (!interactiveDataWithStatus.value) return []; // 防止在数据加载前尝试slice undefined
  const start = (interactiveCurrentPage.value - 1) * interactivePageSize.value;
  const end = start + interactivePageSize.value;
  return interactiveDataWithStatus.value.slice(start, end);
});

const isValidFileType = (file) => {
  const allowedTypes = [
    'text/csv', 
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  const allowedExtensions = ['.csv', '.xls', '.xlsx'];
  
  // 检查MIME类型
  const validType = allowedTypes.includes(file.type);
  
  // 检查文件扩展名
  const fileName = file.name || '';
  const extension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
  const validExtension = allowedExtensions.includes(extension);
  
  return validType || validExtension;
};

const handleBeforeUpload = (uploadFile) => {
  if (!isValidFileType(uploadFile)) {
    message.error('文件类型不支持！请上传 Excel (.xls, .xlsx) 或 CSV (.csv) 文件。');
    return false; // 阻止上传
  }
  leadImportStore.setFile(uploadFile);
  return false; // 返回 false 以阻止 antd 的默认上传行为，我们将手动处理
};

const handleFileChange = (info) => {
  // console.log("File change:", info);
  // 如果 beforeUpload 返回 false，这里可能不会按预期执行文件内容处理
  // 我们在 beforeUpload 中调用了 setFile，然后应该手动触发解析
  if (leadImportStore.file && leadImportStore.currentStep === 0) {
    leadImportStore.parseFile(leadImportStore.file).catch(err => {
        notification.error({message: "文件解析错误", description: err.message});
    });
  }
};

const handleFileDrop = (e) => {
  // console.log('Dropped files', e.dataTransfer.files);
  if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
    const droppedFile = e.dataTransfer.files[0];
     if (handleBeforeUpload(droppedFile)) { // 复用校验逻辑
        // leadImportStore.setFile(droppedFile);
        // leadImportStore.parseFile(); // 已经在handleFileChange中处理
     }
  }
};

const triggerImport = async () => { // 这个函数现在主要用于直接批量导入
  if (!apiService) {
      notification.error({ message: '错误', description: 'API服务未就绪，无法开始导入。' });
      return;
  }
  // canProceedToImport 已经被 canProceedToModeSelection 替换，实际的权限在 store 的 action 中判断
  // 确认是否要进行直接导入
  if (currentStep.value === 3) { // 确保是在正确的步骤（已选择直接导入模式）
      await leadImportStore.startDirectBatchImport(apiService);
  } else {
      notification.warn({message: "流程错误", description: "当前不处于直接导入步骤。"});
  }
};

// 新增方法，用于字段映射后的"下一步"按钮
const handleProceedToModeSelection = () => {
  if (canProceedToModeSelection.value) {
    leadImportStore.proceedToModeSelection();
  } else {
    let errorMsg = "请确保所有必填映射和来源信息已填写正确。";
    if (!contactContentColumns.value || contactContentColumns.value.length === 0) errorMsg = "请选择至少一个联系方式内容列。";
    else if (!leadSource.value) errorMsg = "请填写线索来源。";
    else if (!contactRecordSource.value) errorMsg = "请填写联系方式记录来源。";
    message.warning(errorMsg);
  }
};

const reset = () => {
  leadImportStore.resetStore();
  // 可能需要重置 <a-upload> 的内部文件列表，如果它有状态的话
  // 但由于我们阻止了默认上传行为，通常不需要
};

const downloadFailed = () => {
  leadImportStore.exportFailedLogs();
};

const calculatedProgress = computed(() => {
  if (importProgress.value.total > 0) {
    return Math.round(((importProgress.value.success + importProgress.value.failed) / importProgress.value.total) * 100);
  }
  return 0;
});

const tableColumnsForPreview = computed(() => {
  if (columnNames.value.length === 0 && previewRows.value.length === 0) return []; // 如果没有列信息且没有预览行，返回空

  const dynamicCols = columnNames.value
    .filter(name => name !== '_originalRowIndex' && name !== '_contactColumnName' && name !== '_contactContent') // 排除已手动定义的内部字段
    .map(name => ({
      title: name,
      dataIndex: name,
      key: name,
      width: 150,
      ellipsis: true,
    }));

  return [
    {
      title: '序号',
      key: '_previewIndex',
      width: 70,
      fixed: 'left',
      customRender: ({ index }) => `${index + 1}`,
    },
    {
      title: '原始行号',
      dataIndex: '_originalRowIndex',
      key: '_originalRowIndex',
      width: 100,
      customRender: ({ text }) => text !== undefined ? `${text + 1}` : 'N/A', // 假设 _originalRowIndex 是0-indexed
    },
    {
      title: '来源联系列',
      dataIndex: '_contactColumnName',
      key: '_contactColumnName',
      width: 150,
      ellipsis: true,
      customRender: ({ text }) => text ? text : '(无来源列)'
    },
    {
      title: '提取联系方式',
      dataIndex: '_contactContent',
      key: '_contactContent',
      width: 180,
      ellipsis: true,
      customRender: ({ text }) => text ? text : '(未提取)'
    },
    {
      title: '预测类型',
      key: '_predictedType',
      width: 100,
      customRender: ({ record }) => {
        return record._predictedType || '未知';
      }
    },
    ...dynamicCols,
  ];
});

// 计算步骤条应该显示的当前步骤
const effectiveCurrentStep = computed(() => {
  if (currentStep.value === 0) return 0; // 上传文件
  if (currentStep.value === 1) return 1; // 字段映射
  if (currentStep.value === 2) return 2; // 模式选择 (归入导入选项与预览)
  if (currentStep.value === 3) return 3; // 执行直接批量导入 (归入执行导入)
  if (currentStep.value === 4) return 2; // 交互式预览与编辑 (归入导入选项与预览)
  if (currentStep.value === 5) return 3; // 执行交互式批量导入 (归入执行导入)
  if (currentStep.value === 6) return 4; // 完成
  return 0; // 默认
});

// 当组件卸载时，重置store，以便下次进入页面是干净的状态
onUnmounted(() => {
  leadImportStore.resetStore();
});

// 新增方法，用于模式选择步骤
const handleSelectDirectImport = () => {
  leadImportStore.selectDirectImportMode();
  // 紧接着触发实际的导入动作
  triggerImport(); // triggerImport 内部会检查 currentStep 是否为3
};

const handleSelectInteractiveMode = () => {
  leadImportStore.selectInteractiveImportMode();
};

const handleBackToMapping = () => {
  leadImportStore.returnToMappingStep();
};

// 用于交互式表格的列定义
const interactiveTableColumns = computed(() => {
  if (!paginatedInteractiveData.value || paginatedInteractiveData.value.length === 0) {
    return [{ title: '无数据可供预览', dataIndex: 'empty', key: 'empty', align: 'center' }];
  }

  const sampleRecord = paginatedInteractiveData.value[0];
  
  // 过滤并只保留白名单中的字段，或明确不排除的非内部字段
  const dynamicDataColumns = Object.keys(sampleRecord)
    .filter(key => {
      // 如果是白名单中的字段，直接显示
      if (INTERACTIVE_TABLE_VISIBLE_FIELDS.includes(key)) return true;
      
      // 排除以下划线开头的内部字段和其他不需要显示的字段
      if (key.startsWith('_') || key === 'id') return false;
      
      // 排除Vue相关内部属性
      if (key.startsWith('__v_')) return false;
      
      // 排除特定字段
      const excludedFields = [
        '_rowIndex', 'key', '_isEditing', '_originalData', 
        '_status', '_error', '_isEdited', '_isImported', '_importStatus',
        '_splitSourceId', '_isSplit', '_validationErrors', '_internalId'
      ];
      if (excludedFields.includes(key)) return false;
      
      // 对可能导致混淆的类型相关列进行警告（但仍然显示）
      const KEYWORDS_CONTACT_TYPE = ["平台", "类型", "来源", "方式", "type", "platform", "source"];
      if (KEYWORDS_CONTACT_TYPE.some(keyword => key.toLowerCase().includes(keyword.toLowerCase()))) {
        console.warn(`警告: 列 "${key}" 包含类型相关关键词，但不会用于判断联系方式类型。系统将使用智能预测。`);
      }
      
      // 显示原始Excel中的列（不以下划线开头，并且不在内部字段映射中）
      return !Object.keys(INTERNAL_FIELD_TITLE_MAP).includes(key);
    })
    .map(key => ({
      title: INTERNAL_FIELD_TITLE_MAP[key] || key, // 使用映射或原始名称
      dataIndex: key,
      key: key,
      width: (INTERNAL_FIELD_TITLE_MAP[key] || key).length > 10 ? 200 : 150,
      ellipsis: true,
      editable: !Object.keys(INTERNAL_FIELD_TITLE_MAP).includes(key),
    }));

  // 构建最终的列定义
  return [
    // 序号列
    {
      title: '序号',
      key: '_interactiveIndex',
      width: 70,
      fixed: 'left',
      align: 'center',
      customRender: ({ index }) => {
        return index + 1 + (interactiveCurrentPage.value - 1) * interactivePageSize.value;
      }
    },
    // 联系方式内容列
    {
      title: '联系方式内容',
      dataIndex: '_contactContent',
      key: '_contactContent',
      width: 180,
      ellipsis: true,
      customRender: ({ text }) => text || '(未提取)'
    },
    // 联系方式来源列
    {
      title: '来源列',
      dataIndex: '_contactColumnName',
      key: '_contactColumnName',
      width: 150,
      ellipsis: true,
      customRender: ({ text }) => text || '(无来源列)'
    },
    // 联系方式预测类型列
    {
      title: '预测类型',
      key: '_predictedType',
      width: 100,
      customRender: ({ record }) => {
        return record._predictedType || '未知';
      }
    },
    // 动态列（过滤后的）
    ...dynamicDataColumns,
    // 状态列
    {
      title: '状态',
      dataIndex: '_importStatus',
      key: '_statusColumn',
      width: 120,
      fixed: 'right',
      align: 'center'
    },
    // 操作列
    {
      title: '操作',
      key: 'actions',
      dataIndex: 'id',
      width: 260,
      fixed: 'right',
      align: 'center'
    }
  ];
});

const isImportingSingleRow = ref({}); // 用于跟踪单条导入的加载状态 { id: boolean }

// 原 handleEditRow 修改为 startEdit
const startEdit = (record) => {
  editingRowKey.value = record.id;
  editingRowData.value = { ...record };
};

const saveEdit = () => {
  if (editingRowKey.value !== null && editingRowData.value) {
    const dataToSave = { ...editingRowData.value };
    
    delete dataToSave.id;
    delete dataToSave._rowIndex; 
    delete dataToSave._isEditing; 
    delete dataToSave._isImported;
    delete dataToSave._importStatus;
    delete dataToSave._status;
    delete dataToSave._error;
    delete dataToSave._originalData; 

    leadImportStore.updateEditableRow(editingRowKey.value, dataToSave);
  }
  editingRowKey.value = null;
  editingRowData.value = {};
};

const cancelEdit = () => {
  editingRowKey.value = null;
  editingRowData.value = {};
};

const handleImportSingleRow = async (rowId) => {
  if (!apiService && import.meta.env.MODE !== 'test') {
    notification.error({ message: '错误', description: 'API服务未就绪' });
    return;
  }
  isImportingSingleRow.value[rowId] = true;
  try {
    await leadImportStore.importSingleEditableRow(rowId, apiService);
  } catch (e) {
    // 错误已由 store action 内部的 editedRowsStatus 记录，组件层面主要关注 UI 反馈
    // notification.error({ message: '导入单条记录出错', description: e.message }); // 如果 store action 重新抛出错误，这里可以捕获并显示
    // 通常 store action 会更新状态，UI 根据状态反应，不需要在这里重复 notification，除非 store action 不处理 UI notification
  } finally {
    isImportingSingleRow.value[rowId] = false;
  }
};

const handleStartInteractiveBatchImport = async () => {
  if (!apiService) {
    notification.error({ message: '错误', description: 'API服务未就绪' });
    return;
  }
  await leadImportStore.startInteractiveBatchImport(apiService);
};

const handleBackToModeSelection = () => {
  if (currentStep.value === 4) { // 仅当从交互式预览界面返回时
    leadImportStore.currentStep = 2;
  }
};

// 重新定义failedRecordsTableColumns为响应式引用
const failedRecordsTableColumns = ref([
  {
    title: '#',
    key: '_index',
    width: 60,
    customRender: ({ index }) => `${index + 1}`,
  },
  {
    title: '原始行号',
    dataIndex: ['rowData', '_originalRowIndex'], // Adapt to two data sources
    key: 'originalRowIndex',
    width: 100,
    customRender: ({ record }) => record.rowData?._originalRowIndex ?? record._originalRowIndex ?? 'N/A'
  },
  {
    title: '联系方式内容',
    dataIndex: ['rowData', '_contactContent'],
    key: 'contactContent',
    width: 200,
    ellipsis: true,
    customRender: ({ record }) => {
      const content = record.rowData?._contactContent ?? record._contactContent ?? 'N/A';
      return content.length > 50 ? `${content.substring(0, 50)}...` : content;
    }
  },
  {
    title: '错误原因',
    dataIndex: 'error', // Mainly for importProgress.errors
    key: 'errorReason',
    ellipsis: true, 
    customRender: ({ text, record }) => {
      try {
        // 确保始终返回字符串
        const errorText = String(record?._error || text || '未知错误');
        // 如果错误文本过长，截断显示
        if (errorText.length > 50) {
          return `${errorText.substring(0, 50)}...`;
        }
        return errorText;
      } catch (err) {
        console.error('错误原因渲染错误:', err);
        return '未知错误';
      }
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]);

const handlePageChange = (page, pageSize) => {
  interactiveCurrentPage.value = page;
  // 如果 pageSize 也发生变化 (例如，如果 show-size-changer 允许)
  // 但标准的 @change 通常只返回 page。 AntD 的 @change 是 (page, pageSize)
  // 如果 ant-design-vue 的 @change 确实提供了 pageSize，那么下面的赋值是多余的，因为 onShowSizeChange 会处理
  // if (pageSize && interactivePageSize.value !== pageSize) {
  //   interactivePageSize.value = pageSize; 
  // }
};

const handleImportCurrentPage = async () => {
  if (!apiService && import.meta.env.MODE !== 'test') {
    notification.error({ message: '错误', description: 'API服务未就绪' });
    return;
  }

  const rowsToImportOnPage = paginatedInteractiveData.value.filter(
    // row => !row._isImported // 只导入从未尝试过的记录 (旧逻辑)
    row => (!row._isImported || row._importStatus === 'failed') // 导入从未尝试过 或 曾经失败的记录
  );

  if (rowsToImportOnPage.length === 0) {
    message.info('当前页面没有需要导入的记录。');
    return;
  }

  isImportingCurrentPage.value = true;
  let successCount = 0;
  let failCount = 0;

  for (const row of rowsToImportOnPage) {
    if (!apiService && import.meta.env.MODE !== 'test') {
        console.error("API Service not loaded, cannot import row:", row);
        notification.error({ message: '导入中止', description: 'API服务加载失败，无法继续导入。' });
        isImportingCurrentPage.value = false;
        return; 
    }
    try {
      // MODIFIED: Use row.id instead of row._rowIndex
      await leadImportStore.importSingleEditableRow(row.id, apiService);
      successCount++;
    } catch (e) {
      failCount++;
      // Error notification should be handled by importSingleEditableRow or reflected by status change
    }
  }
  isImportingCurrentPage.value = false;
  if (successCount > 0 || failCount > 0) { 
      message.success(`本页记录导入尝试完毕：${successCount} 成功，${failCount} 失败。`);
  }
};

const onShowSizeChange = (current, pageSize) => {
  interactiveCurrentPage.value = 1; // 通常切换每页数量时回到第一页
  interactivePageSize.value = pageSize;
};

const handleCopyInsert = (record) => {
  leadImportStore.duplicateInteractiveRow(record.id);
  message.success('记录已复制并插入');
};

const handleDelete = (rowId) => {
  leadImportStore.deleteInteractiveRow(rowId);
  message.success('记录已删除');

  // 如果删除的是正在编辑的行，需要重置编辑状态
  if (editingRowKey.value === rowId) {
      cancelEdit(); // cancelEdit 会重置 editingRowKey 和 editingRowData
  }

  // 删除后检查当前页是否还有数据，如果没有则回到上一页
  nextTick(() => {
    const totalPages = totalInteractivePages.value;
    if (interactiveCurrentPage.value > totalPages && totalPages > 0) {
      interactiveCurrentPage.value = totalPages;
    }
    // 如果当前页没有数据了，确保刷新显示
    if (paginatedInteractiveData.value.length === 0 && totalPages > 0) {
      interactiveCurrentPage.value = Math.max(1, totalPages);
    }
  });
};

const handleEdit = (record) => {
  // ... existing code ...
};

const isReimportingFailedRow = ref({}); // ADD THIS REF

const handleReimportFailedRow = async (failedEntry) => {
  if (!apiService && import.meta.env.MODE !== 'test') {
    notification.error({ message: '错误', description: 'API 服务未就绪' });
    return;
  }
  const leadData = failedEntry.rowData;
  if (!leadData || leadData.id === undefined) {
    notification.error({ message: '错误', description: '无效的失败记录数据，无法重导入。' });
    return;
  }

  isReimportingFailedRow.value[leadData.id] = true;
  try {
    await leadImportStore.reimportFailedLead(leadData, apiService);
    message.success(`记录 (ID: ${leadData.id}) 已重新导入成功`);
  } catch (e) {
    notification.error({ message: '重新导入失败', description: `尝试重新导入记录 (ID: ${leadData.id}) 时发生错误: ${e.message}` });
  } finally {
    isReimportingFailedRow.value[leadData.id] = false;
  }
};

const debugPreviewData = () => {
  console.log("预览数据详情:", JSON.parse(JSON.stringify(previewRows.value)));
  if (previewRows.value.length > 0) {
    const sample = previewRows.value[0];
    console.log("样本记录关键字段:", {
      表头: columnNames.value,
      字段名称: Object.keys(sample),
      联系方式列: contactContentColumns.value,
      联系方式类型列: contactTypeColumn.value,
      来源联系列: sample._contactColumnName,
      提取联系方式: sample._contactContent,
      原始行索引: sample._originalRowIndex
    });
    
    // 检查选择的联系方式列中的实际值
    const contactFieldValues = {};
    if (contactContentColumns.value && contactContentColumns.value.length) {
      contactContentColumns.value.forEach(colName => {
        contactFieldValues[colName] = sample[colName];
      });
      console.log("选择的联系方式列中的值:", contactFieldValues);
    }
    
    // 检查Store中的状态
    console.log("Store中的状态:", {
      文件名: leadImportStore.rawFileName,
      当前步骤: leadImportStore.currentStep,
      联系方式列: leadImportStore.contactContentColumns,
      总行数: leadImportStore.totalRows
    });
  } else {
    console.warn("没有预览数据可供分析");
  }
  message.info("预览数据已输出到控制台，请打开开发者工具查看");
};

// 添加监听器，当联系方式内容列选择发生变化时重新处理数据
watch(contactContentColumns, async (newValue, oldValue) => {
  // 确保选择发生了实际变化
  if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
    console.log("联系方式内容列发生变化，新值:", newValue, "旧值:", oldValue);
    message.info("联系方式内容列已更新，正在重新处理数据...");
    
    if (file.value) {
      try {
        // 如果有文件，重新解析（新的parseFile方法会保留用户选择）
        await leadImportStore.parseFile(file.value);
      } catch (error) {
        message.error("重新解析数据失败: " + error.message);
      }
    } else if (leadImportStore.parsedData && leadImportStore.parsedData.length > 0) {
      // 如果没有文件但有已解析的数据，直接处理联系方式列变化
      leadImportStore.handleContactColumnsChange(newValue);
    } else {
      console.warn("无法处理联系方式列变化：没有文件或已解析的数据");
    }
  }
}, { deep: true });

</script>

<style scoped>
.lead-bulk-import-container {
  padding: 24px;
  background: #fff;
}
.content-area {
  margin-top: 24px;
}
.import-steps {
  margin-bottom: 32px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}
.step-content {
  padding: 24px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
}
.step-upload .ant-upload-drag-icon .anticon {
  font-size: 48px;
  color: #40a9ff;
}
.file-info {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
}
.mapping-alert {
  margin-bottom: 16px;
}
.mapping-form {
  margin-bottom: 24px;
}
.preview-title {
  margin-bottom: 12px;
  font-size: 16px;
}
.action-buttons {
  margin-top: 24px;
  text-align: right;
}
.action-buttons .ant-btn {
  margin-left: 8px;
}
.progress-text {
  text-align: center;
  margin-top: 16px;
  font-size: 16px;
}
.step-complete .ant-result {
  padding: 48px 0;
}
.error-details {
  margin-top: 24px;
  max-height: 300px; /* Or any suitable max height */
  overflow-y: auto;
}

/* 添加错误详情相关样式 */
:deep(.error-detail-popconfirm .ant-popover-inner-content) {
  max-width: 500px;
  max-height: 300px;
  overflow: auto;
}

:deep(.ant-table-tbody td) {
  word-break: break-word;
}

.failed-records-table-container {
  margin-top: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.failed-records-table-container h4 {
  margin-bottom: 16px;
  font-weight: 600;
  color: #ff4d4f;
}
.interactive-top-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.interactive-top-actions .ant-btn {
  margin-left: 8px;
}
.pagination-container {
  margin-top: 24px;
  text-align: right;
}
.debug-tools {
  margin-top: 10px;
  text-align: left;
}
.debug-tools .ant-btn {
  margin-left: 8px;
}
.step-interactive-preview {
  min-height: 800px !important; 
  overflow-y: auto !important; 
}
</style> 