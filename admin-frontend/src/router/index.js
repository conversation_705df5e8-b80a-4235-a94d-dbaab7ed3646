import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '../layouts/AdminLayout.vue'
import { useUserStore } from '../store'

// 路由懒加载视图组件
const Login = () => import('../views/Login.vue')
const Dashboard = () => import('../views/DashboardOptimized.vue') // 使用优化后的Dashboard

const UserManagement = () => import('../views/UserManagement.vue') // 稍后创建
const UserDetails = () => import('../views/users/UserDetails.vue') // 新增：用户详情页
const NotificationManagement = () => import('../views/NotificationManagement.vue') // 稍后创建
const ApiCallLog = () => import('../views/logs/ApiCallLog.vue') // 稍后创建
const LogFileViewer = () => import('../views/logs/LogFileViewer.vue') // 稍后创建
const NotFound = () => import('../views/NotFound.vue') // 稍后创建

// 新增：接口测试平台视图
const ApiTestPlatform = () => import('../views/ApiTestPlatform.vue');
// 新增：批量导入线索视图
const LeadBulkImport = () => import('../views/LeadBulkImport.vue');

// 新增：公司审核页面
const CompanyApproval = () => import('../views/CompanyApproval.vue');

// 新增：激活码管理页面
const ActivationCodeManagement = () => import('../views/ActivationCodeManagement.vue');

// 占位导入，待后续实现
const SystemSettings = () => import('../views/SystemSettings.vue');
const RealtimeLog = () => import('../views/logs/RealtimeLog.vue'); // 假设创建此文件

// LangChain智能体管理相关页面
const AdminAgentManagement = () => import('../views/langchain/AdminAgentManagement.vue');

const routes = [
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: AdminLayout,
    redirect: '/dashboard',
    meta: { requiresAuth: true }, // 这个父路由下的所有子路由都需要认证
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: { title: '仪表盘', icon: 'PieChartOutlined' } // Changed icon
      },

      {
        path: 'users',
        name: 'users',
        component: UserManagement,
        meta: { title: '用户管理', icon: 'UserOutlined' } // Changed icon
      },
      {
        path: 'users/:userId', // 新增：用户详情页路由
        name: 'UserDetails',
        component: UserDetails,
        meta: {
          title: '用户详情',
          requiresAuth: true,
          hideInMenu: true // 不在菜单中显示
        }
      },
      {
        path: 'activation-codes',
        name: 'ActivationCodeManagement',
        component: ActivationCodeManagement,
        meta: { title: '激活码管理', icon: 'KeyOutlined', requiresAuth: true }
      },
      {
        path: 'notifications',
        name: 'notifications',
        component: NotificationManagement,
        meta: { title: '通告管理', icon: 'NotificationOutlined' } // Changed icon
      },
      {
        path: 'leads',
        name: 'LeadsMenu',
        meta: { title: '线索管理', icon: 'GoldOutlined' }, // Parent for Leads
        component: { template: '<router-view />' },
        redirect: '/leads/bulk-import',
        children: [
          {
            path: 'bulk-import',
            name: 'LeadBulkImport',
            component: LeadBulkImport,
            meta: { title: '批量导入线索', icon: 'CloudUploadOutlined', requiresAuth: true }
          }
        ]
      },
      {
        path: 'api-testing',
        name: 'ApiTestPlatform',
        component: ApiTestPlatform, // 使用上面导入的组件
        meta: { title: '接口测试平台', icon: 'CodeSandboxOutlined', requiresAuth: true } // Ant Design的 code-sandbox 图标
      },
      {
        path: 'logs',
        name: 'LogsMenu',
        meta: { title: '日志管理', icon: 'ProfileOutlined' }, // Changed icon, ProfileOutlined is a common choice for logs/profile
        component: { template: '<router-view />' },
        redirect: '/logs/apicalls',
        children: [
          {
            path: 'apicalls',
            name: 'ApiCallLog',
            component: ApiCallLog,
            meta: { title: '接口调用日志', requiresAuth: true }
          },
          {
            path: 'fileviewer',
            name: 'LogFileViewer',
            component: LogFileViewer,
            meta: { title: '日志文件查看', requiresAuth: true }
          },
          {
            path: 'realtimelog',
            name: 'RealtimeLog',
            component: RealtimeLog,
            meta: { title: '实时日志', requiresAuth: true }
          }
        ]
      },
      {
        path: 'langchain',
        name: 'LangChainManagement',
        meta: { title: 'LangChain智能体', icon: 'RobotOutlined' },
        component: { template: '<router-view />' },
        redirect: '/langchain/overview',
        children: [
          {
            path: 'overview',
            name: 'LangChainOverview',
            component: () => import('../views/langchain/AgentOverview.vue'),
            meta: { title: '智能体概览', icon: 'DashboardOutlined', requiresAuth: true }
          },
          {
            path: 'agents',
            name: 'LangChainAgents',
            component: () => import('../views/langchain/AdminAgentManagement.vue'),
            meta: { title: '智能体管理', icon: 'AndroidOutlined', requiresAuth: true }
          },
          {
            path: 'agents/create',
            name: 'CreateAgent',
            component: () => import('../views/langchain/AgentEditor.vue'),
            meta: {
              title: '创建智能体',
              requiresAuth: true,
              hideInMenu: true // 不在菜单中显示
            }
          },
          {
            path: 'agents/:id/edit',
            name: 'EditAgent',
            component: () => import('../views/langchain/AgentEditor.vue'),
            meta: {
              title: '编辑智能体',
              requiresAuth: true,
              hideInMenu: true // 不在菜单中显示
            }
          },
          {
            path: 'model-providers',
            name: 'ModelProviders',
            component: () => import('../views/langchain/ModelProviderManagement.vue'),
            meta: { title: '模型供应商', icon: 'CloudServerOutlined', requiresAuth: true }
          },
          {
            path: 'knowledge-base',
            name: 'KnowledgeBaseManagement',
            component: () => import('../views/langchain/KnowledgeBaseManagement.vue'),
            meta: { title: '知识库管理', icon: 'DatabaseOutlined', requiresAuth: true }
          },
          {
            path: 'knowledge-base/:id',
            name: 'KnowledgeBaseDetail',
            component: () => import('../views/langchain/KnowledgeBaseDetail.vue'),
            meta: { title: '知识库详情', requiresAuth: true, hideInMenu: true }
          },
          {
            path: 'knowledge-base/:knowledgeBaseId/documents',
            name: 'DocumentManagement',
            component: () => import('../views/langchain/DocumentManagement.vue'),
            meta: { title: '文档管理', requiresAuth: true, hideInMenu: true }
          },
          {
            path: 'knowledge-base/:knowledgeBaseId/retrieval',
            name: 'RetrievalConfiguration',
            component: () => import('../views/langchain/RetrievalConfiguration.vue'),
            meta: { title: '检索配置', requiresAuth: true, hideInMenu: true }
          },
          {
            path: 'usage-statistics',
            name: 'LangChainUsageStatistics',
            component: () => import('../views/langchain/UsageStatistics.vue'),
            meta: { title: '使用统计', icon: 'BarChartOutlined', requiresAuth: true }
          },
          {
            path: 'langgraph-monitor',
            name: 'LangGraphMonitor',
            component: () => import('../views/langchain/LangGraphMonitor.vue'),
            meta: { title: 'LangGraph监控', icon: 'MonitorOutlined', requiresAuth: true }
          },

          {
            path: 'internal-function-tools',
            name: 'InternalFunctionTools',
            component: () => import('../views/InternalFunctionTools.vue'),
            meta: { title: '内部函数工具', icon: 'ToolOutlined', requiresAuth: true }
          }
        ]
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { title: '系统设置', icon: 'SettingOutlined' } // Changed icon
      },
      {
        path: 'company-approval', // 直接使用简单路径，不再嵌套
        name: 'CompanyApproval',
        component: CompanyApproval,
        meta: { title: '公司审核', icon: 'BankOutlined', requiresAuth: true }
      },
      {
        path: 'wechat-management',
        name: 'WeChatManagement',
        meta: { title: '微信用户管理', icon: 'WechatOutlined' },
        component: { template: '<router-view />' },
        redirect: '/wechat-management/friends',
        children: [
          {
            path: 'friends',
            name: 'WeChatFriends',
            component: () => import('../views/wechat/WeChatFriends.vue'),
            meta: { title: '微信好友', icon: 'TeamOutlined', requiresAuth: true }
          }
        ]
      }
    ]
  },
  {
    path: '/announcements/edit/:announcementId',
    name: 'EditAnnouncement',
    component: () => import('../views/announcement/EditAnnouncement.vue'),
    meta: {
      requiresAuth: true,
      title: '编辑通告',
      hideInMenu: true
    }
  },
  {
    path: '/announcements/create',
    name: 'CreateAnnouncement',
    component: () => import('../views/announcement/EditAnnouncement.vue'),
    meta: {
      requiresAuth: true,
      title: '新增通告',
      hideInMenu: true
    }
  },

  // 兜底路由，匹配所有未定义的路径
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面未找到', hideInMenu: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL), // 使用 HTML5 History 模式
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  try {
    const userStore = useUserStore()

    // 安全检查用户认证状态，捕获可能的store错误
    let isAuthenticated = false
    try {
      isAuthenticated = userStore.isAuthenticated
    } catch (storeError) {
      console.error('获取用户认证状态失败:', storeError)
      // 如果store出错，清理localStorage并重置认证状态
      try {
        userStore.logout()
      } catch (logoutError) {
        console.error('用户登出失败:', logoutError)
        // 手动清理localStorage
        try {
          localStorage.clear()
        } catch (clearError) {
          console.error('清理localStorage失败:', clearError)
        }
      }
      isAuthenticated = false
    }

    // 更新页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 管理后台`
    } else {
      document.title = '管理后台'
    }

    if (to.matched.some(record => record.meta.requiresAuth)) {
      // 如果目标路由需要认证
      if (!isAuthenticated) {
        // 用户未认证，重定向到登录页
        next({
          name: 'login',
          query: { redirect: to.fullPath } // 保存用户尝试访问的路径，登录后可以跳回
        })
      } else {
        // 用户已认证，允许访问
        next()
      }
    } else if (to.name === 'login' && isAuthenticated) {
      // 如果用户已认证但尝试访问登录页，则重定向到仪表盘
      next({ name: 'dashboard' })
    } else {
      // 不需要认证的页面或未定义的情况，直接放行
      next()
    }
  } catch (error) {
    console.error('路由守卫执行失败:', error)
    // 路由守卫出错时，如果是访问登录页则允许，否则重定向到登录页
    if (to.name === 'login') {
      next()
    } else {
      next({ name: 'login' })
    }
  }
})

export default router