import {defineStore} from 'pinia';
import * as XLSX from 'xlsx';
import {message} from 'ant-design-vue';
import {deepClone, generateUUID} from '@/utils'; // 导入新的工具函数
// 假设 apiService 是你项目中封装的请求服务，例如:
// import apiService from '@/services/api'; 

const KEYWORDS_CONTACT_CONTENT = ["联系方式", "手机", "微信", "邮箱", "号码", "电话", "contact", "phone", "mobile", "email", "wechat", "whatsapp"];
const KEYWORDS_CONTACT_TYPE = ["平台", "类型", "来源", "方式", "type", "platform", "source"];

// +++ 私有辅助函数，用于构建API Payload +++
function _buildApiPayload(row, allColumnNames, selectedContactTypeColumn, currentLeadSource, currentContactRecordSource) {
  const payload = {
    联系方式: { 内容: '', 类型: '', 来源: '' },
    线索来源: String(currentLeadSource || '').trim(),
    额外信息: {}
  };

  // 提取联系方式内容
  // 优先使用 _contactContent (如果是拆分或编辑后)，否则尝试从原始列名获取
  let contactContent = '';
  if (row && row._contactContent !== undefined && row._contactContent !== null) {
    contactContent = String(row._contactContent).trim();
  } else if (row && row._contactColumnName && row[row._contactColumnName] !== undefined && row[row._contactColumnName] !== null) {
    contactContent = String(row[row._contactColumnName]).trim();
  } else {
    // 检查row中是否存在联系方式内容列，如果存在则使用
    if (allColumnNames && Array.isArray(allColumnNames)) {
      for (const colName of allColumnNames) {
        if (row[colName] !== undefined && row[colName] !== null && 
            KEYWORDS_CONTACT_CONTENT.some(keyword => colName.toLowerCase().includes(keyword.toLowerCase()))) {
          contactContent = String(row[colName]).trim();
          console.log(`从列 ${colName} 中提取联系方式内容: ${contactContent}`);
          break;
        }
      }
    }
  }
  payload.联系方式.内容 = contactContent;

  // 设置联系方式来源
  payload.联系方式.来源 = String(currentContactRecordSource || '').trim();

  // 确定联系方式类型
  let contactType = null;
  if (contactContent) { // 智能识别
    console.log(`[_buildApiPayload] 开始智能识别联系方式类型，内容: "${contactContent}"`);
    const contentStrLower = contactContent.toLowerCase();
    
    // 简化手机号判断逻辑，使用更直接的方式
    const cleanPhone = contactContent.replace(/\s|-|\+86/g, '');
    console.log(`[_buildApiPayload] 清理后的可能手机号: "${cleanPhone}"`);
    
    // 中国大陆手机号规则: 1开头，第二位是3-9之间的数字，后跟9位数字，总共11位
    if (/^1[3-9]\d{9}$/.test(cleanPhone)) {
      contactType = '电话';
      console.log(`[_buildApiPayload] 识别为电话: ${cleanPhone}`);
    } else if (contentStrLower.includes('@') && contentStrLower.includes('.')) { 
      contactType = '邮箱';
      console.log(`[_buildApiPayload] 识别为邮箱: ${contactContent}`);
    } else if (contentStrLower.length >= 5 && contentStrLower.length <= 30 && 
              !contentStrLower.includes(' ') && // 不包含空格
              !contentStrLower.includes('@') && // 不包含邮箱字符
              !/^\d+$/.test(contentStrLower)) { // 不是纯数字
      contactType = '微信';
      console.log(`[_buildApiPayload] 识别为微信: ${contactContent}`);
    } else {
      console.log(`[_buildApiPayload] 无法识别类型，将使用默认值"未知": ${contactContent}`);
    }
  }
  payload.联系方式.类型 = contactType || '未知'; // 默认未知
  console.log(`[_buildApiPayload] 最终确定的联系方式类型: ${payload.联系方式.类型}`);
  
  // 添加调试信息，不影响正常功能
  payload._debug = {
    预测类型: payload.联系方式.类型,
    原始内容: contactContent,
    清理后内容: contactContent.replace(/\s|-|\+86/g, ''),
    类型来源: '智能预测', // 添加类型来源信息
    智能预测规则: contactType ? `根据内容格式识别为${contactType}` : '无法识别'
  };

  // 如果有手动选择的类型列，记录它的值（但不使用它）
  if (selectedContactTypeColumn && row && row[selectedContactTypeColumn] !== undefined) {
    const manualTypeValue = String(row[selectedContactTypeColumn]).trim();
    console.log(`[_buildApiPayload] 注意：存在类型列 ${selectedContactTypeColumn}，值为 "${manualTypeValue}"，但不会使用它`);
    payload._debug.忽略的手动类型列 = `${selectedContactTypeColumn}: ${manualTypeValue}`;
  }

  // 填充额外信息
  if (row && allColumnNames && Array.isArray(allColumnNames)) {
    allColumnNames.forEach(colName => {
      // 排除已用于核心字段的列和内部字段
      if (
        !currentContactContentColumns.includes(colName) && // 新的、更精确的检查
        colName !== selectedContactTypeColumn &&
        !colName.startsWith('_') &&
        row.hasOwnProperty(colName) &&
        row[colName] !== undefined &&
        row[colName] !== null &&
        String(row[colName]).trim() !== ''
      ) {
        payload.额外信息[colName] = row[colName];
      }
    });
  }
  // 确保额外信息中的id (如果原始列名是id) 或其他内部字段名被移除
  if (payload.额外信息.hasOwnProperty('id')) delete payload.额外信息.id;


  return payload;
}
// +++ 辅助函数定义结束 +++

export const useLeadImportStore = defineStore('leadImport', {
  state: () => ({
    file: null,
    rawFileName: '',
    parsedData: [],
    columnNames: [],
    contactContentColumns: [],
    contactTypeColumn: null,
    leadSource: '', // 设置初始默认值为空，稍后会根据文件名填充
    contactRecordSource: '批量导入', // 默认值
    importProgress: {
      total: 0,
      success: 0,
      failed: 0,
      errors: [], // {rowData: Object, error: string}
      isComplete: false,
    },
    isParsing: false,
    isImporting: false,
    currentStep: 0, // 0:待上传, 1:字段映射, 2:模式选择, 3:执行直接批量导入, 4:交互式预览与编辑, 5:执行交互式批量导入, 6:完成
    initialRowCountFromFile: 0, // 新增：用于存储文件原始行数（不含表头）
    
    // +++ ADDED STATE PROPERTIES +++
    sheetNames: [], 
    selectedSheet: null,
    // +++ END ADDED STATE PROPERTIES +++

    // 新增状态以支持交互式编辑
    editableData: [], // Array<Object>，存储可在交互模式下编辑的数据副本
    editedRowsStatus: new Map(), // Map<number, { isEdited?: boolean, isImported?: boolean, importStatus?: 'success'|'failed', error?: string }>, key是行索引
  }),

  getters: {
    previewRows: (state) => state.parsedData.slice(0, 5),
    canProceedToModeSelection: (state) => 
      !!state.contactContentColumns.length &&
      !!state.leadSource &&
      !!state.contactRecordSource &&
      state.parsedData.length > 0 &&
      !state.isParsing && // 确保解析完成
      !state.isImporting, // 确保没有其他导入正在进行

    // Getter 判断是否可以开始直接批量导入 (条件基本同上，用于按钮状态)
    canStartDirectImport: (state) => state.canProceedToModeSelection, 

    // Getter 判断是否可以进入交互式预览与编辑 (条件基本同上)
    canStartInteractivePreview: (state) => state.canProceedToModeSelection,

    // Getter 合并 editableData 和 editedRowsStatus，方便表格展示
    interactiveDataWithStatus: (state) => {
      return state.editableData.map(row => { // row 中已包含 id, _originalRowIndex 等
        const statusEntry = state.editedRowsStatus.get(row.id) || {}; // 使用 row.id 获取状态
        const determinedStatus = statusEntry.importStatus || 'pending'; // 默认状态

        return {
          ...row, // 包含 id, _originalRowIndex, 和其他从 editableData 来的数据
          _rowIndex: state.editableData.indexOf(row), // _rowIndex 现在明确为在 editableData 中的当前索引
          _isEdited: statusEntry.isEdited || false,
          _isImported: statusEntry.isImported || false,
          _importStatus: determinedStatus,
          _error: statusEntry.error,
          _status: determinedStatus, // _status 供显示，与 _importStatus 保持一致
        };
      });
    },

    totalRows: (state) => state.parsedData.length, // 这个totalRows指的是原始解析出的，用于直接导入模式的计数
    // 如果需要交互模式的总行数，应该是 editableData.length
    totalInteractiveRows: (state) => state.editableData.length, // 新增 getter，用于交互模式的总行数

    processedRows: (state) => state.importProgress.success + state.importProgress.failed,
    // 交互模式下的已处理行数 (已尝试导入的)
    interactiveProcessedRows: (state) => {
      let count = 0;
      state.editedRowsStatus.forEach(status => {
        if (status.isImported) {
          count++;
        }
      });
      return count;
    },
    // 交互模式下的成功导入行数
    interactiveSuccessRows: (state) => {
      let count = 0;
      state.editedRowsStatus.forEach(status => {
        if (status.isImported && status.importStatus === 'success') {
          count++;
        }
      });
      return count;
    },
     // 交互模式下的失败导入行数
    interactiveFailedRows: (state) => {
      let count = 0;
      state.editedRowsStatus.forEach(status => {
        if (status.isImported && status.importStatus === 'failed') {
          count++;
        }
      });
      return count;
    },
  },

  actions: {
    // ++ 新增内部方法：预测联系方式类型 ++
    _predictContactTypeInternal(content) {
      if (!content) return '未知';
      const contentStr = String(content).trim();
      if (!contentStr) return '未知'; // 如果trim后为空，也视为未知

      const contentStrLower = contentStr.toLowerCase();
      const cleanPhone = contentStr.replace(/\s|-|\+86/g, '');

      if (/^1[3-9]\d{9}$/.test(cleanPhone)) {
        console.log(`[_predictContactTypeInternal] Content: "${content}", Cleaned: "${cleanPhone}" -> Predicted: 电话`);
        return '电话';
      } else if (contentStrLower.includes('@') && contentStrLower.includes('.')) {
        console.log(`[_predictContactTypeInternal] Content: "${content}" -> Predicted: 邮箱`);
        return '邮箱';
      } else if (contentStrLower.length >= 5 && contentStrLower.length <= 30 &&
                 !contentStrLower.includes(' ') && 
                 !contentStrLower.includes('@') && 
                 !/^\d+$/.test(contentStrLower)) {
        console.log(`[_predictContactTypeInternal] Content: "${content}" -> Predicted: 微信`);
        return '微信';
      }
      
      console.log(`[_predictContactTypeInternal] Content: "${content}" -> Predicted: 未知`);
      return '未知';
    },
    // ++ 内部方法结束 ++

    // ++ 将原来的 _buildApiPayload 移入并重命名为 _buildApiPayloadInternal ++
    _buildApiPayloadInternal(row, allColumnNames, selectedContactTypeColumn, currentLeadSource, currentContactRecordSource, currentContactContentColumns) {
      const payload = {
        联系方式: { 内容: '', 类型: '', 来源: '' },
        线索来源: String(currentLeadSource || '').trim(),
        额外信息: {}
      };

      let contactContent = '';
      if (row && row._contactContent !== undefined && row._contactContent !== null) {
        contactContent = String(row._contactContent).trim();
      } else if (row && row._contactColumnName && row[row._contactColumnName] !== undefined && row[row._contactColumnName] !== null) {
        contactContent = String(row[row._contactColumnName]).trim();
      } else {
        if (allColumnNames && Array.isArray(allColumnNames)) {
          for (const colName of allColumnNames) {
            if (row[colName] !== undefined && row[colName] !== null && 
                KEYWORDS_CONTACT_CONTENT.some(keyword => colName.toLowerCase().includes(keyword.toLowerCase()))) {
              contactContent = String(row[colName]).trim();
              break;
            }
          }
        }
      }
      payload.联系方式.内容 = contactContent;
      payload.联系方式.来源 = String(currentContactRecordSource || '').trim();
      
      // 使用新的内部预测方法
      payload.联系方式.类型 = this._predictContactTypeInternal(contactContent);
      console.log(`[_buildApiPayloadInternal] 最终确定的联系方式类型 (using _predictContactTypeInternal): ${payload.联系方式.类型}`);

      payload._debug = {
        预测类型: payload.联系方式.类型,
        原始内容: contactContent,
        清理后内容: contactContent.replace(/\s|-|\+86/g, ''), // 保留此处的清理用于调试展示
        类型来源: 'Store内部预测函数',
        智能预测规则: `调用 this._predictContactTypeInternal`
      };

      if (selectedContactTypeColumn && row && row[selectedContactTypeColumn] !== undefined) {
        const manualTypeValue = String(row[selectedContactTypeColumn]).trim();
        payload._debug.忽略的手动类型列 = `${selectedContactTypeColumn}: ${manualTypeValue}`;
      }

      if (row && allColumnNames && Array.isArray(allColumnNames)) {
        allColumnNames.forEach(colName => {
          if (
            !currentContactContentColumns.includes(colName) && // 新的、更精确的检查
            colName !== selectedContactTypeColumn &&
            !colName.startsWith('_') &&
            row.hasOwnProperty(colName) &&
            row[colName] !== undefined &&
            row[colName] !== null &&
            String(row[colName]).trim() !== ''
          ) {
            payload.额外信息[colName] = row[colName];
          }
        });
      }
      if (payload.额外信息.hasOwnProperty('id')) delete payload.额外信息.id;
      return payload;
    },
    // ++ _buildApiPayloadInternal 结束 ++

    // ++ 新增：读取并解析Sheet的私有方法 ++
    async _readAndParseSheet(file, selectedSheetName) {
      if (!file) {
        throw new Error("未提供文件对象");
      }
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      
      const sheetNameToUse = selectedSheetName || workbook.SheetNames[0];
      if (!workbook.Sheets[sheetNameToUse]) {
        throw new Error(`工作表 "${sheetNameToUse}" 不存在于文件中。`);
      }
      const worksheet = workbook.Sheets[sheetNameToUse];
      const merges = worksheet['!merges'] || [];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null });

      if (!jsonData || jsonData.length === 0) {
        throw new Error("无法解析文件，或者选择的工作表为空。");
      }
      const columnNames = jsonData[0] ? jsonData[0].map(col => col ? String(col).trim() : '') : [];
      const rawDataRows = jsonData.slice(1);

      return {
        columnNames,
        rawDataRows,
        merges,
        sheetNames: workbook.SheetNames, // 还返回所有sheet的名称，供外部更新
        actuallyUsedSheet: sheetNameToUse // 返回实际使用的sheet名
      };
    },
    // ++ _readAndParseSheet 方法结束 ++

    // ++ 新增：构建基础行数据对象的私有方法 ++
    _buildBaseRows(parsedRawDataRows, columnNames, mergedValuesMap) {
      const baseRows = [];
      parsedRawDataRows.forEach((rawRowArray, zeroBasedRowIndex) => {
        const baseRowData = {};
        const originalSheetRowIndex = zeroBasedRowIndex + 1; // 原始Excel表中数据行的1基索引 (header是第0行)

        columnNames.forEach((colName, colIdx) => {
          if (!colName) return; // 跳过没有列名的列
          
          let cellValueToProcess = rawRowArray[colIdx];

          // 优先使用原始值，如果为null/undefined，再检查是否为合并单元格的一部分
          if (cellValueToProcess === null || cellValueToProcess === undefined) {
            // mergedValuesMap 的 key 是 `${r}_${c}`，r是1-based原始数据行号
            const mergedKey = `${originalSheetRowIndex}_${colIdx}`;
            if (mergedValuesMap.has(mergedKey)) {
              cellValueToProcess = mergedValuesMap.get(mergedKey);
              // console.log(`Row ${originalSheetRowIndex}, Col ${colIdx} got value from merge: ${cellValueToProcess}`);
            }
          }
          baseRowData[colName] = cellValueToProcess;
        });

        const uniqueId = generateUUID();
        baseRows.push({
          ...baseRowData,
          id: uniqueId, // 使用UUID
          _internalId: uniqueId, // 保持一致
          _originalRowIndex: zeroBasedRowIndex, // 存储0基的原始数据行索引 (相对于parsedRawDataRows)
          _isSplit: false,
          _splitSourceId: null,
          _validationErrors: {},
          _contactColumnName: '',
          _contactContent: '' 
        });
      });
      return baseRows;
    },
    // ++ _buildBaseRows 方法结束 ++

    // ++ 新增：根据换行符拆分记录的私有方法 ++
    _splitRecordsByDelimiters(processedRows) {
      const finalSplitData = [];
      processedRows.forEach(originalProcessedRow => {
        const content = originalProcessedRow._contactContent;
        if (content === null || content === undefined || typeof content !== 'string' || content.trim() === '') {
          finalSplitData.push({ ...originalProcessedRow, _contactContent: '' });
          return;
        }
        const individualContactStrings = content.split(/[\s\/、]+/g);
        if (individualContactStrings.length <= 1 && content.trim() !== '') { 
          originalProcessedRow._contactContent = content.trim();
          finalSplitData.push(originalProcessedRow);
        } else {
          let hasAddedAnySplitRow = false;
          individualContactStrings.forEach(contactString => {
            const trimmedContact = contactString.trim();
            if (trimmedContact !== '' && trimmedContact.length >= 6) {
              const newSplitRow = deepClone(originalProcessedRow);
              newSplitRow._contactContent = trimmedContact;
              newSplitRow.id = generateUUID(); // 为拆分出的新行生成新的唯一ID
              newSplitRow._internalId = newSplitRow.id;
              newSplitRow._isSplit = true;
              newSplitRow._splitSourceId = originalProcessedRow.id;
              finalSplitData.push(newSplitRow);
              hasAddedAnySplitRow = true;
            }
          });
          if (!hasAddedAnySplitRow && originalProcessedRow._contactContent.trim() !== '') {
              const fallbackRow = deepClone(originalProcessedRow);
              fallbackRow._contactContent = originalProcessedRow._contactContent.trim();
              finalSplitData.push(fallbackRow);
          }
        }
      });
      return finalSplitData;
    },
    // ++ _splitRecordsByDelimiters 方法结束 ++

    // ++ 新增：为记录预测联系方式类型的私有方法 ++
    _predictTypesForRecords(finalSplitRows) {
      return finalSplitRows.map(row => {
        const contactContentForPrediction = (typeof row._contactContent === 'string') ? row._contactContent : '';
        return {
          ...row,
          _predictedType: this._predictContactTypeInternal(contactContentForPrediction)
        };
      });
    },
    // ++ _predictTypesForRecords 方法结束 ++

    setFile(newFile) {
      this.file = newFile;
      this.rawFileName = newFile.name;
      this.currentStep = 0; // 回到上传阶段，准备解析
      // 重置部分状态，以便重新解析和映射
      this.parsedData = [];
      this.columnNames = [];
      this.contactContentColumns = [];
      this.contactTypeColumn = null; // 确保设为null，启用智能判断
      this.leadSource = this.rawFileName; // 将线索来源设置为文件名
      this.contactRecordSource = '批量导入'; // 重置为默认
      this.importProgress = { total: 0, success: 0, failed: 0, errors: [], isComplete: false };
      this.editableData = [];
      this.editedRowsStatus.clear();
      this.selectedSheet = null; // 确保每次选择新文件时，都从第一个sheet开始解析
    },

    async parseFile(file) {
      // 保存当前用户选择的配置，避免被resetStore()清除
      const savedContactContentColumns = [...this.contactContentColumns];
      // const savedContactTypeColumn = null; // 已移除，类型列不再手动选择
      const savedLeadSource = this.leadSource || file.name;
      const savedContactRecordSource = this.contactRecordSource;
      
      this.parsedData = [];
      this.isParsing = true;
      this.error = null;
      this.currentStep = 0; // Reset to 0, then _readAndParseSheet might move it if sheet selection is needed, or set to 1 after this block
      // Actually, set currentStep for UI after successful parsing or if sheet selection is needed.
      console.log("开始解析文件:", file.name, "保存的联系方式列:", savedContactContentColumns);
      
      try {
        const { 
          columnNames: parsedColumnNames, 
          rawDataRows: parsedRawDataRows, 
          merges,
          sheetNames: allSheetNames,
          actuallyUsedSheet
        } = await this._readAndParseSheet(file, this.selectedSheet);

        this.sheetNames = allSheetNames;
        if (allSheetNames.length > 1 && !this.selectedSheet) {
          this.currentStep = 0; // Or a specific step for sheet selection if UI supports it
          this.isParsing = false;
          //  Add UI mechanism for sheet selection if desired. For now, defaults to first sheet.
          // message.info("文件包含多个工作表，已默认选择第一个。您可以稍后在设置中更改。");
        }
        this.selectedSheet = actuallyUsedSheet;
        this.columnNames = parsedColumnNames;

        console.log(`工作表 ${this.selectedSheet} 包含合并单元格: ${merges.length} 个`);
        console.log(`从文件解析到 ${parsedRawDataRows.length} 行原始数据 (不含表头)`);

        const mergedValuesMap = new Map();
        if (merges.length > 0 && parsedRawDataRows) {
          merges.forEach(merge => {
            const { s: startCell, e: endCell } = merge;
            const dataRowIndexForStartCell = startCell.r -1;
            if (parsedRawDataRows[dataRowIndexForStartCell] && 
                parsedRawDataRows[dataRowIndexForStartCell][startCell.c] !== undefined && 
                parsedRawDataRows[dataRowIndexForStartCell][startCell.c] !== null) {
              const anchorValue = parsedRawDataRows[dataRowIndexForStartCell][startCell.c];
              for (let r = startCell.r; r <= endCell.r; r++) {
                for (let c = startCell.c; c <= endCell.c; c++) {
                  if (r > 0) { 
                     mergedValuesMap.set(`${r}_${c}`, anchorValue); 
                  }
                }
              }
            }
          });
        }

        this.initialRowCountFromFile = parsedRawDataRows.length;
        
        let processedData = this._buildBaseRows(parsedRawDataRows, this.columnNames, mergedValuesMap);
        
        // 恢复用户之前的选择 (特别是 contactContentColumns)
        this.contactContentColumns = savedContactContentColumns;
        this.leadSource = savedLeadSource; 
        this.contactRecordSource = savedContactRecordSource;
        console.log("解析完成后恢复联系方式列选择:", this.contactContentColumns);
        
        if (this.contactContentColumns && this.contactContentColumns.length > 0) {
          processedData = this._processContactColumns(processedData); // _processContactColumns is the old name for _applyContactProcessing
        }
        
        processedData = this._splitRecordsByDelimiters(processedData);

            // 新增：对拆分后的联系方式内容进行最终格式清理，主要处理数字格式
            processedData = processedData.map(record => {
              if (record._contactContent && typeof record._contactContent === 'string') {
                const originalContact = record._contactContent;
                // 正则表达式仅移除数字字符串中的常见分隔符，保留纯数字
                // 例如: "138-1234-5678" -> "13812345678"
                // "138 1234 5678" -> "13812345678"
                // "138/1234/5678" -> "13812345678"
                // 如果已经是 "13812345678"，则不会改变
                // 这个设计确保它只处理看起来像数字串的，避免错误清理其他类型的联系方式
                const cleanedContact = originalContact.replace(/^(\d[\d\s-/]*\d|\d+)$/g, (match) => {
                  return match.replace(/[-\s/]/g, '');
                });

                if (cleanedContact !== originalContact) {
                  console.log(`[Contact Number Cleaning] Cleaned contact number: "${originalContact}" -> "${cleanedContact}" for row with original index ${record._originalRowIndex}`);
                  record._contactContent = cleanedContact;
                }
              }
              return record;
            });
            // 联系方式数字清理结束

        processedData = this._predictTypesForRecords(processedData);
        
        this.parsedData = processedData;
        
        console.log(`解析完成，最终生成 ${this.parsedData.length} 条记录 (所有处理步骤完成), 其中包含联系方式列: ${this.contactContentColumns.join(', ')}`);
        if (this.parsedData.length > 0) {
          const sampleRecord = this.parsedData[0];
          console.log("样本记录 (最终):", {
            id: sampleRecord.id,
            _contactContent: sampleRecord._contactContent,
            _predictedType: sampleRecord._predictedType,
            _originalRowIndex: sampleRecord._originalRowIndex,
          });
        } else {
          console.warn("解析后没有记录");
        }

        this.contactTypeColumn = null; // 确保类型列始终为null，强制使用智能预测
        this.currentStep = 1; // 进入字段映射阶段

      } catch (parseError) {
        console.error("文件解析过程中发生错误:", parseError);
        this.isParsing = false;
        this.error = parseError.message; // Store error message
        message.error(`文件解析失败: ${parseError.message}`);
        this.currentStep = 0; // Or a specific error step if exists
      } finally {
        this.isParsing = false;
      }
    },

    setLeadSource(source) {
      this.leadSource = source;
    },
    setContactRecordSource(source) {
      this.contactRecordSource = source;
    },

    // 新 Action: 当字段映射完成，准备进入模式选择
    proceedToModeSelection() {
      if (!this.canProceedToModeSelection) return;
      // 初始化可编辑数据区域
      this.editableData = deepClone(this.parsedData); // 使用 deepClone，并保持从 parsedData 继承的UUID

      this.editedRowsStatus.clear();
      this.editableData.forEach((item) => { // 使用 item.id (UUID) 作为 key
        this.editedRowsStatus.set(item.id, { isEdited: false, isImported: false, importStatus: 'pending' });
      });
      this.currentStep = 2; // 进入模式选择步骤
    },

    duplicateInteractiveRow(rowIdToCopy) {
      const originalRecordIndex = this.editableData.findIndex(item => item.id === rowIdToCopy);
      if (originalRecordIndex === -1) {
        console.error('[Store Action] Original record not found for duplication, id:', rowIdToCopy);
        message.error('找不到要复制的原始记录');
        return; // 或者返回一个表示失败的状态
      }

      const originalRecord = this.editableData[originalRecordIndex];
      
      // 使用我们的深拷贝工具函数，确保新对象是独立的
      const newRecord = deepClone(originalRecord);

      // 分配新的唯一ID
      newRecord.id = generateUUID(); // 使用UUID

      // 重置状态字段
      newRecord._isEdited = false;
      newRecord._isImported = false;
      newRecord._importStatus = 'pending';
      newRecord._error = null;

      // 将新记录插入到原始记录之后
      this.editableData.splice(originalRecordIndex + 1, 0, newRecord);

      //为新记录在 editedRowsStatus 中设置初始状态
      this.editedRowsStatus.set(newRecord.id, {
        isEdited: false,
        isImported: false,
        importStatus: 'pending'
      });
      
      // 更新相关统计
      this.calculateStatistics();
    },

    deleteInteractiveRow(rowIdToDelete) {
      const rowIndexToDelete = this.editableData.findIndex(item => item.id === rowIdToDelete);
      if (rowIndexToDelete === -1) {
        console.error('[Store Action] Record not found for deletion, id:', rowIdToDelete);
        return; // 或者返回失败状态
      }

      this.editableData.splice(rowIndexToDelete, 1); // 从 editableData 中移除
      this.editedRowsStatus.delete(rowIdToDelete); // 从 editedRowsStatus 中移除状态
      
      // 如果删除的是正在编辑的行，需要重置编辑状态 (虽然UI可能已经处理)
      // if (this.editingRowKey === rowIdToDelete) { // 假设 editingRowKey 也在 store 中管理，如果不在，则此逻辑在组件中
      //   this.editingRowKey = null;
      //   this.editingRowData = {};
      // }
    },

    // 新 Action: 用户选择直接批量导入模式
    selectDirectImportMode() {
      this.currentStep = 3; // 标记进入直接批量导入执行阶段
      this.importProgress = { total: 0, success: 0, failed: 0, errors: [], isComplete: false };
    },

    // 新 Action: 用户选择交互式导入模式
    selectInteractiveImportMode() {
      this.importProgress = { total: 0, success: 0, failed: 0, errors: [], isComplete: false };
      this.currentStep = 4; // 进入交互式预览与编辑阶段
    },

    returnToMappingStep() {
      this.currentStep = 1;
      this.editableData = [];
      this.editedRowsStatus.clear();
      this.importProgress = { total: 0, success: 0, failed: 0, errors: [], isComplete: false };
    },

    // 原 startImport 更名为 startDirectBatchImport
    async startDirectBatchImport(apiService) { 
      if (this.parsedData.length === 0) {
        message.error("没有可导入的数据。");
        return;
      }
      if (this.isImporting) {
        message.warn("已有导入任务在进行中。");
        return;
      }

      this.isImporting = true;
      this.importProgress = { 
        total: this.parsedData.length, 
        success: 0, 
        failed: 0, 
        errors: [], 
        isComplete: false 
      };

      // 添加批处理逻辑，改进性能
      const batchSize = 50; // 每批处理的记录数
      const totalRecords = this.parsedData.length;
      
      for (let startIndex = 0; startIndex < totalRecords; startIndex += batchSize) {
        const endIndex = Math.min(startIndex + batchSize, totalRecords);
        const currentBatch = this.parsedData.slice(startIndex, endIndex);
        
        await Promise.all(currentBatch.map(async row => {
          // 使用新的内部方法构建 payload
          const payload = this._buildApiPayloadInternal(row, this.columnNames, this.contactTypeColumn, this.leadSource, this.contactRecordSource, this.contactContentColumns);

          // 检查联系方式内容是否有效，因为 _buildApiPayload 内部可能返回空内容
          if (!payload.联系方式.内容) {
              this.importProgress.failed++;
              try {
                  this.importProgress.errors.push({ rowData: deepClone(row), error: "联系方式内容缺失或无效" });
              } catch (e) {
                  this.importProgress.errors.push({ rowData: { _originalRowIndex: row._originalRowIndex, _contactColumnName: row._contactColumnName }, error: "联系方式内容缺失或无效 (原始行数据序列化失败)" });
              }
              return; 
          }

          let isSuccess = false;
          let errorMessage = '导入操作未能完成';

          try {
            // 修正API路径，确保与后端一致
            const 响应数据 = await apiService.post('/leads/upload', payload);
            console.log("线索上传API响应:", 响应数据);

            // 优化后的拦截器直接返回response.data，所以响应数据就是统一响应格式
            if (响应数据 && 响应数据.status === 100) {
              isSuccess = true;
            } else if (响应数据 && 响应数据.message) {
              errorMessage = 响应数据.message;
            } else if (响应数据) {
              errorMessage = `导入失败，状态码: ${响应数据.status}`;
            } else {
              errorMessage = '导入失败，后端响应无效';
            }
          } catch (err) {
            console.error("API call /leads/upload failed in startDirectBatchImport:", err);
            if (err.response && err.response.data && err.response.data.message) {
              errorMessage = err.response.data.message;
            } else if (err.message) {
              errorMessage = err.message;
            } else {
              errorMessage = 'API 请求异常，无详细信息';
            }
          }

          if (isSuccess) {
            this.importProgress.success++;
          } else {
            this.importProgress.failed++;
            this.importProgress.errors.push({ rowData: deepClone(row), error: errorMessage });
          }
        }));
        
        // 每批次完成后更新进度
        if ((startIndex + batchSize) % (batchSize * 5) === 0 || endIndex === totalRecords) {
          message.info(`已处理 ${endIndex}/${totalRecords} 条记录`);
        }
      }
      
      this.importProgress.isComplete = true;
      this.isImporting = false;
      this.currentStep = 6;
      this.editedRowsStatus.clear();
    },

    updateEditableRow(rowId, updatedData) {
      const rowIndex = this.editableData.findIndex(row => row.id === rowId);
      if (rowIndex !== -1) {
        // 创建一个新的对象以确保响应性，只更新传入的字段
        // 同时保留原始记录中未被更新的字段，以及重要的内部字段如 id, _originalRowIndex
        const originalRecord = this.editableData[rowIndex];
        this.editableData[rowIndex] = {
           ...originalRecord, // 保留原始数据和内部字段
           ...updatedData,    // 应用传入的更新
           id: rowId,        // 确保 id 不变
        };
        
        // 更新编辑状态
        const currentStatus = this.editedRowsStatus.get(rowId) || {};
        this.editedRowsStatus.set(rowId, { 
          ...currentStatus, 
          isEdited: true, 
          // importStatus: 'pending' // 编辑后可能需要重置导入状态，或根据业务逻辑决定
        });
        // console.log("Updated row:", rowId, this.editableData[rowIndex]);
      } else {
        console.warn(`[Store Action] Row with id ${rowId} not found for update.`);
      }
    },

    async importSingleEditableRow(rowId, apiService) {
      const row = this.editableData.find(r => r.id === rowId);
      if (!row) {
        console.error(`[Store] Row with id ${rowId} not found in editableData for single import.`);
        message.error(`无法找到要导入的记录 (ID: ${rowId})`);
        return;
      }

      const currentStatus = this.editedRowsStatus.get(rowId) || {};
      this.editedRowsStatus.set(rowId, { ...currentStatus, isImported: false, importStatus: 'pending', error: null });
      
      const allColumnNames = this.columnNames; 
      const selectedContactTypeColumn = this.contactTypeColumn;
      const currentLeadSource = this.leadSource;
      const currentContactRecordSource = this.contactRecordSource;

      const apiPayload = this._buildApiPayloadInternal(row, allColumnNames, selectedContactTypeColumn, currentLeadSource, currentContactRecordSource, this.contactContentColumns);
      
      console.log(`[Store] Importing single row (ID: ${rowId}), Payload:`, JSON.parse(JSON.stringify(apiPayload)));

      try {
        // const response = await apiClient.post('/leads/upload', apiPayload);
        const response = await apiService.post('/leads/upload', apiPayload); // 使用传入的apiService
        console.log(`[Store] Row ID ${rowId} import successful:`, response.data);
        this.editedRowsStatus.set(rowId, { ...currentStatus, isImported: true, importStatus: 'success', error: null });
      } catch (err) {
        console.error(`[Store Error Action] Error importing single editable row (ID: ${rowId}): `, err);
        console.log("[Store Error Action] Full error object (err):", JSON.parse(JSON.stringify(err)));
        console.log("[Store Error Action] err.response:", JSON.parse(JSON.stringify(err.response)));
        console.log("[Store Error Action] err.response.data:", JSON.parse(JSON.stringify(err.response?.data)));
        console.log("[Store Error Action] err.response.data.message:", err.response?.data?.message);
        console.log("[Store Error Action] err.message:", err.message);

        const errorMsg = err.response?.data?.message || err.message || '未知导入错误';
        console.log(`[Store Error Action] Determined errorMsg for row ${rowId}:`, errorMsg);
        this.editedRowsStatus.set(rowId, { ...currentStatus, isImported: true, importStatus: 'failed', error: errorMsg });
        // throw err; // 根据是否需要在组件层面进一步处理错误，决定是否重新抛出
      }
    },

    async startInteractiveBatchImport(apiService) {
      if (this.isImporting) {
        message.warn("已有导入任务在进行中。");
        return;
      }
      this.isImporting = true;
      this.currentStep = 5;

      const rowsToProcessInThisBatch = this.editableData.filter((row, index) => {
        const status = this.editedRowsStatus.get(row.id);
        return !status || !status.isImported || status.importStatus !== 'success';
      });

      // Local progress for this batch's UI, distinct from global this.importProgress.errors
      const batchProgress = {
        total: rowsToProcessInThisBatch.length,
        success: 0,
        failed: 0,
        // errors for this batch UI, if needed, but main errors go to global this.importProgress.errors
      };
      // For UI display of batch progress, we can update the main importProgress temporarily
      // Or better, introduce a new state for batch specific progress, if complex UI is needed.
      // For now, we will update the main `this.importProgress` for the UI progress bar,
      // but be mindful its `errors` array will be the global one.
      this.importProgress.total = rowsToProcessInThisBatch.length;
      this.importProgress.success = 0; 
      this.importProgress.failed = 0;  

      for (const rowToImport of rowsToProcessInThisBatch) {
          // const rowIndex = this.editableData.findIndex(r => r === rowToImport); // rowIndex in editableData
          // 确保使用 rowToImport.id 来更新 editedRowsStatus
          const rowId = rowToImport.id; 
          const status = this.editedRowsStatus.get(rowId) || {};
          let errorMessage = '导入操作未能完成';
          let isSuccess = false;
          
          // 使用新的内部方法构建 payload
          const payload = this._buildApiPayloadInternal(rowToImport, this.columnNames, this.contactTypeColumn, this.leadSource, this.contactRecordSource, this.contactContentColumns);

          if (!payload.联系方式.内容) {
            errorMessage = '联系方式内容缺失或无效';
            this.importProgress.failed++; 
            this.editedRowsStatus.set(rowId, { ...status, isEdited: rowToImport._isEdited, isImported: true, importStatus: 'failed', error: errorMessage });
            try {
              this.importProgress.errors.push({ rowData: deepClone(rowToImport), error: errorMessage });
            } catch (e) {
              this.importProgress.errors.push({ rowData: { _originalRowIndex: rowToImport._originalRowIndex, _contactColumnName: rowToImport._contactColumnName, _contactContent: payload.联系方式.内容 }, error: "联系方式内容缺失或无效 (原始行数据序列化失败)" });
            }
            continue; 
          }
          
          try {
            const 响应数据 = await apiService.post('/leads/upload', payload);
            console.log("批量导入API响应:", 响应数据);

            // 优化后的拦截器直接返回response.data，所以响应数据就是统一响应格式
            if (响应数据 && 响应数据.status === 100) {
              isSuccess = true;
            } else if (响应数据 && 响应数据.message) {
              errorMessage = 响应数据.message;
            } else if (响应数据) {
              errorMessage = `导入失败，状态码: ${响应数据.status}`;
            } else {
              errorMessage = '导入失败，后端响应无效';
            }
          } catch (err) {
            console.error("API call /leads/upload failed in startInteractiveBatchImport:", err);
            if (err.response && err.response.data && err.response.data.message) {
              errorMessage = err.response.data.message;
            } else if (err.message) {
              errorMessage = err.message;
            } else {
              errorMessage = 'API 请求异常，无详细信息';
            }
          }

          if (isSuccess) {
            this.importProgress.success++; 
            this.editedRowsStatus.set(rowId, { ...status, isEdited: rowToImport._isEdited, isImported: true, importStatus: 'success', error: null });
          } else {
            this.importProgress.failed++; 
            this.editedRowsStatus.set(rowId, { ...status, isEdited: rowToImport._isEdited, isImported: true, importStatus: 'failed', error: errorMessage });
            this.importProgress.errors.push({ rowData: deepClone(rowToImport), error: errorMessage });
          }
      }
      this.importProgress.isComplete = (this.importProgress.success + this.importProgress.failed) === this.importProgress.total;
      this.isImporting = false;
      this.currentStep = 6; 
    },

    resetStore() {
      this.file = null;
      this.rawFileName = '';
      this.parsedData = [];
      this.columnNames = [];
      this.contactContentColumns = [];
      this.contactTypeColumn = null; // 确保设为null
      this.leadSource = '';
      this.contactRecordSource = '批量导入';
      this.importProgress = { total: 0, success: 0, failed: 0, errors: [], isComplete: false };
      this.isParsing = false;
      this.isImporting = false;
      this.currentStep = 0;
      this.initialRowCountFromFile = 0; // 添加重置
      // 重置新增的状态
      this.editableData = [];
      this.editedRowsStatus.clear();
    },

    exportFailedLogs() {
      if (this.importProgress.errors.length === 0) {
        message.info('没有失败记录可供下载。');
        return;
      }
      try {
        const dataToExport = this.importProgress.errors.map(err => ({
          ...err.rowData,
          错误原因: err.error,
        }));
        
        const worksheet = XLSX.utils.json_to_sheet(dataToExport);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "失败记录");
        XLSX.writeFile(workbook, "导入失败日志.csv");
      } catch (error) {
        console.error("Error exporting failed logs:", error);
        message.error('下载失败记录时出错，请检查控制台获取更多信息。');
      }
    },

    // New Action: Re-import a lead that previously failed
    async reimportFailedLead(leadData, apiService) {
      if (!apiService) {
        console.error("reimportFailedLead: API service is not available.");
        throw new Error("API服务不可用");
      }
      if (!leadData || leadData.id === undefined) {
        console.error("reimportFailedLead: Invalid leadData.", leadData);
        throw new Error("无效的线索数据");
      }

      // 使用新的内部方法构建 payload
      const apiPayload = this._buildApiPayloadInternal(leadData, this.columnNames, this.contactTypeColumn, this.leadSource, this.contactRecordSource, this.contactContentColumns);
      
      // 检查联系方式内容是否有效
      if (!apiPayload.联系方式.内容) {
        const errorMessage = "联系方式内容缺失或无效";
        this.editedRowsStatus.set(leadData.id, { ...this.editedRowsStatus.get(leadData.id) || {}, status: 'failed', error: errorMessage });
        // Update error in importProgress.errors if it was already there
        const failedEntryInErrors = this.importProgress.errors.find(entry => entry.rowData.id === leadData.id);
        if (failedEntryInErrors) {
            failedEntryInErrors.error = `(重试失败) ${errorMessage}`;
        }
        throw new Error(errorMessage);
      }

      this.editedRowsStatus.set(leadData.id, { ...this.editedRowsStatus.get(leadData.id) || {}, status: 'pending', error: null });

      try {
        const 响应数据 = await apiService.post('/leads/upload', apiPayload);
        console.log("重新导入线索API响应:", 响应数据);

        // 优化后的拦截器直接返回response.data，所以响应数据就是统一响应格式
        if (响应数据 && 响应数据.status === 100) {
          this.editedRowsStatus.set(leadData.id, { status: 'success', error: null });
          this.importProgress.errors = this.importProgress.errors.filter(entry => entry.rowData.id !== leadData.id);
          this.importProgress.failed = Math.max(0, this.importProgress.failed - 1);
          this.importProgress.success++;
          
          // Update status in editableData if the re-imported item is there
          const editableEntryIndex = this.editableData.findIndex(item => item.id === leadData.id);
          if (editableEntryIndex !== -1) {
            // Ensure we are updating the correct entry
            this.editableData[editableEntryIndex] = {
              ...this.editableData[editableEntryIndex],
              _isImported: true,
              _importStatus: 'success',
              _error: null
            };
            // To ensure reactivity if not deeply watched, could also do:
            // this.editableData.splice(editableEntryIndex, 1, { ...this.editableData[editableEntryIndex], _isImported: true, _importStatus: 'success', _error: null });
          }
          return { success: true, message: response.data.message || '导入成功' };
        } else {
          const errorMessage = response && response.data ? (response.data.message || JSON.stringify(response.data)) : "未知错误"; 
          this.editedRowsStatus.set(leadData.id, { status: 'failed', error: errorMessage });
          const failedEntryInErrors = this.importProgress.errors.find(entry => entry.rowData.id === leadData.id);
          if (failedEntryInErrors) {
            failedEntryInErrors.error = `(重试失败) ${errorMessage}`;
          }
          console.error("导入失败，API响应:", response);
          throw new Error(errorMessage);
        }
      } catch (error) {
        const errorMessage = error.message || "重新导入时发生网络或服务器错误";
        this.editedRowsStatus.set(leadData.id, { status: 'failed', error: errorMessage });
        const failedEntryInErrors = this.importProgress.errors.find(entry => entry.rowData.id === leadData.id);
        if (failedEntryInErrors) {
          failedEntryInErrors.error = `(重试失败) ${errorMessage}`;
        }
        console.error("Error during reimportFailedLead:", error, "Payload sent:", apiPayload, "请求路径:", '/leads/upload');
        throw error; 
      }
    },

    // 实现calculateStatistics方法
    calculateStatistics() {
      // 计算交互式模式下的各项统计数据
      let successCount = 0;
      let failedCount = 0;
      let processedCount = 0;
      
      this.editedRowsStatus.forEach(status => {
        if (status.isImported) {
          processedCount++;
          if (status.importStatus === 'success') {
            successCount++;
          } else if (status.importStatus === 'failed') {
            failedCount++;
          }
        }
      });
      
      // 这里不直接设置getter的值，而是更新store中可能影响getter的状态
      // 例如可以更新某个专用于统计的状态属性
      // 如果没有这样的属性，这个方法可以作为辅助方法，不直接修改状态
      
      // 如果遇到性能问题，可以考虑在此方法中缓存计算结果
      return {
        processedCount,
        successCount,
        failedCount,
        totalCount: this.editableData.length
      };
    },

    // 内部辅助方法，用于实际处理联系方式列的提取和组合
    _processContactColumns(data) {
      if (!this.contactContentColumns || this.contactContentColumns.length === 0) {
        console.warn("[_processContactColumns] No contact content columns selected, returning data as is.");
        return data.map(row => ({ ...row, _contactContent: '', _contactColumnName: '' }));
      }

      console.log("[_processContactColumns] Processing contact columns:", this.contactContentColumns);
      
      return data.map(row => {
        const contactContentParts = [];
        this.contactContentColumns.forEach(columnName => {
          let cellValue = row[columnName] || "";

          // ++ 新增预处理逻辑 ++
          if (typeof cellValue === 'string' || typeof cellValue === 'number') {
            const originalCellValueForLog = cellValue;
            console.log(`[Preprocessing] Original content from column "${columnName}" for row ${row._originalRowIndex}: "${originalCellValueForLog}"`);
            cellValue = String(cellValue).replace(/或/g, ' '); // 将汉字 "或" 替换为空格
            cellValue = String(cellValue).replace(/vx号[:：]/gi, ' '); // 将 "vx号:" 或 "vx号：" 替换为空格 (不区分大小写)
            cellValue = String(cellValue).replace(/\+vx/gi, ' '); // 将 "+vx" 替换为空格 (不区分大小写)
            cellValue = String(cellValue).replace(/vx[:：]/gi, ' '); // 将 "vx:" 或 "vx：" 替换为空格 (不区分大小写)
            
            // V 后面跟数字（如 V1, V123）以及可能的 emoji variation selector (如 V1️) 将被移除
            // 这条规则优先处理这种特定组合，先于下面的通用裸露 v/vx/wx 规则
            cellValue = String(cellValue).replace(/V\d+(\uFE0F)?/gi, ''); // 移除 V 后跟一个或多个数字及可选的 variation selector

            cellValue = String(cellValue).replace(/(?<![-_\da-zA-Z])(vx|wx|v)(?![_\-\da-zA-Z])/gi, ' '); // 将前后非特定字符的vx,wx,v替换为空格
            cellValue = String(cellValue).replace(/[\u4e00-\u9fa5]/g, ' '); // 替换中文字符为空格
            cellValue = String(cellValue).replace(/wechat[:：]/gi, ' '); // 将 "wechat:" 或 "wechat：" 替换为空格 (不区分大小写)
            cellValue = String(cellValue).replace(/v[:：]/gi, ' '); // 将 "v:" 或 "v：" 替换为空格 (不区分大小写)
            cellValue = String(cellValue).replace(/[:：]/g, ' '); // 将中英文冒号替换为空格
            cellValue = String(cellValue).replace(/[；;]/g, ' '); // 将中英文分号替换为空格
            cellValue = String(cellValue).replace(/[,，‘’]/g, ' '); // 移除中英文逗号
            cellValue = String(cellValue).replace(/[（）()]/g, ' '); // 移除中英文圆括号
            cellValue = String(cellValue).replace(/[\\]/g, ' '); // 
            cellValue = String(cellValue).replace(/[+]/g, ' '); // 
            cellValue = String(cellValue).replace(/\+/g, ''); // 移除加号
            cellValue = String(cellValue).replace(/[!！]/g, ''); // 移除感叹号
            cellValue = String(cellValue).replace(/[?？]/g, ' '); // 将中英文问号替换为空格
            cellValue = String(cellValue).replace(/[【】]/g, ' '); // 移除中文中括号
            cellValue = String(cellValue).replace(/[~～]/g, ''); // 移除中英文波浪号
            cellValue = String(cellValue).replace(/。/g, '');   // 移除中文句号
            // ++ 新增移除 Emoji 的逻辑 ++
            cellValue = String(cellValue).replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{2B50}\u{1F004}\u{1F0CF}\u{1F170}-\u{1F251}\u{20E3}\u{FE0F}]/gu, '');
            // -- 结束新增移除 Emoji 的逻辑 --
            cellValue = String(cellValue).replace(/\s+/g, ' ').trim(); // 多个空白变一个，然后trim
            console.log(`[Preprocessing] Cleaned content for row ${row._originalRowIndex} from column "${columnName}": "${cellValue}"`);
          }
          // ++ 预处理逻辑结束 ++

          if (cellValue) { // 只有当清理后非空时才添加
            contactContentParts.push(cellValue);
          }
        });

        const newContactContent = contactContentParts.join('\n');
        const newContactColumnName = this.contactContentColumns.join(', ');

        return {
          ...row,
          _contactContent: newContactContent,
          _contactColumnName: newContactColumnName,
        };
      });
    },

    // 处理联系方式内容列选择变化
    handleContactColumnsChange(newColumns) {
      console.log("联系方式列变化:", newColumns);
      
      this.contactContentColumns = newColumns;
      
      if (this.parsedData && this.parsedData.length > 0) {
        const baseData = this._createBaseData();
        
        let processedData = this._processContactColumns(baseData);
        processedData = this._splitRecordsByDelimiters(processedData);
        processedData = this._predictTypesForRecords(processedData);
        
        this.parsedData = processedData;
        console.log(`联系方式列更新，重新完整处理后生成了${processedData.length}条记录`);

        if (this.currentStep >= 2) {
            message.warn("联系方式列已更改，交互式编辑内容和导入状态已重置。");
            this.proceedToModeSelection(); 
        }
      }
    },

    // 创建基础数据（不包含联系方式拆分）
    _createBaseData() {
      // 如果还没有原始数据，返回空数组
      if (!this.initialRowCountFromFile || this.initialRowCountFromFile === 0) {
        return [];
      }
      
      // 从parsedData中收集所有唯一的原始行索引
      const uniqueRowIndices = new Set();
      this.parsedData.forEach(item => {
        if (item._originalRowIndex !== undefined) {
          uniqueRowIndices.add(item._originalRowIndex);
        }
      });
      
      // 为每个唯一的原始行创建一条基础记录
      const baseData = [];
      uniqueRowIndices.forEach(rowIndex => {
        // 找到这个行索引的第一条记录，作为基础数据
        const baseRecord = this.parsedData.find(item => item._originalRowIndex === rowIndex);
        
        if (baseRecord) {
          // 创建一个不包含联系方式特定字段的副本
          const newRecord = { ...baseRecord };
          delete newRecord._contactContent;
          delete newRecord._contactColumnName;
          
          baseData.push(newRecord);
        }
      });
      
      console.log(`从${this.parsedData.length}条记录中提取了${baseData.length}条基础数据`);
      return baseData;
    },
  },
}); 