/**
 * 服务层统一导出
 * 使用清晰的中文命名，消除重复导出
 *
 * 优化说明：
 * 1. 统一使用中文命名，提高代码可读性
 * 2. 移除冗余的兼容性导出，简化代码结构
 * 3. 按功能分组组织服务
 * 4. 工具方法已迁移到 utils/langchainUtils.js
 */

// 导入各个服务模块
import activationCodeService from './activationCodeService';
import adminLangchainService from './adminLangchainService';
import * as apiTestingService from './apiTestingService';
import documentService from './documentService';
import knowledgeBaseService from './knowledgeBaseService';
import langchainService from './langchainService';
import { modelProviderService } from './modelProviderService';
import notificationService from './notificationService';
import superAdminService from './superAdminService';
import userLangchainService from './userLangchainService';

// 统一的中文命名导出
export const 管理员LangChain服务 = adminLangchainService;
export const 用户LangChain服务 = userLangchainService;
export const LangChain基础服务 = langchainService;
export const API测试服务 = apiTestingService;
export const 超级管理员服务 = superAdminService;
export const 通知服务 = notificationService;
export const 模型供应商服务 = modelProviderService;
export const 文档服务 = documentService;
export const 知识库服务 = knowledgeBaseService;
export const 激活码服务 = activationCodeService;

// 兼容性导出（保持现有组件正常工作）
export const adminLangChainService = adminLangchainService;
export const userLangChainService = userLangchainService;
export const langChainService = langchainService;
export { activationCodeService, apiTestingService, documentService, knowledgeBaseService, modelProviderService, notificationService, superAdminService };

// 默认导出主要服务
export default {
  superAdminService,
  apiTestingService,
  notificationService,
  adminLangchainService,
  langchainService,
  userLangchainService,
  knowledgeBaseService,
  modelProviderService,
  documentService,
  activationCodeService
}