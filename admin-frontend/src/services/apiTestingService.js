// admin-frontend/src/services/apiTestingService.js
import { createTestApiClient } from './apiClient';
import { useApiTestingModule } from '../store/apiTestingModule';

/**
 * 接口测试服务
 * 提供统一的API测试功能，包括请求发送、响应处理和错误管理
 * 优化版本：使用统一的HTTP客户端工厂，消除重复代码
 */

/**
 * 发送API测试请求的核心方法
 * 统一处理各种HTTP方法和响应格式
 * 
 * @param {Object} apiDetails - 接口详情 {path, method, ...}
 * @param {Object} params - 请求参数
 * @param {Object} requestConfig - 请求配置 {headers, ...}
 * @param {string} authToken - 认证令牌
 * @param {string} baseUrl - API基础URL
 * @returns {Promise<Object>} 标准化的响应对象
 */
export const sendRequest = async (apiDetails, params, requestConfig, authToken, baseUrl = 'http://127.0.0.1:8000') => {
  // 创建专用的测试客户端
  const testClient = createTestApiClient(baseUrl);
  
  try {
    // 准备请求配置
    const config = {
      ...requestConfig,
      headers: {
        'Content-Type': 'application/json',
        ...requestConfig.headers
      }
    };

    // 添加认证头
    if (authToken) {
      config.headers['Authorization'] = `Bearer ${authToken}`;
    }

    // 构建完整路径，处理路径参数替换
    let fullPath = apiDetails.path;
    const method = apiDetails.method.toLowerCase();

    // 分离路径参数和其他参数
    const pathParams = {};
    const otherParams = {};

    Object.entries(params).forEach(([key, value]) => {
      // 检查这个参数是否是路径参数（URL中包含 {key} 模式）
      if (fullPath.includes(`{${key}}`)) {
        pathParams[key] = value;
      } else {
        otherParams[key] = value;
      }
    });

    // 替换路径参数到URL中
    Object.entries(pathParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        fullPath = fullPath.replace(`{${key}}`, encodeURIComponent(value));
      }
    });

    // 检查是否还有未替换的路径参数
    const unreplacedParams = fullPath.match(/\{[^}]+\}/g);
    if (unreplacedParams) {
      throw new Error(`路径参数未提供: ${unreplacedParams.join(', ')}`);
    }

    let response;

    // 根据HTTP方法发送请求（使用处理后的otherParams，不包含路径参数）
    switch (method) {
      case 'get':
        // GET请求：参数作为查询字符串
        response = await testClient.get(fullPath, {
          ...config,
          params: Object.keys(otherParams).length > 0 ? otherParams : undefined
        });
        break;

      case 'post':
        response = await testClient.post(fullPath, otherParams, config);
        break;

      case 'put':
        response = await testClient.put(fullPath, otherParams, config);
        break;

      case 'patch':
        response = await testClient.patch(fullPath, otherParams, config);
        break;

      case 'delete':
        response = await testClient.delete(fullPath, {
          ...config,
          data: Object.keys(otherParams).length > 0 ? otherParams : undefined
        });
        break;

      default:
        throw new Error(`不支持的HTTP方法: ${apiDetails.method}`);
    }

    // 返回标准化的成功响应
    return {
      success: true,
      status: response.status,
      headers: response.headers,
      data: response.data,
      metadata: response.metadata
    };

  } catch (error) {
    // 返回标准化的错误响应
    return {
      success: false,
      error: true,
      status: error.response?.status || 'NETWORK_ERROR',
      headers: error.response?.headers || {},
      data: error.response?.data || { error: error.message },
      message: error.message,
      metadata: error.metadata || {}
    };
  }
};

/**
 * @description 获取接口定义列表 (如果采用方案A+：后端提供接口定义)
 * @returns {Promise<object>} - 包含接口分类和接口端点列表的Promise
 */
export const getApiDefinitionsFromServer = async () => {
  // 具体的API端点需要根据实际情况确定
  // return apiClient.get('/api-definitions');
  console.warn('getApiDefinitionsFromServer: 示例实现，请替换为真实API调用');
  return Promise.reject(new Error('尚未实现从服务器获取接口定义'));
};

/**
 * @description 获取OpenAPI规范的方法
 * 优化版本：改进错误处理和重试机制
 */
export const fetchOpenApiSchema = async (schemaUrl, maxRetries = 3, timeoutMs = 15000) => {
  console.log(`[OpenAPI] 获取接口定义: ${schemaUrl}`);
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[OpenAPI] 第 ${attempt} 次尝试...`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
      
      const response = await fetch(schemaUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'include',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const schema = await response.json();
      console.log(`[OpenAPI] 成功获取接口定义，包含 ${Object.keys(schema.paths || {}).length} 个路径`);
      return schema;
      
    } catch (error) {
      console.error(`[OpenAPI] 第 ${attempt} 次尝试失败:`, error.message);
      
      if (attempt === maxRetries) {
        throw new Error(`获取OpenAPI规范失败 (已重试${maxRetries}次): ${error.message}`);
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};

/**
 * @description 接口测试平台自身的登录（如果需要独立认证体系）
 * @param {object} credentials - 包含手机号和密码的对象
 * @returns {Promise<object>} - 包含token或用户信息的Promise
 */
export const loginToTestPlatform = async (credentials) => {
  // 具体的API端点需要根据实际情况确定
  // return apiClient.post('/auth/test-platform-login', credentials);
  console.warn('loginToTestPlatform: 示例实现，请替换为真实API调用');
  // 模拟登录成功
  if (credentials.username === 'admin' && credentials.password === 'password') {
    return Promise.resolve({ token: 'fake-test-platform-token', user: { name: '测试管理员' } });
  }
  return Promise.reject(new Error('测试平台登录失败'));
};

// 兼容store调用，导出executeRequest别名
export { sendRequest as executeRequest };

// --- OpenAPI Schema Loading and Parsing ---

/**
 * @description Helper function to resolve $ref pointers in OpenAPI schema.
 * @param {string} refPath - The $ref path (e.g., "#/components/schemas/MyModel").
 * @param {object} openApiDoc - The full OpenAPI document.
 * @returns {object|null} - The resolved schema object or null if not found.
 */
const resolveSchemaRef = (refPath, openApiDoc) => {
  if (!refPath || !refPath.startsWith('#/')) {
    console.warn('Invalid or unsupported $ref path:', refPath);
    return null;
  }
  const parts = refPath.substring(2).split('/'); // Remove '#/' and split
  let current = openApiDoc;
  for (const part of parts) {
    if (current && current.hasOwnProperty(part)) {
      current = current[part];
    } else {
      console.warn(`Could not resolve $ref part "${part}" in path "${refPath}"`);
      return null;
    }
  }
  return current;
};

/**
 * @description 智能判断接口是否需要认证
 * @param {object} operation - OpenAPI操作对象
 * @param {string} path - 接口路径
 * @param {object} openApiDoc - 完整的OpenAPI文档
 * @returns {boolean} 是否需要认证
 */
const determineAuthRequirement = (operation, path, openApiDoc) => {
  // 1. 检查操作级别的安全定义
  if (operation.security !== undefined) {
    if (operation.security === null || operation.security.length === 0) {
      // 明确设置为不需要认证
      return false;
    }
    // 检查是否有非空的安全配置
    return operation.security.some(securityItem => 
      securityItem && Object.keys(securityItem).length > 0
    );
  }
  
  // 2. 检查全局安全定义
  if (openApiDoc.security && openApiDoc.security.length > 0) {
    const hasGlobalSecurity = openApiDoc.security.some(securityItem => 
      securityItem && Object.keys(securityItem).length > 0
    );
    if (hasGlobalSecurity) {
      // 有全局安全配置，检查路径是否在免认证列表中
      const noAuthPaths = [
        '/server-status',
        '/health', 
        '/850483315-docs',
        '/850483315-redoc',
        '/850483315-openapi.json',
        '/admin/login', // admin登录
        '/login', // 普通登录
        '/register', // 注册
        '/验证码', // 验证码相关
        '/public' // 公开接口
      ];
      
      // 检查路径是否匹配免认证模式
      const isNoAuthPath = noAuthPaths.some(noAuthPath => 
        path.startsWith(noAuthPath) || path.includes('login') || path.includes('register')
      );
      
      return !isNoAuthPath;
    }
  }
  
  // 3. 基于路径模式判断
  const authRequiredPatterns = [
    '/admin/', // 管理员接口
    '/api/users/', // 用户相关接口
    '/api/teams/', // 团队相关接口
    '/api/wechat/', // 微信相关接口
    '/创建', '/更新', '/删除', // 中文动作词
    '/用户', '/团队', '/微信' // 中文资源词
  ];
  
  const noAuthPatterns = [
    '/login', '/register', '/auth/login', '/auth/register',
    '/验证码', '/captcha', '/health', '/status',
    '/docs', '/redoc', '/openapi'
  ];
  
  // 检查是否明确不需要认证
  const isNoAuthPath = noAuthPatterns.some(pattern => 
    path.toLowerCase().includes(pattern.toLowerCase())
  );
  if (isNoAuthPath) return false;
  
  // 检查是否明确需要认证
  const isAuthRequiredPath = authRequiredPatterns.some(pattern => 
    path.includes(pattern)
  );
  if (isAuthRequiredPath) return true;
  
  // 4. 默认策略：POST/PUT/PATCH/DELETE操作通常需要认证
  const method = operation.parameters ? 'unknown' : 'POST'; // 如果有参数定义通常是需要认证的
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    return true;
  }
  
  // 5. 其他情况默认为不需要认证
  return false;
};

/**
 * @description Parses the OpenAPI document into a format usable by the API testing UI.
 * 优化版本：改进分类处理、接口解析、认证识别和中文字段支持
 * @param {object} openApiDoc - The OpenAPI document (JSON object).
 * @returns {{categories: Array<object>, endpoints: Array<object>}}
 */
export const parseOpenApiSchema = (openApiDoc) => {
  if (!openApiDoc || !openApiDoc.paths) {
    console.error('[OpenAPI解析] 无效的OpenAPI文档');
    return { categories: [], endpoints: [] };
  }

  console.log(`[OpenAPI解析] 开始解析文档，包含 ${Object.keys(openApiDoc.paths).length} 个路径`);

  const categories = [];
  const seenCategories = new Set();

  // 1. 解析Tags为分类，优化分类处理
  if (openApiDoc.tags && Array.isArray(openApiDoc.tags)) {
    openApiDoc.tags.forEach(tag => {
      if (tag.name && !seenCategories.has(tag.name)) {
        const categoryName = tag.name;
        categories.push({
          key: categoryName,
          name: categoryName,
          icon: mapTagToIcon(categoryName),
          description: tag.description || '',
          count: 0 // 将在后面统计接口数量
        });
        seenCategories.add(categoryName);
      }
    });
  }
  
  // 如果没有标签定义，创建默认分类
  if (categories.length === 0) {
    categories.push({ 
      key: 'default', 
      name: '默认分类', 
      icon: 'folder', 
      description: '未分类的接口',
      count: 0
    });
    seenCategories.add('default');
  }

  const endpoints = [];
  let endpointIdCounter = 0;

  // 2. 解析Paths为接口端点，增强解析逻辑
  for (const path in openApiDoc.paths) {
    const pathItem = openApiDoc.paths[path];
    
    for (const method in pathItem) {
      // 只处理HTTP方法，跳过OpenAPI的特殊字段如$ref、summary等
      if (!['get', 'post', 'put', 'patch', 'delete', 'head', 'options'].includes(method.toLowerCase())) {
        continue;
      }
      
      const operation = pathItem[method];
      if (!operation) continue;

      endpointIdCounter++;
      
      // 确定接口分类
      const endpointCategory = operation.tags && operation.tags.length > 0 
                               ? operation.tags[0] 
                               : 'default';
      
      // 如果分类不存在，动态添加
      if (!seenCategories.has(endpointCategory)) {
        categories.push({
          key: endpointCategory,
          name: endpointCategory,
          icon: mapTagToIcon(endpointCategory),
          description: `动态添加的分类: ${endpointCategory}`,
          count: 0
        });
        seenCategories.add(endpointCategory);
      }

      // 创建接口端点对象
      const endpoint = {
        id: `api_${endpointIdCounter}`,
        name: operation.summary || operation.operationId || `${method.toUpperCase()} ${path}`,
        path: path,
        method: method.toUpperCase(),
        category: endpointCategory,
        description: operation.description || operation.summary || '',
        operationId: operation.operationId || null,
        
        // 优化：智能判断是否需要认证
        requiresAuth: determineAuthRequirement(operation, path, openApiDoc),
        
        // 参数和响应字段
        params: [],
        headers: [],
        responses: {},
        
        // 扩展信息
        deprecated: operation.deprecated || false,
        tags: operation.tags || [],
        externalDocs: operation.externalDocs || null
      };

      // Helper to extract parameters from a schema object (used for requestBody)
      const extractParamsFromSchema = (schemaObject, requiredList = []) => {
        const params = [];
        if (schemaObject && schemaObject.properties) {
          Object.entries(schemaObject.properties).forEach(([propName, propSchema]) => {
            
            let mainType = propSchema.type;
            let mainFormat = propSchema.format;

            if (propSchema.anyOf && Array.isArray(propSchema.anyOf)) {
              const nonNullEntry = propSchema.anyOf.find(s => s.type !== 'null');
              if (nonNullEntry) {
                mainType = nonNullEntry.type;
                // Use format from nonNullEntry if it exists, otherwise fallback to propSchema's own format (if any)
                mainFormat = nonNullEntry.format || propSchema.format;
              }
              // If all in anyOf are null or nonNullEntry has no type, mainType might remain undefined from propSchema.type
              // mapOpenApiTypeToFormType should handle undefined mainType by defaulting to 'string'
            }
            
            const param = {
              key: propName,
              name: propSchema.title || propName,
              type: mapOpenApiTypeToFormType(mainType, mainFormat),
              required: requiredList.includes(propName),
              defaultValue: propSchema.default, // Keep original default
              description: propSchema.description,
              placeholder: propSchema.description || propSchema.title || propName,
              isBodyParam: true,
              // options for select/radio if defined by enum
              options: propSchema.enum ? propSchema.enum.map(val => ({ label: String(val), value: val })) : undefined,
              rawSchema: propSchema,
            };
            params.push(param);
          });
        }
        return params;
      };

      // Request body (for POST, PUT, PATCH)
      if (operation.requestBody && operation.requestBody.content && operation.requestBody.content['application/json']) {
        const requestBodySchema = operation.requestBody.content['application/json'].schema;
        if (requestBodySchema.$ref) {
          // Handle $ref for requestBody schema
          const refSchemaName = requestBodySchema.$ref.split('/').pop();
          const actualSchema = openApiDoc.components.schemas[refSchemaName];
          if (actualSchema) {
            endpoint.params = endpoint.params.concat(extractParamsFromSchema(actualSchema, actualSchema.required || []));
            endpoint.requestBodySchemaName = refSchemaName; // Store the schema name for later use if needed
          }
        } else if (requestBodySchema.type === 'object' && requestBodySchema.properties) {
          // Handle inline requestBody schema
          endpoint.params = endpoint.params.concat(extractParamsFromSchema(requestBodySchema, requestBodySchema.required || []));
        }
        // Ensure body params are marked, though extractParamsFromSchema does this
         endpoint.params.forEach(p => { if (p.isBodyParam === undefined && !p.isQueryParam && !p.isPathParam && !p.isHeaderParam) p.isBodyParam = true; });
      }
      
      // 解析操作参数（query, path, header等）
      if (operation.parameters && Array.isArray(operation.parameters)) {
        for (const param of operation.parameters) {
          const resolvedParam = param.$ref 
            ? resolveSchemaRef(param.$ref, openApiDoc) 
            : param;
          
          if (!resolvedParam) continue;
          
          // 处理路径参数
          if (resolvedParam.in === 'path') {
            const paramSchema = resolvedParam.schema || {};
            endpoint.params.push({
              name: resolvedParam.name,
              key: resolvedParam.name,
              type: mapOpenApiTypeToFormType(paramSchema.type, paramSchema.format),
              oasType: paramSchema.type,
              oasFormat: paramSchema.format,
              required: true, // 路径参数总是必需的
              defaultValue: paramSchema.default,
              placeholder: resolvedParam.description || resolvedParam.name,
              description: resolvedParam.description || '',
              isPathParam: true, // 标记为路径参数
              options: paramSchema.enum ? paramSchema.enum.map(e => ({label: e, value: e})) : undefined,
              validations: extractValidationRules(paramSchema),
              rawSchema: paramSchema,
            });
            continue;
          }
          
          // 请求头参数
          if (resolvedParam.in === 'header') {
            endpoint.headers.push({
              name: resolvedParam.name,
              description: resolvedParam.description || '',
              required: !!resolvedParam.required,
              schema: resolvedParam.schema,
              example: resolvedParam.example || '',
            });
            continue;
          }
          
          // 查询参数（最常见）
          if (resolvedParam.in === 'query') {
            const paramSchema = resolvedParam.schema || {};
            endpoint.params.push({
              name: resolvedParam.name,
              key: resolvedParam.name,
              type: mapOpenApiTypeToFormType(paramSchema.type, paramSchema.format),
              oasType: paramSchema.type,
              oasFormat: paramSchema.format,
              required: !!resolvedParam.required,
              defaultValue: paramSchema.default,
              placeholder: resolvedParam.description || resolvedParam.name,
              description: resolvedParam.description || '',
              isQueryParam: true, // 标记为查询参数
              options: paramSchema.enum ? paramSchema.enum.map(e => ({label: e, value: e})) : undefined,
              validations: extractValidationRules(paramSchema),
              rawSchema: paramSchema, // 添加rawSchema字段
            });
          }
        }
      }

      endpoints.push(endpoint);
    }
  }
  
  // 3. 统计每个分类的接口数量并完善分类信息
  const categoryStats = new Map();
  endpoints.forEach(endpoint => {
    categoryStats.set(endpoint.category, (categoryStats.get(endpoint.category) || 0) + 1);
  });
  
  // 更新分类的接口数量统计
  categories.forEach(category => {
    category.count = categoryStats.get(category.key) || 0;
  });
  
  // 移除没有接口的分类
  const validCategories = categories.filter(category => category.count > 0);
  
  // 4. 生成解析统计信息
  const authRequiredCount = endpoints.filter(e => e.requiresAuth).length;
  const methodStats = {};
  endpoints.forEach(endpoint => {
    methodStats[endpoint.method] = (methodStats[endpoint.method] || 0) + 1;
  });
  
  console.log(`[OpenAPI解析] 解析完成统计:`);
  console.log(`- 总接口数: ${endpoints.length}`);
  console.log(`- 有效分类数: ${validCategories.length}`);
  console.log(`- 需要认证的接口: ${authRequiredCount}/${endpoints.length}`);
  console.log(`- 方法分布:`, methodStats);
  console.log(`- 分类分布:`, Object.fromEntries(categoryStats));
  
  return { 
    categories: validCategories, 
    endpoints,
    stats: {
      totalEndpoints: endpoints.length,
      totalCategories: validCategories.length,
      authRequiredCount,
      methodStats,
      categoryStats: Object.fromEntries(categoryStats)
    }
  };
};

/**
 * @description Maps OpenAPI data types to our custom form field types.
 * @param {string} oasType - OpenAPI type (e.g., 'string', 'integer', 'boolean', 'array', 'object').
 * @param {string} [oasFormat] - OpenAPI format (e.g., 'date', 'date-time', 'password', 'json').
 * @returns {string} Custom form type (e.g., 'string', 'number', 'boolean', 'array', 'object').
 */
const mapOpenApiTypeToFormType = (oasType, oasFormat) => {
  // 处理undefined或null的oasType参数
  if (!oasType || oasType === undefined || oasType === null) {
    // 如果有format信息，尝试根据format推断类型
    if (oasFormat) {
      if (oasFormat === 'date' || oasFormat === 'date-time') return oasFormat === 'date' ? 'date' : 'datetime';
      if (oasFormat === 'password') return 'password';
      if (oasFormat === 'email') return 'email';
      if (oasFormat === 'url') return 'url';
    }
    // 默认返回string类型，不显示警告（因为这是预期的fallback行为）
    return 'string';
  }

  if (oasFormat === 'date') return 'date';
  if (oasFormat === 'date-time') return 'datetime';
  if (oasFormat === 'password') return 'password';
  if (oasFormat === 'email') return 'email';
  if (oasFormat === 'url') return 'url';

  switch (oasType) {
    case 'string':
      return 'string';
    case 'integer':
    case 'number':
      return 'number';
    case 'boolean':
      return 'boolean';
    case 'array':
      return 'array'; // Directly return 'array'
    case 'object':
      return 'object'; // Directly return 'object'
    default:
      // 只有在oasType不是undefined/null且不是已知类型时才显示警告
      console.warn(`Unknown OpenAPI type encountered: ${oasType}, format: ${oasFormat}. Defaulting to 'string'.`);
      return 'string'; // Fallback for unknown types
  }
};

/**
 * @description Maps a tag name to a predefined icon string for the UI.
 * @param {string} tagName
 * @returns {string} Icon key (e.g., 'user', 'appstore', 'robot')
 */
const mapTagToIcon = (tagName) => {
  if (!tagName) return 'folder'; // Default icon
  const lowerTagName = tagName.toLowerCase();
  // Simple mapping, can be extended
  if (lowerTagName.includes('用户') || lowerTagName.includes('user')) return 'user';
  if (lowerTagName.includes('产品') || lowerTagName.includes('product')) return 'appstore';
  if (lowerTagName.includes('订单') || lowerTagName.includes('order')) return 'shopping-cart';
  if (lowerTagName.includes('ai') || lowerTagName.includes('智能')) return 'robot';
  if (lowerTagName.includes('系统') || lowerTagName.includes('system') || lowerTagName.includes('setting')) return 'setting';
  if (lowerTagName.includes('微信') || lowerTagName.includes('wechat')) return 'wechat';
  if (lowerTagName.includes('知识') || lowerTagName.includes('knowledge')) return 'book';
  if (lowerTagName.includes('日志') || lowerTagName.includes('log')) return 'profile'; // Example for a new one
  // Add more mappings as needed
  return 'api'; // Generic API icon or 'folder' or 'ellipsis'
}

/**
 * @description 递归解析嵌套的对象和数组结构
 * @param {object} schema - 当前属性的schema定义
 * @param {object} openApiDoc - 完整的OpenAPI文档（用于解析$ref）
 * @returns {object|null} 单个项目定义对象(for array items) 或 null if not an array or resolution fails
 */
const parseNestedSchema = (schema, openApiDoc) => {
  if (!schema) return null;

  const resolvedItemSchema = schema.$ref ? resolveSchemaRef(schema.$ref, openApiDoc) : schema;
  if (!resolvedItemSchema) return null;

  // Deep clone the original schema part for this item/field
  const originalSubSchema = JSON.parse(JSON.stringify(resolvedItemSchema)); 

  const itemType = mapOpenApiTypeToFormType(resolvedItemSchema.type, resolvedItemSchema.format);
  const itemDefinition = {
    type: itemType,
    oasType: resolvedItemSchema.type,
    oasFormat: resolvedItemSchema.format,
    description: resolvedItemSchema.description || '',
    defaultValue: resolvedItemSchema.default,
    options: resolvedItemSchema.enum ? resolvedItemSchema.enum.map(e => ({label: e, value: e})) : undefined,
    validations: extractValidationRules(resolvedItemSchema),
    oasOriginalSchema: originalSubSchema, // Store the original schema portion for this item/field
    // 'children' or 'itemSchema' will be populated below if applicable
  };

  if (resolvedItemSchema.type === 'object' && resolvedItemSchema.properties) {
    itemDefinition.children = parseObjectProperties(resolvedItemSchema, openApiDoc);
  } else if (resolvedItemSchema.type === 'array' && resolvedItemSchema.items) {
    // Handle nested arrays: recursively call parseNestedSchema for the items of this array
    const nestedItemSchema = parseNestedSchema(resolvedItemSchema.items, openApiDoc);
    if (nestedItemSchema) {
      itemDefinition.itemSchema = nestedItemSchema;
    }
  }
  // If it's an object without properties, or array without .items, 
  // oasOriginalSchema will be used by JSON editor for schema validation.
  return itemDefinition;
};

/**
 * @description 解析对象属性为表单字段数组
 * @param {object} schema - 包含properties的对象schema
 * @param {object} openApiDoc - 完整的OpenAPI文档
 * @returns {Array} 表单字段数组
 */
const parseObjectProperties = (schema, openApiDoc) => {
  const properties = [];
  const requiredFields = new Set(schema.required || []);

  for (const propName in schema.properties) {
    const propSchema = schema.properties[propName];
    const resolvedPropSchema = propSchema.$ref ? resolveSchemaRef(propSchema.$ref, openApiDoc) : propSchema;

    if (resolvedPropSchema) {
      // Use parseNestedSchema to get the full definition, including potential children or itemSchema AND oasOriginalSchema
      const propDefinition = parseNestedSchema(resolvedPropSchema, openApiDoc);
      if (propDefinition) {
        properties.push({
          key: propName,
          name: resolvedPropSchema.title || propName,
          type: propDefinition.type,
          oasType: propDefinition.oasType,
          oasFormat: propDefinition.oasFormat,
          required: requiredFields.has(propName),
          defaultValue: propDefinition.defaultValue, // Already in propDefinition
          placeholder: propDefinition.description || resolvedPropSchema.title || propName,
          description: propDefinition.description, // Already in propDefinition
          options: propDefinition.options, // Already in propDefinition
          children: propDefinition.children, // From propDefinition
          itemSchema: propDefinition.itemSchema, // From propDefinition
          validations: propDefinition.validations, // From propDefinition
          oasOriginalSchema: propDefinition.oasOriginalSchema // Crucially, this comes from parseNestedSchema
        });
      }
    }
  }
  return properties;
};

// ADDED: Helper function to extract validation rules
const extractValidationRules = (schema) => {
  if (!schema) return {};
  const rules = {};
  if (schema.pattern !== undefined) rules.pattern = schema.pattern;
  if (schema.minLength !== undefined) rules.minLength = schema.minLength;
  if (schema.maxLength !== undefined) rules.maxLength = schema.maxLength;
  if (schema.minimum !== undefined) rules.minimum = schema.minimum;
  if (schema.maximum !== undefined) rules.maximum = schema.maximum;
  if (schema.exclusiveMinimum !== undefined) rules.exclusiveMinimum = schema.exclusiveMinimum;
  if (schema.exclusiveMaximum !== undefined) rules.exclusiveMaximum = schema.exclusiveMaximum;
  if (schema.multipleOf !== undefined) rules.multipleOf = schema.multipleOf;
  if (schema.enum !== undefined) rules.enum = schema.enum; // Keep raw enum for validation logic
  // For arrays:
  if (schema.minItems !== undefined) rules.minItems = schema.minItems;
  if (schema.maxItems !== undefined) rules.maxItems = schema.maxItems;
  if (schema.uniqueItems !== undefined) rules.uniqueItems = schema.uniqueItems;
  return rules;
};