/**
 * 激活码管理服务
 * 提供激活码相关的API接口调用
 * 
 * 功能包括：
 * - 激活码类型管理
 * - 激活码生成
 * - 激活码列表查询
 * - 激活码详情查看
 * - 激活码删除管理
 * - 激活码统计分析
 */

import apiClient from './apiClient.js'

class ActivationCodeService {
  constructor() {
    this.baseURL = '/admin/activation-codes'
  }

  /**
   * 通用请求处理器
   * @param {Function} requestFn - 请求函数
   * @param {string} operation - 操作描述
   * @returns {Promise} 直接返回后端数据
   */
  async _handleRequest(requestFn, operation) {
    try {
      console.log(`🚀 激活码管理API调用: ${operation}`)
      const response = await requestFn()
      console.log(`✅ ${operation} 响应:`, response)
      return response
    } catch (error) {
      console.error(`❌ ${operation} 失败:`, error)

      // 标准化错误响应
      const errorResponse = {
        status: error.response?.status || 500,
        message: error.userFriendlyMessage || error.message || `${operation}失败`,
        data: null
      }

      if (error.response?.status === 401) {
        errorResponse.needLogin = true
      }

      return errorResponse
    }
  }

  // ==================== 激活码类型管理 ====================

  /**
   * 获取激活码类型列表
   * @returns {Promise<Object>} 激活码类型列表
   */
  async 获取激活码类型列表() {
    return this._handleRequest(
      () => apiClient.get(`${this.baseURL}/types`),
      '获取激活码类型列表'
    )
  }

  /**
   * 获取激活码类型列表（带统计）
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.page - 页码
   * @param {number} 查询参数.size - 每页数量
   * @param {string} 查询参数.搜索关键词 - 搜索关键词
   * @returns {Promise<Object>} 激活码类型列表
   */
  async 获取激活码类型列表带统计(查询参数 = {}) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/types/list`, 查询参数),
      '获取激活码类型列表（带统计）'
    )
  }

  /**
   * 创建激活码类型
   * @param {Object} 类型数据 - 类型数据
   * @param {string} 类型数据.名称 - 类型名称
   * @param {string} 类型数据.描述 - 类型描述
   * @param {number} 类型数据.价格 - 类型价格
   * @param {number} 类型数据.会员表id - 关联的会员表ID
   * @param {number} 类型数据.会员天数 - 会员天数
   * @returns {Promise<Object>} 创建结果
   */
  async 创建激活码类型(类型数据) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/types`, 类型数据),
      '创建激活码类型'
    )
  }

  /**
   * 获取激活码类型详情
   * @param {number} 类型id - 激活码类型id
   * @returns {Promise<Object>} 激活码类型详情
   */
  async 获取激活码类型详情(类型id) {
    return this._handleRequest(
      () => apiClient.get(`${this.baseURL}/types/${类型id}`),
      '获取激活码类型详情'
    )
  }

  /**
   * 更新激活码类型
   * @param {number} 类型id - 激活码类型id
   * @param {Object} 类型数据 - 类型数据
   * @param {string} 类型数据.名称 - 类型名称
   * @param {string} 类型数据.描述 - 类型描述
   * @param {number} 类型数据.价格 - 类型价格
   * @param {number} 类型数据.会员表id - 关联的会员表ID
   * @param {number} 类型数据.会员天数 - 会员天数
   * @returns {Promise<Object>} 更新结果
   */
  async 更新激活码类型(类型id, 类型数据) {
    return this._handleRequest(
      () => apiClient.put(`${this.baseURL}/types/${类型id}`, 类型数据),
      '更新激活码类型'
    )
  }

  /**
   * 删除激活码类型
   * @param {number} 类型id - 激活码类型id
   * @returns {Promise<Object>} 删除结果
   */
  async 删除激活码类型(类型id) {
    return this._handleRequest(
      () => apiClient.delete(`${this.baseURL}/types/${类型id}`),
      '删除激活码类型'
    )
  }

  // ==================== 激活码生成 ====================

  /**
   * 生成激活码
   * @param {Object} 生成参数 - 生成参数
   * @param {number} 生成参数.数量 - 生成数量
   * @param {number} 生成参数.类型id - 激活码类型id
   * @param {number} 生成参数.是否为一次性激活 - 激活码类型：1=一次性激活码，0=永久激活码
   * @param {string} 生成参数.备注 - 备注信息
   * @returns {Promise<Object>} 生成结果
   */
  async 生成激活码(生成参数) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/generate`, 生成参数),
      '生成激活码'
    )
  }

  // ==================== 激活码列表管理 ====================

  /**
   * 获取激活码列表
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.page - 页码
   * @param {number} 查询参数.size - 每页数量
   * @param {string} 查询参数.搜索关键词 - 搜索关键词
   * @param {number} 查询参数.类型id - 激活码类型id
   * @param {string} 查询参数.状态 - 激活码状态
   * @param {string} 查询参数.开始日期 - 开始日期
   * @param {string} 查询参数.结束日期 - 结束日期
   * @returns {Promise<Object>} 激活码列表
   */
  async 获取激活码列表(查询参数 = {}) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/list`, 查询参数),
      '获取激活码列表'
    )
  }

  // ==================== 激活码详情管理 ====================

  /**
   * 获取激活码详情
   * @param {number} 激活码id - 激活码ID
   * @returns {Promise<Object>} 激活码详情
   */
  async 获取激活码详情(激活码id) {
    return this._handleRequest(
      () => apiClient.get(`${this.baseURL}/${激活码id}`),
      '获取激活码详情'
    )
  }

  // ==================== 激活码删除管理 ====================

  /**
   * 删除激活码
   * @param {number} 激活码id - 激活码ID
   * @returns {Promise<Object>} 删除结果
   */
  async 删除激活码(激活码id) {
    return this._handleRequest(
      () => apiClient.delete(`${this.baseURL}/${激活码id}`),
      '删除激活码'
    )
  }

  /**
   * 批量删除激活码
   * @param {Array<number>} 激活码id列表 - 激活码ID列表
   * @returns {Promise<Object>} 批量删除结果
   */
  async 批量删除激活码(激活码id列表) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/batch-delete`, { 激活码id列表 }),
      '批量删除激活码'
    )
  }

  // ==================== 激活码统计分析 ====================

  /**
   * 获取激活码统计
   * @returns {Promise<Object>} 激活码统计数据
   */
  async 获取激活码统计() {
    return this._handleRequest(
      () => apiClient.get(`${this.baseURL}/statistics/overview`),
      '获取激活码统计'
    )
  }

  // ==================== 便捷方法 ====================

  /**
   * 导出激活码数据
   * @param {Object} 查询参数 - 查询参数
   * @returns {Promise<Blob>} 导出文件
   */
  async 导出激活码数据(查询参数 = {}) {
    try {
      console.log('🚀 激活码管理API调用: 导出激活码数据')

      // 构建导出参数
      const 导出参数 = {
        page: 1,
        size: 10000,
        搜索关键词: 查询参数.搜索关键词 || '',
        类型id: 查询参数.类型id || null,
        状态: 查询参数.状态 || null,
        开始日期: 查询参数.开始日期 || null,
        结束日期: 查询参数.结束日期 || null
      }

      // 调用后端导出接口
      const response = await apiClient.post(`${this.baseURL}/export`, 导出参数, {
        responseType: 'blob'
      })

      console.log('✅ 导出激活码数据 响应:', response)
      return response

    } catch (error) {
      console.error('❌ 导出激活码数据失败:', error)
      throw error
    }
  }

  /**
   * 将激活码数据转换为CSV格式
   * @private
   * @param {Array} 激活码列表 - 激活码列表
   * @returns {string} CSV格式字符串
   */
  _convertToCSV(激活码列表) {
    const headers = [
      'ID',
      '激活码',
      '类型名称',
      '类型描述',
      '会员天数',
      '状态',
      '用户昵称',
      '备注',
      '创建时间',
      '使用时间'
    ]

    const csvRows = [headers.join(',')]

    激活码列表.forEach(item => {
      const row = [
        item.id || '',
        item.激活码 || '',
        item.类型名称 || '',
        item.类型描述 || '',
        item.会员天数 || '',
        item.状态 || '',
        item.用户昵称 || '',
        item.备注 || '',
        item.创建时间 || '',
        item.使用时间 || ''
      ]
      csvRows.push(row.map(field => `"${field}"`).join(','))
    })

    return csvRows.join('\n')
  }
}

// 创建服务实例
const activationCodeService = new ActivationCodeService()

export default activationCodeService 