/**
 * 性能优化工具函数
 * 提供缓存、防抖、节流等性能优化功能
 */

// 防抖函数
export const debounce = (func, wait, immediate = false) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 内存缓存类
export class MemoryCache {
  constructor(defaultTTL = 5 * 60 * 1000) { // 默认5分钟过期
    this.cache = new Map()
    this.defaultTTL = defaultTTL
  }

  set(key, value, ttl = this.defaultTTL) {
    const expireTime = Date.now() + ttl
    this.cache.set(key, {
      value,
      expireTime
    })
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() > item.expireTime) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  has(key) {
    return this.get(key) !== null
  }

  delete(key) {
    return this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  // 清理过期项
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key)
      }
    }
  }

  // 获取缓存统计信息
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 请求去重器
export class RequestDeduplicator {
  constructor() {
    this.pendingRequests = new Map()
  }

  async execute(key, requestFn) {
    // 如果已有相同请求在进行中，返回该请求的Promise
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)
    }

    // 创建新请求
    const promise = requestFn().finally(() => {
      // 请求完成后清理
      this.pendingRequests.delete(key)
    })

    this.pendingRequests.set(key, promise)
    return promise
  }

  clear() {
    this.pendingRequests.clear()
  }
}

// 图表resize处理器
export class ChartResizeHandler {
  constructor() {
    this.charts = new Set()
    this.resizeObserver = null
    this.init()
  }

  init() {
    // 窗口resize监听
    const handleResize = throttle(() => {
      this.resizeAllCharts()
    }, 100)

    window.addEventListener('resize', handleResize)

    // ResizeObserver监听容器大小变化
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(handleResize)
    }
  }

  addChart(chartInstance, container) {
    this.charts.add(chartInstance)
    
    if (this.resizeObserver && container) {
      this.resizeObserver.observe(container)
    }
  }

  removeChart(chartInstance) {
    this.charts.delete(chartInstance)
  }

  resizeAllCharts() {
    this.charts.forEach(chart => {
      if (chart && typeof chart.resize === 'function') {
        chart.resize()
      }
    })
  }

  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    this.charts.clear()
  }
}

// 虚拟滚动辅助函数
export const createVirtualScrollConfig = (itemHeight = 50, bufferSize = 5) => {
  return {
    itemHeight,
    bufferSize,
    getVisibleRange(scrollTop, containerHeight, totalItems) {
      const startIndex = Math.floor(scrollTop / itemHeight)
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / itemHeight) + bufferSize,
        totalItems
      )
      return {
        startIndex: Math.max(0, startIndex - bufferSize),
        endIndex
      }
    }
  }
}

// 数据格式化缓存
const formatCache = new MemoryCache(10 * 60 * 1000) // 10分钟缓存

export const cachedFormat = (key, formatter, value) => {
  const cacheKey = `${key}_${value}`
  let result = formatCache.get(cacheKey)
  
  if (result === null) {
    result = formatter(value)
    formatCache.set(cacheKey, result)
  }
  
  return result
}

// 批量操作辅助函数
export const batchProcess = async (items, processor, batchSize = 10, delay = 0) => {
  const results = []
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(item => processor(item))
    )
    results.push(...batchResults)
    
    // 批次间延迟，避免过度占用资源
    if (delay > 0 && i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  return results
}

// 页面可见性监听器
export class VisibilityManager {
  constructor() {
    this.callbacks = new Set()
    this.init()
  }

  init() {
    document.addEventListener('visibilitychange', () => {
      const isVisible = !document.hidden
      this.callbacks.forEach(callback => {
        callback(isVisible)
      })
    })
  }

  onVisibilityChange(callback) {
    this.callbacks.add(callback)
    return () => this.callbacks.delete(callback)
  }

  isVisible() {
    return !document.hidden
  }
}

// 单例实例
export const globalCache = new MemoryCache()
export const requestDeduplicator = new RequestDeduplicator()
export const chartResizeHandler = new ChartResizeHandler()
export const visibilityManager = new VisibilityManager()

// 清理函数
export const cleanup = () => {
  globalCache.clear()
  requestDeduplicator.clear()
  chartResizeHandler.destroy()
}
