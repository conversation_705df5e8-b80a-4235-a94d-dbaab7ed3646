/**
 * 通用工具函数
 */

/**
 * 生成唯一标识符UUID
 * @returns {String} UUID字符串
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 深拷贝对象
 * @param {Object} obj 要复制的对象
 * @returns {Object} 复制的新对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理Date对象
  if (obj instanceof Date) {
    return new Date(obj);
  }
  
  // 处理RegExp对象
  if (obj instanceof RegExp) {
    return new RegExp(obj);
  }
  
  // 处理Map对象
  if (obj instanceof Map) {
    const result = new Map();
    obj.forEach((value, key) => {
      result.set(key, deepClone(value));
    });
    return result;
  }
  
  // 处理Set对象
  if (obj instanceof Set) {
    const result = new Set();
    obj.forEach(value => {
      result.add(deepClone(value));
    });
    return result;
  }
  
  // 处理数组和普通对象
  try {
    const result = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = deepClone(obj[key]);
      }
    }
    return result;
  } catch (e) {
    console.error('deepClone error:', e);
    return Array.isArray(obj) ? [] : {};
  }
}

/**
 * 安全地访问对象的深层属性
 * 避免出现 Cannot read property 'x' of undefined 错误
 * @param {Object} obj 要访问的对象
 * @param {String} path 属性路径，如 'user.address.city'
 * @param {*} defaultValue 默认值，当路径不存在时返回
 * @returns {*} 属性值或默认值
 */
export function getDeepValue(obj, path, defaultValue = undefined) {
  if (!obj || !path) return defaultValue;
  
  const keys = path.split('.');
  let value = obj;
  
  for (const key of keys) {
    if (value === undefined || value === null) {
      return defaultValue;
    }
    value = value[key];
  }
  
  return value === undefined ? defaultValue : value;
}

/**
 * 格式化数字为千分位，如 1000 -> 1,000
 * @param {Number} num 要格式化的数字
 * @returns {String} 格式化后的字符串
 */
export function formatNumber(num) {
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 防抖函数
 * @param {Function} fn 要执行的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @returns {Function} 防抖后的函数
 */
export function debounce(fn, delay) {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn 要执行的函数
 * @param {Number} interval 时间间隔，单位毫秒
 * @returns {Function} 节流后的函数
 */
export function throttle(fn, interval) {
  let lastTime = 0;
  return function(...args) {
    const now = Date.now();
    if (now - lastTime >= interval) {
      lastTime = now;
      fn.apply(this, args);
    }
  };
}

/**
 * 格式化日期时间
 * @param {Date|String|Number} date 日期对象、时间戳或日期字符串
 * @param {String} format 格式化模式，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {String} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = date instanceof Date ? date : new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const pad = (num) => String(num).padStart(2, '0');
  
  const replacements = {
    'YYYY': d.getFullYear(),
    'MM': pad(d.getMonth() + 1),
    'DD': pad(d.getDate()),
    'HH': pad(d.getHours()),
    'mm': pad(d.getMinutes()),
    'ss': pad(d.getSeconds()),
  };
  
  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => replacements[match]);
}

/**
 * 检测联系方式类型（电话、邮箱等）
 * @param {String} contact 联系方式字符串
 * @returns {String} 联系方式类型：'phone', 'email', 'unknown'
 */
export function detectContactType(contact) {
  if (!contact || typeof contact !== 'string') return 'unknown';
  
  // 电话号码正则（中国大陆手机号）
  const phoneRegex = /^1[3-9]\d{9}$/;
  
  // 邮箱正则
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  if (phoneRegex.test(contact.replace(/\s+/g, ''))) {
    return 'phone';
  }
  
  if (emailRegex.test(contact)) {
    return 'email';
  }
  
  return 'unknown';
}

/**
 * 从文本中提取联系方式
 * @param {String} text 包含联系方式的文本
 * @returns {Object} 提取的联系方式 {phones: [], emails: []}
 */
export function extractContactInfo(text) {
  if (!text || typeof text !== 'string') {
    return { phones: [], emails: [] };
  }
  
  const result = {
    phones: [],
    emails: []
  };
  
  // 提取手机号
  const phoneMatches = text.match(/1[3-9]\d{9}/g);
  if (phoneMatches) {
    result.phones = [...new Set(phoneMatches)]; // 去重
  }
  
  // 提取邮箱
  const emailMatches = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
  if (emailMatches) {
    result.emails = [...new Set(emailMatches)]; // 去重
  }
  
  return result;
} 