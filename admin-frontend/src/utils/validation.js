/**
 * 表单验证规则集合
 * 提供常用的验证规则，支持链式调用和自定义错误消息
 */

/**
 * 基础验证规则工厂
 */
export const rulesets = {
  /**
   * 必填验证
   * @param {string} fieldName - 字段名称
   * @param {string} customMessage - 自定义错误消息
   */
  required(fieldName = '此字段', customMessage = null) {
    return {
      required: true,
      message: customMessage || `${fieldName}不能为空`,
      trigger: ['blur', 'change']
    };
  },

  /**
   * 最小长度验证
   * @param {number} min - 最小长度
   * @param {string} customMessage - 自定义错误消息
   */
  minLength(min, customMessage = null) {
    return {
      min,
      message: customMessage || `长度不能少于${min}个字符`,
      trigger: ['blur', 'change']
    };
  },

  /**
   * 最大长度验证
   * @param {number} max - 最大长度
   * @param {string} customMessage - 自定义错误消息
   */
  maxLength(max, customMessage = null) {
    return {
      max,
      message: customMessage || `长度不能超过${max}个字符`,
      trigger: ['blur', 'change']
    };
  },

  /**
   * 长度范围验证
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   * @param {string} customMessage - 自定义错误消息
   */
  lengthRange(min, max, customMessage = null) {
    return {
      min,
      max,
      message: customMessage || `长度必须在${min}-${max}个字符之间`,
      trigger: ['blur', 'change']
    };
  },

  /**
   * 邮箱验证
   * @param {string} customMessage - 自定义错误消息
   */
  email(customMessage = null) {
    return {
      type: 'email',
      message: customMessage || '请输入有效的邮箱地址',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 手机号验证
   * @param {string} customMessage - 自定义错误消息
   */
  phone(customMessage = null) {
    return {
      pattern: /^1[3-9]\d{9}$/,
      message: customMessage || '请输入有效的手机号码',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 数字验证
   * @param {string} customMessage - 自定义错误消息
   */
  number(customMessage = null) {
    return {
      type: 'number',
      message: customMessage || '请输入有效的数字',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 整数验证
   * @param {string} customMessage - 自定义错误消息
   */
  integer(customMessage = null) {
    return {
      type: 'integer',
      message: customMessage || '请输入有效的整数',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 数值范围验证
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @param {string} customMessage - 自定义错误消息
   */
  numberRange(min, max, customMessage = null) {
    return {
      type: 'number',
      min,
      max,
      message: customMessage || `数值必须在${min}-${max}之间`,
      trigger: ['blur', 'change']
    };
  },

  /**
   * URL验证
   * @param {string} customMessage - 自定义错误消息
   */
  url(customMessage = null) {
    return {
      type: 'url',
      message: customMessage || '请输入有效的URL地址',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 正则表达式验证
   * @param {RegExp} pattern - 正则表达式
   * @param {string} message - 错误消息
   */
  pattern(pattern, message) {
    return {
      pattern,
      message,
      trigger: ['blur', 'change']
    };
  },

  /**
   * 自定义验证函数
   * @param {Function} validator - 验证函数
   * @param {string} trigger - 触发时机
   */
  custom(validator, trigger = ['blur', 'change']) {
    return {
      validator,
      trigger
    };
  },

  /**
   * 密码强度验证
   * @param {string} customMessage - 自定义错误消息
   */
  password(customMessage = null) {
    return {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
      message: customMessage || '密码必须包含大小写字母和数字，长度至少8位',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 确认密码验证
   * @param {Function} getPassword - 获取原密码的函数
   * @param {string} customMessage - 自定义错误消息
   */
  confirmPassword(getPassword, customMessage = null) {
    return {
      validator: (rule, value) => {
        if (!value) {
          return Promise.reject(new Error('请确认密码'));
        }
        if (value !== getPassword()) {
          return Promise.reject(new Error(customMessage || '两次输入的密码不一致'));
        }
        return Promise.resolve();
      },
      trigger: ['blur', 'change']
    };
  },

  /**
   * 身份证号验证
   * @param {string} customMessage - 自定义错误消息
   */
  idCard(customMessage = null) {
    return {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: customMessage || '请输入有效的身份证号码',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 中文姓名验证
   * @param {string} customMessage - 自定义错误消息
   */
  chineseName(customMessage = null) {
    return {
      pattern: /^[\u4e00-\u9fa5]{2,4}$/,
      message: customMessage || '请输入有效的中文姓名（2-4个字符）',
      trigger: ['blur', 'change']
    };
  },

  /**
   * 银行卡号验证
   * @param {string} customMessage - 自定义错误消息
   */
  bankCard(customMessage = null) {
    return {
      pattern: /^\d{16,19}$/,
      message: customMessage || '请输入有效的银行卡号（16-19位数字）',
      trigger: ['blur', 'change']
    };
  }
};

/**
 * 常用验证规则组合
 */
export const commonRules = {
  // 用户名规则
  username: [
    rulesets.required('用户名'),
    rulesets.lengthRange(3, 20),
    rulesets.pattern(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '用户名只能包含字母、数字、下划线和中文')
  ],

  // 邮箱规则
  email: [
    rulesets.required('邮箱'),
    rulesets.email()
  ],

  // 手机号规则
  phone: [
    rulesets.required('手机号'),
    rulesets.phone()
  ],

  // 密码规则
  password: [
    rulesets.required('密码'),
    rulesets.minLength(6)
  ],

  // 强密码规则
  strongPassword: [
    rulesets.required('密码'),
    rulesets.password()
  ],

  // 真实姓名规则
  realName: [
    rulesets.required('姓名'),
    rulesets.chineseName()
  ],

  // 身份证规则
  idCard: [
    rulesets.required('身份证号'),
    rulesets.idCard()
  ]
};

/**
 * 动态验证规则生成器
 */
export const createValidationRules = {
  /**
   * 创建确认密码规则
   * @param {Function} getOriginalPassword - 获取原密码的函数
   */
  confirmPassword(getOriginalPassword) {
    return [
      rulesets.required('确认密码'),
      rulesets.confirmPassword(getOriginalPassword)
    ];
  },

  /**
   * 创建数值范围规则
   * @param {string} fieldName - 字段名称
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   */
  numberRange(fieldName, min, max) {
    return [
      rulesets.required(fieldName),
      rulesets.numberRange(min, max)
    ];
  },

  /**
   * 创建长度范围规则
   * @param {string} fieldName - 字段名称
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   */
  lengthRange(fieldName, min, max) {
    return [
      rulesets.required(fieldName),
      rulesets.lengthRange(min, max)
    ];
  }
};

export default {
  rulesets,
  commonRules,
  createValidationRules
};