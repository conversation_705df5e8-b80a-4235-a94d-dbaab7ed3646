# Apache配置文件 - 解决Vue SPA路由404问题
# 将所有不存在的文件请求重定向到index.html，让前端路由接管

<IfModule mod_rewrite.c>
  RewriteEngine On

  # 处理SPA路由
  # 如果请求的是文件或目录，直接返回
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d

  # 排除后端API请求 - 这些路径应该直接访问后端服务器
  RewriteCond %{REQUEST_URI} !^/api/
  RewriteCond %{REQUEST_URI} !^/customer/
  RewriteCond %{REQUEST_URI} !^/admin/
  RewriteCond %{REQUEST_URI} !^/wechat/
  RewriteCond %{REQUEST_URI} !^/ai/
  RewriteCond %{REQUEST_URI} !^/order/
  RewriteCond %{REQUEST_URI} !^/knowledge/
  RewriteCond %{REQUEST_URI} !^/product/
  RewriteCond %{REQUEST_URI} !^/store/
  RewriteCond %{REQUEST_URI} !^/samples/
  RewriteCond %{REQUEST_URI} !^/dashboard/
  RewriteCond %{REQUEST_URI} !^/workspace/
  RewriteCond %{REQUEST_URI} !^/reports/
  RewriteCond %{REQUEST_URI} !^/items/
  RewriteCond %{REQUEST_URI} !^/system/
  RewriteCond %{REQUEST_URI} !^/user/
  RewriteCond %{REQUEST_URI} !^/login$
  RewriteCond %{REQUEST_URI} !^/register$

  # 将所有其他请求重定向到index.html
  RewriteRule . /index.html [L]
</IfModule>

# 设置缓存策略
<IfModule mod_expires.c>
  ExpiresActive on
  
  # HTML文件不缓存，确保更新及时
  ExpiresByType text/html "access plus 0 seconds"
  
  # CSS和JS文件缓存1年（因为有hash）
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  
  # 图片文件缓存1个月
  ExpiresByType image/png "access plus 1 month"
  ExpiresByType image/jpg "access plus 1 month"
  ExpiresByType image/jpeg "access plus 1 month"
  ExpiresByType image/gif "access plus 1 month"
  ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# 启用Gzip压缩
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 安全头设置
<IfModule mod_headers.c>
  # 防止点击劫持
  Header always append X-Frame-Options SAMEORIGIN
  
  # 防止MIME类型嗅探
  Header always append X-Content-Type-Options nosniff
  
  # XSS保护
  Header always append X-XSS-Protection "1; mode=block"
</IfModule>
