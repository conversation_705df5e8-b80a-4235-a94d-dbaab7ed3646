"""
用户通知相关的Pydantic模型
包含通知管理相关的请求和响应模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


# =============== 通知内容相关模型 ===============

class 通知内容项(BaseModel):
    """通知内容项模型"""
    类型: str = Field(..., description="内容类型：文本、链接等")
    内容: str = Field(..., description="内容文本")


# =============== 请求模型 ===============

class 获取通知列表请求(BaseModel):
    """获取通知列表请求模型"""
    页码: int = Field(1, ge=1, description="页码，从1开始")
    每页数量: int = Field(20, ge=1, le=100, description="每页显示数量，1-100")
    通知类型: Optional[str] = Field(None, description="通知类型筛选：system_update、business")
    是否已读: Optional[bool] = Field(None, description="已读状态筛选：true已读、false未读、null全部")
    排序字段: str = Field("创建时间", description="排序字段")
    排序顺序: str = Field("DESC", description="排序顺序：ASC、DESC")


class 标记已读请求(BaseModel):
    """标记已读请求模型"""
    通知id: str = Field(..., description="要标记的通知id，支持整数ID和announcement_XX格式")


class 批量标记已读请求(BaseModel):
    """批量标记已读请求模型"""
    通知id列表: List[str] = Field(..., description="要标记的通知id列表，支持整数ID和announcement_XX格式")


class 获取通知详情请求(BaseModel):
    """获取通知详情请求模型"""
    通知id: str = Field(..., description="通知id，支持整数ID和announcement_XX格式")


class 创建业务通知请求(BaseModel):
    """创建业务通知请求模型（内部使用）"""
    用户id: int = Field(..., description="接收通知的用户id")
    业务类型: str = Field(..., description="业务类型")
    业务关联id: str = Field(..., description="业务关联id")
    模板变量: Dict[str, Any] = Field(..., description="模板变量")


# =============== 响应模型 ===============

class 通知基本信息模型(BaseModel):
    """通知基本信息模型"""
    id: int = Field(..., description="通知id")
    用户id: int = Field(..., description="用户id")
    通知类型: str = Field(..., description="通知类型")
    标题: str = Field(..., description="通知标题")
    内容: List[通知内容项] = Field(..., description="通知内容")
    重要性: int = Field(..., description="重要性级别：1普通，2重要，3紧急")
    是否已读: bool = Field(..., description="是否已读")
    阅读时间: Optional[datetime] = Field(None, description="阅读时间")
    来源通告ID: Optional[int] = Field(None, description="来源通告ID")
    业务关联id: Optional[str] = Field(None, description="业务关联id")
    业务类型: Optional[str] = Field(None, description="业务类型")
    创建时间: datetime = Field(..., description="创建时间")
    更新时间: datetime = Field(..., description="更新时间")


class 通知列表响应模型(BaseModel):
    """通知列表响应模型"""
    列表: List[通知基本信息模型] = Field(..., description="通知列表")
    总数: int = Field(..., description="总记录数")
    总页数: int = Field(..., description="总页数")
    当前页: int = Field(..., description="当前页码")


class 未读数量响应模型(BaseModel):
    """未读通知数量响应模型"""
    未读数量: int = Field(..., description="未读通知数量")


class 标记已读响应模型(BaseModel):
    """标记已读响应模型"""
    成功: bool = Field(..., description="操作是否成功")
    消息: str = Field(..., description="操作结果消息")
    影响行数: Optional[int] = Field(None, description="影响的行数")


# =============== 管理员相关模型 ===============

class 通知模板模型(BaseModel):
    """通知模板模型"""
    id: int = Field(..., description="模板ID")
    模板名称: str = Field(..., description="模板名称")
    模板类型: str = Field(..., description="模板类型")
    业务类型: str = Field(..., description="业务类型")
    标题模板: str = Field(..., description="标题模板")
    内容模板: str = Field(..., description="内容模板")
    重要性: int = Field(..., description="默认重要性级别")
    是否启用: bool = Field(..., description="是否启用")
    创建时间: datetime = Field(..., description="创建时间")
    更新时间: datetime = Field(..., description="更新时间")


class 创建系统通知请求(BaseModel):
    """创建系统通知请求模型（管理员使用）"""
    通告ID: int = Field(..., description="基于的通告ID")


class 通知统计模型(BaseModel):
    """通知统计模型"""
    总通知数: int = Field(..., description="总通知数")
    未读通知数: int = Field(..., description="未读通知数")
    系统通知数: int = Field(..., description="系统通知数")
    业务通知数: int = Field(..., description="业务通知数")
    今日新增: int = Field(..., description="今日新增通知数")
    本周新增: int = Field(..., description="本周新增通知数")


# =============== 内部服务模型 ===============

class 业务通知触发模型(BaseModel):
    """业务通知触发模型（内部服务间调用）"""
    用户id: int = Field(..., description="用户id")
    业务类型: str = Field(..., description="业务类型")
    业务关联id: str = Field(..., description="业务关联id")
    模板变量: Dict[str, Any] = Field(default_factory=dict, description="模板变量")


class 系统通知触发模型(BaseModel):
    """系统通知触发模型（内部服务间调用）"""
    通告ID: int = Field(..., description="通告ID")
    目标用户: Optional[List[int]] = Field(None, description="目标用户id列表，为空则发送给所有用户")


# =============== 前端展示模型 ===============

class 通知卡片模型(BaseModel):
    """前端通知卡片展示模型"""
    id: int = Field(..., description="通知id")
    标题: str = Field(..., description="通知标题")
    摘要: str = Field(..., description="内容摘要")
    通知类型: str = Field(..., description="通知类型")
    重要性: int = Field(..., description="重要性级别")
    是否已读: bool = Field(..., description="是否已读")
    创建时间: datetime = Field(..., description="创建时间")
    时间显示: str = Field(..., description="友好的时间显示")


class 通知中心数据模型(BaseModel):
    """通知中心页面数据模型"""
    未读数量: int = Field(..., description="未读通知数量")
    通知列表: List[通知卡片模型] = Field(..., description="通知列表")
    分页信息: Dict[str, int] = Field(..., description="分页信息")
    筛选选项: Dict[str, Any] = Field(..., description="筛选选项")


# =============== 工具函数 ===============

def 格式化通知时间(创建时间: datetime) -> str:
    """
    格式化通知时间为友好显示
    
    参数:
        创建时间: 通知创建时间
    
    返回:
        友好的时间显示字符串
    """
    现在 = datetime.now()
    时间差 = 现在 - 创建时间
    
    if 时间差.days > 0:
        if 时间差.days == 1:
            return "1天前"
        elif 时间差.days < 7:
            return f"{时间差.days}天前"
        elif 时间差.days < 30:
            周数 = 时间差.days // 7
            return f"{周数}周前"
        else:
            return 创建时间.strftime("%Y-%m-%d")
    else:
        小时数 = 时间差.seconds // 3600
        if 小时数 > 0:
            return f"{小时数}小时前"
        else:
            分钟数 = 时间差.seconds // 60
            if 分钟数 > 0:
                return f"{分钟数}分钟前"
            else:
                return "刚刚"


def 生成通知摘要(内容: List[通知内容项], 最大长度: int = 100) -> str:
    """
    生成通知内容摘要
    
    参数:
        内容: 通知内容列表
        最大长度: 摘要最大长度
    
    返回:
        内容摘要字符串
    """
    if not 内容:
        return ""
    
    # 提取所有文本内容
    文本内容 = []
    for 项 in 内容:
        if 项.get('类型') == '文本' and 项.get('内容'):
            文本内容.append(项['内容'])
    
    if not 文本内容:
        return ""
    
    完整文本 = " ".join(文本内容)
    
    if len(完整文本) <= 最大长度:
        return 完整文本
    else:
        return 完整文本[:最大长度-3] + "..."
