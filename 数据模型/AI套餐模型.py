from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field


# 导入统一日志系统


class 获取AI模型市场请求主体(BaseModel):
    """获取AI模型市场请求模型"""
    page: int = 1
    page_size: int = 10

class 获取AI模型详情请求(BaseModel):
    """获取AI模型详情请求模型"""
    id: int

class 创建AI智能体请求(BaseModel):
    """创建AI智能体请求模型"""
    模型id: int
    员工名称: Optional[str] = None
    员工性格: Optional[str] = None
    店铺名称: Optional[str] = None

class 获取我的AI配置请求主体(BaseModel):
    """获取我的AI配置请求模型"""
    page: int = 1
    page_size: int = 10

class 获取我的AI配置详情请求主体(BaseModel):
    """获取我的AI配置详情请求模型"""
    用户AI信息id: int

class 更新AI配置请求(BaseModel):
    """更新AI配置请求模型"""
    用户AI信息id: int
    员工名称: Optional[str] = None
    员工性格: Optional[str] = None
    店铺名称: Optional[str] = None
    模型id: Optional[int] = None  # 新增：支持模型切换（数据库模型id）

class 知识项(BaseModel):
    文件名称: str
    内容BASE64: str
    文件类型: str = "txt"

class 训练知识库请求(BaseModel):
    用户AI信息id: int
    知识列表: List[知识项]

class AI模型基础信息模型(BaseModel):
    """AI模型基础信息模型"""
    模型id: Optional[int] = Field(None, description="AI模型id")
    模型名称: Optional[str] = Field(None, description="AI模型名称")

class AI模型详情模型(BaseModel):
    """AI模型详情信息模型"""
    模型id: Optional[int] = Field(None, description="AI模型id")
    模型名称: Optional[str] = Field(None, description="AI模型名称")
    模型描述: Optional[str] = Field(None, description="AI模型描述")

class AI模型列表请求模型(BaseModel):
    """AI模型列表查询请求模型"""
    页码: int = Field(1, description="页码，从1开始")
    每页数量: int = Field(10, description="每页记录数")

class 用户AI信息模型(BaseModel):
    """用户AI信息关系模型"""
    关联id: Optional[int] = Field(None, description="关联id")
    用户id: Optional[int] = Field(None, description="用户id")
    模型id: Optional[str] = Field(None, description="AI模型id")

class AI模型创建请求模型(BaseModel):
    """AI模型创建请求模型"""
    模型名称: str = Field(..., description="AI模型名称")
    模型描述: Optional[str] = Field(None, description="AI模型描述")
    模型配置: Optional[Dict[str, Any]] = Field(None, description="AI模型配置信息")