"""
超级管理功能相关的Pydantic模型
包含通告管理、用户管理、系统管理等相关的请求和响应模型
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


# =============== 通告管理模型 ===============

class 通告内容项(BaseModel):
    类型: str
    内容: str
    操作类型: Optional[int] = 0

class 通告创建请求(BaseModel):
    类型: str = Field(..., min_length=1, max_length=50)
    标题: str = Field(..., min_length=1, max_length=200)
    内容: Optional[List[通告内容项]] = None
    已发布: Optional[bool] = False
    重要性: Optional[int] = 1
    开始时间: Optional[datetime] = None
    结束时间: Optional[datetime] = None
    排序: Optional[int] = None
    操作标识: Optional[int] = Field(0, description="操作标识，默认为0")

class 通告更新请求(BaseModel):
    类型: Optional[str] = Field(None, min_length=1, max_length=50)
    标题: Optional[str] = Field(None, min_length=1, max_length=200)
    内容: Optional[List[通告内容项]] = None
    已发布: Optional[bool] = None
    重要性: Optional[int] = None
    开始时间: Optional[datetime] = None
    结束时间: Optional[datetime] = None
    排序: Optional[int] = None
    操作标识: Optional[int] = Field(None, description="操作标识，默认为0")

class 通告列表请求(BaseModel):
    page: int = Field(1, ge=1, description="当前页码")
    size: int = Field(10, ge=1, le=200, description="每页数量")
    标题: Optional[str] = Field(None, description="按标题模糊筛选")
    类型: Optional[str] = Field(None, description="按类型精确筛选")
    状态: Optional[int] = Field(None, description="按状态筛选 (0: 草稿, 1: 已发布)")
    创建时间开始: Optional[datetime] = Field(None, description="创建时间范围开始")
    创建时间结束: Optional[datetime] = Field(None, description="创建时间范围结束")
    排序字段: Optional[str] = Field(None, description="排序字段")
    排序顺序: Optional[str] = Field(None, description="排序顺序 (ascend/descend)")

class 通告删除请求(BaseModel):
    通告id: int = Field(..., description="要删除的通告ID")


# =============== 系统监控模型 ===============

class 系统信息请求(BaseModel):
    详细级别: Optional[str] = Field("基础", description="信息详细级别：基础、详细、完整")

# =============== 接口统计模型 ===============

class 接口统计请求(BaseModel):
    时间范围: str = Field("今天", description="时间范围")
    开始日期: Optional[str] = None
    结束日期: Optional[str] = None
    page: int = 1
    size: int = 10

class 接口调用用户列表请求(BaseModel):
    接口路径: str
    时间段: str = 'all'
    开始日期: Optional[str] = None
    结束日期: Optional[str] = None


# =============== 日志文件模型 ===============

class 日志文件请求(BaseModel):
    目录: Optional[str] = Field(None, description="日志目录")

class 日志内容请求(BaseModel):
    文件名: str = Field(..., description="要读取的日志文件名")
    行数限制: int = Field(200, gt=0, le=5000, description="要读取的最后行数")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")

class 读取日志文件请求(BaseModel):
    文件名: str = Field(..., description="要读取的日志文件名")
    行数: int = Field(200, gt=0, le=5000, description="要读取的最后行数 (默认200, 最大5000)")


# =============== 用户管理模型 ===============

class 用户基本信息模型(BaseModel):
    用户名: Optional[str] = Field(None, description="用户昵称/登录名")
    邮箱: Optional[str] = Field(None, description="用户邮箱")
    手机号: Optional[str] = Field(None, description="用户手机号")
    密码: Optional[str] = Field(None, description="用户密码，仅创建时需要")

class 用户创建请求模型(用户基本信息模型):
    用户名: str = Field(..., description="用户昵称/登录名")
    密码: str = Field(..., description="用户密码")

class 用户详情请求模型(BaseModel):
    用户id: int = Field(..., description="用户id")

class 用户更新请求模型(BaseModel):
    用户id: int = Field(..., description="要更新的用户id")
    用户名: Optional[str] = Field(None, description="新的用户昵称/登录名")
    邮箱: Optional[str] = Field(None, description="新的用户邮箱")
    手机号: Optional[str] = Field(None, description="新的用户手机号")

class 用户删除请求模型(BaseModel):
    用户id: int = Field(..., description="要删除的用户id")

# {{ AURA-X: Add - 添加排序字段支持，简洁高效直接对接. Approval: 寸止(ID:1721062800). }}
class 用户列表请求模型(BaseModel):
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    状态筛选: Optional[str] = Field(None, description="状态筛选")
    时间范围: Optional[str] = Field(None, description="时间范围")
    排序字段: Optional[str] = Field(None, description="排序字段")
    排序顺序: Optional[str] = Field(None, description="排序顺序 (ascend/descend)")


# =============== 用户响应模型 ===============

class 单个用户响应数据模型(BaseModel):
    id: int
    昵称: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login_time: Optional[datetime] = None

class 用户列表响应数据模型(BaseModel):
    total: int
    items: List[单个用户响应数据模型]
    page: int
    size: int

class 用户关联店铺列表请求模型(BaseModel):
    用户id: int = Field(..., description="用户id")

class 店铺基本信息模型(BaseModel):
    id: int
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    avatar: Optional[str] = None
    created_at: Optional[datetime] = None

class 用户关联店铺列表响应数据模型(BaseModel):
    items: List[店铺基本信息模型]

class 用户最后登录时间请求模型(BaseModel):
    用户id: int = Field(..., description="用户id")

class 用户最后登录时间响应数据模型(BaseModel):
    """用户最后登录信息响应模型 - 优化版，只保留中文键名"""
    用户id: Optional[int] = Field(None, description="用户id")
    用户名: Optional[str] = Field(None, description="用户名/昵称")
    手机号: Optional[str] = Field(None, description="用户手机号")
    邮箱: Optional[str] = Field(None, description="用户邮箱")
    上次登录时间: Optional[datetime] = Field(None, description="最后一次登录时间")
    ip地址: Optional[str] = Field(None, description="最后一次登录IP地址")
    登录次数: Optional[int] = Field(None, description="总登录次数")
    首次登录时间: Optional[datetime] = Field(None, description="首次登录时间")
    最近7天登录次数: Optional[int] = Field(None, description="最近7天登录次数")
    用户状态: Optional[str] = Field(None, description="用户状态（活跃/不活跃）")
    注册时间: Optional[datetime] = Field(None, description="用户注册时间")
    最后活跃时间: Optional[datetime] = Field(None, description="最后活跃时间")
    设备信息: Optional[str] = Field(None, description="最后登录设备信息")
    地理位置: Optional[str] = Field(None, description="最后登录地理位置（如果有）")

class 用户状态更新请求模型(BaseModel):
    用户id: int = Field(..., description="用户id")
    状态: str = Field(..., description="新状态")
    原因: Optional[str] = Field(None, description="操作原因")

class 用户批量操作请求模型(BaseModel):
    用户id列表: List[int] = Field(..., description="用户id列表")
    操作类型: str = Field(..., description="操作类型")
    操作参数: Optional[dict] = Field(None, description="操作参数")


# =============== 激活码管理模型 ===============

class 生成激活码请求(BaseModel):
    数量: int = Field(..., gt=0, description="要生成的激活码数量")
    类型id: int = Field(..., description="关联的 激活码类型表 的 ID")
    备注: str = Field(..., min_length=1, max_length=50, description="激活码备注")


# =============== 管理员登录模型 ===============

class 管理员登录请求(BaseModel):
    用户名: str = Field(..., description="管理员用户名")
    密码: str = Field(..., description="管理员密码")