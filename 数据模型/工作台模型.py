"""
工作台数据模型
基于真实业务数据的工作台模型定义
"""
from datetime import date, datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


# 请求模型
class 工作台数据请求模型(BaseModel):
    """工作台数据请求模型"""
    用户id: Optional[int] = Field(default=None, description="用户id，为空则使用当前登录用户")
    时间范围: str = Field(default="本周", description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）", example="本周")
    开始日期: Optional[date] = Field(default=None, description="开始日期")
    结束日期: Optional[date] = Field(default=None, description="结束日期")
    包含模块: List[str] = Field(
        default=["personal", "team", "trends", "todos"],
        description="包含的模块列表"
    )


class 个人数据请求模型(BaseModel):
    """个人数据请求模型"""
    用户id: Optional[int] = Field(default=None, description="用户id，为空则使用当前登录用户")
    时间范围: str = Field(default="7d", description="时间范围：1d(今日)、yesterday(昨日)、7d(7天)、30d(30天)、custom(自定义)")
    开始日期: Optional[date] = Field(default=None, description="开始日期（时间范围为custom时使用）")
    结束日期: Optional[date] = Field(default=None, description="结束日期（时间范围为custom时使用）")


class 团队数据请求模型(BaseModel):
    """团队数据请求模型"""
    用户id: Optional[int] = Field(default=None, description="用户id，为空则使用当前登录用户")
    团队id: Optional[int] = Field(default=None, description="团队id，为空则获取用户所有团队")
    时间范围: str = Field(default="7d", description="时间范围：1d(今日)、yesterday(昨日)、7d(7天)、30d(30天)、custom(自定义)")
    开始日期: Optional[date] = Field(default=None, description="开始日期（时间范围为custom时使用）")
    结束日期: Optional[date] = Field(default=None, description="结束日期（时间范围为custom时使用）")


class 趋势数据请求模型(BaseModel):
    """趋势数据请求模型"""
    用户id: Optional[int] = Field(default=None, description="用户id，为空则使用当前登录用户")
    数据类型: str = Field(default="invitation", description="数据类型：invitation/cooperation/sales")
    时间范围: str = Field(default="本周", description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）")
    开始日期: Optional[date] = Field(default=None, description="开始日期（时间范围为custom时使用）")
    结束日期: Optional[date] = Field(default=None, description="结束日期（时间范围为custom时使用）")

class 多指标趋势数据请求模型(BaseModel):
    """多指标趋势数据请求模型"""
    用户id: Optional[int] = Field(default=None, description="用户id，为空则使用当前登录用户")
    业务模块: str = Field(default="wechat", description="业务模块：wechat/invitation/talent/sample")
    指标列表: List[str] = Field(default=["wechat_accounts"], description="要查询的指标列表")
    时间维度: str = Field(default="week", description="时间维度：day/week/month/quarter")
    时间范围: str = Field(default="本周", description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）")
    开始日期: Optional[date] = Field(default=None, description="开始日期（时间范围为custom时使用）")
    结束日期: Optional[date] = Field(default=None, description="结束日期（时间范围为custom时使用）")


# 响应数据模型
class 指标卡片模型(BaseModel):
    """指标卡片数据模型"""
    标题: str = Field(description="指标标题")
    数值: float = Field(description="指标数值")
    格式化数值: str = Field(description="格式化后的数值")
    趋势: Optional[str] = Field(default=None, description="趋势描述")
    趋势数值: Optional[float] = Field(default=None, description="趋势数值")
    趋势类型: Optional[str] = Field(default=None, description="趋势类型：up/down/stable")
    图标: Optional[str] = Field(default=None, description="图标名称")
    颜色: Optional[str] = Field(default=None, description="主题颜色")


class 微信运营数据模型(BaseModel):
    """微信运营数据模型"""
    微信账号数: int = Field(description="微信账号数量")
    好友总数: int = Field(description="好友总数")
    今日新增好友: int = Field(description="今日新增好友数")
    昨日新增好友: int = Field(description="昨日新增好友数")
    本周新增好友: int = Field(description="本周新增好友数")
    本月新增好友: int = Field(description="本月新增好友数")
    时间范围新增好友: int = Field(description="指定时间范围内新增好友数")
    当前新增好友: int = Field(description="当前选择时间范围的新增好友数")
    新增好友标题: str = Field(description="新增好友指标的标题")
    平均好友数: float = Field(description="平均每账号好友数")

class 微信运营核心指标模型(BaseModel):
    """微信运营核心指标模型 - 7个关键指标"""
    微信账号数量: int = Field(description="用户绑定的有效微信账号总数")
    好友总数: int = Field(description="当前用户微信账号的好友总数量")
    今日新增: int = Field(description="今日新增的好友数量")
    发送好友请求数: int = Field(description="主动发送的好友请求总数")
    入库好友数: int = Field(description="已成功添加并入库有好友入库时间的好友数量")
    沟通好友数: int = Field(description="我方最后一条消息发送时间有数据，但是对方最后一条消息发送时间无数据的好友数量")
    互动好友数: int = Field(description="我方最后一条消息发送时间与对方最后一条消息发送时间都有的好友数量")

class 微信运营指标请求模型(BaseModel):
    """微信运营指标请求模型"""
    时间范围: str = Field(default="今日", description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）")
    开始日期: Optional[date] = Field(default=None, description="自定义开始日期")
    结束日期: Optional[date] = Field(default=None, description="自定义结束日期")


class 邀约业务数据模型(BaseModel):
    """邀约业务数据模型"""
    邀约总数: int = Field(description="邀约总数")
    今日邀约数: int = Field(description="今日邀约数")
    昨日邀约数: int = Field(description="昨日邀约数")
    本周邀约数: int = Field(description="本周邀约数")
    本月邀约数: int = Field(description="本月邀约数")
    时间范围邀约数: int = Field(description="指定时间范围内邀约数")
    当前邀约数: int = Field(description="当前选择时间范围的邀约数")
    邀约标题: str = Field(description="邀约指标的标题")
    邀约成功率: float = Field(description="邀约成功率")
    意向合作数: int = Field(description="意向合作数")
    已建联数: int = Field(description="已建联数")
    合作中数: int = Field(description="合作中数")


class 达人管理数据模型(BaseModel):
    """达人管理数据模型"""
    认领达人数: int = Field(description="认领达人数量")
    有效认领数: int = Field(description="有效认领数量")
    今日新增认领: int = Field(description="今日新增认领")
    昨日新增认领: int = Field(description="昨日新增认领")
    本周新增认领: int = Field(description="本周新增认领")
    本月新增认领: int = Field(description="本月新增认领")
    时间范围新增认领: int = Field(description="指定时间范围内新增认领")
    当前新增认领: int = Field(description="当前选择时间范围的新增认领")
    认领标题: str = Field(description="认领指标的标题")
    达人活跃数: int = Field(description="活跃达人数")
    达人活跃率: float = Field(description="达人活跃率")


class 合作项目数据模型(BaseModel):
    """合作项目数据模型"""
    合作项目数: int = Field(description="合作项目总数")
    意向合作数: int = Field(description="意向合作数")
    样品已发数: int = Field(description="样品已发数")
    已开播数: int = Field(description="已开播数")
    总销售额: float = Field(description="总销售额")
    今日新增项目: int = Field(description="今日新增项目")
    昨日新增项目: int = Field(description="昨日新增项目")
    本周新增项目: int = Field(description="本周新增项目")
    本月新增项目: int = Field(description="本月新增项目")
    时间范围新增项目: int = Field(description="指定时间范围内新增项目")
    当前新增项目: int = Field(description="当前选择时间范围的新增项目")
    项目标题: str = Field(description="项目指标的标题")
    本月销售额: float = Field(description="本月销售额")


class 团队概览数据模型(BaseModel):
    """团队概览数据模型 - 优化后只包含数据库中真实存在的数据"""
    参与团队数: int = Field(description="参与团队数")
    团队成员总数: int = Field(description="团队成员总数")
    团队邀约总数: int = Field(description="团队邀约总数")
    主要团队id: Optional[int] = Field(default=None, description="用户主要团队id，用于跳转详情页")
    主要团队名称: Optional[str] = Field(default=None, description="用户主要团队名称")
    # 移除无效字段：团队合作项目数、团队销售额、我的团队排名（数据库中无对应数据源）


class 趋势数据点模型(BaseModel):
    """趋势数据点模型"""
    日期: str = Field(description="日期")
    数值: float = Field(description="数值")
    标签: Optional[str] = Field(default=None, description="数据标签")


class 趋势图表模型(BaseModel):
    """趋势图表数据模型"""
    标题: str = Field(description="图表标题")
    类型: str = Field(description="图表类型：line/bar/area")
    数据: List[趋势数据点模型] = Field(description="趋势数据点")
    总计: Optional[float] = Field(default=None, description="总计数值")
    平均值: Optional[float] = Field(default=None, description="平均值")
    最大值: Optional[float] = Field(default=None, description="最大值")
    最小值: Optional[float] = Field(default=None, description="最小值")


class 待办事项模型(BaseModel):
    """待办事项模型"""
    ID: str = Field(description="事项ID")
    标题: str = Field(description="事项标题")
    描述: Optional[str] = Field(default=None, description="事项描述")
    截止时间: Optional[str] = Field(default=None, description="截止时间")
    优先级: str = Field(default="normal", description="优先级：high/normal/low")
    状态: str = Field(default="pending", description="状态：pending/processing/completed")
    标签: Optional[str] = Field(default=None, description="标签")
    标签颜色: Optional[str] = Field(default=None, description="标签颜色")
    关联数据: Optional[Dict[str, Any]] = Field(default=None, description="关联的业务数据")


class 团队成员模型(BaseModel):
    """团队成员模型"""
    用户id: int = Field(description="用户id")
    昵称: str = Field(description="用户昵称")
    头像: Optional[str] = Field(default=None, description="头像URL")
    角色: str = Field(description="团队角色")
    邀约数: int = Field(description="邀约数量")
    合作数: int = Field(description="合作数量")
    销售额: float = Field(description="销售额")
    活跃度: float = Field(description="活跃度评分")


class 团队详情模型(BaseModel):
    """团队详情模型"""
    团队id: int = Field(description="团队id")
    团队名称: str = Field(description="团队名称")
    成员数: int = Field(description="成员数量")
    最大成员数: int = Field(description="最大成员数")
    团队状态: str = Field(description="团队状态")
    创建时间: datetime = Field(description="创建时间")
    成员列表: List[团队成员模型] = Field(description="团队成员列表")
    团队统计: Dict[str, Any] = Field(description="团队统计数据")


class 个人工作台数据模型(BaseModel):
    """个人工作台数据响应模型"""
    用户信息: Dict[str, Any] = Field(description="用户基本信息")
    微信运营: 微信运营数据模型 = Field(description="微信运营数据")
    邀约业务: 邀约业务数据模型 = Field(description="邀约业务数据")
    达人管理: 达人管理数据模型 = Field(description="达人管理数据")
    合作项目: 合作项目数据模型 = Field(description="合作项目数据")
    今日概览: Dict[str, Any] = Field(description="今日数据概览")


class 团队工作台数据模型(BaseModel):
    """团队工作台数据响应模型"""
    团队概览: 团队概览数据模型 = Field(description="团队概览数据")
    我的团队: List[团队详情模型] = Field(description="我参与的团队列表")
    团队排行: List[Dict[str, Any]] = Field(description="团队成员排行")


class 多指标趋势图表模型(BaseModel):
    """多指标趋势图表数据模型"""
    标题: str = Field(description="图表标题")
    时间维度: str = Field(description="时间维度：day/week/month/quarter")
    数据间隔: str = Field(description="数据间隔：1天/3天/7天")
    日期列表: List[str] = Field(description="日期列表")
    指标系列: List[Dict[str, Any]] = Field(description="指标系列数据")
    统计信息: Dict[str, Any] = Field(description="统计信息（总计、平均值、最大值、最小值）")

class 趋势分析数据模型(BaseModel):
    """趋势分析数据响应模型"""
    邀约趋势: 趋势图表模型 = Field(description="邀约趋势图表")
    合作趋势: 趋势图表模型 = Field(description="合作趋势图表")
    销售趋势: 趋势图表模型 = Field(description="销售趋势图表")
    对比分析: Dict[str, Any] = Field(description="对比分析数据")

class 多指标趋势分析数据模型(BaseModel):
    """多指标趋势分析数据响应模型"""
    业务模块: str = Field(description="业务模块")
    图表数据: 多指标趋势图表模型 = Field(description="多指标趋势图表数据")
    指标配置: List[Dict[str, Any]] = Field(description="指标配置信息")


class 工作台完整数据模型(BaseModel):
    """工作台完整数据响应模型"""
    个人数据: 个人工作台数据模型 = Field(description="个人工作台数据")
    团队数据: 团队工作台数据模型 = Field(description="团队工作台数据")
    趋势分析: 趋势分析数据模型 = Field(description="趋势分析数据")
    待办事项: List[待办事项模型] = Field(description="待办事项列表")
    快捷操作: List[Dict[str, Any]] = Field(description="快捷操作列表")
    更新时间: datetime = Field(description="数据更新时间")


# 快捷操作模型
class 快捷操作模型(BaseModel):
    """快捷操作模型"""
    操作ID: str = Field(description="操作ID")
    标题: str = Field(description="操作标题")
    描述: Optional[str] = Field(default=None, description="操作描述")
    图标: str = Field(description="操作图标")
    链接: str = Field(description="操作链接")
    颜色: Optional[str] = Field(default=None, description="主题颜色")
    权限: Optional[List[str]] = Field(default=None, description="所需权限")
