from typing import TypeVar, Generic, Optional, Any
from pydantic import BaseModel, Field

# 定义泛型类型
T = TypeVar('T')


# 导入统一日志系统

class 统一响应模型(BaseModel, Generic[T]):
    """API统一响应模型"""
    status: int = Field(..., description="状态码")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    
    @classmethod
    def 成功(cls, 数据: T = None, 消息: str = "操作成功", 状态码: int = 100):
        """创建成功响应"""
        return cls(status=状态码, message=消息, data=数据)
    
    @classmethod
    def 失败(cls, 状态码: int, 消息: str, 数据: T = None):
        """创建失败响应"""
        return cls(status=状态码, message=消息, data=数据)
    
    def 转字典(self):
        """转换为字典格式"""
        result = {
            "status": self.status,
            "message": self.message
        }
        if self.data is not None:
            result["data"] = self.data
        return result

class 设置昵称请求(BaseModel):
    """设置用户昵称请求模型"""
    昵称: str = Field(..., min_length=2, max_length=20, description="用户希望设置的新昵称，长度2-20字符")

# 其他可能存在的模型... 

class 分页响应模型(BaseModel):
    """分页响应基础模型"""
    总数: int = Field(..., description="总记录数")
    页码: int = Field(..., description="当前页码")
    每页数量: int = Field(..., description="每页记录数")
    总页数: int = Field(..., description="总页数")

    @classmethod
    def 计算总页数(cls, 总数: int, 每页数量: int) -> int:
        """计算总页数"""
        return (总数 + 每页数量 - 1) // 每页数量


class 分页数据响应模型(分页响应模型, Generic[T]):
    """带分页的数据响应模型"""
    列表: list[T] = Field(..., description="数据列表")


class 基础响应模型(BaseModel):
    """基础响应数据模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    code: Optional[int] = Field(None, description="业务状态码")


class 错误响应模型(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    code: Optional[int] = Field(None, description="错误代码")
    details: Optional[Any] = Field(None, description="错误详情") 