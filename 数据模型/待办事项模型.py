"""
待办事项数据模型
定义待办事项相关的数据结构、枚举和请求响应模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field


class 待办事项类型(str, Enum):
    """待办事项类型枚举"""
    _未发货样品 = "pendingShipment"
    _运输中样品 = "inTransit"
    _待办合作 = "pendingCooperation"
    _其他待办 = "otherTasks"
    _审核任务 = "approvalTasks"
    _跟进任务 = "followUpTasks"

class 待办事项状态(str, Enum):
    """待办事项状态枚举"""
    _待处理 = "pending"
    _进行中 = "inProgress"
    _已完成 = "completed"
    _已取消 = "cancelled"
    _已过期 = "expired"

class 待办事项优先级(str, Enum):
    """待办事项优先级枚举"""
    _低 = "low"
    _普通 = "normal"
    _高 = "high"
    _紧急 = "urgent"

class 待办事项标签颜色(str, Enum):
    """待办事项标签颜色枚举"""
    _默认 = "default"
    _红色 = "red"
    _橙色 = "orange"
    _黄色 = "yellow"
    _绿色 = "green"
    _蓝色 = "blue"
    _紫色 = "purple"

class 待办事项基础模型(BaseModel):
    """待办事项基础数据模型"""
    id: str = Field(..., description="待办事项唯一标识")
    title: str = Field(..., min_length=1, max_length=200, description="待办事项标题")
    description: Optional[str] = Field(None, max_length=1000, description="待办事项描述")
    type: 待办事项类型 = Field(..., description="待办事项类型")
    status: 待办事项状态 = Field(default=待办事项状态.待处理, description="待办事项状态")
    priority: 待办事项优先级 = Field(default=待办事项优先级.普通, description="优先级")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    deadline_text: Optional[str] = Field(None, max_length=50, description="截止时间显示文本")
    tag: Optional[str] = Field(None, max_length=20, description="标签文本")
    tag_color: 待办事项标签颜色 = Field(default=待办事项标签颜色.默认, description="标签颜色")
    created_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_time: datetime = Field(default_factory=datetime.now, description="更新时间")
    user_id: int = Field(..., gt=0, description="关联用户id")
    related_id: Optional[int] = Field(None, description="关联业务ID（如样品id、合作ID等）")
    related_data: Optional[Dict[str, Any]] = Field(None, description="关联业务数据")

class 待办事项创建请求(BaseModel):
    """创建待办事项请求模型"""
    title: str = Field(..., min_length=1, max_length=200, description="待办事项标题")
    description: Optional[str] = Field(None, max_length=1000, description="待办事项描述")
    type: 待办事项类型 = Field(..., description="待办事项类型")
    priority: 待办事项优先级 = Field(default=待办事项优先级.普通, description="优先级")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    tag: Optional[str] = Field(None, max_length=20, description="标签文本")
    tag_color: 待办事项标签颜色 = Field(default=待办事项标签颜色.默认, description="标签颜色")
    related_id: Optional[int] = Field(None, description="关联业务ID")
    related_data: Optional[Dict[str, Any]] = Field(None, description="关联业务数据")

class 待办事项更新请求(BaseModel):
    """更新待办事项请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="待办事项标题")
    description: Optional[str] = Field(None, max_length=1000, description="待办事项描述")
    status: Optional[待办事项状态] = Field(None, description="待办事项状态")
    priority: Optional[待办事项优先级] = Field(None, description="优先级")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    tag: Optional[str] = Field(None, max_length=20, description="标签文本")
    tag_color: Optional[待办事项标签颜色] = Field(None, description="标签颜色")
    related_data: Optional[Dict[str, Any]] = Field(None, description="关联业务数据")

class 待办事项查询请求(BaseModel):
    """查询待办事项请求模型"""
    用户id: Optional[int] = Field(None, gt=0, description="用户id，不传则获取当前用户")
    分类: Optional[待办事项类型] = Field(None, description="待办事项类型筛选")
    状态: Optional[待办事项状态] = Field(None, description="状态筛选")
    优先级: Optional[待办事项优先级] = Field(None, description="优先级筛选")
    条数限制: int = Field(20, ge=1, le=100, description="返回条数限制")
    页码: int = Field(1, ge=1, description="页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, max_length=100, description="搜索关键词")
    是否包含已完成: bool = Field(False, description="是否包含已完成的任务")

class 待办事项操作请求(BaseModel):
    """待办事项操作请求模型"""
    待办事项ID: str = Field(..., description="待办事项ID")
    操作类型: str = Field(..., description="操作类型：complete（完成）、cancel（取消）、restart（重新开始）")
    备注: Optional[str] = Field(None, max_length=500, description="操作备注")

class 批量待办事项操作请求(BaseModel):
    """批量待办事项操作请求模型"""
    待办事项ID列表: List[str] = Field(..., min_items=1, max_items=50, description="待办事项ID列表")
    操作类型: str = Field(..., description="操作类型")
    备注: Optional[str] = Field(None, max_length=500, description="操作备注")

class 待办事项响应模型(BaseModel):
    """待办事项响应模型"""
    id: str = Field(..., description="待办事项唯一标识")
    title: str = Field(..., description="待办事项标题")
    description: Optional[str] = Field(None, description="待办事项描述")
    type: 待办事项类型 = Field(..., description="待办事项类型")
    status: 待办事项状态 = Field(..., description="待办事项状态")
    priority: 待办事项优先级 = Field(..., description="优先级")
    deadline: Optional[str] = Field(None, description="截止时间显示文本")
    tag: Optional[str] = Field(None, description="标签文本")
    tag_color: 待办事项标签颜色 = Field(..., description="标签颜色")
    created_time: str = Field(..., description="创建时间")
    updated_time: str = Field(..., description="更新时间")
    related_id: Optional[int] = Field(None, description="关联业务ID")
    related_data: Optional[Dict[str, Any]] = Field(None, description="关联业务数据")

class 待办事项列表响应模型(BaseModel):
    """待办事项列表响应模型"""
    todos: List[Dict[str, Any]] = Field(..., description="分类待办事项列表")
    statistics: Optional[Dict[str, int]] = Field(None, description="统计信息")
    total: int = Field(0, description="总数")
    page: int = Field(1, description="当前页码")
    size: int = Field(20, description="每页数量")

class 待办事项统计响应模型(BaseModel):
    """待办事项统计响应模型"""
    总数: int = Field(0, description="总待办事项数")
    待处理: int = Field(0, description="待处理数量")
    进行中: int = Field(0, description="进行中数量")
    已完成: int = Field(0, description="已完成数量")
    今日到期: int = Field(0, description="今日到期数量")
    已过期: int = Field(0, description="已过期数量")
    按类型统计: Dict[str, int] = Field(default_factory=dict, description="按类型统计")
    按优先级统计: Dict[str, int] = Field(default_factory=dict, description="按优先级统计")

class 待办事项详情响应模型(BaseModel):
    """待办事项详情响应模型"""
    待办事项: 待办事项响应模型 = Field(..., description="待办事项详情")
    操作历史: Optional[List[Dict[str, Any]]] = Field(None, description="操作历史")
    相关业务信息: Optional[Dict[str, Any]] = Field(None, description="相关业务信息")

# 兼容现有API格式的模型
class 仪表板待办事项模型(BaseModel):
    """仪表板待办事项模型（兼容现有格式）"""
    key: str = Field(..., description="分类键")
    title: str = Field(..., description="分类标题")
    items: List[Dict[str, Any]] = Field(..., description="待办事项列表")

class 仪表板待办事项响应模型(BaseModel):
    """仪表板待办事项响应模型"""
    todos: List[仪表板待办事项模型] = Field(..., description="分类待办事项列表") 