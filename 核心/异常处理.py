from fastapi import HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse

from 日志 import 错误日志器  # 假设错误日志器可以全局导入


# -----------------------------
# 异常处理器
# -----------------------------
async def 全局异常处理器(_request: Request, exc: HTTPException) -> JSONResponse:
    """处理HTTP异常"""
    # 如果detail已经是字典格式并包含status字段，则直接使用
    if isinstance(exc.detail, dict) and "status" in exc.detail:
        # 确保包含所有必需字段
        if "message" not in exc.detail and "msg" in exc.detail:
            exc.detail["message"] = exc.detail["msg"]
            del exc.detail["msg"]
        if "data" not in exc.detail:
            exc.detail["data"] = None
        return JSONResponse(status_code=exc.status_code, content=exc.detail)

    # 否则构造标准响应格式
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": exc.status_code,
            "message": str(exc.detail)
            if not isinstance(exc.detail, dict)
            else exc.detail.get("message", "未知错误"),
            "data": None,
        },
    )


async def 全局未知异常处理器(_request: Request, exc: Exception) -> JSONResponse:
    """处理未知异常"""
    错误日志器.critical(f"全局未知异常处理器捕获: {exc}", exc_info=True)  # 添加日志记录
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "message": f"服务器内部错误: {str(exc)}",
            "data": None,
        },
    )


async def 自定义验证异常处理器(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """处理请求验证错误，提供友好错误信息"""
    error_messages = []

    for error in exc.errors():
        # 提取字段路径和错误类型
        field = ".".join(str(loc) for loc in error["loc"] if loc != "body")
        error_type = error["type"]

        # 生成友好提示
        if (
            error_type == "value_error.missing" or error_type == "missing"
        ):  # 统一处理 missing 类型
            message = "该字段是必填项"
        elif error_type == "type_error.integer":
            message = "需要一个有效的整数"
        elif error_type == "type_error.float":
            message = "需要一个有效的数字"
        elif error_type == "value_error.email":
            message = "需要一个有效的邮箱地址"
        else:
            message = f"无效的值 ({error_type})"

        error_messages.append({"field": field, "msg": message})

    错误日志器.warning(
        f"请求参数验证失败: {error_messages} - 来自 IP: {request.client.host}"
    )
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "status": 400,
            "message": "请求参数错误",
            "data": {"errors": error_messages},
        },
    )
