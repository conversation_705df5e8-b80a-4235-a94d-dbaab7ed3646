from fastapi import Request
from fastapi.responses import JSONResponse

from 工具.限速器 import IP限速器实例  # 从新的限速器模块导入实例


async def IP限流中间件(request: Request, call_next):
    """IP限流中间件"""
    # 获取真实IP（考虑代理情况）
    client_ip = request.client.host
    if "X-Forwarded-For" in request.headers:
        client_ip = request.headers["X-Forwarded-For"].split(",")[0]

    # 跳过健康检查接口等特定路径 (可以考虑将这些路径配置化)
    # 注意：openapi.json 路径可能也需要在此处排除，取决于具体需求
    if request.url.path in ["/server-status", "/health", "/850483315-docs", "/850483315-redoc", "/850483315-openapi.json"]:
        return await call_next(request)

    # 检查频率限制
    if not await IP限速器实例.检查IP(client_ip, request.url.path):
        return JSONResponse(
            status_code=429,
            content={
                "status": 429,
                "msg": "请求过于频繁，请稍后再试" # 符合项目规范的消息格式
            }
        )

    # 记录访问并继续
    await IP限速器实例.记录访问(client_ip)
    return await call_next(request) 