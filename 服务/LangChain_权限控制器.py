"""
LangChain 权限控制器
负责内部函数工具的权限验证和安全控制

主要功能：
1. 检查用户是否有权限调用特定内部函数工具
2. 基于用户角色和权限表进行验证
3. 记录敏感操作的审计日志
4. 提供权限配置管理接口

作者：系统
创建时间：2024年
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

# 设置日志
权限控制器日志器 = logging.getLogger("LangChain.权限控制器")


class LangChain权限控制器:
    """LangChain 内部函数工具权限控制器"""

    def __init__(self):
        self.权限配置 = {}  # 工具名称 -> 权限要求
        self.用户权限缓存 = {}  # 用户id -> 权限集合
        self.调用日志 = []  # 调用审计日志
        self.已初始化 = False
        self.数据库操作 = None  # 数据库操作实例

    async def 初始化(self):
        """初始化权限控制器"""
        try:
            if self.已初始化:
                return

            权限控制器日志器.info("开始初始化权限控制器...")

            # 加载默认权限配置
            await self._加载默认权限配置()

            # 初始化数据库连接（如果需要）
            await self._初始化数据库连接()

            self.已初始化 = True
            权限控制器日志器.info("✅ 权限控制器初始化完成")

        except Exception as e:
            权限控制器日志器.error(f"权限控制器初始化失败: {str(e)}")
            raise

    async def _加载默认权限配置(self):
        """加载默认的权限配置"""
        try:
            # 默认权限配置
            默认权限配置 = {
                # 用户管理工具权限
                "获取用户列表": {
                    "权限要求": "用户管理.查看",
                    "角色要求": ["管理员", "超级管理员"],
                    "描述": "查看用户列表的权限",
                },
                "获取用户详情": {
                    "权限要求": "用户管理.查看",
                    "角色要求": ["管理员", "超级管理员"],
                    "描述": "查看用户详情的权限",
                },
                # 团队管理工具权限
                "创建团队": {
                    "权限要求": "团队管理.创建",
                    "角色要求": ["普通用户", "管理员", "超级管理员"],
                    "描述": "创建团队的权限",
                },
                "获取团队列表": {
                    "权限要求": "团队管理.查看",
                    "角色要求": ["普通用户", "管理员", "超级管理员"],
                    "描述": "查看团队列表的权限",
                },
                # 达人管理工具权限
                "获取达人列表": {
                    "权限要求": "达人管理.查看",
                    "角色要求": ["普通用户", "管理员", "超级管理员"],
                    "描述": "查看达人列表的权限",
                },
                # 工作台统计工具权限
                "获取工作台统计数据": {
                    "权限要求": "工作台.查看",
                    "角色要求": ["普通用户", "管理员", "超级管理员"],
                    "描述": "查看工作台统计数据的权限",
                },
            }

            self.权限配置.update(默认权限配置)
            权限控制器日志器.info(f"✅ 加载了 {len(默认权限配置)} 个默认权限配置")

        except Exception as e:
            权限控制器日志器.error(f"加载默认权限配置失败: {str(e)}")

    async def _初始化数据库连接(self):
        """初始化数据库连接"""
        try:
            # 导入数据库操作类
            from 数据.异步数据库函数 import 异步数据库操作

            self.数据库操作 = 异步数据库操作()
            权限控制器日志器.info("数据库连接初始化完成")

        except Exception as e:
            权限控制器日志器.error(f"数据库连接初始化失败: {str(e)}")
            # 如果数据库连接失败，使用简化权限检查
            self.数据库操作 = None

    async def 检查工具调用权限(
        self, 用户id: int, 工具名称: str, 用户角色: str = None
    ) -> bool:
        """细粒度权限检查：基于用户角色、工具安全级别、权限要求进行权限验证

        Args:
            用户id: 用户的唯一标识符（必须是当前登录用户的ID，不能传入别人的ID）
            工具名称: 要调用的工具名称
            用户角色: 用户角色，可选

        Returns:
            bool: 是否有权限调用
        """
        try:
            if not self.已初始化:
                await self.初始化()

            # 记录权限检查日志
            权限控制器日志器.info(
                f"检查用户权限: 用户id={用户id}, 工具名称={工具名称}, 用户角色={用户角色}"
            )

            if not self.数据库操作:
                # 如果数据库不可用，默认允许所有工具调用
                权限控制器日志器.warning("数据库不可用，使用默认权限策略")
                return True

            # 1. 获取工具配置信息（包括安全级别和权限要求）
            工具查询SQL = """
            SELECT 工具名称, 启用状态, 安全级别, 权限要求, 工具描述
            FROM langchain_工具配置表
            WHERE 工具名称 = $1
            """
            工具信息列表 = await self.数据库操作.执行查询(工具查询SQL, (工具名称,))

            if not 工具信息列表:
                权限控制器日志器.warning(f"❌ 工具 {工具名称} 不存在")
                await self._记录权限检查日志(用户id, 工具名称, False, "工具不存在")
                return False

            工具信息 = 工具信息列表[0]

            # 2. 检查工具是否启用
            if not 工具信息.get("启用状态", 0):
                权限控制器日志器.warning(f"❌ 工具 {工具名称} 未启用")
                await self._记录权限检查日志(用户id, 工具名称, False, "工具未启用")
                return False

            # 3. 检查用户安全级别
            用户安全级别 = await self._获取用户安全级别(用户id)
            工具安全级别 = 工具信息.get("安全级别", 1)

            if 用户安全级别 < 工具安全级别:
                权限控制器日志器.warning(
                    f"❌ 用户安全级别不足: 用户级别={用户安全级别}, 工具要求级别={工具安全级别}"
                )
                await self._记录权限检查日志(
                    用户id, 工具名称, False, "用户安全级别不足"
                )
                return False

            # 4. 检查权限要求
            权限要求 = 工具信息.get("权限要求", "")
            if 权限要求:
                权限检查结果 = await self._检查权限要求(用户id, 权限要求)
                if not 权限检查结果:
                    权限控制器日志器.warning(f"❌ 用户不满足权限要求: {权限要求}")
                    await self._记录权限检查日志(
                        用户id, 工具名称, False, f"不满足权限要求: {权限要求}"
                    )
                    return False

            # 5. 检查用户角色权限（如果提供了用户角色）
            if 用户角色:
                角色权限检查结果 = await self._检查角色权限(
                    用户角色, 工具名称, 工具安全级别
                )
                if not 角色权限检查结果:
                    权限控制器日志器.warning(
                        f"❌ 用户角色 {用户角色} 无权限调用工具 {工具名称}"
                    )
                    await self._记录权限检查日志(
                        用户id, 工具名称, False, f"角色 {用户角色} 权限不足"
                    )
                    return False

            # 6. 所有检查通过，允许调用
            权限控制器日志器.info(f"✅ 工具 {工具名称} 权限检查通过，允许调用")
            await self._记录权限检查日志(用户id, 工具名称, True, "权限检查通过")
            return True

        except Exception as e:
            权限控制器日志器.error(f"权限检查失败: {str(e)}")
            await self._记录权限检查日志(
                用户id, 工具名称, False, f"权限检查异常: {str(e)}"
            )
            return False

    async def _获取用户权限级别(self, 用户id: int) -> str:
        """获取用户的权限级别"""
        try:
            # 从用户表获取用户权限级别
            用户权限查询SQL = """
            SELECT 权限级别 FROM users WHERE id = $1
            """
            用户信息 = await self.数据库操作.执行查询(用户权限查询SQL, (用户id,))

            if 用户信息:
                权限级别 = 用户信息[0].get("权限级别", "basic")
                权限控制器日志器.debug(f"用户 {用户id} 权限级别: {权限级别}")
                return 权限级别
            else:
                # 特殊处理：用户id为1的设为管理员
                if 用户id == 1:
                    return "admin"
                权限控制器日志器.warning(f"用户 {用户id} 不存在，使用默认权限")
                return "basic"

        except Exception as e:
            权限控制器日志器.error(f"获取用户权限级别失败: {str(e)}")
            # 特殊处理：用户id为1的设为管理员
            if 用户id == 1:
                return "admin"
            return "basic"  # 默认返回基础权限

    async def _获取用户权限信息(self, 用户id: int) -> Optional[Dict[str, Any]]:
        """获取用户的权限信息"""
        try:
            # 检查缓存
            if 用户id in self.用户权限缓存:
                return self.用户权限缓存[用户id]

            # 这里应该从数据库查询用户权限
            # 暂时使用模拟数据
            模拟权限信息 = {
                "用户id": 用户id,
                "用户角色": "普通用户",  # 可以是：普通用户、管理员、超级管理员
                "权限集合": {
                    "团队管理.创建",
                    "团队管理.查看",
                    "达人管理.查看",
                    "工作台.查看",
                },
            }

            # 特殊处理：用户id为1的设为超级管理员
            if 用户id == 1:
                模拟权限信息["用户角色"] = "超级管理员"
                模拟权限信息["权限集合"].update(
                    {
                        "用户管理.查看",
                        "用户管理.创建",
                        "用户管理.编辑",
                        "用户管理.删除",
                        "团队管理.创建",
                        "团队管理.查看",
                        "团队管理.编辑",
                        "团队管理.删除",
                        "达人管理.查看",
                        "达人管理.创建",
                        "达人管理.编辑",
                        "工作台.查看",
                        "系统管理.查看",
                    }
                )

            # 缓存权限信息
            self.用户权限缓存[用户id] = 模拟权限信息

            return 模拟权限信息

        except Exception as e:
            权限控制器日志器.error(f"获取用户权限信息失败: {str(e)}")
            return None

    async def _记录权限检查日志(
        self, 用户id: int, 工具名称: str, 检查结果: bool, 备注: str = ""
    ):
        """记录权限检查的审计日志"""
        try:
            日志记录 = {
                "时间": datetime.now().isoformat(),
                "用户id": 用户id,
                "工具名称": 工具名称,
                "检查结果": "通过" if 检查结果 else "拒绝",
                "备注": 备注,
            }

            self.调用日志.append(日志记录)

            # 保持日志数量在合理范围内
            if len(self.调用日志) > 1000:
                self.调用日志 = self.调用日志[-500:]  # 保留最近500条

        except Exception as e:
            权限控制器日志器.error(f"记录权限检查日志失败: {str(e)}")

    async def 获取权限配置(self, 工具名称: str = None) -> Dict[str, Any]:
        """获取权限配置"""
        if 工具名称:
            return self.权限配置.get(工具名称, {})
        return self.权限配置.copy()

    async def 更新权限配置(self, 工具名称: str, 权限配置: Dict[str, Any]) -> bool:
        """更新工具的权限配置"""
        try:
            self.权限配置[工具名称] = 权限配置
            权限控制器日志器.info(f"✅ 更新工具 {工具名称} 的权限配置")
            return True

        except Exception as e:
            权限控制器日志器.error(f"更新权限配置失败: {str(e)}")
            return False

    async def 清除用户权限缓存(self, 用户id: int = None):
        """清除用户权限缓存"""
        try:
            if 用户id:
                if 用户id in self.用户权限缓存:
                    del self.用户权限缓存[用户id]
                    权限控制器日志器.info(f"清除用户 {用户id} 的权限缓存")
            else:
                self.用户权限缓存.clear()
                权限控制器日志器.info("清除所有用户权限缓存")

        except Exception as e:
            权限控制器日志器.error(f"清除权限缓存失败: {str(e)}")

    async def 获取调用日志(
        self, 用户id: int = None, 限制数量: int = 100
    ) -> List[Dict[str, Any]]:
        """获取权限检查调用日志"""
        try:
            if 用户id:
                # 过滤指定用户的日志
                用户日志 = [
                    日志 for 日志 in self.调用日志 if 日志.get("用户id") == 用户id
                ]
                return 用户日志[-限制数量:] if 用户日志 else []
            else:
                # 返回所有日志
                return self.调用日志[-限制数量:]

        except Exception as e:
            权限控制器日志器.error(f"获取调用日志失败: {str(e)}")
            return []

    def 获取状态(self) -> Dict[str, Any]:
        """获取权限控制器状态"""
        return {
            "已初始化": self.已初始化,
            "权限配置数量": len(self.权限配置),
            "用户权限缓存数量": len(self.用户权限缓存),
            "调用日志数量": len(self.调用日志),
        }

    async def _获取用户安全级别(self, 用户id: int) -> int:
        """获取用户的安全级别"""
        try:
            if not self.数据库操作:
                return 1  # 默认安全级别

            # 查询用户的安全级别，可能存储在用户表或用户权限表中
            用户查询SQL = """
            SELECT COALESCE(u.安全级别, 1) as 安全级别
            FROM 用户表 u
            WHERE u.id = $1
            """
            用户信息 = await self.数据库操作.执行查询(用户查询SQL, (用户id,))

            if 用户信息:
                return 用户信息[0].get("安全级别", 1)
            else:
                权限控制器日志器.warning(f"用户 {用户id} 不存在，使用默认安全级别")
                return 1

        except Exception as e:
            权限控制器日志器.error(f"获取用户安全级别失败: {str(e)}")
            return 1  # 出错时返回最低安全级别

    async def _检查权限要求(self, 用户id: int, 权限要求: str) -> bool:
        """检查用户是否满足权限要求"""
        try:
            if not 权限要求 or not self.数据库操作:
                return True  # 无权限要求或数据库不可用时默认通过

            # 解析权限要求（可能是JSON格式或逗号分隔的权限列表）
            import json

            try:
                权限要求列表 = json.loads(权限要求)
                if isinstance(权限要求列表, str):
                    权限要求列表 = [权限要求列表]
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试按逗号分隔
                权限要求列表 = [p.strip() for p in 权限要求.split(",") if p.strip()]

            if not 权限要求列表:
                return True

            # 查询用户权限
            权限查询SQL = """
            SELECT COUNT(*) as 权限数量
            FROM 用户权限表 up
            WHERE up.user_id = $1
            AND (up.expiry_date IS NULL OR up.expiry_date > NOW())
            AND up.permission_id IN (
                SELECT p.权限id
                FROM 权限表 p
                WHERE p.权限名称 = ANY($2)
            )
            """
            权限结果 = await self.数据库操作.执行查询(
                权限查询SQL, (用户id, 权限要求列表)
            )

            if 权限结果:
                拥有权限数量 = 权限结果[0].get("权限数量", 0)
                return 拥有权限数量 >= len(权限要求列表)  # 需要拥有所有要求的权限

            return False

        except Exception as e:
            权限控制器日志器.error(f"检查权限要求失败: {str(e)}")
            return False  # 出错时拒绝访问

    async def _检查角色权限(
        self, 用户角色: str, 工具名称: str, 工具安全级别: int
    ) -> bool:
        """检查用户角色是否有权限调用指定工具"""
        try:
            if not 用户角色:
                return True  # 无角色信息时默认通过

            # 定义角色权限映射
            角色权限映射 = {
                "admin": {"max_security_level": 10, "allowed_tools": "*"},
                "manager": {"max_security_level": 5, "allowed_tools": "*"},
                "user": {
                    "max_security_level": 3,
                    "allowed_tools": ["basic_*", "search_*", "query_*"],
                },
                "guest": {
                    "max_security_level": 1,
                    "allowed_tools": ["basic_info", "search_basic"],
                },
            }

            角色配置 = 角色权限映射.get(用户角色.lower())
            if not 角色配置:
                权限控制器日志器.warning(f"未知用户角色: {用户角色}")
                return False

            # 检查安全级别
            最大安全级别 = 角色配置.get("max_security_level", 1)
            if 工具安全级别 > 最大安全级别:
                权限控制器日志器.debug(
                    f"角色 {用户角色} 安全级别不足: 最大级别={最大安全级别}, 工具要求级别={工具安全级别}"
                )
                return False

            # 检查工具权限
            允许的工具 = 角色配置.get("allowed_tools", [])
            if 允许的工具 == "*":
                return True  # 允许所有工具

            # 检查工具名称是否匹配模式
            for 工具模式 in 允许的工具:
                if 工具模式.endswith("*"):
                    if 工具名称.startswith(工具模式[:-1]):
                        return True
                elif 工具模式 == 工具名称:
                    return True

            权限控制器日志器.debug(
                f"角色 {用户角色} 无权限调用工具 {工具名称}，允许的工具模式: {允许的工具}"
            )
            return False

        except Exception as e:
            权限控制器日志器.error(f"检查角色权限失败: {str(e)}")
            return False


# 创建全局实例
权限控制器实例 = LangChain权限控制器()
