# 已使用统一日志系统替代
from typing import Any, Dict, Optional

# PostgreSQL数据操作导入
from 数据.管理数据操作 import 获取系统配置, 获取系统统计信息 as 获取系统信息

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.异步系统 import 异步获取最新更新 as 数据库_异步获取最新更新

# 导入统一日志系统
from 日志 import 错误日志器


async def 异步获取最新更新() -> Optional[Dict[str, Any]]:
    """
    服务层函数：获取系统最新更新信息
    :return: 最新更新数据或None
    """
    try:
        # 获取通用更新信息
        updates = await 数据库_异步获取最新更新()

        if not updates:
            错误日志器.warning("未获取到系统最新更新，返回空值")
            return None

        # 处理返回的数据格式，如果是列表则取第一项
        if isinstance(updates, list) and updates:
            更新数据 = updates[0]
            return 更新数据
        return updates
    except (TimeoutError, ConnectionError) as e:
        错误日志器.error(f"获取系统最新更新失败: {str(e)}")
        return None
    except Exception as e:
        错误日志器.error(f"获取系统最新更新发生未知错误: {str(e)}")
        return None
