"""
React Agent集成
现代化的create_react_agent实现
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict
from uuid import uuid4

from 数据.LangChain_状态持久化数据层 import LangChain状态持久化数据层实例
from 服务.LangChain_现代化工具注册系统 import 现代化工具注册系统实例
from 服务.LangGraph_数据库检查点 import MySQL检查点管理器实例

# 尝试导入LangGraph React Agent
try:
    from langchain_core.language_models import BaseChatModel
    from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
    from langchain_core.runnables import RunnableConfig
    from langgraph.prebuilt import create_react_agent

    REACT_AGENT_AVAILABLE = True
except ImportError:
    REACT_AGENT_AVAILABLE = False

    # 提供兼容性定义
    class BaseChatModel:
        def __init__(self):
            pass

        async def ainvoke(self, messages, **kwargs):
            return AIMessage(content="React Agent兼容模式响应")

    def create_react_agent(llm, tools, checkpointer=None):
        return React兼容Agent(llm, tools, checkpointer)

    class HumanMessage:
        def __init__(self, content):
            self.content = content
            self.type = "human"

    class AIMessage:
        def __init__(self, content):
            self.content = content
            self.type = "ai"

    class ToolMessage:
        def __init__(self, content, tool_call_id):
            self.content = content
            self.tool_call_id = tool_call_id
            self.type = "tool"

    class RunnableConfig:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)


# 日志配置
React日志器 = logging.getLogger("React_Agent集成")


class React兼容Agent:
    """React Agent兼容实现"""

    def __init__(self, llm, tools, checkpointer=None):
        self.llm = llm
        self.tools = tools
        self.checkpointer = checkpointer
        React日志器.info("React兼容Agent初始化")

    async def ainvoke(self, input_data, config=None):
        """异步调用"""
        try:
            if "messages" in input_data:
                messages = input_data["messages"]
                if messages:
                    最后消息 = messages[-1]
                    if hasattr(最后消息, "content"):
                        用户输入 = 最后消息.content
                    else:
                        用户输入 = str(最后消息)

                    # 简单的工具调用逻辑
                    if "计算" in 用户输入 or "算" in 用户输入:
                        响应 = f"我理解您需要进行计算。输入内容: {用户输入}"
                    elif "时间" in 用户输入:
                        响应 = f"当前时间是: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    else:
                        响应 = f"React Agent兼容模式响应: {用户输入}"

                    return {"messages": messages + [AIMessage(content=响应)]}

            return {"messages": [AIMessage(content="React Agent兼容模式默认响应")]}

        except Exception as e:
            React日志器.error(f"React兼容Agent调用失败: {str(e)}")
            return {"messages": [AIMessage(content=f"处理失败: {str(e)}")]}

    def invoke(self, input_data, config=None):
        """同步调用"""
        return asyncio.run(self.ainvoke(input_data, config))


class 模型适配器(BaseChatModel):
    """模型适配器，将现有模型管理器适配为LangChain格式"""

    def __init__(self, 模型管理器, 模型名称: str):
        super().__init__()
        self.模型管理器 = 模型管理器
        self.模型名称 = 模型名称
        React日志器.info(f"模型适配器初始化: {模型名称}")

    async def ainvoke(self, messages, **kwargs):
        """异步调用模型"""
        try:
            # 转换消息格式
            转换消息 = []
            for msg in messages:
                if hasattr(msg, "type") and hasattr(msg, "content"):
                    if msg.type == "human":
                        转换消息.append({"role": "user", "content": msg.content})
                    elif msg.type == "ai":
                        转换消息.append({"role": "assistant", "content": msg.content})
                    elif msg.type == "system":
                        转换消息.append({"role": "system", "content": msg.content})
                else:
                    转换消息.append({"role": "user", "content": str(msg)})

            # 调用模型管理器
            if self.模型管理器 and hasattr(self.模型管理器, "调用模型"):
                结果 = await self.模型管理器.调用模型(self.模型名称, 转换消息, **kwargs)

                if 结果.get("success"):
                    return AIMessage(content=结果.get("response", "模型响应为空"))
                else:
                    return AIMessage(
                        content=f"模型调用失败: {结果.get('error', '未知错误')}"
                    )
            else:
                return AIMessage(content="模型管理器不可用")

        except Exception as e:
            React日志器.error(f"模型适配器调用失败: {str(e)}")
            return AIMessage(content=f"模型调用异常: {str(e)}")

    def invoke(self, messages, **kwargs):
        """同步调用"""
        return asyncio.run(self.ainvoke(messages, **kwargs))


class React_Agent集成:
    """React Agent集成管理器"""

    def __init__(self, 智能体配置, 模型管理器):
        self.配置 = 智能体配置
        self.模型管理器 = 模型管理器

        # 核心组件
        self.工具系统 = 现代化工具注册系统实例
        self.检查点管理器 = MySQL检查点管理器实例
        self.数据层 = LangChain状态持久化数据层实例

        # React Agent
        self.模型适配器 = None
        self.react_agent = None
        self.已初始化 = False

        React日志器.info(f"React Agent集成初始化: {智能体配置.智能体名称}")

    async def 初始化(self):
        """初始化React Agent集成"""
        try:
            if self.已初始化:
                return True

            # 初始化工具系统
            if not self.工具系统.已初始化:
                await self.工具系统.初始化()

            # 初始化检查点管理器
            await self.检查点管理器.初始化()

            # 初始化数据层
            if not self.数据层.已初始化:
                await self.数据层.初始化()

            # 创建模型适配器
            self.模型适配器 = 模型适配器(self.模型管理器, self.配置.模型名称)

            # 创建React Agent
            await self._创建React_Agent()

            self.已初始化 = True
            React日志器.info(f"✅ React Agent集成初始化成功: {self.配置.智能体名称}")
            return True

        except Exception as e:
            React日志器.error(f"❌ React Agent集成初始化失败: {str(e)}")

            # 使用统一错误处理
            from 服务.LangChain_智能体错误处理器 import 智能体错误处理器

            智能体错误处理器.处理通用错误(
                e, "React Agent集成初始化", {"智能体名称": self.配置.智能体名称}
            )

            return False

    async def _创建React_Agent(self):
        """创建React Agent - 修复工具获取和转换逻辑"""
        try:
            # 从工具管理器获取可用工具
            langchain_工具 = await self._获取并转换工具()

            React日志器.info(f"可用工具数量: {len(langchain_工具)}")

            # 创建React Agent
            if REACT_AGENT_AVAILABLE:
                self.react_agent = create_react_agent(
                    llm=self.模型适配器,
                    tools=langchain_工具,
                    checkpointer=self.检查点管理器,
                )
                React日志器.info("✅ 使用真实的create_react_agent")
            else:
                self.react_agent = create_react_agent(
                    llm=self.模型适配器,
                    tools=langchain_工具,
                    checkpointer=self.检查点管理器,
                )
                React日志器.info("✅ 使用兼容的React Agent")

        except Exception as e:
            React日志器.error(f"❌ 创建React Agent失败: {str(e)}")

            # 使用统一错误处理
            from 服务.LangChain_智能体错误处理器 import 智能体错误处理器

            智能体错误处理器.处理通用错误(
                e, "创建React Agent", {"智能体名称": self.配置.智能体名称}
            )

            raise

    async def _获取并转换工具(self):
        """从工具管理器获取工具并转换为LangChain格式"""
        try:
            # 导入工具管理器
            from 服务.LangChain_工具管理器 import LangChain工具管理器实例

            # 确保工具管理器已初始化
            if not LangChain工具管理器实例.已初始化:
                await LangChain工具管理器实例.初始化()

            # 获取所有可用工具
            工具注册表 = LangChain工具管理器实例.工具注册表
            langchain_工具列表 = []

            for 工具名称, 工具实例 in 工具注册表.items():
                try:
                    # 检查工具是否启用
                    工具配置 = LangChain工具管理器实例.工具配置.get(工具名称, {})
                    if not 工具配置.get("启用状态", True):
                        continue

                    # 转换为LangChain工具
                    langchain_工具 = await self._转换为LangChain工具(
                        工具名称, 工具实例, 工具配置
                    )
                    if langchain_工具:
                        langchain_工具列表.append(langchain_工具)
                        React日志器.debug(f"成功转换工具: {工具名称}")

                except Exception as e:
                    React日志器.warning(f"转换工具 {工具名称} 失败: {str(e)}")
                    continue

            React日志器.info(f"成功转换 {len(langchain_工具列表)} 个工具")
            return langchain_工具列表

        except Exception as e:
            React日志器.error(f"获取并转换工具失败: {str(e)}")

            # 使用统一错误处理
            from 服务.LangChain_智能体错误处理器 import 智能体错误处理器

            智能体错误处理器.处理工具调用错误(e, "工具获取和转换", 0)

            return []

    async def 对话(
        self, 用户输入: str, 用户id: int, 会话ID: str = None, 线程ID: str = None
    ) -> Dict[str, Any]:
        """处理用户对话"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 生成会话和线程ID
            if not 会话ID:
                会话ID = f"react_session_{uuid4().hex[:16]}"
            if not 线程ID:
                线程ID = f"react_thread_{uuid4().hex[:16]}"

            # 创建或获取对话线程
            线程信息 = await self.数据层.获取对话线程(线程ID)
            if not 线程信息:
                await self.数据层.创建对话线程(
                    用户id,
                    self.配置.智能体id,
                    f"React对话_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                )

            # 配置
            配置 = RunnableConfig(
                configurable={
                    "thread_id": 线程ID,
                    "user_id": 用户id,
                    "agent_id": self.配置.智能体id,
                }
            )

            # 执行React Agent
            开始时间 = time.time()

            输入数据 = {"messages": [HumanMessage(content=用户输入)]}

            if self.react_agent:
                结果 = await self.react_agent.ainvoke(输入数据, config=配置)
            else:
                # 降级处理
                结果 = {
                    "messages": [AIMessage(content="React Agent不可用，使用降级响应")]
                }

            处理时长 = time.time() - 开始时间

            # 提取响应
            if "messages" in 结果 and 结果["messages"]:
                最后消息 = 结果["messages"][-1]
                if hasattr(最后消息, "content"):
                    响应内容 = 最后消息.content
                else:
                    响应内容 = str(最后消息)
            else:
                响应内容 = "抱歉，React Agent处理请求时发生错误"

            # 更新统计
            await self.数据层.更新对话统计(
                线程ID=线程ID,
                智能体id=self.配置.智能体id,
                消息数增量=2,  # 用户消息 + AI响应
                响应时间=处理时长,
            )

            React日志器.info(f"✅ React Agent对话完成: {处理时长:.2f}s")

            # 使用统一状态码体系
            from 状态 import 状态

            return {
                "status_code": 状态.通用.成功,
                "success": True,
                "message": "React Agent对话成功",
                "response": 响应内容,
                "data": {
                    "session_id": 会话ID,
                    "thread_id": 线程ID,
                    "processing_time": 处理时长,
                    "model_name": self.配置.模型名称,
                    "agent_type": "react_agent",
                    "react_available": REACT_AGENT_AVAILABLE,
                    "tools_used": len(self.工具系统.工具注册表),
                },
            }

        except Exception as e:
            React日志器.error(f"❌ React Agent对话失败: {str(e)}")

            # 使用统一状态码体系和错误处理
            from 服务.LangChain_智能体错误处理器 import 智能体错误处理器
            from 状态 import 状态

            错误信息 = 智能体错误处理器.处理通用错误(
                e, "React Agent对话", {"用户id": 用户id, "会话ID": 会话ID}
            )

            return {
                "status_code": 状态.通用.服务器错误,
                "success": False,
                "message": f"React Agent处理请求时发生错误: {str(e)}",
                "error": 错误信息,
                "data": {
                    "agent_type": "react_agent",
                    "react_available": REACT_AGENT_AVAILABLE,
                },
            }

    async def 流式对话(
        self, 用户输入: str, 用户id: int, 会话ID: str = None, 线程ID: str = None
    ):
        """流式对话处理"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 生成会话和线程ID
            if not 会话ID:
                会话ID = f"react_stream_{uuid4().hex[:16]}"
            if not 线程ID:
                线程ID = f"react_thread_{uuid4().hex[:16]}"

            # 配置
            配置 = RunnableConfig(
                configurable={
                    "thread_id": 线程ID,
                    "user_id": 用户id,
                    "agent_id": self.配置.智能体id,
                }
            )

            输入数据 = {"messages": [HumanMessage(content=用户输入)]}

            # 流式处理
            if hasattr(self.react_agent, "astream"):
                async for chunk in self.react_agent.astream(输入数据, config=配置):
                    yield {
                        "type": "chunk",
                        "content": chunk,
                        "session_id": 会话ID,
                        "thread_id": 线程ID,
                    }
            else:
                # 模拟流式响应
                结果 = await self.react_agent.ainvoke(输入数据, config=配置)
                yield {
                    "type": "complete",
                    "content": 结果,
                    "session_id": 会话ID,
                    "thread_id": 线程ID,
                }

        except Exception as e:
            React日志器.error(f"❌ React Agent流式对话失败: {str(e)}")

            # 使用统一状态码体系和错误处理
            from 服务.LangChain_智能体错误处理器 import 智能体错误处理器
            from 状态 import 状态

            错误信息 = 智能体错误处理器.处理通用错误(
                e, "React Agent流式对话", {"用户id": 用户id, "会话ID": 会话ID}
            )

            yield {
                "type": "error",
                "status_code": 状态.通用.服务器错误,
                "success": False,
                "message": f"React Agent流式处理请求时发生错误: {str(e)}",
                "error": 错误信息,
                "data": {
                    "session_id": 会话ID,
                    "thread_id": 线程ID,
                    "agent_type": "react_agent",
                    "react_available": REACT_AGENT_AVAILABLE,
                },
            }

    def 获取工具统计(self) -> Dict[str, Any]:
        """获取工具使用统计"""
        return self.工具系统.获取工具统计()

    async def _转换为LangChain工具(
        self, 工具名称: str, 工具实例, 工具配置: Dict[str, Any]
    ):
        """将工具实例转换为LangChain工具格式"""
        try:
            # 导入必要的类
            from 服务.LangChain_工具管理器 import MCPTool, 简单工具

            # 检查是否有LangChain可用
            try:
                from langchain_core.pydantic_v1 import BaseModel, Field
                from langchain_core.tools import BaseTool, tool

                LANGCHAIN_AVAILABLE = True
            except ImportError:
                LANGCHAIN_AVAILABLE = False

            if not LANGCHAIN_AVAILABLE:
                React日志器.warning("LangChain不可用，跳过工具转换")
                return None

            # 获取工具描述和参数
            工具描述 = 工具配置.get("描述", f"工具: {工具名称}")

            # 根据工具类型进行转换
            if isinstance(工具实例, 简单工具):
                return await self._转换简单工具(工具名称, 工具实例, 工具描述)
            elif LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
                # 已经是LangChain工具，直接返回
                return 工具实例
            elif isinstance(工具实例, MCPTool):
                return await self._转换MCP工具(工具名称, 工具实例, 工具描述)
            elif hasattr(工具实例, "run"):
                return await self._转换通用工具(工具名称, 工具实例, 工具描述)
            else:
                React日志器.warning(f"不支持的工具类型: {type(工具实例)}")
                return None

        except Exception as e:
            React日志器.error(f"转换工具 {工具名称} 失败: {str(e)}")
            return None

    async def _转换简单工具(self, 工具名称: str, 工具实例, 工具描述: str):
        """转换简单工具为LangChain格式"""
        try:
            from langchain_core.tools import tool

            @tool(name=工具名称, description=工具描述)
            async def langchain_工具(*args, **kwargs):
                """动态生成的LangChain工具包装器"""
                try:
                    return await 工具实例.run(*args, **kwargs)
                except Exception as e:
                    return f"工具执行失败: {str(e)}"

            return langchain_工具

        except Exception as e:
            React日志器.error(f"转换简单工具 {工具名称} 失败: {str(e)}")
            return None

    async def _转换MCP工具(self, 工具名称: str, 工具实例, 工具描述: str):
        """转换MCP工具为LangChain格式"""
        try:
            from langchain_core.tools import tool

            @tool(name=工具名称, description=工具描述)
            async def langchain_mcp工具(*args, **kwargs):
                """动态生成的LangChain MCP工具包装器"""
                try:
                    return await 工具实例.run(*args, **kwargs)
                except Exception as e:
                    return f"MCP工具执行失败: {str(e)}"

            return langchain_mcp工具

        except Exception as e:
            React日志器.error(f"转换MCP工具 {工具名称} 失败: {str(e)}")
            return None

    async def _转换通用工具(self, 工具名称: str, 工具实例, 工具描述: str):
        """转换通用工具为LangChain格式"""
        try:
            import asyncio

            from langchain_core.tools import tool

            @tool(name=工具名称, description=工具描述)
            async def langchain_通用工具(*args, **kwargs):
                """动态生成的LangChain通用工具包装器"""
                try:
                    if asyncio.iscoroutinefunction(工具实例.run):
                        return await 工具实例.run(*args, **kwargs)
                    else:
                        return 工具实例.run(*args, **kwargs)
                except Exception as e:
                    return f"通用工具执行失败: {str(e)}"

            return langchain_通用工具

        except Exception as e:
            React日志器.error(f"转换通用工具 {工具名称} 失败: {str(e)}")
            return None


# 创建全局实例工厂
def 创建React_Agent集成(智能体配置, 模型管理器):
    """创建React Agent集成实例"""
    return React_Agent集成(智能体配置, 模型管理器)
