"""
达人业务服务层 - 基于三层分离架构
负责处理达人相关的业务逻辑，调用数据访问层进行数据操作

特性：
1. 达人信息查询和管理
2. 达人认领业务逻辑
3. 补充联系方式管理
4. 寄样申请处理
5. 统一的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional

from 服务.基础服务 import 数据服务基类
from 数据.达人数据访问层 import 达人基础数据访问, 达人认领数据访问
from 数据.达人补充信息数据访问层 import 达人补充信息数据访问
from 数据.线索数据操作 import 获取或创建联系方式并返回完整数据
from 服务.达人服务 import 查询或更新达人, 认领达人
from 日志 import 接口日志器, 错误日志器


class 达人信息服务(数据服务基类):
    """达人信息业务服务"""

    def __init__(self):
        super().__init__("达人信息服务", None)  # 不使用实例，直接调用静态方法
        
    async def 获取达人信息(
        self, 
        达人id: Optional[int], 
        平台: str, 
        平台账号: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取达人信息
        
        Args:
            达人id: 达人id
            平台: 平台类型
            平台账号: 平台账号
            
        Returns:
            达人信息
        """
        try:
            达人信息 = await 达人基础数据访问.获取达人信息_通过ID和平台(达人id, 平台, 平台账号)

            self.记录信息("获取达人信息成功", 达人id=达人id, 平台=平台)
            return self.构建成功响应(达人信息, "获取达人信息成功")

        except Exception as e:
            self.记录错误("获取达人信息失败", e, 达人id=达人id, 平台=平台)
            return self.构建失败响应("获取达人信息失败")

    async def 验证达人所有权(self, 用户id: int, 达人id: int) -> Dict[str, Any]:
        """
        验证达人所有权

        Args:
            用户id: 用户ID
            达人id: 达人id

        Returns:
            验证结果
        """
        try:
            return await 达人基础数据访问.验证达人所有权(用户id, 达人id)

        except Exception as e:
            self.记录错误("验证达人所有权失败", e, 用户id=用户id, 达人id=达人id)
            return self.构建失败响应("验证达人所有权失败")

    async def 获取用户团队信息(self, 用户id: int) -> Dict[str, Any]:
        """
        获取用户团队信息
        
        Args:
            用户id: 用户ID
            
        Returns:
            团队信息
        """
        try:
            团队结果 = await 达人基础数据访问.获取用户团队信息(用户id)
            
            if 团队结果["status"] == "success":
                团队id = 团队结果["data"]["团队id"]
                self.记录信息("获取用户团队信息成功", 用户id=用户id, 团队id=团队id)
                return self.构建成功响应({"团队id": 团队id}, 团队结果["message"])
            else:
                self.记录警告("获取用户团队信息失败", 用户id=用户id, 错误=团队结果["message"])
                return self.构建失败响应(团队结果["message"])
                
        except Exception as e:
            self.记录错误("获取用户团队信息失败", e, 用户id=用户id)
            return self.构建失败响应("获取团队信息失败")
    
    async def 检查达人认领状态(self, 用户id: int, 达人id: int) -> Dict[str, Any]:
        """
        检查达人认领状态
        
        Args:
            用户id: 用户ID
            达人id: 达人id
            
        Returns:
            认领状态
        """
        try:
            已认领 = await 达人基础数据访问.检查达人认领状态(用户id, 达人id)
            
            self.记录信息("检查达人认领状态成功", 用户id=用户id, 达人id=达人id, 已认领=已认领)
            return self.构建成功响应({"已认领": 已认领}, "检查认领状态成功")
            
        except Exception as e:
            self.记录错误("检查达人认领状态失败", e, 用户id=用户id, 达人id=达人id)
            return self.构建失败响应("检查认领状态失败")
    

    
    async def 获取达人详情(self, 达人id: int, 平台: str = "抖音") -> Dict[str, Any]:
        """
        获取达人详细信息
        
        Args:
            达人id: 达人id
            平台: 平台类型
            
        Returns:
            达人详细信息
        """
        try:
            达人详情 = await 达人基础数据访问.获取达人详情_通过ID(达人id, 平台)
            
            if 达人详情:
                self.记录信息("获取达人详情成功", 达人id=达人id, 平台=平台)
                return self.构建成功响应(达人详情, "获取达人详情成功")
            else:
                self.记录警告("达人不存在", 达人id=达人id, 平台=平台)
                return self.构建失败响应("达人不存在", "TALENT_NOT_FOUND")
                
        except Exception as e:
            self.记录错误("获取达人详情失败", e, 达人id=达人id, 平台=平台)
            return self.构建失败响应("获取达人详情失败")


class 达人认领服务(数据服务基类):
    """达人认领业务服务"""

    def __init__(self):
        super().__init__("达人认领服务", None)  # 不使用实例，直接调用静态方法
        
    async def 检查达人认领关联(self, 用户id: int, 达人id: int) -> Dict[str, Any]:
        """
        检查达人认领关联
        
        Args:
            用户id: 用户ID
            达人id: 达人id
            
        Returns:
            关联信息
        """
        try:
            关联表id = await 达人认领数据访问.检查达人认领关联(用户id, 达人id)

            if 关联表id:
                self.记录信息("检查达人认领关联成功", 用户id=用户id, 达人id=达人id, 关联表id=关联表id)
                return self.构建成功响应({"关联表id": 关联表id, "已关联": True}, "检查关联成功")
            else:
                self.记录信息("达人未关联", 用户id=用户id, 达人id=达人id)
                return self.构建成功响应({"关联表id": None, "已关联": False}, "达人未关联")
                
        except Exception as e:
            self.记录错误("检查达人认领关联失败", e, 用户id=用户id, 达人id=达人id)
            return self.构建失败响应("检查关联失败")
    
    async def 更新达人关联表_达人id(self, 关联表id: int, 用户id: int, 新达人id: int) -> Dict[str, Any]:
        """
        更新达人关联表的达人id
        
        Args:
            关联表id: 关联表id
            用户id: 用户ID
            新达人id: 新的达人id
            
        Returns:
            更新结果
        """
        try:
            更新成功 = await 达人认领数据访问.更新达人关联表_达人id(关联表id, 用户id, 新达人id)
            
            if 更新成功:
                self.记录信息("更新达人关联表成功", 关联表id=关联表id, 新达人id=新达人id)
                return self.构建成功响应(None, "更新关联表成功")
            else:
                self.记录警告("更新达人关联表失败", 关联表id=关联表id, 新达人id=新达人id)
                return self.构建失败响应("更新关联表失败")
                
        except Exception as e:
            self.记录错误("更新达人关联表失败", e, 关联表id=关联表id, 新达人id=新达人id)
            return self.构建失败响应("更新关联表失败")


class 达人补充信息服务(数据服务基类):
    """达人补充信息业务服务"""

    def __init__(self):
        super().__init__("达人补充信息服务", None)  # 不使用实例，直接调用静态方法
        
    async def 添加补充联系方式(
        self,
        用户达人关联表id: int,
        联系方式: str,
        联系方式类型: str,
        联系方式表id: Optional[int] = None,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        添加补充联系方式
        
        Args:
            用户达人关联表id: 用户达人关联表id
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            联系方式表id: 联系方式表ID
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息
            
        Returns:
            添加结果
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"用户达人关联表id": 用户达人关联表id, "联系方式": 联系方式, "联系方式类型": 联系方式类型},
            ["用户达人关联表id", "联系方式", "联系方式类型"]
        )
        if 验证错误:
            return self.构建失败响应(验证错误)
        
        try:
            补充信息id = await 达人补充信息数据访问.添加补充联系方式(
                用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 补充信息
            )
            
            if 补充信息id:
                self.记录信息("添加补充联系方式成功", 补充信息id=补充信息id)
                return self.构建成功响应({"补充信息id": 补充信息id}, "添加补充联系方式成功")
            else:
                return self.构建失败响应("添加补充联系方式失败")
                
        except Exception as e:
            self.记录错误("添加补充联系方式失败", e, 用户达人关联表id=用户达人关联表id)
            return self.构建失败响应("添加补充联系方式失败")
    
    async def 检查补充信息权限(self, 补充信息id: int, 用户id: int) -> Dict[str, Any]:
        """
        检查补充信息权限
        
        Args:
            补充信息id: 补充信息ID
            用户id: 用户ID
            
        Returns:
            权限检查结果
        """
        try:
            权限信息 = await 达人补充信息数据访问.检查补充信息权限(补充信息id, 用户id)
            
            if 权限信息:
                self.记录信息("权限检查通过", 补充信息id=补充信息id, 用户id=用户id)
                return self.构建成功响应(权限信息, "权限检查通过")
            else:
                self.记录警告("权限检查失败", 补充信息id=补充信息id, 用户id=用户id)
                return self.构建失败响应("没有操作权限", "PERMISSION_DENIED")
                
        except Exception as e:
            self.记录错误("权限检查失败", e, 补充信息id=补充信息id, 用户id=用户id)
            return self.构建失败响应("权限检查失败")


# 创建服务实例
达人信息服务实例 = 达人信息服务()
达人认领服务实例 = 达人认领服务()
达人补充信息服务实例 = 达人补充信息服务()


class 达人业务处理服务(数据服务基类):
    """达人业务处理服务 - 处理复杂的达人业务逻辑"""

    def __init__(self):
        super().__init__("达人业务处理服务", None)

    async def 添加补充联系方式_业务处理(
        self, 用户id: int, 达人id: int, 联系方式: str, 联系方式类型: str,
        个人备注: Optional[str] = None, 个人标签: Optional[List[str]] = None,
        补充信息: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        添加补充联系方式业务处理

        Args:
            用户id: 用户ID
            达人id: 达人ID
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            个人备注: 个人备注
            个人标签: 个人标签
            补充信息: 补充信息

        Returns:
            处理结果
        """
        try:
            # 1. 检查用户是否认领了该达人
            关联信息 = await 达人补充信息数据访问.检查用户达人关联权限(用户id, 达人id)
            if not 关联信息:
                return self.构建失败响应("您没有认领该达人，无法添加联系方式")

            关联表id = 关联信息["关联表id"]

            # 2. 创建联系方式记录
            联系方式数据 = await 获取或创建联系方式并返回完整数据(
                内容=联系方式, 类型=联系方式类型 or "未知", 记录来源="用户添加"
            )
            联系方式表ID = 联系方式数据.get("id") if 联系方式数据 else None

            if not 联系方式表ID:
                return self.构建失败响应("创建联系方式记录失败")

            # 3. 添加补充联系方式
            import json
            补充信息JSON = json.dumps(补充信息) if 补充信息 else None

            补充信息ID = await 达人补充信息数据访问.添加补充联系方式(
                用户达人关联表id=关联表id,
                联系方式=联系方式,
                联系方式类型=联系方式类型,
                联系方式表id=联系方式表ID,
                个人备注=个人备注,
                个人标签=个人标签,
                补充信息=补充信息JSON
            )

            if 补充信息ID:
                self.记录信息("添加补充联系方式成功", 用户id=用户id, 达人id=达人id, 补充信息ID=补充信息ID)
                return self.构建成功响应({"补充信息ID": 补充信息ID}, "添加补充联系方式成功")
            else:
                return self.构建失败响应("添加补充联系方式失败")

        except Exception as e:
            self.记录错误("添加补充联系方式业务处理失败", e, 用户id=用户id, 达人id=达人id)
            return self.构建失败响应(f"添加补充联系方式失败: {str(e)}")

    async def 关联达人_业务处理(
        self, 用户id: int, 补充信息id: int, 平台账号: str, 达人UID: str
    ) -> Dict[str, Any]:
        """
        关联达人业务处理

        Args:
            用户id: 用户ID
            补充信息id: 补充信息ID
            平台账号: 平台账号
            达人UID: 达人UID

        Returns:
            关联结果
        """
        try:
            # 1. 查询关联记录
            关联信息 = await 达人补充信息数据访问.查询关联记录_通过补充信息ID(补充信息id, 用户id)
            if not 关联信息:
                return self.构建失败响应("补充信息记录不存在或无权限访问")

            # 2. 验证达人id为null
            if 关联信息["达人id"] is not None:
                return self.构建失败响应("该记录已关联达人，无需重复关联")

            # 3. 处理达人数据
            更新结果 = await 查询或更新达人(达人UID)
            if 更新结果["状态"] != "成功" or not 更新结果["达人id"]:
                return self.构建失败响应(f"达人处理失败: {更新结果['消息']}")

            达人id = 更新结果["达人id"]

            # 4. 获取达人详细信息
            达人详情 = await 达人基础数据访问.获取达人详细信息_用于返回(达人id)

            # 5. 更新关联表
            更新成功 = await 达人补充信息数据访问.更新关联表达人ID(
                关联信息["关联表id"], 达人id, 用户id
            )

            if 更新成功:
                # 构建达人信息
                达人信息 = {
                    "ID": 达人id,
                    "UID": 达人UID,
                    "状态": "新创建并更新" if "创建" in 更新结果["消息"] else "已存在"
                }

                if 达人详情:
                    达人信息.update({
                        "昵称": 达人详情.get("昵称", ""),
                        "抖音号": 达人详情.get("account_douyin", ""),
                        "头像": 达人详情.get("avatar", ""),
                        "粉丝数": 达人详情.get("粉丝数", 0),
                    })

                self.记录信息("关联达人成功", 用户id=用户id, 达人id=达人id, 补充信息id=补充信息id)
                return self.构建成功响应({
                    "达人id": 达人id,
                    "补充信息id": 补充信息id,
                    "用户达人关联表id": 关联信息["关联表id"],
                    "达人信息": 达人信息,
                    "关联状态": "成功",
                }, f"关联达人成功 - {达人信息['状态']}")
            else:
                return self.构建失败响应("更新用户达人关联表失败，可能该记录已被其他操作修改")

        except Exception as e:
            self.记录错误("关联达人业务处理失败", e, 用户id=用户id, 补充信息id=补充信息id)
            return self.构建失败响应(f"关联达人失败: {str(e)}")

    async def 更新达人数据_业务处理(self, 用户id: int, 达人id: int, UID: Optional[str] = None) -> Dict[str, Any]:
        """
        更新达人数据业务处理

        Args:
            用户id: 用户ID
            达人id: 达人ID
            UID: 达人UID（可选）

        Returns:
            更新结果
        """
        try:
            # 1. 验证达人存在性
            达人信息 = await 达人基础数据访问.验证达人存在性(达人id)
            if not 达人信息:
                return self.构建失败响应("达人不存在")

            # 2. 确定要使用的UID
            实际UID = None
            if UID and UID.strip():
                实际UID = UID.strip()
                接口日志器.info(f"使用传入的UID: {实际UID}")
            else:
                数据库UID = 达人信息.get("uid_number")
                if 数据库UID and 数据库UID.strip():
                    实际UID = 数据库UID.strip()
                    接口日志器.info(f"使用数据库中的UID: {实际UID}")
                else:
                    return self.构建失败响应("达人UID不存在，无法更新数据。请确保达人记录中包含有效的uid_number字段")

            # 3. 更新达人数据
            更新结果 = await 查询或更新达人(实际UID)
            if 更新结果["状态"] != "成功":
                return self.构建失败响应(f"达人数据更新失败: {更新结果.get('消息', '未知错误')}")

            # 4. 获取更新后的达人信息
            更新后达人信息 = await 达人基础数据访问.获取更新后达人信息(达人id)

            if 更新后达人信息:
                响应数据 = {
                    "达人id": 达人id,
                    "UID": 实际UID,
                    "昵称": 更新后达人信息.get("昵称", ""),
                    "抖音号": 更新后达人信息.get("account_douyin", ""),
                    "头像": 更新后达人信息.get("avatar", ""),
                    "粉丝数": 更新后达人信息.get("粉丝数", 0),
                    "关注数": 更新后达人信息.get("关注数", 0),
                    "简介": 更新后达人信息.get("introduction", ""),
                    "处理状态": 更新结果["消息"],
                }

                self.记录信息("达人数据更新成功", 用户id=用户id, 达人id=达人id, 实际UID=实际UID)
                return self.构建成功响应(响应数据, "达人数据更新成功")
            else:
                return self.构建成功响应({"处理状态": 更新结果["消息"]}, "达人数据更新成功")

        except Exception as e:
            self.记录错误("更新达人数据业务处理失败", e, 用户id=用户id, 达人id=达人id)
            return self.构建失败响应(f"更新达人数据失败: {str(e)}")

    async def 添加达人_业务处理(self, 用户id: int, UID: str) -> Dict[str, Any]:
        """
        添加达人业务处理

        Args:
            用户id: 用户ID
            UID: 达人UID

        Returns:
            添加结果
        """
        try:
            # 1. 查询或更新达人
            更新结果 = await 查询或更新达人(UID)
            if 更新结果["状态"] != "成功" or not 更新结果["达人id"]:
                return self.构建失败响应(f"查询或更新达人失败: {更新结果['消息']}")

            达人id = 更新结果["达人id"]

            # 2. 检查是否已经关联
            已关联 = await 达人基础数据访问.检查用户达人关联状态(用户id, 达人id)
            if 已关联:
                return self.构建失败响应("您已经认领了这个达人")

            # 3. 建立用户关联
            关联结果 = await 认领达人(用户id, 达人id)
            if not 关联结果:
                return self.构建失败响应("建立用户达人关联失败")

            # 4. 获取达人详细信息
            达人详情 = await 达人基础数据访问.获取达人详细信息_用于返回(达人id)

            达人信息 = {"达人id": 达人id, "UID": UID, "处理状态": 更新结果["消息"]}
            if 达人详情:
                达人信息.update({
                    "昵称": 达人详情.get("昵称", ""),
                    "抖音号": 达人详情.get("account_douyin", ""),
                    "头像": 达人详情.get("avatar", ""),
                    "粉丝数": 达人详情.get("粉丝数", 0),
                })

            self.记录信息("添加达人成功", 用户id=用户id, 达人id=达人id, UID=UID)
            return self.构建成功响应(达人信息, "添加达人成功")

        except Exception as e:
            self.记录错误("添加达人业务处理失败", e, 用户id=用户id, UID=UID)
            return self.构建失败响应(f"添加达人失败: {str(e)}")


# 创建业务处理服务实例
达人业务处理服务实例 = 达人业务处理服务()
