from datetime import datetime
from typing import Dict, Any, Optional, List

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.异步通告 import 异步创建通告 as 数据库_异步创建通告
from 数据.异步通告 import 异步删除通告 as 数据库_异步删除通告
from 数据.异步通告 import 异步更新通告 as 数据库_异步更新通告
from 数据.异步通告 import 异步获取通告列表 as 数据库_异步获取通告列表
from 数据.异步通告 import 异步获取通告详情 as 数据库_异步获取通告详情

# 导入统一日志系统
from 日志 import 应用日志器, 错误日志器, 安全日志器


async def 异步获取通告列表(
    页码: int = 1, 
    每页数量: int = 10, 
    类型: Optional[str] = None, 
    # 只获取已发布: bool = False, # 暂时注释掉，由新的状态参数处理
    # 不返回内容: bool = True,   # 暂时注释掉
    标题: Optional[str] = None, # 新增
    状态: Optional[int] = None, # 新增 (0: 草稿, 1: 已发布)
    创建时间开始: Optional[datetime] = None, # 新增
    创建时间结束: Optional[datetime] = None, # 新增
    排序字段: Optional[str] = None, # 新增
    排序顺序: Optional[str] = None # 新增
) -> Dict[str, Any]:
    """
    服务层函数：获取通告列表，支持分页、类型、标题、状态、创建时间范围筛选和排序
    
    参数:
    页码: int - 当前页码，默认为1
    每页数量: int - 每页显示记录数，默认为10
    类型: Optional[str] - 可选的通告类型筛选
    标题: Optional[str] - 按标题模糊筛选
    状态: Optional[int] - 按状态筛选 (0: 草稿, 1: 已发布)
    创建时间开始: Optional[datetime] - 创建时间范围开始
    创建时间结束: Optional[datetime] - 创建时间范围结束
    排序字段: Optional[str] - 排序字段 (例如 '创建时间', '排序')
    排序顺序: Optional[str] - 排序顺序 ('ascend', 'descend')
    
    返回值:
    Dict[str, Any]: 包含通告列表数据和分页信息
    """
    try:
        # 调用数据层函数获取通告列表，传递所有相关参数
        结果 = await 数据库_异步获取通告列表(
            页码=页码, 
            每页数量=每页数量, 
            类型=类型,
            # 只获取已发布=只获取已发布, # 对应注释
            # 不返回内容=不返回内容,     # 对应注释
            标题=标题,             # 新增传递
            状态=状态,             # 新增传递
            创建时间开始=创建时间开始, # 新增传递
            创建时间结束=创建时间结束, # 新增传递
            排序字段=排序字段,       # 新增传递
            排序顺序=排序顺序        # 新增传递
        )
        
        # 记录查询日志
        应用日志器.info(f"成功获取通告列表: 页码={页码}, 每页数量={每页数量}, 类型={类型}, 标题={标题}, 状态={状态}, 排序={排序字段} {排序顺序}, 总数={结果['总数']}")
        
        return 结果
    except (TimeoutError, ConnectionError) as e:
        错误日志器.error(f"获取通告列表网络错误: {str(e)}")
        return {'列表': [], '总数': 0, '总页数': 0, '当前页': 页码}
    except Exception as e:
        错误日志器.error(f"获取通告列表发生未知错误: {str(e)}")
        return {'列表': [], '总数': 0, '总页数': 0, '当前页': 页码}


async def 异步获取通告详情(通告id: int, 只获取已发布: bool = False) -> Optional[Dict[str, Any]]:
    """
    服务层函数：获取单个通告的详细信息
    
    参数:
    通告id: int - 通告ID
    只获取已发布: bool - 是否只获取已发布的通告，默认为False
    
    返回值:
    Optional[Dict[str, Any]]: 包含通告详情的字典，若未找到则返回None
    """
    try:
        错误日志器.info(f"服务层-开始获取通告详情: ID={通告id}, 只获取已发布={只获取已发布}")
        
        # 调用数据层函数获取通告详情
        通告详情 = await 数据库_异步获取通告详情(通告id, 只获取已发布)
        
        if 通告详情:
            错误日志器.info(f"服务层-成功获取通告详情: ID={通告id}, 标题={通告详情.get('标题', '未知')}")
            应用日志器.info(f"成功获取通告详情: ID={通告id}")
        else:
            错误日志器.warning(f"服务层-未找到指定通告: ID={通告id}")
            应用日志器.warning(f"未找到指定通告: ID={通告id}")
            
        return 通告详情
    except (TimeoutError, ConnectionError) as e:
        错误日志器.error(f"服务层-获取通告详情网络错误: ID={通告id}, 错误={str(e)}")
        return None
    except Exception as e:
        错误日志器.error(f"服务层-获取通告详情发生未知错误: ID={通告id}, 错误={str(e)}", exc_info=True)
        return None


async def 异步创建通告(
    类型: str, 
    标题: str, 
    内容: List[Dict[str, str]], 
    用户id: int, 
    已发布: bool = False, 
    重要性: int = 1, 
    开始时间: Optional[str] = None, # 在Pydantic模型中是datetime，但这里是str，数据层需要能处理
    结束时间: Optional[str] = None, # 同上
    排序: Optional[int] = None, # 新增
    操作标识: Optional[int] = 0
) -> Dict[str, Any]:
    """
    服务层函数：创建新通告
    
    参数:
    类型: str - 通告类型
    标题: str - 通告标题
    内容: List[Dict[str, str]] - 通告内容，格式为[{"内容": "xxx", "类型": "文本"}, ...]
    用户id: int - 创建通告的管理员ID
    已发布: bool - 是否已发布，默认False
    重要性: int - 重要性级别：1普通，2重要，3紧急，默认1
    开始时间: Optional[str] - 有效期开始时间，可为None
    结束时间: Optional[str] - 有效期结束时间，可为None
    排序: Optional[int] - 显示排序，数字越大越靠前
    操作标识: Optional[int] - 操作标识，默认为0
    
    返回值:
    Dict[str, Any]: 包含操作结果的字典
    """
    try:
        # 参数验证
        if not 类型 or not 标题:
            return {"成功": False, "消息": "类型和标题不能为空", "id": None}
        
        if not 内容 or not isinstance(内容, list):
            return {"成功": False, "消息": "内容必须是有效的数组", "id": None}
        
        # 验证内容中的每个项目
        for 项目 in 内容:
            if not isinstance(项目, dict) or '内容' not in 项目 or '类型' not in 项目:
                return {"成功": False, "消息": "内容数组的每个项目必须包含'内容'和'类型'字段", "id": None}
            if not isinstance(项目['内容'], str) or not isinstance(项目['类型'], str):
                 return {"成功": False, "消息": "内容和类型字段必须是字符串", "id": None}
        
        # 调用数据层函数创建通告，传递所有参数
        结果 = await 数据库_异步创建通告(
            类型=类型, 
            标题=标题, 
            内容=内容,
            已发布=已发布,
            重要性=重要性,
            开始时间=开始时间,
            结束时间=结束时间,
            排序=排序,
            操作标识=操作标识
        )
        
        if 结果['成功']:
            安全日志器.info(f"管理员ID {用户id} 创建了通告: ID={结果['id']}, 类型={类型}, 标题={标题}, 操作标识={操作标识}")
            
            # 如果设置了已发布为True，且创建成功，记录发布时间
            if 已发布 and 结果['id']:
                try:
                    发布时间更新语句 = "UPDATE 通告 SET 发布时间 = NOW() WHERE id = $1 AND 发布时间 IS NULL"
                    await 异步连接池实例.执行更新(发布时间更新语句, [结果['id']])
                    安全日志器.info(f"已更新通告 ID={结果['id']} 的发布时间")
                except Exception as update_err:
                    错误日志器.warning(f"更新通告发布时间失败: {update_err}，这不影响通告创建")
        else:
            错误日志器.error(f"管理员ID {用户id} 创建通告失败: {结果['消息']}")
            
        return 结果
    except Exception as e:
        错误日志器.error(f"创建通告服务发生未知错误: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"创建通告时发生系统错误: {str(e)}", "id": None}


async def 异步更新通告(
    通告id: int, 
    类型: str, 
    标题: str, 
    内容: List[Dict[str, str]], 
    用户id: int, 
    操作标识: Optional[int] = None, 
    已发布: Optional[bool] = None, 
    重要性: Optional[int] = None, 
    开始时间: Optional[datetime] = None, 
    结束时间: Optional[datetime] = None,
    排序: Optional[int] = None # 新增
) -> Dict[str, Any]:
    """
    服务层函数：更新现有通告
    
    参数:
    通告id: int - 通告ID
    类型: str - 通告类型
    标题: str - 通告标题
    内容: List[Dict[str, str]] - 通告内容
    用户id: int - 更新通告的管理员ID
    操作标识: Optional[int] - 操作标识
    已发布: Optional[bool] - 是否已发布
    重要性: Optional[int] - 重要性级别
    开始时间: Optional[datetime] - 有效期开始时间
    结束时间: Optional[datetime] - 有效期结束时间
    排序: Optional[int] - 显示排序

    返回值:
    Dict[str, Any]: 包含操作结果的字典
    """
    try:
        # 参数验证
        if not 类型 or not 标题:
            return {"成功": False, "消息": "类型和标题不能为空"}
        
        if not 内容 or not isinstance(内容, list):
            return {"成功": False, "消息": "内容必须是有效的数组"}
        
        # 验证内容中的每个项目
        for 项目 in 内容:
            if not isinstance(项目, dict) or '内容' not in 项目 or '类型' not in 项目:
                return {"成功": False, "消息": "内容数组的每个项目必须包含'内容'和'类型'字段"}
            if not isinstance(项目['内容'], str) or not isinstance(项目['类型'], str):
                 return {"成功": False, "消息": "内容和类型字段必须是字符串"}
        
        # 调用数据层函数更新通告 (数据层现在处理所有可选字段)
        结果 = await 数据库_异步更新通告(
            通告id=通告id, 
            类型=类型, 
            标题=标题, 
            内容=内容, 
            操作标识=操作标识,
            已发布=已发布,
            重要性=重要性,
            开始时间=开始时间,
            结束时间=结束时间,
            排序=排序 # 新增传递
        )
        
        if 结果['成功']:
            安全日志器.info(f"管理员ID {用户id} 更新了通告: ID={通告id}, 类型={类型}, 标题={标题}, 操作标识={操作标识}, 已发布={已发布}, 重要性={重要性}")
        else:
            错误日志器.error(f"管理员ID {用户id} 更新通告失败: ID={通告id}, 错误: {结果['消息']}")
            
        return 结果
    except Exception as e:
        错误日志器.error(f"更新通告服务发生未知错误: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"更新通告时发生系统错误: {str(e)}"}


async def 异步删除通告(通告id: int, 用户id: int) -> Dict[str, Any]:
    """
    服务层函数：删除通告
    
    参数:
    通告id: int - 要删除的通告ID
    用户id: int - 执行删除操作的管理员ID
    
    返回值:
    Dict[str, Any]: 包含操作结果的字典
    """
    try:
        # 调用数据层函数删除通告 (数据层已包含存在性检查)
        结果 = await 数据库_异步删除通告(通告id)
        
        if 结果['成功']:
            安全日志器.info(f"管理员ID {用户id} 删除了通告: ID={通告id}")
        else:
            错误日志器.error(f"管理员ID {用户id} 删除通告失败: ID={通告id}, 错误: {结果['消息']}")
            
        return 结果
    except Exception as e:
        错误日志器.error(f"删除通告服务发生未知错误: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"删除通告时发生系统错误: {str(e)}"} 