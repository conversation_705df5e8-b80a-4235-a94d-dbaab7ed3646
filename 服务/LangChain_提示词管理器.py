"""
LangChain提示词管理器 - 动态提示词配置和管理

功能特性：
1. 系统提示词、用户提示词、角色设定的动态配置
2. 提示词模板管理和版本控制
3. 上下文变量注入和替换
4. 提示词优化和A/B测试
5. 多语言提示词支持
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from string import Template

from langchain.prompts import (
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
    MessagesPlaceholder
)

# 配置日志
提示词日志器 = logging.getLogger("LangChain.提示词管理器")
错误日志器 = logging.getLogger("LangChain.错误")


class 提示词类型(Enum):
    """提示词类型枚举"""
    系统提示词 = "system"
    用户提示词 = "user"
    助手提示词 = "assistant"
    角色设定 = "role"
    行为规范 = "behavior"
    任务指令 = "instruction"
    示例对话 = "example"


class 变量类型(Enum):
    """变量类型枚举"""
    文本 = "text"
    数字 = "number"
    布尔值 = "boolean"
    列表 = "list"
    对象 = "object"
    上下文 = "context"


@dataclass
class 提示词变量:
    """提示词变量定义"""
    变量名: str
    变量类型: 变量类型
    默认值: Any = None
    描述: str = ""
    必需: bool = False
    验证规则: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class 提示词模板:
    """提示词模板数据类"""
    模板id: str
    模板名称: str
    模板类型: 提示词类型
    模板内容: str
    变量列表: List[提示词变量]
    版本号: str = "1.0"
    语言: str = "zh-CN"
    标签: Optional[List[str]] = None
    创建时间: Optional[datetime] = None
    更新时间: Optional[datetime] = None
    使用次数: int = 0

    def __post_init__(self):
        if self.标签 is None:
            self.标签 = []
        if self.创建时间 is None:
            self.创建时间 = datetime.now()
        if self.更新时间 is None:
            self.更新时间 = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        数据 = asdict(self)
        数据['创建时间'] = self.创建时间.isoformat()
        数据['更新时间'] = self.更新时间.isoformat() if self.更新时间 else None
        数据['模板类型'] = self.模板类型.value
        数据['变量列表'] = [变量.to_dict() for 变量 in self.变量列表]
        return 数据


class LangChain提示词管理器:
    """LangChain提示词管理器"""
    
    def __init__(self):
        # 提示词存储
        self.系统提示词 = ""
        self.用户提示词 = ""
        self.角色设定 = ""
        self.行为规范 = ""
        self.任务指令 = ""
        
        # 模板管理
        self.提示词模板库: Dict[str, 提示词模板] = {}
        self.当前模板配置: Dict[str, str] = {}
        
        # 变量管理
        self.全局变量: Dict[str, Any] = {}
        self.上下文变量: Dict[str, Any] = {}
        
        # LangChain组件
        self.聊天提示词模板: Optional[ChatPromptTemplate] = None
        self.系统消息模板: Optional[SystemMessagePromptTemplate] = None
        self.用户消息模板: Optional[HumanMessagePromptTemplate] = None
        
        # 统计信息
        self.模板使用统计: Dict[str, int] = {}

    async def 初始化(self):
        """初始化提示词管理器"""
        try:
            # 加载预定义模板
            await self._加载预定义模板()
            
            # 设置默认全局变量
            await self._设置默认全局变量()
            
            提示词日志器.info("提示词管理器初始化完成")
            
        except Exception as e:
            错误日志器.error(f"提示词管理器初始化失败: {str(e)}", exc_info=True)
            raise

    async def _加载预定义模板(self):
        """加载预定义的提示词模板"""
        预定义模板 = [
            提示词模板(
                模板id="default_system",
                模板名称="默认系统提示词",
                模板类型=提示词类型.系统提示词,
                模板内容="你是一个友好、专业的AI助手。请根据用户的问题提供准确、有用的回答。",
                变量列表=[],
                标签=["默认", "通用"]
            ),
            提示词模板(
                模板id="customer_service",
                模板名称="客服助手",
                模板类型=提示词类型.系统提示词,
                模板内容="你是一个专业的客服助手，名字叫{assistant_name}。你的任务是帮助用户解决{company_name}相关的问题。请保持礼貌、耐心，并提供准确的信息。",
                变量列表=[
                    提示词变量("assistant_name", 变量类型.文本, "小助手", "助手名称", True),
                    提示词变量("company_name", 变量类型.文本, "我们公司", "公司名称", True)
                ],
                标签=["客服", "专业"]
            ),
            提示词模板(
                模板id="technical_expert",
                模板名称="技术专家",
                模板类型=提示词类型.系统提示词,
                模板内容="你是一个{domain}领域的技术专家。你拥有丰富的{domain}知识和实践经验。请用专业但易懂的方式回答用户的技术问题。",
                变量列表=[
                    提示词变量("domain", 变量类型.文本, "软件开发", "专业领域", True)
                ],
                标签=["技术", "专家"]
            ),
            提示词模板(
                模板id="creative_writer",
                模板名称="创意写作助手",
                模板类型=提示词类型.系统提示词,
                模板内容="你是一个富有创意的写作助手。你擅长{writing_style}风格的写作，能够创作{content_type}。请发挥你的创意，为用户提供高质量的写作内容。",
                变量列表=[
                    提示词变量("writing_style", 变量类型.文本, "优雅流畅", "写作风格", False),
                    提示词变量("content_type", 变量类型.文本, "各种类型的文章", "内容类型", False)
                ],
                标签=["创意", "写作"]
            )
        ]
        
        for 模板 in 预定义模板:
            self.提示词模板库[模板.模板id] = 模板
            self.模板使用统计[模板.模板id] = 0

    async def _设置默认全局变量(self):
        """设置默认全局变量"""
        self.全局变量.update({
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "current_date": datetime.now().strftime("%Y-%m-%d"),
            "assistant_version": "LangChain v1.0",
            "language": "zh-CN"
        })

    async def 设置提示词(self, 系统提示词: Optional[str] = None, 用户提示词: Optional[str] = None,
                        角色设定: Optional[str] = None, 行为规范: Optional[str] = None, 任务指令: Optional[str] = None):
        """设置各类提示词"""
        try:
            if 系统提示词 is not None:
                self.系统提示词 = 系统提示词
            if 用户提示词 is not None:
                self.用户提示词 = 用户提示词
            if 角色设定 is not None:
                self.角色设定 = 角色设定
            if 行为规范 is not None:
                self.行为规范 = 行为规范
            if 任务指令 is not None:
                self.任务指令 = 任务指令
            
            # 重新构建提示词模板
            await self._构建提示词模板()
            
            提示词日志器.info("提示词设置完成")
            
        except Exception as e:
            错误日志器.error(f"设置提示词失败: {str(e)}", exc_info=True)

    async def _构建提示词模板(self):
        """构建LangChain提示词模板"""
        try:
            # 构建完整的系统提示词
            完整系统提示词 = self._组合系统提示词()
            
            # 创建系统消息模板
            self.系统消息模板 = SystemMessagePromptTemplate.from_template(完整系统提示词)
            
            # 创建用户消息模板
            用户模板内容 = self.用户提示词 if self.用户提示词 else "{input}"
            self.用户消息模板 = HumanMessagePromptTemplate.from_template(用户模板内容)
            
            # 创建聊天提示词模板
            消息列表 = [
                self.系统消息模板,
                MessagesPlaceholder(variable_name="chat_history"),
                self.用户消息模板
            ]
            
            # 如果有上下文信息，添加上下文模板
            if "{context}" in 完整系统提示词 or "{context}" in 用户模板内容:
                上下文模板 = HumanMessagePromptTemplate.from_template(
                    "相关上下文信息：\n{context}\n\n用户问题：{input}"
                )
                消息列表[-1] = 上下文模板
            
            self.聊天提示词模板 = ChatPromptTemplate.from_messages(消息列表)
            
        except Exception as e:
            错误日志器.error(f"构建提示词模板失败: {str(e)}", exc_info=True)

    def _组合系统提示词(self) -> str:
        """组合完整的系统提示词"""
        组件列表 = []
        
        if self.系统提示词:
            组件列表.append(self.系统提示词)
        
        if self.角色设定:
            组件列表.append(f"角色设定：{self.角色设定}")
        
        if self.行为规范:
            组件列表.append(f"行为规范：{self.行为规范}")
        
        if self.任务指令:
            组件列表.append(f"任务指令：{self.任务指令}")
        
        return "\n\n".join(组件列表)

    async def 使用模板(self, 模板id: str, 变量值: Optional[Dict[str, Any]] = None) -> bool:
        """使用指定模板"""
        try:
            if 模板id not in self.提示词模板库:
                错误日志器.error(f"模板不存在: {模板id}")
                return False
            
            模板 = self.提示词模板库[模板id]
            
            # 处理变量替换
            处理后内容 = await self._处理模板变量(模板, 变量值 or {})
            
            # 根据模板类型设置对应的提示词
            if 模板.模板类型 == 提示词类型.系统提示词:
                self.系统提示词 = 处理后内容
            elif 模板.模板类型 == 提示词类型.用户提示词:
                self.用户提示词 = 处理后内容
            elif 模板.模板类型 == 提示词类型.角色设定:
                self.角色设定 = 处理后内容
            elif 模板.模板类型 == 提示词类型.行为规范:
                self.行为规范 = 处理后内容
            elif 模板.模板类型 == 提示词类型.任务指令:
                self.任务指令 = 处理后内容
            
            # 重新构建模板
            await self._构建提示词模板()
            
            # 更新使用统计
            self.模板使用统计[模板id] += 1
            模板.使用次数 += 1
            
            提示词日志器.info(f"模板应用成功: {模板id}")
            return True
            
        except Exception as e:
            错误日志器.error(f"使用模板失败 {模板id}: {str(e)}", exc_info=True)
            return False

    async def _处理模板变量(self, 模板: 提示词模板, 变量值: Dict[str, Any]) -> str:
        """处理模板中的变量替换 - 与智能体服务保持一致的处理逻辑"""
        try:
            内容 = 模板.模板内容

            # 合并变量值 - 优先级：传入变量 > 上下文变量 > 全局变量
            最终变量值 = self.全局变量.copy()
            最终变量值.update(self.上下文变量)

            if 变量值:
                最终变量值.update(变量值)

            # 添加系统标准变量
            最终变量值.update({
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "current_date": datetime.now().strftime("%Y-%m-%d"),
            })

            # 为模板变量设置默认值
            for 变量 in 模板.变量列表:
                if 变量.变量名 not in 最终变量值:
                    if 变量.默认值 is not None:
                        最终变量值[变量.变量名] = 变量.默认值
                    elif 变量.必需:
                        raise ValueError(f"必需变量未提供: {变量.变量名}")

            # 使用Template进行安全的变量替换
            模板对象 = Template(内容)
            处理结果 = 模板对象.safe_substitute(最终变量值)

            # 记录变量替换信息（调试模式）
            if 提示词日志器.isEnabledFor(logging.DEBUG):
                使用的变量 = [变量名 for 变量名 in 最终变量值.keys() if f"$变量名" in 内容 or f"${{变量名}}" in 内容]
                if 使用的变量:
                    提示词日志器.debug(f"🔄 模板变量替换: 模板ID={模板.模板id}, 使用变量={使用的变量}")

            return 处理结果

        except Exception as e:
            错误日志器.error(f"❌ 处理模板变量失败: {str(e)}", exc_info=True)
            return 模板.模板内容

    async def 构建LangChain模板(self, 模板id: str, 变量值: Dict[str, Any] = None) -> Optional[Any]:
        """构建LangChain标准的ChatPromptTemplate"""
        try:
            # 导入LangChain组件
            try:
                from langchain_core.prompts import ChatPromptTemplate
            except ImportError:
                提示词日志器.warning("LangChain不可用，无法构建标准模板")
                return None

            模板 = self.提示词模板库.get(模板id)
            if not 模板:
                提示词日志器.warning(f"模板不存在: {模板id}")
                return None

            # 处理变量替换
            处理后内容 = await self._处理模板变量(模板, 变量值 or {})

            # 根据模板类型构建不同的LangChain模板
            if 模板.模板类型 == 提示词类型.系统提示词:
                return ChatPromptTemplate.from_messages([
                    ("system", 处理后内容)
                ])
            elif 模板.模板类型 == 提示词类型.用户提示词:
                return ChatPromptTemplate.from_messages([
                    ("human", 处理后内容)
                ])
            elif 模板.模板类型 == 提示词类型.对话模板:
                # 对话模板可能包含多个消息
                return ChatPromptTemplate.from_messages([
                    ("system", "你是一个有用的助手。"),
                    ("human", 处理后内容)
                ])
            else:
                # 默认作为系统消息处理
                return ChatPromptTemplate.from_messages([
                    ("system", 处理后内容)
                ])

        except Exception as e:
            错误日志器.error(f"❌ 构建LangChain模板失败 {模板id}: {str(e)}", exc_info=True)
            return None

    async def 验证模板语法(self, 模板内容: str) -> Tuple[bool, str]:
        """验证模板语法的正确性"""
        try:
            from string import Template

            # 尝试创建Template对象
            模板对象 = Template(模板内容)

            # 尝试进行基本的变量替换测试
            测试变量 = {
                "test_var": "测试值",
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "current_date": datetime.now().strftime("%Y-%m-%d")
            }

            # 使用safe_substitute避免KeyError
            模板对象.safe_substitute(测试变量)

            return True, "模板语法验证通过"

        except Exception as e:
            return False, f"模板语法错误: {str(e)}"

    async def 添加模板(self, 模板: 提示词模板) -> bool:
        """添加新的提示词模板"""
        try:
            self.提示词模板库[模板.模板id] = 模板
            self.模板使用统计[模板.模板id] = 0
            
            # 保存到数据库
            await self._保存模板到数据库(模板)
            
            提示词日志器.info(f"模板添加成功: {模板.模板id}")
            return True
            
        except Exception as e:
            错误日志器.error(f"添加模板失败: {str(e)}", exc_info=True)
            return False

    async def _保存模板到数据库(self, 模板: 提示词模板):
        """保存模板到数据库"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
            
            插入SQL = """
            INSERT INTO LangChain_提示词模板表
            (模板id, 模板名称, 模板类型, 模板内容, 变量列表, 版本号, 语言, 标签, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (模板id) DO UPDATE SET
            模板内容 = EXCLUDED.模板内容,
            变量列表 = EXCLUDED.变量列表,
            版本号 = EXCLUDED.版本号,
            更新时间 = NOW()
            """
            
            参数 = (
                模板.模板id,
                模板.模板名称,
                模板.模板类型.value,
                模板.模板内容,
                json.dumps([变量.to_dict() for 变量 in 模板.变量列表], ensure_ascii=False),
                模板.版本号,
                模板.语言,
                json.dumps(模板.标签, ensure_ascii=False),
                模板.创建时间
            )
            
            await 异步连接池实例.执行更新(插入SQL, 参数)
            
        except Exception as e:
            错误日志器.error(f"保存模板到数据库失败: {str(e)}", exc_info=True)

    async def 设置变量(self, 变量名: str, 变量值: Any, 是否全局: bool = True):
        """设置变量值"""
        try:
            if 是否全局:
                self.全局变量[变量名] = 变量值
            else:
                self.上下文变量[变量名] = 变量值
            
            提示词日志器.debug(f"变量设置成功: {变量名} = {变量值}")
            
        except Exception as e:
            错误日志器.error(f"设置变量失败: {str(e)}", exc_info=True)

    async def 批量设置变量(self, 变量字典: Dict[str, Any], 是否全局: bool = True):
        """批量设置变量"""
        try:
            if 是否全局:
                self.全局变量.update(变量字典)
            else:
                self.上下文变量.update(变量字典)
            
            提示词日志器.debug(f"批量设置变量成功，共 {len(变量字典)} 个变量")
            
        except Exception as e:
            错误日志器.error(f"批量设置变量失败: {str(e)}", exc_info=True)

    async def 构建提示词模板(self) -> Optional[ChatPromptTemplate]:
        """构建并返回LangChain提示词模板"""
        if not self.聊天提示词模板:
            await self._构建提示词模板()
        return self.聊天提示词模板

    def 获取模板列表(self, 模板类型: Optional[提示词类型] = None, 标签: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取模板列表"""
        模板列表 = []
        
        for 模板 in self.提示词模板库.values():
            # 类型过滤
            if 模板类型 and 模板.模板类型 != 模板类型:
                continue
            
            # 标签过滤
            if 标签 and (模板.标签 is None or 标签 not in 模板.标签):
                continue
            
            模板信息 = 模板.to_dict()
            模板信息['使用统计'] = self.模板使用统计.get(模板.模板id, 0)
            模板列表.append(模板信息)
        
        return 模板列表

    def 获取当前配置(self) -> Dict[str, Any]:
        """获取当前提示词配置"""
        return {
            "系统提示词": self.系统提示词,
            "用户提示词": self.用户提示词,
            "角色设定": self.角色设定,
            "行为规范": self.行为规范,
            "任务指令": self.任务指令,
            "全局变量": self.全局变量,
            "上下文变量": self.上下文变量,
            "当前模板配置": self.当前模板配置
        }

    def 获取变量列表(self) -> Dict[str, Any]:
        """获取所有变量"""
        return {
            "全局变量": self.全局变量,
            "上下文变量": self.上下文变量
        }

    async def 清理上下文变量(self):
        """清理上下文变量"""
        self.上下文变量.clear()
        提示词日志器.debug("上下文变量已清理")

    def 获取统计信息(self) -> Dict[str, Any]:
        """获取提示词管理器统计信息"""
        return {
            "模板总数": len(self.提示词模板库),
            "全局变量数": len(self.全局变量),
            "上下文变量数": len(self.上下文变量),
            "模板使用统计": self.模板使用统计,
            "最常用模板": max(self.模板使用统计.items(), key=lambda x: x[1])[0] if self.模板使用统计 else None
        }
