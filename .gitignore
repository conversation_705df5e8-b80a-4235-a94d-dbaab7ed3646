# Dependencies
/node_modules

# Build output
/dist
/build
/out

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 寸止记忆模块
.cunzhi-memory
metadata.json

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-intermediate-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache file
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development
.env.production
.env.local
.env.*.local

# parcel-bundler cache files
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build output
.nuxt
dist

# Docusaurus build output
.docusaurus

# Gatsby build output
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# SvelteKit build output
.svelte-kit

# Svelte build output
.svelte

# Remix build output
.cache/
build/
public/build/

# Deno dependency directory
deno.lock

# Vite build output
dist
dist-ssr
*.js.map

# Local History for Visual Studio Code
.history/

# IDE/Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables
.env
.env.local
.env.*.local
.env.*
# IMPORTANT: Don't ignore .env.example or similar, as they are templates
!/.env.example
!/.env.sample

# Optional: If 'docs' is a generated documentation folder
# If 'docs' contains manually written documentation, DO NOT ignore it.
# /docs

# Other temporary or cache files
temp/
tmp/

# Specific to your screenshot, though covered by /dist already
# invite-INDEX/ (If this is a build output or temporary, otherwise don't ignore)
# public/ (Usually NOT ignored, as it contains static assets for deployment.
#          Only ignore if it's a build target for some reason.)