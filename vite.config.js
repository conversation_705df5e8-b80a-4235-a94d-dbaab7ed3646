import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    open: true,
    // 抑制过时的DOM事件警告
    hmr: {
      overlay: {
        warnings: false
      }
    },
    // 开发环境代理配置 - 统一代理规则，简化配置
    // 使用正则表达式匹配所有API路径，避免重复配置
    // 生产环境不使用代理，直接连接远程服务器
    proxy: {
      // 统一代理规则：匹配所有API相关路径，但排除前端路由
      // 注意：移除了register和login，因为它们是前端路由，不应该被代理
      '^/(order|api|wechat|workspace|user|kol|customer|sms_code|password|upload|get_permission|get_order|full-info|find-by-phone|smart-search|submit_contact|invite_kol|bind_shop|activate_code|update_password)': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        // 保持原路径不变
        rewrite: (path) => path,
        // 配置日志，便于调试
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`🔄 代理请求: ${req.method} ${req.url} -> ${options.target}${req.url}`)
          })
          proxy.on('error', (err, req, res) => {
            console.error(`❌ 代理错误: ${req.url}`, err.message)
          })
        }
      },
      // 团队管理API代理规则 - 精确匹配，避免前端路由冲突
      // 关键修复：使用$结尾确保精确匹配，避免前端路由被误代理
      // ✅ 代理: /team/detail (POST请求，后端API)
      // ❌ 不代理: /team/detail/9 (GET请求，前端路由)
      '^/team/(create|detail|members|invite|leave|dissolve|transfer-ownership|smart-leave)$': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`团队API代理: ${req.method} ${req.url} -> ${options.target}${req.url}`)
          })
          proxy.on('error', (err, req, res) => {
            console.error(`团队API代理错误: ${req.url}`, err.message)
          })
        }
      },
      // 团队嵌套路径API代理
      '^/team/(user|company|member|activity|talent|wechat-talent)/.+$': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`团队嵌套API代理: ${req.method} ${req.url} -> ${options.target}${req.url}`)
          })
          proxy.on('error', (err, req, res) => {
            console.error(`团队嵌套API代理错误: ${req.url}`, err.message)
          })
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    // 资源文件目录配置
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        // 优化代码分割，避免单个文件过大导致加载问题
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue', '@ant-design/icons-vue'],
          utils: ['axios']
        },
        // 确保文件名包含hash，避免缓存问题
        entryFileNames: 'assets/js/[name].[hash].js',
        chunkFileNames: 'assets/js/[name].[hash].js',
        assetFileNames: 'assets/[ext]/[name].[hash].[ext]'
      }
    }
  },
  // 设置基础路径 - 使用绝对路径避免深层路由时的资源加载问题
  // 修复客户邀请链接访问时的MIME类型错误
  base: '/'
}) 