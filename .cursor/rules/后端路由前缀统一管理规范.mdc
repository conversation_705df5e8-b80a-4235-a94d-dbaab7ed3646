---
description: 
globs: 
alwaysApply: false
---
# 后端路由前缀统一管理规范

## 🎯 核心原则

所有FastAPI路由的前缀（prefix）必须在 [main.py](mdc:limob-crm-front-end/invitation-system-backend/main.py) 中统一配置和管理，**禁止在各自的路由文件中设置prefix参数**。

## 📁 规范要求

### ✅ 正确做法：在main.py中统一配置

```python
# invitation-system-backend/main.py
路由配置 = [
    # 基础功能路由
    (用户路由, ["用户接口"]),
    (系统路由, ["系统接口"]),
    
    # 业务功能路由（带前缀）
    (团队管理路由, ["团队管理"], "/team"),
    (邀请管理路由, ["团队邀请管理"], "/team/invite"),
    (权限管理路由, ["团队权限管理"], "/team/permissions"),
    
    # AI功能路由
    (AI对话路由, ["AI对话接口"], "/ai"),
    
    # 管理功能路由
    (超级管理路由, ["超级管理接口"], "/admin"),
    (仪表板路由, ["仪表板"], "/dashboard"),
    
    # 外部服务路由
    (微信路由, ["微信"], "/wechat"),
    (外部服务路由, ["外部服务"], "/external"),
]
```

### ❌ 错误做法：在路由文件中设置前缀

```python
# 路由/团队管理.py - 错误示例
团队管理路由 = APIRouter(prefix="/team", tags=["团队管理"])  # ❌ 不要这样做

# 路由/微信路由.py - 错误示例  
微信路由 = APIRouter(prefix="/wechat")  # ❌ 不要这样做
```

### ✅ 路由文件的正确写法

```python
# 路由/团队管理.py - 正确示例
团队管理路由 = APIRouter(tags=["团队管理"])  # ✅ 只设置tags，不设置prefix

# 路由/微信路由.py - 正确示例
微信路由 = APIRouter()  # ✅ 不设置任何prefix
```

## 🏗️ 路由前缀设计规范

### 前缀命名原则
- **简短明确**：使用简洁的英文单词
- **层级清晰**：体现功能模块层级关系
- **避免冲突**：确保前缀唯一性

### 推荐的前缀结构
```
/team                    # 团队基础功能
├── /team/invite        # 团队邀请管理
├── /team/permissions   # 团队权限管理
└── /team/members       # 团队成员管理

/ai                     # AI相关功能
├── /ai/chat           # AI对话
└── /ai/packages       # AI套餐

/admin                  # 管理功能
├── /admin/users       # 用户管理
└── /admin/system      # 系统管理

/external               # 外部服务
├── /external/wechat   # 微信服务
└── /external/sms      # 短信服务
```

## 🔧 迁移指南

### 当前需要修正的路由文件

以下路由文件中存在prefix设置，需要移除：

1. **路由/订单.py** - 移除 `prefix="/order"`
2. **路由/系统更新.py** - 移除 `prefix="/system"`  
3. **路由/知识库路由.py** - 移除 `prefix="/knowledge"`
4. **路由/样品信息路由.py** - 移除 `prefix="/sample"`
5. **路由/报表路由.py** - 移除 `prefix="/report"`
6. **路由/微信路由.py** - 移除 `prefix="/wechat"`
7. **路由/团队管理.py** - 移除 `prefix="/team"`
8. **路由/商品路由.py** - 移除 `prefix="/products"`
9. **路由/仪表板路由.py** - 移除 `prefix="/dashboard"`
10. **路由/产品解析.py** - 移除 `prefix="/product"`

### 修正步骤

1. **修改路由文件**：移除APIRouter中的prefix参数
2. **更新main.py**：在路由配置中添加对应的前缀
3. **验证路径**：确保前后端接口路径匹配
4. **更新文档**：同步API文档和接口调用代码

## 📈 统一管理的优势

### 1. **集中管理**
- 所有API路径在一个文件中可见
- 便于规划和维护API结构
- 避免路径冲突和重复

### 2. **版本控制**
- API版本升级时统一调整前缀
- 便于API版本兼容性管理

### 3. **文档生成**
- FastAPI自动文档更清晰
- API分组更合理

### 4. **调试优化**
- 快速定位API路径问题
- 便于监控和日志分析

## ⚠️ 注意事项

1. **前后端协作**：修改前缀时必须同步更新前端调用路径
2. **渐进迁移**：建议分批次修正现有路由，避免大规模改动
3. **测试验证**：每次修改后确保相关功能正常工作
4. **文档同步**：及时更新API文档和接口说明

## 🎯 实施目标

通过统一路由前缀管理，实现：
- ✅ 路由配置标准化
- ✅ API结构清晰化  
- ✅ 维护成本最小化
- ✅ 团队协作高效化

**记住：所有新增路由都必须遵循此规范，在main.py中统一配置前缀！**

