---
description: 
globs: 
alwaysApply: true
---
# 工作区核心规范

## 🏗️ 项目概览

### 工作区结构
```
limob-crm-front-end/          # 前端项目 - Vue 3 + Ant Design Vue
invitation-system-backend/    # 后端项目 - Python FastAPI
```

### 技术栈
- **前端**：Vue 3 + Vite + Ant Design Vue + Pinia + Vue Router
- **后端**：Python FastAPI + MySQL + JWT + 异步编程

## 🎯 核心开发原则

### 1. 架构理解优先
- **编码前必读**：阅读相关项目文档和任务文件，了解项目历史和错误记录
- **避免冗余代码**：复用现有逻辑和组件，避免重复实现，通过查看整个项目文件来确认冗余和重复代码，删除无用的代码。
- **模块化设计**：高内聚、低耦合的代码组织

### 2. 命名规范统一
- **后端全面中文**：路由、函数、变量、数据库字段全部使用中文
- **响应数据中文化**：所有API响应数据字段必须使用中文命名，如`团队id`、`公司名称`、`用户角色`等，避免英文中文混用
- **数据库操作**：查询已存在字段时使用原始字段名，创建新字段使用中文
- **前后端数据一致性**：前端代码已适配中文字段名，后端响应数据格式与前端预期保持完全一致

### 3. 代码质量保证
- **类型安全**：前端JSDoc，后端Pydantic类型验证
- **错误处理**：统一的异常处理机制
- **性能优化**：合理使用缓存、分页、异步操作
- **安全规范**：输入验证、权限控制、数据加密


### 请严格遵循以下规则编写Vue 3组件​


## 🚨 编码前检查清单

### 必须执行的检查
- 修复报错时，一定要先了解相关文件，确保没有冗余代码
1. **查看项目文档**：阅读相关任务文件和项目文档
2. **API调用规范**：useApiService必须传入服务名称参数
3. **导入导出规范**：确保名称完全一致，遵循命名规范
4. **架构一致性**：前后端命名规范保持一致
5. **响应数据格式**：后端API响应数据字段必须使用中文，前端调用时直接使用中文字段名
6. 更新或者增加vue3时代码要添加详细的教学级别的中文注释
7. **禁止创建测试文件**：除非用户明确要求，否则不得自动创建任何测试文件或临时验证文件
### 常见错误预防
- 避免前后端调用接口路径不一致的问题
- 避免后端响应数据中英文字段名混用，统一使用中文字段名

### 提交前检查
- [ ] 代码符合ESLint/PEP8规范
- [ ] 无控制台警告或错误
- [ ] 所有功能正常运行
- [ ] 更新相关文档记录

## 🛠️ 开发工作流

1. **需求分析** → 确定前后端接口规范
2. **后端开发** → 实现API接口和业务逻辑
3. **前端开发** → 调用API并实现用户界面
4. **联调测试** → 确保前后端数据格式一致
5. **部署发布** → 遵循部署检查清单

- 如果有测试代码，在测试后记得删除测试代码。
- **严禁主动创建测试文件**：AI不应主动创建test_*.py、测试.py等任何形式的测试文件，用户会自行决定测试方式。

## ⚡ 性能与安全

### 性能优化
- **前端**：组件懒加载、API缓存、状态管理优化
- **后端**：数据库索引、异步处理、缓存策略

### 安全规范
- **认证授权**：JWT令牌、RBAC权限控制
- **数据安全**：输入验证、XSS防护、CSRF保护

## 📚 参考资源

- **数据库规范**：参考 [Mysql命名规则.mdc](mdc:invitation-system-backend/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/Mysql命名规则.mdc)
- **AI智能配置**：参考 [Cursor智能助手配置.mdc](mdc:invitation-system-backend/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/.cursor/rules/Cursor智能助手配置.mdc)
- **路由前缀管理**：参考 [后端路由前缀统一管理规范.mdc](mdc:invitation-system-backend/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/.cursor/rules/后端路由前缀统一管理规范.mdc)
- **前后端协作**：参考 [前后端协作规范.mdc](mdc:invitation-system-backend/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/limob-crm-front-end/前后端协作规范.mdc)

## 🎖️ 质量目标

- 重复错误数量逐月下降
- 开发效率逐步提升  
- 代码质量持续改善
- 团队协作更加顺畅

**记住：预防错误比修复错误更重要！**


