#!/usr/bin/env python3
"""
智能体自动参数注入测试脚本

专注测试智能体调用"更新微信好友下次沟通时间"工具的自动参数注入功能。
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 服务.LangChain_智能体服务 import LangChain智能体服务实例


async def 测试自动参数注入():
    """测试智能体调用更新微信好友下次沟通时间工具的自动参数注入功能"""
    try:
        print("🚀 开始测试自动参数注入功能...")
        print("📋 测试目标: 更新微信好友下次沟通时间")
        print(
            "📋 自定义变量: 我方微信号id=6, 识别id=1, 下次沟通时间=2024-09-31 14:30:25"
        )
        print()

        # 调用智能体对话
        print("🔄 调用智能体对话...")
        对话结果 = await <PERSON><PERSON><PERSON>n智能体服务实例.智能体对话(
            智能体id=5,
            用户表id=3,
            用户消息="请更新微信好友下次沟通时间",
            会话id="test-update-wechat-friend-time",
            自定义变量={
                "我方微信号id": 6,
                "识别id": 1,
                "下次沟通时间": "2024-09-31 14:30:25",
            },
        )

        print("📊 对话结果:")
        print(f"   状态码: {对话结果.get('status')}")
        print(f"   消息: {对话结果.get('message', 'N/A')}")

        if 对话结果.get("status") == 100:
            data = 对话结果.get("data", {})
            智能体回复 = data.get("智能体回复", "")
            工具使用信息 = data.get("工具使用信息", [])

            print("✅ 智能体回复:")
            print(智能体回复)
            print()

            # 查找目标工具调用
            目标工具 = None
            for 工具信息 in 工具使用信息:
                if 工具信息.get("工具名称") == "更新微信好友下次沟通时间":
                    目标工具 = 工具信息
                    break

            if 目标工具:
                工具状态 = 目标工具.get("调用状态", "N/A")
                工具参数 = 目标工具.get("调用参数", {})

                print("🎯 工具调用记录:")
                print("   工具名称: 更新微信好友下次沟通时间")
                print(f"   调用状态: {工具状态}")
                print(f"   传递参数: {工具参数}")
                print()

                # 验证参数注入
                验证结果 = []
                期望参数 = {
                    "我方微信号id": 6,
                    "识别id": 1,
                    "下次沟通时间": "2024-09-31 14:30:25",
                }

                for 参数名, 期望值 in 期望参数.items():
                    实际值 = 工具参数.get(参数名)
                    if 实际值 == 期望值:
                        验证结果.append(f"✅ {参数名}: {实际值}")
                    else:
                        验证结果.append(f"❌ {参数名}: 期望 {期望值}, 实际 {实际值}")

                if "用户id" in 工具参数:
                    验证结果.append(f"✅ 用户id: {工具参数.get('用户id')} (自动注入)")

                print("🔍 参数验证结果:")
                for 结果 in 验证结果:
                    print(f"   {结果}")

                # 判断测试结果
                if all("✅" in 结果 for 结果 in 验证结果):
                    print("\n🎉 自动参数注入测试成功！")
                    print("   ✅ 工具成功调用")
                    print("   ✅ 参数正确传递")
                    print("   ✅ 自定义变量自动注入")
                    print("   ✅ 用户ID安全隔离")
                else:
                    print("\n⚠️ 部分参数验证失败，请检查配置")

            else:
                print("❌ 未找到目标工具调用记录")
                if len(工具使用信息) > 0:
                    print("📋 实际调用的工具:")
                    for 工具信息 in 工具使用信息:
                        print(f"   - {工具信息.get('工具名称', 'N/A')}")
        else:
            print(f"❌ 对话失败: {对话结果}")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 60)
    print("智能体自动参数注入测试")
    print("=" * 60)
    print()

    await 测试自动参数注入()

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
