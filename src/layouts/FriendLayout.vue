<template>
  <div class="friend-layout">
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 侧边导航栏 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <!-- 侧边栏标题区域 -->
        <div class="sidebar-title">
          <div class="title-content">
            <h3 v-if="!sidebarCollapsed">好友管理</h3>
            <a-button
              type="text"
              @click="toggleSidebar"
              class="collapse-btn"
            >
              <template #icon>
                <MenuFoldOutlined v-if="!sidebarCollapsed" />
                <MenuUnfoldOutlined v-else />
              </template>
            </a-button>
          </div>
        </div>

        <!-- 自定义菜单实现，避免Ant Design Vue的ResizeObserver问题 -->
        <div v-if="isMounted" class="custom-menu">
          <div
            v-for="menuItem in menuItems"
            :key="menuItem.key"
            :class="[
              'custom-menu-item',
              { 'active': selectedKeys.includes(menuItem.key) },
              { 'collapsed': sidebarCollapsed }
            ]"
            @click="handleCustomMenuClick(menuItem.key)"
          >
            <div class="menu-item-content">
              <span class="menu-icon">
                <component :is="getIconComponent(menuItem.icon)" />
              </span>
              <span v-if="!sidebarCollapsed" class="menu-label">{{ menuItem.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <router-view v-if="isMounted && !isDestroyed" />
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  WechatOutlined,
  TeamOutlined,
  ProjectOutlined,
  BarChartOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 组件状态管理
const isMounted = ref(false)
const isDestroyed = ref(false)

const router = useRouter()
const route = useRoute()

// 响应式数据
const sidebarCollapsed = ref(false)
const selectedKeys = ref(['overview'])

// 菜单项配置
const menuItems = ref([
  {
    key: 'overview',
    icon: 'DashboardOutlined',
    label: '概览'
  },
  {
    key: 'wechat-accounts',
    icon: 'WechatOutlined',
    label: '微信账号'
  },
  {
    key: 'friend-list',
    icon: 'TeamOutlined',
    label: '好友列表'
  },
  {
    key: 'progress-management',
    icon: 'ProjectOutlined',
    label: '对接进度'
  },
  {
    key: 'auto-add',
    icon: 'PlusOutlined',
    label: '自动添加'
  }
])

// 路由名称映射
const routeMap = {
  'FriendOverview': 'overview',
  'FriendWechatAccounts': 'wechat-accounts',
  'FriendList': 'friend-list',
  'FriendProgressManagement': 'progress-management',
  'AutoAddFriends': 'auto-add'
}

// 页面名称映射已移除，如需要可重新添加

// 图标组件映射
const iconComponents = {
  DashboardOutlined,
  WechatOutlined,
  TeamOutlined,
  ProjectOutlined,
  BarChartOutlined,
  PlusOutlined
}

// 获取图标组件
const getIconComponent = (iconName) => {
  return iconComponents[iconName] || DashboardOutlined
}

// 计算属性已移除，如需要可重新添加

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 更新选中的菜单项
const updateSelectedKeys = () => {
  if (isDestroyed.value || !isMounted.value) return

  try {
    const routeName = route.name
    const key = routeMap[routeName]
    if (key && !isDestroyed.value && isMounted.value) {
      selectedKeys.value = [key]
    }
  } catch (error) {
    console.warn('更新菜单选中状态时出现警告:', error)
  }
}

// 防抖的更新函数
const debouncedUpdateSelectedKeys = debounce(updateSelectedKeys, 50)

// 监听路由变化 - 只在组件挂载后开始监听
let routeWatcher = null

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// handleMenuClick函数已移除，使用handleCustomMenuClick代替

// 自定义菜单点击处理
const handleCustomMenuClick = (key) => {
  if (isDestroyed.value || !isMounted.value) return

  selectedKeys.value = [key]

  const routeNames = {
    'overview': 'FriendOverview',
    'wechat-accounts': 'FriendWechatAccounts',
    'friend-list': 'FriendList',
    'progress-management': 'FriendProgressManagement',
    'auto-add': 'AutoAddFriends'
  }

  const routeName = routeNames[key]
  if (routeName && !isDestroyed.value && isMounted.value) {
    router.push({ name: routeName })
  }
}




// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  isMounted.value = true

  // 在组件挂载后开始监听路由变化
  routeWatcher = watch(() => route.name, debouncedUpdateSelectedKeys, { immediate: true })
})

// 组件卸载时清理
onBeforeUnmount(() => {
  isDestroyed.value = true
  isMounted.value = false

  // 停止路由监听
  if (routeWatcher) {
    routeWatcher()
    routeWatcher = null
  }

  // 清理响应式数据
  try {
    sidebarCollapsed.value = false
    selectedKeys.value = ['overview']
  } catch (error) {
    // 静默处理清理过程中的错误
    console.warn('组件清理过程中的警告:', error)
  }
})
</script>

<style scoped>
.friend-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}



/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 256px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 80px;
}

/* 侧边栏标题区域 */
.sidebar-title {
  height: 56px;
  background: #fafafa;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sidebar-title h3 {
  color: #262626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.collapse-btn {
  color: #666;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.collapse-btn:hover {
  color: #1890ff;
  background-color: #f0f0f0;
}

/* 自定义菜单样式 - 替代Ant Design Menu避免ResizeObserver问题 */
.custom-menu {
  height: calc(100vh - 64px - 56px);
  overflow-y: auto;
  padding: 8px 0;
}

.custom-menu-item {
  position: relative;
  margin: 0 8px 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.custom-menu-item:hover {
  background-color: #f0f0f0;
}

.custom-menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.custom-menu-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
  border-radius: 1.5px 0 0 1.5px;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 48px;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  font-size: 16px;
}

.custom-menu-item.collapsed .menu-icon {
  margin-right: 0;
}

.menu-label {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f5f5;
  /* 优化布局稳定性 - 防止内容变化时的移动 */
  min-height: calc(100vh - 64px); /* 固定最小高度，减去顶部导航高度 */
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 确保router-view内容稳定 */
.content-area > * {
  flex: 1;
  min-height: 0; /* 允许子元素收缩 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 999;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }
  
  .sidebar.collapsed {
    left: -256px;
  }
  
  .content-area {
    margin-left: 0;
  }
}
</style> 