<template>
  <div class="talent-layout" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <!-- 左侧导航栏 -->
    <div class="talent-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <!-- 侧边栏标题区域 -->
      <div class="sidebar-title">
        <div class="title-content">
          <h3 v-if="!sidebarCollapsed">达人管理</h3>
          <a-button
            type="text"
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <template #icon>
              <MenuFoldOutlined v-if="!sidebarCollapsed" />
              <MenuUnfoldOutlined v-else />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 自定义菜单实现，避免Ant Design Vue的ResizeObserver问题 -->
      <div class="custom-talent-menu">
        <div
          v-for="menuItem in talentMenuItems"
          :key="menuItem.key"
          :class="[
            'custom-talent-menu-item',
            { 'active': selectedKeys.includes(menuItem.key) },
            { 'collapsed': sidebarCollapsed }
          ]"
          @click="handleCustomTalentMenuClick(menuItem.key)"
        >
          <div class="talent-menu-item-content">
            <span class="talent-menu-icon">
              <component :is="getTalentIconComponent(menuItem.icon)" />
            </span>
            <span v-if="!sidebarCollapsed" class="talent-menu-label">{{ menuItem.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧主要内容区域 -->
    <div class="talent-main">
      <!-- 内容区域 -->
      <div class="talent-content">
        <router-view />
      </div>
    </div>

    <!-- 小屏幕下侧边栏收起时的浮动展开按钮 -->
    <a-button
      v-if="sidebarCollapsed"
      type="primary"
      @click="toggleSidebar"
      class="floating-menu-btn"
    >
      <template #icon>
        <MenuUnfoldOutlined />
      </template>
    </a-button>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  UserOutlined,
  TeamOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  ContactsOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单项
const activeTab = ref('my-talents')
const selectedKeys = ref(['my-talents'])

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 达人菜单项配置
const talentMenuItems = ref([
    {
    key: 'my-talents',
    icon: 'UserOutlined',
    label: '我的达人'
  },
  {
    key: 'contact-info',
    icon: 'ContactsOutlined',
    label: '联系方式'
  },
  {
    key: 'team-talents',
    icon: 'TeamOutlined',
    label: '团队达人'
  },
  {
    key: 'talent-pool',
    icon: 'GlobalOutlined',
    label: '达人公海'
  },
  {
    key: 'leads-center',
    icon: 'DatabaseOutlined',
    label: '线索大全'
  }
])

// 达人图标组件映射
const talentIconComponents = {
  UserOutlined,
  TeamOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  ContactsOutlined
}

// 获取达人图标组件
const getTalentIconComponent = (iconName) => {
  return talentIconComponents[iconName] || UserOutlined
}

// 菜单配置
const menuConfig = {
  'my-talents': { title: '我的达人', path: '/talent/my-talents' },
  'team-talents': { title: '团队达人', path: '/talent/team-talents' },
  'talent-pool': { title: '达人公海', path: '/talent/talent-pool' },
  'leads-center': { title: '线索大全', path: '/talent/leads-center' },
  'contact-info': { title: '联系方式', path: '/talent/contact-info' }
}

// 自定义达人菜单点击处理
const handleCustomTalentMenuClick = (key) => {
  activeTab.value = key
  selectedKeys.value = [key]
  const targetPath = menuConfig[key]?.path
  if (targetPath && route.path !== targetPath) {
    router.push(targetPath)
  }
}

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 检查屏幕尺寸并设置侧边栏状态
const checkScreenSize = () => {
  const isMobile = window.innerWidth <= 768
  if (isMobile) {
    sidebarCollapsed.value = true
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkScreenSize()
}

// 组件挂载时检查屏幕尺寸
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听路由变化，更新激活菜单项
watch(() => route.path, (newPath) => {
  const menuKey = Object.keys(menuConfig).find(key =>
    menuConfig[key].path === newPath
  )
  if (menuKey) {
    activeTab.value = menuKey
    selectedKeys.value = [menuKey]
  }
}, { immediate: true })

defineOptions({
  name: 'TalentLayout'
})
</script>

<style scoped>
.talent-layout {
  display: flex;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 左侧导航栏 */
.talent-sidebar {
  width: 256px;
  flex-shrink: 0;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: width 0.3s ease;
  overflow: hidden;
}

.talent-sidebar.collapsed {
  width: 80px;
}

/* 侧边栏标题区域 */
.sidebar-title {
  height: 56px;
  background: #fafafa;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sidebar-title h3 {
  color: #262626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.collapse-btn {
  color: #666;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.collapse-btn:hover {
  color: #1890ff;
  background-color: #f0f0f0;
}

/* 右侧主要内容区域 */
.talent-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px 24px 24px 24px;
  background: #ffffff;
}



/* 自定义达人菜单样式 - 替代Ant Design Menu避免ResizeObserver问题 */
.custom-talent-menu {
  height: calc(100vh - 64px - 56px);
  overflow-y: auto;
  padding: 8px 0;
}

.custom-talent-menu-item {
  position: relative;
  margin: 0 8px 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.custom-talent-menu-item:hover {
  background-color: #f0f0f0;
}

.custom-talent-menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.custom-talent-menu-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
  border-radius: 1.5px 0 0 1.5px;
}

.talent-menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 48px;
}

.talent-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  font-size: 16px;
}

.custom-talent-menu-item.collapsed .talent-menu-icon {
  margin-right: 0;
}

.talent-menu-label {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 内容区域 */
.talent-content {
  flex: 1;
  background: transparent;
  min-height: 600px;
}

/* 浮动菜单按钮 */
.floating-menu-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-layout {
    position: relative;
    overflow-x: hidden;
  }

  .talent-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .talent-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .talent-main {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 16px;
    position: relative;
    z-index: 1;
  }

  .floating-menu-btn {
    display: block;
  }

  /* 添加遮罩层，当侧边栏展开时 */
  .talent-layout:not(.sidebar-collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }
}
</style>