/**
 * 数据导出工具
 * 支持导出Excel、CSV等格式
 */

/**
 * 导出数据到Excel文件
 * @param {Array} data - 要导出的数据数组
 * @param {string} filename - 文件名（不含扩展名）
 * @param {Object} options - 导出选项
 */
export function exportToExcel(data, filename = '导出数据', options = {}) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.warn('导出数据为空')
    return
  }

  try {
    // 创建CSV内容
    const csvContent = convertToCSV(data, options)
    
    // 创建Blob对象
    const blob = new Blob(['\ufeff' + csvContent], { 
      type: 'text/csv;charset=utf-8;' 
    })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    URL.revokeObjectURL(url)
    
    console.log(`文件 ${filename}.csv 导出成功`)
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw new Error('导出失败，请重试')
  }
}

/**
 * 将数据转换为CSV格式
 * @param {Array} data - 数据数组
 * @param {Object} options - 转换选项
 * @returns {string} CSV格式字符串
 */
function convertToCSV(data, options = {}) {
  const { 
    delimiter = ',',
    includeHeaders = true,
    customHeaders = null
  } = options

  if (!data || data.length === 0) {
    return ''
  }

  // 获取表头
  let headers = []
  if (customHeaders && Array.isArray(customHeaders)) {
    headers = customHeaders
  } else {
    headers = Object.keys(data[0])
  }

  // 构建CSV内容
  let csvContent = ''

  // 添加表头
  if (includeHeaders) {
    csvContent += headers.map(header => escapeCSVField(header)).join(delimiter) + '\n'
  }

  // 添加数据行
  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header]
      return escapeCSVField(value)
    })
    csvContent += values.join(delimiter) + '\n'
  })

  return csvContent
}

/**
 * 转义CSV字段
 * @param {any} field - 字段值
 * @returns {string} 转义后的字段值
 */
function escapeCSVField(field) {
  if (field === null || field === undefined) {
    return ''
  }

  let value = String(field)

  // 如果包含逗号、引号或换行符，需要用引号包围
  if (value.includes(',') || value.includes('"') || value.includes('\n') || value.includes('\r')) {
    // 转义引号
    value = value.replace(/"/g, '""')
    // 用引号包围
    value = `"${value}"`
  }

  return value
}

/**
 * 导出JSON数据
 * @param {any} data - 要导出的数据
 * @param {string} filename - 文件名
 */
export function exportToJSON(data, filename = '导出数据') {
  try {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.json`)
    link.style.visibility = 'hidden'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
    
    console.log(`文件 ${filename}.json 导出成功`)
  } catch (error) {
    console.error('导出JSON失败:', error)
    throw new Error('导出失败，请重试')
  }
}

/**
 * 导出文本文件
 * @param {string} content - 文本内容
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function exportToText(content, filename = '导出文本', mimeType = 'text/plain') {
  try {
    const blob = new Blob([content], { type: mimeType })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.txt`)
    link.style.visibility = 'hidden'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
    
    console.log(`文件 ${filename}.txt 导出成功`)
  } catch (error) {
    console.error('导出文本失败:', error)
    throw new Error('导出失败，请重试')
  }
}

/**
 * 批量导出多个文件
 * @param {Array} files - 文件数组，每个文件包含 {data, filename, type}
 */
export async function batchExport(files) {
  if (!files || !Array.isArray(files) || files.length === 0) {
    console.warn('没有要导出的文件')
    return
  }

  try {
    for (const file of files) {
      const { data, filename, type = 'excel' } = file
      
      switch (type.toLowerCase()) {
        case 'excel':
        case 'csv':
          exportToExcel(data, filename)
          break
        case 'json':
          exportToJSON(data, filename)
          break
        case 'text':
        case 'txt':
          exportToText(data, filename)
          break
        default:
          console.warn(`不支持的导出类型: ${type}`)
      }
      
      // 添加延迟避免浏览器阻止多个下载
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    console.log(`批量导出完成，共 ${files.length} 个文件`)
  } catch (error) {
    console.error('批量导出失败:', error)
    throw new Error('批量导出失败，请重试')
  }
}

/**
 * 格式化导出数据
 * 将复杂对象转换为适合导出的扁平结构
 * @param {Array} data - 原始数据
 * @param {Object} fieldMapping - 字段映射配置
 * @returns {Array} 格式化后的数据
 */
export function formatExportData(data, fieldMapping = {}) {
  if (!data || !Array.isArray(data)) {
    return []
  }

  return data.map(item => {
    const formattedItem = {}
    
    // 如果有字段映射配置，按配置处理
    if (Object.keys(fieldMapping).length > 0) {
      Object.entries(fieldMapping).forEach(([key, config]) => {
        if (typeof config === 'string') {
          // 简单映射
          formattedItem[key] = item[config] || ''
        } else if (typeof config === 'object') {
          // 复杂映射
          const { field, formatter, defaultValue = '' } = config
          let value = item[field] || defaultValue
          
          // 应用格式化函数
          if (formatter && typeof formatter === 'function') {
            value = formatter(value, item)
          }
          
          formattedItem[key] = value
        }
      })
    } else {
      // 没有映射配置，直接复制所有字段
      Object.assign(formattedItem, item)
    }
    
    return formattedItem
  })
}

/**
 * 常用的数据格式化函数
 */
export const formatters = {
  // 日期格式化
  date: (value) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleDateString()
  },
  
  // 日期时间格式化
  datetime: (value) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleString()
  },
  
  // 数组转字符串
  arrayToString: (value, separator = ', ') => {
    if (!Array.isArray(value)) return ''
    return value.join(separator)
  },
  
  // 布尔值转文本
  booleanToText: (value, trueText = '是', falseText = '否') => {
    return value ? trueText : falseText
  },
  
  // 数字格式化
  number: (value, decimals = 2) => {
    if (typeof value !== 'number') return ''
    return value.toFixed(decimals)
  },
  
  // 货币格式化
  currency: (value, symbol = '¥') => {
    if (typeof value !== 'number') return ''
    return `${symbol}${value.toFixed(2)}`
  }
}
