/**
 * HTTP请求工具
 * 基于axios封装的统一请求客户端
 */

import axios from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/store/user'
import { API_CONFIG } from '@/config'

// 创建axios实例
const request = axios.create({
  baseURL: API_CONFIG.getBaseURL(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log(`🌐 API请求: ${config.method?.toUpperCase()} ${config.url}`)
    }

    return config
  },
  (error) => {
    console.error('❌ 请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response

    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, data)
    }

    // 统一处理响应格式
    if (data && typeof data === 'object') {
      // 检查是否为标准响应格式
      if ('状态码' in data) {
        return {
          状态码: data.状态码,
          消息: data.消息 || '',
          数据: data.数据 || null,
          success: [0, 1, 100].includes(data.状态码)
        }
      }
      
      // 兼容其他格式
      if ('status' in data) {
        return {
          状态码: data.status,
          消息: data.message || '',
          数据: data.data || null,
          success: [0, 1, 100].includes(data.status)
        }
      }
    }

    // 直接返回原始数据
    return data
  },
  (error) => {
    console.error('❌ API请求失败:', error)

    let errorMessage = '网络请求失败'
    
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录'
          // 清除用户信息并跳转到登录页
          const userStore = useUserStore()
          userStore.logout()
          break
        case 403:
          errorMessage = '没有权限访问该资源'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.message || data?.消息 || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 请求配置错误
      errorMessage = '请求配置错误: ' + error.message
    }

    // 显示错误提示
    message.error(errorMessage)

    // 返回标准错误格式
    return Promise.reject({
      状态码: error.response?.status || -1,
      消息: errorMessage,
      数据: null,
      success: false,
      originalError: error
    })
  }
)

export default request
