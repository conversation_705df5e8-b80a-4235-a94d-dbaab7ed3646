import { 处理错误, 错误类型, 错误严重程度 } from './errorHandler'

/**
 * 统一API响应处理工具
 * 提供标准化的API响应处理和错误处理
 * 
 * 功能特性：
 * - 统一的响应数据提取
 * - 标准化的成功/失败判断
 * - 分页数据处理
 * - 表单提交响应处理
 */

/**
 * 判断API响应是否成功
 * @param {Object} response - API响应对象
 * @returns {boolean} 是否成功
 */
export const 判断API成功 = (response) => {
  if (!response || typeof response !== 'object') {
    return false
  }
  
  // 支持多种成功状态码格式
  return response.status === 100 || 
         response.status === 1 || 
         response.status === 0 ||
         response.code === 200 ||
         response.success === true
}

/**
 * 获取API响应数据
 * @param {Object} response - API响应对象
 * @returns {any} 响应数据
 */
export const 获取API数据 = (response) => {
  if (!response || typeof response !== 'object') {
    return null
  }
  
  // 支持多种数据字段格式
  return response.data || 
         response.result || 
         response.content || 
         response.payload ||
         response
}

/**
 * 获取API响应消息
 * @param {Object} response - API响应对象
 * @returns {string} 响应消息
 */
export const 获取API消息 = (response) => {
  if (!response || typeof response !== 'object') {
    return ''
  }
  
  return response.message || 
         response.msg || 
         response.info || 
         response.description ||
         ''
}

/**
 * 调试API响应（开发环境）
 * @param {Object} response - API响应对象
 * @param {string} context - 上下文信息
 */
export const 调试API响应 = (response, context = '') => {
  if (!import.meta.env.DEV) {
    return
  }
  
  const 是成功 = 判断API成功(response)
  const 数据 = 获取API数据(response)
  const 消息 = 获取API消息(response)
  
  console.group(`🔍 API响应调试 ${context ? `[${context}]` : ''}`)
  console.log('响应状态:', 是成功 ? '✅ 成功' : '❌ 失败')
  console.log('原始响应:', response)
  console.log('提取数据:', 数据)
  console.log('响应消息:', 消息)
  console.groupEnd()
}

/**
 * 处理分页响应数据
 * @param {Object} response - API响应对象
 * @param {string} listKey - 列表数据的键名，默认自动检测
 * @returns {Object} 标准化的分页数据
 */
export const 处理分页响应 = (response, listKey = '') => {
  try {
    if (!判断API成功(response)) {
      throw new Error(获取API消息(response) || '获取数据失败')
    }
    
    const 数据 = 获取API数据(response)
    if (!数据 || typeof 数据 !== 'object') {
      throw new Error('响应数据格式错误')
    }
    
    // 自动检测列表数据键名
    const 可能的列表键名 = [
      listKey,
      'list', 'data', 'items', 'records', 'results',
      '列表', '数据', '记录', '结果'
    ].filter(Boolean)
    
    let 列表数据 = null
    let 找到的键名 = ''
    
    for (const 键名 of 可能的列表键名) {
      if (Array.isArray(数据[键名])) {
        列表数据 = 数据[键名]
        找到的键名 = 键名
        break
      }
    }
    
    // 如果没找到列表，检查数据本身是否为数组
    if (!列表数据 && Array.isArray(数据)) {
      列表数据 = 数据
      找到的键名 = 'root'
    }
    
    if (!列表数据) {
      console.warn('⚠️ 未找到列表数据，可用键名:', Object.keys(数据))
      列表数据 = []
    }
    
    // 提取分页信息
    const 分页信息 = {
      总数: 数据.total || 数据.count || 数据.totalCount || 数据['总数'] || 数据['总计'] || 0,
      当前页: 数据.page || 数据.currentPage || 数据.pageNum || 数据['当前页'] || 数据['页码'] || 1,
      每页数量: 数据.pageSize || 数据.size || 数据.limit || 数据['每页数量'] || 数据['页面大小'] || 10,
      总页数: 数据.totalPages || 数据.pages || 数据['总页数'] || 0
    }
    
    // 如果没有总页数，尝试计算
    if (!分页信息.总页数 && 分页信息.总数 && 分页信息.每页数量) {
      分页信息.总页数 = Math.ceil(分页信息.总数 / 分页信息.每页数量)
    }
    
    const 结果 = {
      success: true,
      list: 列表数据,
      total: 分页信息.总数,
      page: 分页信息.当前页,
      pageSize: 分页信息.每页数量,
      totalPages: 分页信息.总页数,
      message: 获取API消息(response) || '获取数据成功',
      // 调试信息
      _debug: import.meta.env.DEV ? {
        找到的列表键名: 找到的键名,
        原始数据键名: Object.keys(数据),
        分页信息来源: 分页信息
      } : undefined
    }
    
    if (import.meta.env.DEV) {
      console.log('📄 分页数据处理结果:', 结果)
    }
    
    return 结果
  } catch (error) {
    console.error('❌ 分页响应处理失败:', error)
    return {
      success: false,
      list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      totalPages: 0,
      message: error.message || '处理分页数据失败',
      error: error
    }
  }
}

/**
 * 处理表单提交响应
 * @param {Object} response - API响应对象
 * @param {string} successMessage - 成功消息
 * @param {string} failureMessage - 失败消息
 * @returns {Object} 标准化的表单响应
 */
export const 处理表单响应 = (response, successMessage = '操作成功', failureMessage = '操作失败') => {
  try {
    const 是成功 = 判断API成功(response)
    const 数据 = 获取API数据(response)
    const 响应消息 = 获取API消息(response)
    
    if (是成功) {
      return {
        success: true,
        data: 数据,
        message: 响应消息 || successMessage
      }
    } else {
      throw new Error(响应消息 || failureMessage)
    }
  } catch (error) {
    console.error('❌ 表单响应处理失败:', error)
    return {
      success: false,
      data: null,
      message: error.message || failureMessage,
      error: error
    }
  }
}

/**
 * 创建API请求包装器
 * @param {Function} apiFunction - API请求函数
 * @param {string} context - 上下文信息
 * @param {Object} options - 选项配置
 * @returns {Function} 包装后的API函数
 */
export const 创建API请求包装器 = (apiFunction, context = '', options = {}) => {
  const {
    显示成功提示 = false,
    显示错误提示 = true,
    成功消息 = '操作成功',
    错误消息 = '操作失败',
    使用通知 = false,
    记录日志 = true
  } = options
  
  return async (...args) => {
    try {
      const 开始时间 = Date.now()
      
      if (import.meta.env.DEV) {
        console.log(`📤 API请求开始 [${context}]:`, args)
      }
      
      const response = await apiFunction(...args)
      
      const 耗时 = Date.now() - 开始时间
      
      if (import.meta.env.DEV) {
        console.log(`✅ API请求完成 [${context}] 耗时: ${耗时}ms`)
        调试API响应(response, context)
      }
      
      // 检查响应是否成功
      if (!判断API成功(response)) {
        const 错误消息内容 = 获取API消息(response) || 错误消息
        throw new Error(错误消息内容)
      }
      
      // 显示成功提示
      if (显示成功提示) {
        const 成功消息内容 = 获取API消息(response) || 成功消息
        if (使用通知) {
          import('ant-design-vue').then(({ notification }) => {
            notification.success({
              message: '操作成功',
              description: 成功消息内容,
              duration: 3
            })
          })
        } else {
          import('ant-design-vue').then(({ message }) => {
            message.success(成功消息内容)
          })
        }
      }
      
      return response
    } catch (error) {
      const 耗时 = Date.now() - (args._startTime || Date.now())
      
      if (import.meta.env.DEV) {
        console.error(`❌ API请求失败 [${context}] 耗时: ${耗时}ms`, error)
      }
      
      // 使用统一错误处理
      处理错误(error, context, {
        显示提示: 显示错误提示,
        记录日志,
        使用通知,
        自定义消息: 错误消息,
        抛出错误: true
      })
    }
  }
}

/**
 * 批量API请求处理
 * @param {Array} apiRequests - API请求数组
 * @param {Object} options - 选项配置
 * @returns {Promise<Object>} 批量请求结果
 */
export const 批量API请求 = async (apiRequests, options = {}) => {
  const {
    并发限制 = 5,
    失败策略 = 'continue', // 'continue' | 'stop'
    显示进度 = false
  } = options
  
  const 结果 = {
    成功数量: 0,
    失败数量: 0,
    总数量: apiRequests.length,
    成功结果: [],
    失败结果: [],
    详细结果: []
  }
  
  try {
    // 分批处理请求
    for (let i = 0; i < apiRequests.length; i += 并发限制) {
      const 当前批次 = apiRequests.slice(i, i + 并发限制)
      
      const 批次结果 = await Promise.allSettled(
        当前批次.map(async (请求, 索引) => {
          try {
            const 响应 = await 请求()
            return { 索引: i + 索引, 状态: 'fulfilled', 数据: 响应 }
          } catch (error) {
            return { 索引: i + 索引, 状态: 'rejected', 错误: error }
          }
        })
      )
      
      // 处理批次结果
      批次结果.forEach(result => {
        if (result.status === 'fulfilled') {
          结果.成功数量++
          结果.成功结果.push(result.value.数据)
          结果.详细结果.push(result.value)
        } else {
          结果.失败数量++
          结果.失败结果.push(result.reason)
          结果.详细结果.push({
            索引: result.value?.索引 || -1,
            状态: 'rejected',
            错误: result.reason
          })
          
          // 如果策略是停止，则中断处理
          if (失败策略 === 'stop') {
            throw new Error(`批量请求在第${result.value?.索引 || -1}个请求时失败: ${result.reason?.message}`)
          }
        }
      })
      
      // 显示进度
      if (显示进度) {
        const 进度 = Math.min(i + 并发限制, apiRequests.length)
        console.log(`📊 批量请求进度: ${进度}/${apiRequests.length} (${Math.round(进度 / apiRequests.length * 100)}%)`)
      }
    }
    
    return 结果
  } catch (error) {
    处理错误(error, '批量API请求', {
      显示提示: true,
      记录日志: true
    })
    throw error
  }
}

// 导出所有工具函数
export {
  判断API成功 as isApiSuccess,
  获取API数据 as getApiData,
  获取API消息 as getApiMessage,
  调试API响应 as debugApiResponse,
  处理分页响应 as processPaginationResponse,
  处理表单响应 as processFormResponse
}
