/**
 * 数据隐私保护工具
 * 用于处理敏感数据的模糊化显示
 */

/**
 * 格式化总数显示 - 智能模糊化
 * 根据数据规模和用户权限，返回模糊化的总数显示
 * 
 * @param {number} actualTotal - 实际总数
 * @param {boolean} isVip - 是否为VIP用户（会员）
 * @param {string} type - 数据类型（'leads', 'talents', 'contacts'等）
 * @returns {string} 格式化后的显示文本
 */
export function formatTotalCount(actualTotal, isVip = false, type = 'leads') {
  // 如果没有数据，直接返回0
  if (!actualTotal || actualTotal === 0) {
    return '0'
  }

  // VIP用户显示更精确的信息（但仍然模糊化）
  if (isVip) {
    return formatVipTotalCount(actualTotal, type)
  }

  // 普通用户显示模糊化信息
  return formatPublicTotalCount(actualTotal, type)
}

/**
 * VIP用户的总数显示格式
 * 显示相对精确但仍然保护隐私的信息
 */
function formatVipTotalCount(total, type) {
  const typeNames = {
    'leads': '条线索',
    'talents': '位达人',
    'contacts': '个联系方式',
    'default': '条数据'
  }
  
  const suffix = typeNames[type] || typeNames.default

  if (total < 1000) {
    // 小于1000，显示百位数模糊化
    const rounded = Math.floor(total / 100) * 100
    return `${rounded}+ ${suffix}`
  } else if (total < 10000) {
    // 1000-9999，显示千位数模糊化
    const rounded = Math.floor(total / 1000) * 1000
    return `${(rounded / 1000).toFixed(0)}K+ ${suffix}`
  } else if (total < 100000) {
    // 1万-9.9万，显示万位数模糊化
    const rounded = Math.floor(total / 10000) * 10000
    return `${(rounded / 10000).toFixed(0)}万+ ${suffix}`
  } else {
    // 10万以上，显示十万位数模糊化
    const rounded = Math.floor(total / 100000) * 100000
    return `${(rounded / 10000).toFixed(0)}万+ ${suffix}`
  }
}

/**
 * 普通用户的总数显示格式
 * 显示高度模糊化的信息
 */
function formatPublicTotalCount(total, type) {
  const typeNames = {
    'leads': '条线索',
    'talents': '位达人', 
    'contacts': '个联系方式',
    'default': '条数据'
  }
  
  const suffix = typeNames[type] || typeNames.default

  if (total < 1000) {
    return `数百 ${suffix}`
  } else if (total < 10000) {
    return `数千 ${suffix}`
  } else if (total < 50000) {
    return `数万 ${suffix}`
  } else if (total < 100000) {
    return `5万+ ${suffix}`
  } else if (total < 200000) {
    return `10万+ ${suffix}`
  } else {
    return `20万+ ${suffix}`
  }
}

/**
 * 分页显示文本格式化
 * 隐藏具体的总数，但保留分页功能
 * 
 * @param {number} total - 总数
 * @param {Array} range - 当前显示范围 [start, end]
 * @param {boolean} isVip - 是否为VIP用户
 * @returns {string} 格式化后的分页文本
 */
export function formatPaginationText(total, range, isVip = false) {
  if (!total || total === 0) {
    return '暂无数据'
  }

  const [start, end] = range

  if (isVip) {
    // VIP用户显示相对精确的信息
    const fuzzyTotal = formatTotalCount(total, true, 'default').replace(' 条数据', '')
    return `第 ${start}-${end} 项，约 ${fuzzyTotal} 项`
  } else {
    // 普通用户只显示当前范围，不显示总数
    return `第 ${start}-${end} 项`
  }
}

/**
 * 检查用户是否为VIP/会员
 * 这里可以根据实际的用户权限系统进行调整
 * 
 * @param {Object} userInfo - 用户信息
 * @returns {boolean} 是否为VIP用户
 */
export function checkUserVipStatus(userInfo) {
  // 这里可以根据实际的用户权限字段进行判断
  // 例如：userInfo.memberLevel, userInfo.isVip, userInfo.subscription等
  return userInfo?.isVip || userInfo?.memberLevel > 0 || false
}

/**
 * 获取数据概览统计的模糊化显示
 * 用于仪表板等概览页面
 * 
 * @param {Object} stats - 统计数据对象
 * @param {boolean} isVip - 是否为VIP用户
 * @returns {Object} 模糊化后的统计数据
 */
export function formatOverviewStats(stats, isVip = false) {
  const result = {}
  
  Object.keys(stats).forEach(key => {
    const value = stats[key]
    
    if (typeof value === 'number' && value > 0) {
      // 对数值类型进行模糊化处理
      result[key] = formatTotalCount(value, isVip, 'default').replace(' 条数据', '')
    } else {
      // 非数值类型保持原样
      result[key] = value
    }
  })
  
  return result
}

/**
 * 生成数据规模提示文本
 * 用于向用户说明数据规模而不泄露具体数字
 * 
 * @param {number} total - 实际总数
 * @param {string} dataType - 数据类型
 * @returns {string} 提示文本
 */
export function generateDataScaleHint(total, dataType = '数据') {
  if (total < 1000) {
    return `我们为您提供了丰富的${dataType}资源`
  } else if (total < 10000) {
    return `我们拥有大量优质的${dataType}资源`
  } else if (total < 100000) {
    return `我们建立了庞大的${dataType}数据库`
  } else {
    return `我们构建了海量的${dataType}资源池`
  }
}

/**
 * 默认导出配置
 */
export default {
  formatTotalCount,
  formatPaginationText,
  checkUserVipStatus,
  formatOverviewStats,
  generateDataScaleHint
}
