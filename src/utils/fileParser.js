import mammoth from 'mammoth'
import * as XLSX from 'xlsx'
import pptParser from './pptParser'

/**
 * 文件解析工具类
 * File Parser Utility Class
 */
class FileParser {
  constructor() {
    this.supportedTypes = {
      // 文本文件
      'text/plain': this.parseTextFile,
      'text/csv': this.parseCsvFile,
      'application/json': this.parseJsonFile,
      
      // Office文档
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': this.parseDocxFile,
      'application/msword': this.parseDocFile,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': this.parseXlsxFile,
      'application/vnd.ms-excel': this.parseXlsFile,
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': this.parsePptxFile,
      'application/vnd.ms-powerpoint': this.parsePptFile,
      
      // PDF
      'application/pdf': this.parsePdfFile
    }
  }

  /**
   * 检查文件是否支持解析
   * @param {File} file - 文件对象
   * @returns {boolean} 是否支持
   */
  isSupported(file) {
    return this.supportedTypes.hasOwnProperty(file.type) || 
           this.getSupportedExtensions().includes(this.getFileExtension(file.name))
  }

  /**
   * 获取支持的文件扩展名
   * @returns {Array} 扩展名数组
   */
  getSupportedExtensions() {
    return ['.txt', '.csv', '.json', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.pdf']
  }

  /**
   * 获取文件扩展名
   * @param {string} filename - 文件名
   * @returns {string} 扩展名
   */
  getFileExtension(filename) {
    return filename.toLowerCase().substring(filename.lastIndexOf('.'))
  }

  /**
   * 解析文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 解析后的文本内容
   */
  async parseFile(file) {
    try {
      // 检查文件大小（限制为10MB）
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('文件大小超过10MB限制')
      }

      // 根据文件类型或扩展名选择解析方法
      let parseMethod = this.supportedTypes[file.type]
      
      if (!parseMethod) {
        const extension = this.getFileExtension(file.name)
        parseMethod = this.getParseMethodByExtension(extension)
      }

      if (!parseMethod) {
        throw new Error(`不支持的文件格式: ${file.type || this.getFileExtension(file.name)}`)
      }

      return await parseMethod.call(this, file)
    } catch (error) {
      console.error('文件解析失败:', error)
      throw error
    }
  }

  /**
   * 根据扩展名获取解析方法
   * @param {string} extension - 文件扩展名
   * @returns {Function} 解析方法
   */
  getParseMethodByExtension(extension) {
    const extensionMap = {
      '.txt': this.parseTextFile,
      '.csv': this.parseCsvFile,
      '.json': this.parseJsonFile,
      '.docx': this.parseDocxFile,
      '.doc': this.parseDocFile,
      '.xlsx': this.parseXlsxFile,
      '.xls': this.parseXlsFile,
      '.pptx': this.parsePptxFile,
      '.ppt': this.parsePptFile,
      '.pdf': this.parsePdfFile
    }
    return extensionMap[extension]
  }

  /**
   * 解析文本文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文本内容
   */
  async parseTextFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = () => reject(new Error('文本文件读取失败'))
      reader.readAsText(file, 'UTF-8')
    })
  }

  /**
   * 解析CSV文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 格式化的文本内容
   */
  async parseCsvFile(file) {
    const text = await this.parseTextFile(file)
    
    // 简单的CSV解析，转换为易读格式
    const lines = text.split('\n').filter(line => line.trim())
    const result = []
    
    lines.forEach((line, index) => {
      const cells = line.split(',').map(cell => cell.trim().replace(/"/g, ''))
      if (index === 0) {
        result.push('CSV文件内容:')
        result.push('=' * 20)
      }
      result.push(`第${index + 1}行: ${cells.join(' | ')}`)
    })
    
    return result.join('\n')
  }

  /**
   * 解析JSON文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 格式化的JSON内容
   */
  async parseJsonFile(file) {
    const text = await this.parseTextFile(file)
    
    try {
      const jsonData = JSON.parse(text)
      return `JSON文件内容:\n${'='.repeat(20)}\n${JSON.stringify(jsonData, null, 2)}`
    } catch (error) {
      throw new Error('JSON文件格式错误')
    }
  }

  /**
   * 解析DOCX文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文档文本内容
   */
  async parseDocxFile(file) {
    try {
      const arrayBuffer = await this.fileToArrayBuffer(file)
      const result = await mammoth.extractRawText({ arrayBuffer })
      
      if (result.value) {
        return `Word文档内容:\n${'='.repeat(20)}\n${result.value}`
      } else {
        throw new Error('无法提取Word文档内容')
      }
    } catch (error) {
      throw new Error(`DOCX文件解析失败: ${error.message}`)
    }
  }

  /**
   * 解析DOC文件（旧格式）
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 提示信息
   */
  async parseDocFile(file) {
    // DOC格式较复杂，建议用户转换为DOCX
    return `DOC文件检测到:\n${'='.repeat(20)}\n文件名: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n\n建议: 请将DOC文件转换为DOCX格式以获得更好的解析效果。`
  }

  /**
   * 解析XLSX文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 表格内容
   */
  async parseXlsxFile(file) {
    try {
      const arrayBuffer = await this.fileToArrayBuffer(file)
      const workbook = XLSX.read(arrayBuffer, { type: 'array' })
      
      let result = `Excel文件内容:\n${'='.repeat(20)}\n`
      
      // 遍历所有工作表
      workbook.SheetNames.forEach((sheetName, index) => {
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        result += `\n工作表 ${index + 1}: ${sheetName}\n${'-'.repeat(15)}\n`
        
        jsonData.forEach((row, rowIndex) => {
          if (row.length > 0) {
            result += `第${rowIndex + 1}行: ${row.join(' | ')}\n`
          }
        })
      })
      
      return result
    } catch (error) {
      throw new Error(`XLSX文件解析失败: ${error.message}`)
    }
  }

  /**
   * 解析XLS文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 表格内容
   */
  async parseXlsFile(file) {
    // XLS格式也可以用XLSX库解析
    return await this.parseXlsxFile(file)
  }

  /**
   * 解析PPTX文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 演示文稿内容
   */
  async parsePptxFile(file) {
    try {
      console.log('开始解析PPTX文件，这可能需要一些时间...')

      // 使用专业的PPT解析器
      const result = await pptParser.parsePPTX(file)

      // 格式化解析结果
      return pptParser.formatResult(result)
    } catch (error) {
      console.error('PPTX解析失败:', error)
      // 如果解析失败，返回基本信息和建议
      return `PowerPoint文件解析失败:\n${'='.repeat(25)}\n文件名: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n错误信息: ${error.message}\n\n建议: \n1. 请检查文件是否损坏\n2. 尝试手动复制PPT中的文本内容\n3. 或者将PPT另存为较新的格式\n4. 然后粘贴到文本框中进行解析`
    }
  }

  /**
   * 解析PPT文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 提示信息
   */
  async parsePptFile(file) {
    return `PowerPoint文件检测到:\n${'='.repeat(20)}\n文件名: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n\n建议: 请将PPT文件转换为PPTX格式，或手动复制其中的文本内容。`
  }

  /**
   * 解析PDF文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 提示信息
   */
  async parsePdfFile(file) {
    // PDF解析需要额外的库，这里提供提示
    return `PDF文件检测到:\n${'='.repeat(20)}\n文件名: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n\n建议: \n1. 请使用PDF阅读器复制其中的文本内容\n2. 或者将PDF转换为Word文档\n3. 然后粘贴到文本框中进行解析\n\n注意: PDF文件可能包含图片和复杂格式，建议提取纯文本信息。`
  }

  /**
   * 将文件转换为ArrayBuffer
   * @param {File} file - 文件对象
   * @returns {Promise<ArrayBuffer>} ArrayBuffer
   */
  fileToArrayBuffer(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 创建并导出实例
const fileParser = new FileParser()
export default fileParser

// 导出类供其他地方使用
export { FileParser }
