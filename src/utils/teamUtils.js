/**
 * 团队管理工具库
 * 统一管理团队相关的公共函数、状态处理、格式化等
 */

/**
 * 团队状态枚举
 */
export const TEAM_STATUS = {
  NORMAL: '正常',
  DISSOLVED: '已解散',
  SUSPENDED: '已暂停'
}

/**
 * 团队关系类型枚举
 */
export const TEAM_RELATION_TYPES = {
  ALL: 'all',           // 全部
  CREATED: 'created',   // 我创建的
  MANAGED: 'managed',   // 我管理的
  JOINED: 'joined'      // 我所在的
}

/**
 * 邀请状态枚举
 */
export const INVITATION_STATUS = {
  PENDING: '待处理',
  ACCEPTED: '已接受',
  REJECTED: '已拒绝',
  EXPIRED: '已过期',
  REVOKED: '已撤销'
}

/**
 * 格式化日期
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @param {boolean} includeTime - 是否包含时间
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(dateString, includeTime = false) {
  if (!dateString) return '暂无'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: 'Asia/Shanghai'
  }
  
  if (includeTime) {
    options.hour = '2-digit'
    options.minute = '2-digit'
    options.second = '2-digit'
  }
  
  return date.toLocaleDateString('zh-CN', options)
}

/**
 * 格式化相对时间（如：3天前、2小时前）
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(dateString) {
  if (!dateString) return '暂无'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  
  const now = new Date()
  const diffMs = now - date
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (diffDays > 7) {
    return formatDate(dateString)
  } else if (diffDays > 0) {
    return `${diffDays}天前`
  } else if (diffHours > 0) {
    return `${diffHours}小时前`
  } else if (diffMinutes > 0) {
    return `${diffMinutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 获取团队状态颜色
 * @param {string} status - 团队状态
 * @returns {string} 状态对应的颜色
 */
export function getTeamStatusColor(status) {
  const colorMap = {
    [TEAM_STATUS.NORMAL]: 'success',
    [TEAM_STATUS.DISSOLVED]: 'error',
    [TEAM_STATUS.SUSPENDED]: 'warning'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取邀请状态颜色
 * @param {string} status - 邀请状态
 * @returns {string} 状态对应的颜色
 */
export function getInvitationStatusColor(status) {
  const colorMap = {
    [INVITATION_STATUS.PENDING]: 'processing',
    [INVITATION_STATUS.ACCEPTED]: 'success',
    [INVITATION_STATUS.REJECTED]: 'error',
    [INVITATION_STATUS.EXPIRED]: 'warning',
    [INVITATION_STATUS.REVOKED]: 'default'
  }
  return colorMap[status] || 'default'
}

/**
 * 生成团队头像颜色（基于团队名称）
 * @param {string} teamName - 团队名称
 * @returns {string} 颜色值
 */
export function getTeamAvatarColor(teamName) {
  if (!teamName) return '#1890ff'
  
  const colors = [
    '#f56565', '#ed8936', '#ecc94b', '#48bb78', 
    '#38b2ac', '#4299e1', '#667eea', '#9f7aea',
    '#f093fb', '#f5a623', '#50e3c2', '#bd10e0'
  ]
  
  // 基于团队名称的字符码生成固定颜色
  let hash = 0
  for (let i = 0; i < teamName.length; i++) {
    hash = teamName.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const index = Math.abs(hash) % colors.length
  return colors[index]
}

/**
 * 生成用户头像颜色（基于用户昵称或手机号）
 * @param {string} identifier - 用户昵称或手机号
 * @returns {string} 颜色值
 */
export function getUserAvatarColor(identifier) {
  if (!identifier) return '#1890ff'
  
  const colors = [
    '#f56565', '#ed8936', '#ecc94b', '#48bb78', 
    '#38b2ac', '#4299e1', '#667eea', '#9f7aea',
    '#f093fb', '#f5a623', '#50e3c2', '#bd10e0'
  ]
  
  // 基于用户标识符的字符码生成固定颜色
  let hash = 0
  for (let i = 0; i < identifier.length; i++) {
    hash = identifier.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const index = Math.abs(hash) % colors.length
  return colors[index]
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否为有效手机号
 */
export function isValidPhone(phone) {
  if (!phone || typeof phone !== 'string') return false
  
  // 中国手机号正则：1开头，第二位是3-9，总共11位数字
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone.trim())
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
export function isValidEmail(email) {
  if (!email || typeof email !== 'string') return false
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim())
}

/**
 * 格式化成员数量显示
 * @param {number} count - 成员数量
 * @param {number} maxCount - 最大成员数
 * @returns {string} 格式化后的成员数量字符串
 */
export function formatMemberCount(count, maxCount) {
  if (typeof count !== 'number') count = 0
  if (typeof maxCount !== 'number') return `${count}人`
  
  return `${count}/${maxCount}人`
}

/**
 * 计算团队成员完成度百分比
 * @param {number} currentCount - 当前成员数
 * @param {number} maxCount - 最大成员数
 * @returns {number} 百分比（0-100）
 */
export function calculateMemberProgress(currentCount, maxCount) {
  if (!maxCount || maxCount <= 0) return 0
  if (!currentCount || currentCount <= 0) return 0
  
  const progress = (currentCount / maxCount) * 100
  return Math.min(Math.round(progress), 100)
}

/**
 * 生成团队统计数据的显示格式
 * @param {Object} stats - 统计数据
 * @returns {Object} 格式化后的统计数据
 */
export function formatTeamStats(stats) {
  if (!stats || typeof stats !== 'object') {
    return {
      totalMembers: 0,
      activeMembers: 0,
      todayJoined: 0,
      weekJoined: 0,
      monthJoined: 0
    }
  }
  
  return {
    totalMembers: stats.总成员数 || stats.total_members || 0,
    activeMembers: stats.正常成员数 || stats.active_members || 0,
    todayJoined: stats.今日新增 || stats.today_joined || 0,
    weekJoined: stats.本周新增 || stats.week_joined || 0,
    monthJoined: stats.本月新增 || stats.month_joined || 0
  }
}

/**
 * 检查团队是否可以邀请更多成员
 * @param {number} currentCount - 当前成员数
 * @param {number} maxCount - 最大成员数
 * @returns {boolean} 是否可以邀请更多成员
 */
export function canInviteMoreMembers(currentCount, maxCount) {
  if (!maxCount || maxCount <= 0) return true // 无限制
  if (!currentCount || currentCount < 0) currentCount = 0
  
  return currentCount < maxCount
}

/**
 * 获取团队关系类型的显示名称
 * @param {string} relationType - 团队关系类型
 * @returns {string} 显示名称
 */
export function getTeamRelationDisplayName(relationType) {
  const nameMap = {
    [TEAM_RELATION_TYPES.ALL]: '全部团队',
    [TEAM_RELATION_TYPES.CREATED]: '我创建的',
    [TEAM_RELATION_TYPES.MANAGED]: '我管理的',
    [TEAM_RELATION_TYPES.JOINED]: '我参与的'
  }
  return nameMap[relationType] || '未知类型'
}

/**
 * 处理API响应的统一格式
 * @param {Object} 响应数据 - API响应对象（优化后的拦截器直接返回response.data）
 * @returns {Object} 标准化的响应数据
 */
export function normalizeApiResponse(响应数据) {
  if (!响应数据) return { success: false, data: null, message: '无响应数据' };

  // 检查是否为成功状态
  const isSuccess = [100, 0, 1].includes(响应数据.status);

  // 优先从data字段获取业务数据
  const data = 响应数据.data;

  // 确保message字段始终是字符串类型
  let message = 响应数据.message;
  if (typeof message !== 'string') {
    // 如果message不是字符串，这可能是旧接口将业务数据放在了message字段
    console.warn('API响应格式不规范: message字段不是字符串', 响应数据);
    message = isSuccess ? '操作成功' : '操作失败';
  }

  return {
    success: isSuccess,
    data: data,
    message: message,
    status: 响应数据.status
  };
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 节流间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
} 