import JSZip from 'jszip'
import { createWorker } from 'tesseract.js'

/**
 * PPT解析和OCR工具类
 * PowerPoint Parser and OCR Utility Class
 */
class PPTParser {
  constructor() {
    this.ocrWorker = null
    this.isOcrInitialized = false
  }

  /**
   * 初始化OCR工作器
   */
  async initializeOCR() {
    if (this.isOcrInitialized) return

    try {
      this.ocrWorker = await createWorker('chi_sim+eng', 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            console.log(`OCR进度: ${Math.round(m.progress * 100)}%`)
          }
        }
      })
      this.isOcrInitialized = true
      console.log('OCR工作器初始化完成')
    } catch (error) {
      console.error('OCR初始化失败:', error)
      throw new Error('OCR功能初始化失败')
    }
  }

  /**
   * 解析PPTX文件
   * @param {File} file - PPTX文件
   * @returns {Promise<Object>} 解析结果
   */
  async parsePPTX(file) {
    try {
      const zip = new JSZip()
      const zipContent = await zip.loadAsync(file)
      
      const result = {
        slides: [],
        textContent: [],
        images: [],
        totalSlides: 0,
        extractedText: '',
        ocrText: ''
      }

      // 解析幻灯片
      const slides = await this.extractSlides(zipContent)
      result.slides = slides
      result.totalSlides = slides.length

      // 提取文本内容
      const textContent = await this.extractTextFromSlides(zipContent)
      result.textContent = textContent
      result.extractedText = textContent.join('\n\n')

      // 提取图片
      const images = await this.extractImages(zipContent)
      result.images = images

      // 如果有图片，进行OCR识别
      if (images.length > 0) {
        await this.initializeOCR()
        const ocrResults = await this.performOCR(images)
        result.ocrText = ocrResults.join('\n\n')
      }

      return result
    } catch (error) {
      console.error('PPTX解析失败:', error)
      throw new Error(`PPTX文件解析失败: ${error.message}`)
    }
  }

  /**
   * 提取幻灯片信息
   * @param {JSZip} zipContent - ZIP内容
   * @returns {Promise<Array>} 幻灯片信息
   */
  async extractSlides(zipContent) {
    const slides = []
    const slideFiles = Object.keys(zipContent.files).filter(name => 
      name.startsWith('ppt/slides/slide') && name.endsWith('.xml')
    )

    for (const slideFile of slideFiles) {
      try {
        const slideContent = await zipContent.files[slideFile].async('text')
        const slideNumber = this.extractSlideNumber(slideFile)
        
        slides.push({
          number: slideNumber,
          file: slideFile,
          content: slideContent
        })
      } catch (error) {
        console.warn(`解析幻灯片 ${slideFile} 失败:`, error)
      }
    }

    return slides.sort((a, b) => a.number - b.number)
  }

  /**
   * 从幻灯片中提取文本
   * @param {JSZip} zipContent - ZIP内容
   * @returns {Promise<Array>} 文本内容数组
   */
  async extractTextFromSlides(zipContent) {
    const textContent = []
    const slideFiles = Object.keys(zipContent.files).filter(name => 
      name.startsWith('ppt/slides/slide') && name.endsWith('.xml')
    )

    for (const slideFile of slideFiles) {
      try {
        const slideContent = await zipContent.files[slideFile].async('text')
        const slideNumber = this.extractSlideNumber(slideFile)
        const text = this.extractTextFromXML(slideContent)
        
        if (text.trim()) {
          textContent.push(`=== 幻灯片 ${slideNumber} ===\n${text}`)
        }
      } catch (error) {
        console.warn(`提取幻灯片 ${slideFile} 文本失败:`, error)
      }
    }

    return textContent
  }

  /**
   * 从XML中提取文本
   * @param {string} xmlContent - XML内容
   * @returns {string} 提取的文本
   */
  extractTextFromXML(xmlContent) {
    // 使用正则表达式提取 <a:t> 标签中的文本
    const textMatches = xmlContent.match(/<a:t[^>]*>(.*?)<\/a:t>/g)
    if (!textMatches) return ''

    const texts = textMatches.map(match => {
      // 提取标签内的文本内容
      const textContent = match.replace(/<a:t[^>]*>/, '').replace(/<\/a:t>/, '')
      // 解码XML实体
      return this.decodeXMLEntities(textContent)
    }).filter(text => text.trim())

    return texts.join('\n')
  }

  /**
   * 解码XML实体
   * @param {string} text - 包含XML实体的文本
   * @returns {string} 解码后的文本
   */
  decodeXMLEntities(text) {
    const entities = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&apos;': "'"
    }
    
    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity
    })
  }

  /**
   * 提取图片
   * @param {JSZip} zipContent - ZIP内容
   * @returns {Promise<Array>} 图片数组
   */
  async extractImages(zipContent) {
    const images = []
    const imageFiles = Object.keys(zipContent.files).filter(name => 
      name.startsWith('ppt/media/') && this.isImageFile(name)
    )

    for (const imageFile of imageFiles) {
      try {
        const imageData = await zipContent.files[imageFile].async('blob')
        const imageUrl = URL.createObjectURL(imageData)
        
        images.push({
          name: imageFile.split('/').pop(),
          path: imageFile,
          blob: imageData,
          url: imageUrl,
          size: imageData.size
        })
      } catch (error) {
        console.warn(`提取图片 ${imageFile} 失败:`, error)
      }
    }

    return images
  }

  /**
   * 检查是否为图片文件
   * @param {string} filename - 文件名
   * @returns {boolean} 是否为图片
   */
  isImageFile(filename) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
    return imageExtensions.includes(extension)
  }

  /**
   * 执行OCR识别
   * @param {Array} images - 图片数组
   * @returns {Promise<Array>} OCR结果数组
   */
  async performOCR(images) {
    if (!this.isOcrInitialized) {
      await this.initializeOCR()
    }

    const ocrResults = []
    
    for (let i = 0; i < images.length; i++) {
      const image = images[i]
      try {
        console.log(`正在识别图片 ${i + 1}/${images.length}: ${image.name}`)
        
        const { data: { text } } = await this.ocrWorker.recognize(image.blob)
        
        if (text.trim()) {
          ocrResults.push(`=== 图片 ${image.name} 中的文字 ===\n${text.trim()}`)
        }
        
        // 释放图片URL
        URL.revokeObjectURL(image.url)
      } catch (error) {
        console.warn(`OCR识别图片 ${image.name} 失败:`, error)
        ocrResults.push(`=== 图片 ${image.name} ===\n[OCR识别失败: ${error.message}]`)
      }
    }

    return ocrResults
  }

  /**
   * 提取幻灯片编号
   * @param {string} filename - 文件名
   * @returns {number} 幻灯片编号
   */
  extractSlideNumber(filename) {
    const match = filename.match(/slide(\d+)\.xml/)
    return match ? parseInt(match[1]) : 0
  }

  /**
   * 格式化解析结果为文本
   * @param {Object} result - 解析结果
   * @returns {string} 格式化的文本
   */
  formatResult(result) {
    let output = []
    
    output.push(`PowerPoint文件解析结果`)
    output.push(`${'='.repeat(30)}`)
    output.push(`总幻灯片数: ${result.totalSlides}`)
    output.push(`提取图片数: ${result.images.length}`)
    output.push('')

    // 添加提取的文本内容
    if (result.extractedText) {
      output.push(`📝 幻灯片文本内容:`)
      output.push(`${'-'.repeat(25)}`)
      output.push(result.extractedText)
      output.push('')
    }

    // 添加OCR识别的文本
    if (result.ocrText) {
      output.push(`🔍 图片OCR识别结果:`)
      output.push(`${'-'.repeat(25)}`)
      output.push(result.ocrText)
      output.push('')
    }

    // 添加处理统计
    output.push(`📊 处理统计:`)
    output.push(`${'-'.repeat(15)}`)
    output.push(`- 成功解析 ${result.totalSlides} 张幻灯片`)
    output.push(`- 提取文本 ${result.textContent.length} 段`)
    output.push(`- 识别图片 ${result.images.length} 张`)
    
    if (result.images.length > 0) {
      const totalImageSize = result.images.reduce((sum, img) => sum + img.size, 0)
      output.push(`- 图片总大小 ${this.formatFileSize(totalImageSize)}`)
    }

    return output.join('\n')
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.ocrWorker) {
      await this.ocrWorker.terminate()
      this.ocrWorker = null
      this.isOcrInitialized = false
    }
  }
}

// 创建并导出实例
const pptParser = new PPTParser()
export default pptParser

// 导出类供其他地方使用
export { PPTParser }
