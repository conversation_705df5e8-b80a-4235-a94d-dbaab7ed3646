/**
 * 数据看板工具函数
 * 提供数据处理、格式化和计算等功能
 */

/**
 * 格式化数字，添加千位分隔符
 * @param {number} num - 要格式化的数字
 * @param {boolean} keepDecimal - 是否保留小数部分
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, keepDecimal = false) {
  if (num === undefined || num === null) return '0';
  
  if (typeof num !== 'number') {
    num = Number(num);
    if (isNaN(num)) return '0';
  }
  
  const parts = num.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  
  return keepDecimal && parts.length > 1 ? parts.join('.') : parts[0];
}

/**
 * 计算同比增长率
 * @param {number} current - 当前值
 * @param {number} previous - 上一期值
 * @returns {object} 包含增长率和趋势的对象
 */
export function calculateGrowthRate(current, previous) {
  if (!previous || previous === 0) {
    return { rate: 0, trend: 'flat' };
  }
  
  const rate = ((current - previous) / previous) * 100;
  let trend = 'flat';
  
  if (rate > 0) trend = 'up';
  else if (rate < 0) trend = 'down';
  
  return {
    rate: Math.abs(rate).toFixed(2),
    trend
  };
}

/**
 * 格式化日期范围为易读字符串
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {string} 格式化后的日期范围字符串
 */
export function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return '';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const startStr = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`;
  const endStr = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`;
  
  return `${startStr} 至 ${endStr}`;
}

/**
 * 生成近期日期列表
 * @param {number} days - 天数
 * @returns {Array} 日期字符串数组
 */
export function generateRecentDates(days = 30) {
  const dates = [];
  const today = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(today.getDate() - i);
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    dates.push(dateStr);
  }
  
  return dates;
}

/**
 * 填充趋势数据中缺失的日期
 * @param {Array} data - 原始数据数组
 * @param {string} dateKey - 日期字段名
 * @param {string} valueKey - 值字段名
 * @param {number} days - 天数
 * @returns {Array} 填充后的数据数组
 */
export function fillMissingDates(data, dateKey, valueKey, days = 30) {
  const filledData = [];
  const dateMap = {};
  
  // 构建日期映射
  data.forEach(item => {
    dateMap[item[dateKey]] = item[valueKey];
  });
  
  // 生成日期列表并填充数据
  const dates = generateRecentDates(days);
  dates.forEach(date => {
    filledData.push({
      [dateKey]: date,
      [valueKey]: dateMap[date] || 0
    });
  });
  
  return filledData;
}

/**
 * 计算完成率
 * @param {number} completed - 已完成数量
 * @param {number} total - 总数量
 * @returns {number} 完成率百分比
 */
export function calculateCompletionRate(completed, total) {
  if (!total || total === 0) return 0;
  return Math.round((completed / total) * 100);
}

/**
 * 获取状态颜色
 * @param {string} status - 状态值
 * @returns {string} 对应的颜色代码
 */
export function getStatusColor(status) {
  const statusColors = {
    '意向确认': '#1890ff',
    '样品确认': '#52c41a',
    '排期确认': '#faad14',
    '开播完成': '#722ed1',
    '合作失败': '#f5222d',
    '未开始': '#d9d9d9',
    '进行中': '#1890ff',
    '已完成': '#52c41a',
    '已取消': '#f5222d'
  };
  
  return statusColors[status] || '#d9d9d9';
}

/**
 * 根据数值范围获取颜色
 * @param {number} value - 数值
 * @param {number} max - 最大值
 * @returns {string} 颜色代码
 */
export function getColorByValue(value, max = 100) {
  const ratio = value / max;
  
  if (ratio >= 0.8) return '#52c41a'; // 绿色
  if (ratio >= 0.6) return '#1890ff'; // 蓝色
  if (ratio >= 0.4) return '#faad14'; // 黄色
  if (ratio >= 0.2) return '#fa8c16'; // 橙色
  return '#f5222d'; // 红色
}

/**
 * 计算团队绩效得分
 * @param {Object} stats - 团队统计数据
 * @returns {number} 绩效得分
 */
export function calculateTeamPerformance(stats) {
  if (!stats) return 0;
  
  // 各指标权重
  const weights = {
    意向确认数量: 0.2,
    样品确认数量: 0.3,
    排期确认数量: 0.2,
    开播完成数量: 0.3
  };
  
  // 计算总分
  let totalScore = 0;
  let totalWeight = 0;
  
  for (const [key, weight] of Object.entries(weights)) {
    if (stats[key] !== undefined) {
      totalScore += stats[key] * weight;
      totalWeight += weight;
    }
  }
  
  return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
} 