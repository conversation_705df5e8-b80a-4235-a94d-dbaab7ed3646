<template>
  <div class="workspace-test">
    <a-card title="工作台功能测试" :bordered="false">
      <a-space direction="vertical" size="large" style="width: 100%">
        
        <!-- 测试按钮组 -->
        <a-card title="API测试" size="small">
          <a-space wrap>
            <a-button 
              type="primary" 
              @click="testWechatOperations"
              :loading="loading.wechat"
            >
              测试微信运营数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testInvitationBusiness"
              :loading="loading.invitation"
            >
              测试邀约业务数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testTalentManagement"
              :loading="loading.talent"
            >
              测试达人管理数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testCooperationProjects"
              :loading="loading.cooperation"
            >
              测试合作项目数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testTeamOverview"
              :loading="loading.team"
            >
              测试团队数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testTrends"
              :loading="loading.trends"
            >
              测试趋势数据
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testTodos"
              :loading="loading.todos"
            >
              测试待办事项
            </a-button>
            
            <a-button 
              type="primary" 
              @click="testDashboard"
              :loading="loading.dashboard"
            >
              测试完整工作台
            </a-button>
          </a-space>
        </a-card>

        <!-- 测试结果显示 -->
        <a-card title="测试结果" size="small">
          <a-tabs v-model:activeKey="activeTab">
            <a-tab-pane key="wechat" tab="微信运营">
              <pre>{{ JSON.stringify(testResults.wechat, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="invitation" tab="邀约业务">
              <pre>{{ JSON.stringify(testResults.invitation, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="talent" tab="达人管理">
              <pre>{{ JSON.stringify(testResults.talent, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="cooperation" tab="合作项目">
              <pre>{{ JSON.stringify(testResults.cooperation, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="team" tab="团队数据">
              <pre>{{ JSON.stringify(testResults.team, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="trends" tab="趋势数据">
              <pre>{{ JSON.stringify(testResults.trends, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="todos" tab="待办事项">
              <pre>{{ JSON.stringify(testResults.todos, null, 2) }}</pre>
            </a-tab-pane>
            
            <a-tab-pane key="dashboard" tab="完整工作台">
              <pre>{{ JSON.stringify(testResults.dashboard, null, 2) }}</pre>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <!-- 工作台预览 -->
        <a-card title="工作台预览" size="small">
          <a-button type="primary" @click="openWorkspace">
            打开工作台页面
          </a-button>
        </a-card>

        <!-- 错误日志 -->
        <a-card v-if="errors.length > 0" title="错误日志" size="small">
          <a-alert
            v-for="(error, index) in errors"
            :key="index"
            :message="error.message"
            :description="error.detail"
            type="error"
            show-icon
            style="margin-bottom: 8px"
          />
          <a-button @click="clearErrors" size="small">清除错误</a-button>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import workspaceService from '../services/workspaceService.js'

const router = useRouter()

// 响应式数据
const loading = reactive({
  wechat: false,
  invitation: false,
  talent: false,
  cooperation: false,
  team: false,
  trends: false,
  todos: false,
  dashboard: false
})

const testResults = reactive({
  wechat: null,
  invitation: null,
  talent: null,
  cooperation: null,
  team: null,
  trends: null,
  todos: null,
  dashboard: null
})

const activeTab = ref('wechat')
const errors = ref([])

// 通用测试方法
const runTest = async (testName, apiMethod, params = {}) => {
  loading[testName] = true
  try {
    console.log(`开始测试 ${testName}...`)
    const response = await apiMethod(params)
    
    testResults[testName] = response
    activeTab.value = testName
    
    message.success(`${testName} 测试成功`)
    console.log(`${testName} 测试结果:`, response)
    
  } catch (error) {
    console.error(`${testName} 测试失败:`, error)
    
    const errorInfo = {
      message: `${testName} 测试失败`,
      detail: error.message || error.toString(),
      timestamp: new Date().toLocaleString()
    }
    
    errors.value.push(errorInfo)
    message.error(errorInfo.message)
    
    testResults[testName] = {
      error: true,
      message: error.message,
      details: error
    }
    
  } finally {
    loading[testName] = false
  }
}

// 各个测试方法
const testWechatOperations = () => {
  runTest('wechat', workspaceService.getWechatOperations, {
    timeRange: '30d'
  })
}

const testInvitationBusiness = () => {
  runTest('invitation', workspaceService.getInvitationBusiness, {
    timeRange: '30d'
  })
}

const testTalentManagement = () => {
  runTest('talent', workspaceService.getTalentManagement, {
    timeRange: '30d'
  })
}

const testCooperationProjects = () => {
  runTest('cooperation', workspaceService.getCooperationProjects, {
    timeRange: '30d'
  })
}

const testTeamOverview = () => {
  runTest('team', workspaceService.getTeamOverview, {
    timeRange: '30d'
  })
}

const testTrends = () => {
  runTest('trends', workspaceService.getTrends, {
    dataType: 'invitation',
    timeRange: '30d'
  })
}

const testTodos = () => {
  runTest('todos', workspaceService.getTodos)
}

const testDashboard = () => {
  runTest('dashboard', workspaceService.getDashboard, {
    timeRange: '30d',
    modules: ['personal', 'team', 'trends', 'todos']
  })
}

// 其他方法
const openWorkspace = () => {
  router.push('/workspace')
}

const clearErrors = () => {
  errors.value = []
}
</script>

<style scoped>
.workspace-test {
  padding: 24px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  font-size: 12px;
  line-height: 1.4;
}

.ant-space {
  width: 100%;
}

.ant-card {
  margin-bottom: 16px;
}

.ant-alert {
  margin-bottom: 8px;
}

.ant-alert:last-child {
  margin-bottom: 0;
}
</style>
