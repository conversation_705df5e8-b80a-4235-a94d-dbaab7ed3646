<template>
  <div class="promotion-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>推广管理</h2>
      <p class="page-description">管理您的专属推广链接，分享给好友获得推广收益</p>
    </div>

    <!-- 推广链接卡片 -->
    <a-card class="promotion-card" title="我的推广链接">
      <template #extra>
        <a-button 
          type="primary" 
          :loading="generating"
          @click="generatePromotionLink"
          v-if="!promotionData.推广链接"
        >
          <template #icon>
            <LinkOutlined />
          </template>
          生成推广链接
        </a-button>
        <a-button 
          type="primary" 
          :loading="generating"
          @click="generatePromotionLink"
          v-else
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新链接
        </a-button>
      </template>

      <div v-if="!promotionData.推广链接 && !generating" class="empty-state">
        <a-empty description="暂未生成推广链接">
          <template #image>
            <LinkOutlined style="font-size: 48px; color: #d9d9d9;" />
          </template>
          <a-button type="primary" @click="generatePromotionLink">
            立即生成推广链接
          </a-button>
        </a-empty>
      </div>

      <div v-else-if="promotionData.推广链接" class="promotion-content">
        <!-- 推广链接展示 -->
        <div class="link-section">
          <label class="section-label">推广链接：</label>
          <div class="link-display">
            <a-input 
              :value="promotionData.推广链接" 
              readonly 
              class="link-input"
            />
            <a-button 
              type="primary" 
              @click="copyLink"
              :loading="copying"
            >
              <template #icon>
                <CopyOutlined />
              </template>
              复制链接
            </a-button>
          </div>
        </div>

        <!-- 邀请码展示 - 如果邀请码已存在则隐藏 -->
        <div class="code-section" v-if="false" style="display: none;">
          <label class="section-label">我的邀请码：</label>
          <div class="code-display">
            <a-tag color="blue" class="invite-code-tag">
              {{ promotionData.自定义邀请码 }}
            </a-tag>
            <a-button 
              size="small" 
              @click="copyInviteCode"
              :loading="copyingCode"
            >
              <template #icon>
                <CopyOutlined />
              </template>
              复制邀请码
            </a-button>
          </div>
        </div>

        <!-- 生成时间 -->
        <div class="time-section">
          <label class="section-label">生成时间：</label>
          <span class="time-text">{{ formatTime(promotionData.生成时间) }}</span>
        </div>

        <!-- 使用说明 -->
        <div class="usage-section">
          <a-alert
            message="使用说明"
            type="info"
            show-icon
          >
            <template #description>
              <ul class="usage-list">
                <li>将推广链接分享给好友，好友通过链接注册即可建立推广关系</li>
                <li>仅会员用户的推广链接有效，非会员用户无法建立推广关系</li>
                <li>推广关系建立后，您可以获得相应的推广收益</li>
                <li>每个邀请码都是唯一的，请妥善保管您的推广链接</li>
              </ul>
            </template>
          </a-alert>
        </div>
      </div>
    </a-card>

    <!-- 推广统计卡片 -->
    <a-card class="stats-card" title="推广统计" style="margin-top: 24px;">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ stats.总推广人数 || 0 }}</div>
            <div class="stat-label">总推广人数</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ stats.本月推广人数 || 0 }}</div>
            <div class="stat-label">本月推广人数</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ stats.推广收益 || 0 }}</div>
            <div class="stat-label">推广收益</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 推广记录表格 -->
    <a-card class="records-card" title="推广记录" style="margin-top: 24px;">
      <a-table
        :columns="recordColumns"
        :data-source="promotionRecords"
        :loading="recordsLoading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'registerTime'">
            {{ record.registerTime ? formatTime(record.registerTime) : '-' }}
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { userAPI } from '@/services/user'
import {
  CopyOutlined,
  LinkOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref } from 'vue'

// 响应式数据
const generating = ref(false)
const copying = ref(false)
const copyingCode = ref(false)
const recordsLoading = ref(false)

// 推广数据
const promotionData = reactive({
  推广链接: '',
  自定义邀请码: '',
  生成时间: ''
})

// 统计数据
const stats = reactive({
  总推广人数: 0,
  本月推广人数: 0,
  推广收益: 0
})

// 推广记录
const promotionRecords = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const recordColumns = [
  {
    title: '推广用户',
    dataIndex: 'userName',
    key: 'userName',
    width: 120
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 120
  },
  {
    title: '注册时间',
    dataIndex: 'registerTime',
    key: 'registerTime',
    width: 160
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '推广收益',
    dataIndex: 'reward',
    key: 'reward',
    width: 100
  }
]

// 生成推广链接
const generatePromotionLink = async () => {
  try {
    generating.value = true
    
    const response = await userAPI.generatePromotionLink()
    
    if (response && response.status === 100) {
      Object.assign(promotionData, response.data)
      message.success('推广链接生成成功')
    } else {
      const errorMsg = response?.message || '生成推广链接失败'
      message.error(errorMsg)
    }
  } catch (error) {
    console.error('生成推广链接失败:', error)
    message.error('生成推广链接失败，请稍后重试')
  } finally {
    generating.value = false
  }
}

// 复制推广链接
const copyLink = async () => {
  try {
    copying.value = true
    await navigator.clipboard.writeText(promotionData.推广链接)
    message.success('推广链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  } finally {
    copying.value = false
  }
}

// 复制邀请码
const copyInviteCode = async () => {
  try {
    copyingCode.value = true
    await navigator.clipboard.writeText(promotionData.自定义邀请码)
    message.success('邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  } finally {
    copyingCode.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'registered': 'green',
    'pending': 'orange',
    'expired': 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'registered': '已注册',
    'pending': '待注册',
    'expired': '已过期'
  }
  return textMap[status] || '未知'
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPromotionRecords()
}

// 加载推广记录
const loadPromotionRecords = async () => {
  try {
    recordsLoading.value = true

    // const response = await userAPI.getPromotionRecords({
    //   page: pagination.current,
    //   pageSize: pagination.pageSize
    // })
    
    // 模拟数据
    promotionRecords.value = []
    pagination.total = 0
  } catch (error) {
    console.error('加载推广记录失败:', error)
    message.error('加载推广记录失败')
  } finally {
    recordsLoading.value = false
  }
}

// 初始化
onMounted(() => {
  // 如果用户已设置自定义邀请码，自动生成推广链接
  generatePromotionLink()
  loadPromotionRecords()
})
</script>

<style scoped>
.promotion-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.promotion-card,
.stats-card,
.records-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.promotion-content {
  padding: 16px 0;
}

.link-section,
.code-section,
.time-section {
  margin-bottom: 24px;
}

.section-label {
  display: inline-block;
  width: 100px;
  color: #595959;
  font-weight: 500;
  margin-bottom: 8px;
}

.link-display {
  display: flex;
  gap: 12px;
  align-items: center;
}

.link-input {
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.code-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.invite-code-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 6px;
}

.time-text {
  color: #8c8c8c;
  font-size: 14px;
}

.usage-section {
  margin-top: 24px;
}

.usage-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.usage-list li {
  margin-bottom: 4px;
  color: #595959;
  line-height: 1.6;
}

.stat-item {
  text-align: center;
  padding: 16px;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  color: #8c8c8c;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .promotion-management {
    padding: 16px;
  }

  .link-display {
    flex-direction: column;
    align-items: stretch;
  }

  .code-display {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-label {
    width: auto;
    display: block;
  }
}
</style>
