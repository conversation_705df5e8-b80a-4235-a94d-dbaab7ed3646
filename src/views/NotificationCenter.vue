<template>
  <div class="notification-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <BellOutlined />
          通知中心
        </h1>
        <div class="header-actions">
          <a-button 
            type="primary" 
            :loading="markingAllRead"
            @click="handleMarkAllRead"
            :disabled="unreadCount === 0"
          >
            全部已读
          </a-button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-bar">
        <a-statistic title="未读通知" :value="unreadCount" />
        <a-statistic title="总通知" :value="totalCount" />
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.通知类型"
            placeholder="通知类型"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="system_update">系统更新</a-select-option>
            <a-select-option value="business">业务通知</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.是否已读"
            placeholder="阅读状态"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option :value="false">未读</a-select-option>
            <a-select-option :value="true">已读</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button @click="resetFilters">重置筛选</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list">
      <a-spin :spinning="loading">
        <div v-if="notifications.length === 0" class="empty-state">
          <a-empty description="暂无通知" />
        </div>
        
        <div v-else class="notification-items">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{
              'unread': !notification.是否已读,
              'important': notification.重要性 >= 2
            }"
            @click="handleNotificationClick(notification)"
          >
            <!-- 通知图标 -->
            <div class="notification-icon">
              <component 
                :is="getNotificationIcon(notification.通知类型, notification.重要性)"
                :style="{ color: getImportanceColor(notification.重要性) }"
              />
            </div>

            <!-- 通知内容 -->
            <div class="notification-content">
              <div class="notification-header">
                <h3 class="notification-title">{{ notification.标题 }}</h3>
                <div class="notification-meta">
                  <a-tag 
                    :color="getImportanceColor(notification.重要性)"
                    size="small"
                  >
                    {{ formatImportanceLevel(notification.重要性) }}
                  </a-tag>
                  <a-tag size="small">
                    {{ formatNotificationType(notification.通知类型) }}
                  </a-tag>
                  <span class="notification-time">
                    {{ notification.时间显示 }}
                  </span>
                </div>
              </div>
              
              <div class="notification-summary">
                {{ notification.摘要 }}
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="notification-actions">
              <a-button
                v-if="!notification.是否已读"
                type="link"
                size="small"
                @click.stop="handleMarkAsRead(notification.id)"
              >
                标记已读
              </a-button>
              <a-button
                type="link"
                size="small"
                @click.stop="handleViewDetail(notification)"
              >
                查看详情
              </a-button>
            </div>

            <!-- 未读指示器 -->
            <div v-if="!notification.是否已读" class="unread-indicator"></div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <a-pagination
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 通知详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="通知详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedNotification" class="notification-detail">
        <div class="detail-header">
          <h2>{{ selectedNotification.标题 }}</h2>
          <div class="detail-meta">
            <a-tag 
              :color="getImportanceColor(selectedNotification.重要性)"
            >
              {{ formatImportanceLevel(selectedNotification.重要性) }}
            </a-tag>
            <a-tag>
              {{ formatNotificationType(selectedNotification.通知类型) }}
            </a-tag>
            <span class="detail-time">
              {{ selectedNotification.创建时间 }}
            </span>
          </div>
        </div>
        
        <div class="detail-content">
          <div
            v-for="(item, index) in selectedNotification.内容"
            :key="index"
            class="content-item"
          >
            <div v-if="item.类型 === '文本'" class="text-content">
              {{ item.内容 }}
            </div>
            <div v-else-if="item.类型 === '链接'" class="link-content">
              <a :href="item.内容" target="_blank">{{ item.内容 }}</a>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BellOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import notificationService from '../services/notificationService'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const markingAllRead = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const totalCount = ref(0)
const detailModalVisible = ref(false)
const selectedNotification = ref(null)

// 筛选器
const filters = reactive({
  通知类型: undefined,
  是否已读: undefined
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 轮询定时器
let pollingTimer = null

// 获取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true
    
    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      ...filters
    }
    
    const response = await notificationService.getNotificationList(params)
    
    if (response.status === 100 && response.data) {
      notifications.value = response.data.列表
      pagination.total = response.data.总数
      totalCount.value = response.data.总数
    }
  } catch (error) {
    console.error('获取通知列表失败:', error)
    message.error('获取通知列表失败')
  } finally {
    loading.value = false
  }
}

// 注意：fetchUnreadCount 函数已移除，现在使用全局智能轮询系统

// 处理筛选变化
const handleFilterChange = () => {
  pagination.current = 1
  fetchNotifications()
}

// 重置筛选器
const resetFilters = () => {
  filters.通知类型 = undefined
  filters.是否已读 = undefined
  pagination.current = 1
  fetchNotifications()
}

// 处理分页变化
const handlePageChange = (page, pageSize) => {
  pagination.current = page
  pagination.pageSize = pageSize
  fetchNotifications()
}

// 处理页面大小变化
const handlePageSizeChange = (current, size) => {
  pagination.current = 1
  pagination.pageSize = size
  fetchNotifications()
}

// 标记单个通知已读
const handleMarkAsRead = async (notificationId) => {
  try {
    const response = await notificationService.markAsRead(notificationId)
    if (response.status === 100) {
      message.success('标记已读成功')
      // 更新本地状态
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.是否已读 = true
        notification.阅读时间 = new Date().toISOString()
      }
      // markAsRead方法内部会自动触发智能轮询立即更新，无需手动调用
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    message.error('标记已读失败')
  }
}

// 标记所有通知已读
const handleMarkAllRead = async () => {
  try {
    markingAllRead.value = true
    const response = await notificationService.markAllAsRead()
    if (response.status === 100) {
      message.success('全部标记已读成功')
      fetchNotifications()
      // markAllAsRead方法内部会自动触发智能轮询立即更新，无需手动调用
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
    message.error('标记全部已读失败')
  } finally {
    markingAllRead.value = false
  }
}

// 处理通知点击
const handleNotificationClick = async (notification) => {
  await notificationService.handleNotificationClick(notification, router)
  // 刷新列表以更新已读状态
  fetchNotifications()
  // handleNotificationClick内部会自动触发智能轮询立即更新，无需手动调用
}

// 查看通知详情
const handleViewDetail = async (notification) => {
  try {
    const response = await notificationService.getNotificationDetail(notification.id)
    if (response.status === 100 && response.data) {
      selectedNotification.value = response.data
      detailModalVisible.value = true
      
      // 如果未读，标记为已读
      if (!notification.是否已读) {
        await handleMarkAsRead(notification.id)
      }
    }
  } catch (error) {
    console.error('获取通知详情失败:', error)
    message.error('获取通知详情失败')
  }
}

// 格式化通知类型
const formatNotificationType = (type) => {
  return notificationService.formatNotificationType(type)
}

// 格式化重要性级别
const formatImportanceLevel = (level) => {
  return notificationService.formatImportanceLevel(level).text
}

// 获取重要性颜色
const getImportanceColor = (level) => {
  return notificationService.formatImportanceLevel(level).color
}

// 获取通知图标
const getNotificationIcon = (type, importance) => {
  const iconName = notificationService.getNotificationIcon(type, importance)
  const iconMap = {
    'BellOutlined': BellOutlined,
    'InfoCircleOutlined': InfoCircleOutlined,
    'ExclamationCircleOutlined': ExclamationCircleOutlined,
    'WarningOutlined': WarningOutlined
  }
  return iconMap[iconName] || BellOutlined
}

// 组件挂载
onMounted(() => {
  fetchNotifications()

  // 使用全局智能轮询系统（不需要重复调用 fetchUnreadCount）
  const cleanup = notificationService.startSmartPolling((count) => {
    unreadCount.value = count
  })

  // 保存清理函数
  pollingTimer = cleanup
})

// 组件卸载
onUnmounted(() => {
  if (pollingTimer) {
    // 调用智能轮询的清理函数
    pollingTimer()
    pollingTimer = null
  }
})
</script>

<style scoped>
.notification-center {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-bar {
  display: flex;
  gap: 48px;
}

/* 筛选器 */
.filter-section {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 通知列表 */
.notification-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.empty-state {
  padding: 48px;
  text-align: center;
}

.notification-items {
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background: #fafafa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #f6ffed;
  border-left: 4px solid #52c41a;
}

.notification-item.important {
  border-left: 4px solid #ff4d4f;
}

.notification-icon {
  margin-right: 16px;
  font-size: 20px;
  margin-top: 4px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.notification-time {
  color: #8c8c8c;
  font-size: 12px;
}

.notification-summary {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notification-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
  flex-shrink: 0;
}

.unread-indicator {
  position: absolute;
  right: 16px;
  top: 20px;
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 通知详情模态框 */
.notification-detail {
  padding: 8px 0;
}

.detail-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-header h2 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-time {
  color: #8c8c8c;
  font-size: 14px;
  margin-left: 8px;
}

.detail-content {
  line-height: 1.6;
}

.content-item {
  margin-bottom: 12px;
}

.text-content {
  color: #262626;
  font-size: 14px;
}

.link-content a {
  color: #1890ff;
  text-decoration: none;
}

.link-content a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .stats-bar {
    gap: 24px;
  }

  .notification-item {
    padding: 12px 16px;
    flex-direction: column;
    align-items: stretch;
  }

  .notification-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .notification-meta {
    margin-left: 0;
  }

  .notification-actions {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
