<template>
  <div class="knowledge-base-creation">
    <!-- 页面标题 -->
    <div class="page-header">
      <a-page-header
        title="知识库创建与编译"
        sub-title="创建新的知识库并配置相关参数"
        @back="() => $router.go(-1)"
      >
        <template #extra>
          <a-space>
            <a-button @click="重置表单">
              <clear-outlined />
              重置
            </a-button>
            <a-button type="primary" @click="保存草稿" :loading="保存中">
              <save-outlined />
              保存草稿
            </a-button>
            <a-button type="primary" @click="创建知识库" :loading="创建中">
              <plus-outlined />
              创建知识库
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="creation-container">
      <a-row :gutter="24">
        <!-- 左侧：配置区域 -->
        <a-col :span="16">
          <a-card title="基础配置" class="config-card">
            <a-form
              ref="知识库表单"
              :model="知识库配置"
              :rules="表单验证规则"
              layout="vertical"
            >
              <!-- 基本信息 -->
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="知识库名称" name="名称">
                    <a-input
                      v-model:value="知识库配置.名称"
                      placeholder="请输入知识库名称"
                      :maxlength="50"
                      show-count
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="知识库类型" name="类型">
                    <a-select
                      v-model:value="知识库配置.类型"
                      placeholder="请选择知识库类型"
                    >
                      <a-select-option value="默认">默认知识库</a-select-option>
                      <a-select-option value="自定义">自定义知识库</a-select-option>
                      <a-select-option value="产品">产品知识库</a-select-option>
                      <a-select-option value="客服">客服知识库</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="知识库描述" name="描述">
                <a-textarea
                  v-model:value="知识库配置.描述"
                  placeholder="请输入知识库描述"
                  :rows="3"
                  :maxlength="200"
                  show-count
                />
              </a-form-item>

              <!-- 模型配置 -->
              <a-divider>模型配置</a-divider>
              
              <a-row :gutter="16">
                <!-- AI模型配置移除 - 应该在智能体配置中 -->
                <a-col :span="12">
                  <a-form-item label="嵌入模型" name="嵌入模型id">
                    <a-select
                      v-model:value="知识库配置.嵌入模型id"
                      placeholder="请选择嵌入模型"
                      :loading="嵌入模型加载中"
                      @focus="加载嵌入模型列表"
                      @change="处理嵌入模型变更"
                    >
                      <a-select-option
                        v-for="模型 in 嵌入模型列表"
                        :key="模型.id"
                        :value="模型.id"
                      >
                        <div class="model-option">
                          <span>{{ 模型.显示名称 }}</span>
                          <a-tag size="small" color="blue">
                            {{ 模型.向量维度 }}维
                          </a-tag>
                        </div>
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- MCP工具配置移除 - 应该在智能体配置中 -->

              <!-- 提示词配置移除 - 应该在智能体配置中 -->

              <!-- 知识库内容 -->
              <a-divider>知识库内容</a-divider>
              
              <a-form-item label="初始文档">
                <a-upload-dragger
                  v-model:fileList="上传文件列表"
                  name="files"
                  multiple
                  :before-upload="处理文件上传前"
                  @change="处理文件变化"
                  accept=".txt,.md,.pdf,.doc,.docx"
                >
                  <p class="ant-upload-drag-icon">
                    <inbox-outlined />
                  </p>
                  <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p class="ant-upload-hint">
                    支持单个或批量上传。支持 .txt, .md, .pdf, .doc, .docx 格式
                  </p>
                </a-upload-dragger>
              </a-form-item>

              <!-- 高级配置 -->
              <a-divider>高级配置</a-divider>
              
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="是否公开">
                    <a-switch
                      v-model:checked="知识库配置.是否公开"
                      checked-children="公开"
                      un-checked-children="私有"
                    />
                  </a-form-item>
                </a-col>
                <!-- 自动编译和版本控制功能移除 - 暂未实现 -->
              </a-row>
            </a-form>
          </a-card>
        </a-col>

        <!-- 右侧：预览和调试区域 -->
        <a-col :span="8">
          <a-card title="实时预览" class="preview-card">
            <a-tabs v-model:activeKey="预览标签页" type="card">
              <!-- 配置预览 -->
              <a-tab-pane key="config" tab="配置预览">
                <div class="config-preview">
                  <a-descriptions size="small" :column="1" bordered>
                    <a-descriptions-item label="知识库名称">
                      {{ 知识库配置.名称 || '未设置' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="类型">
                      {{ 知识库配置.类型 || '未选择' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="AI模型">
                      {{ 获取选中模型名称() }}
                    </a-descriptions-item>
                    <a-descriptions-item label="嵌入模型">
                      {{ 获取选中嵌入模型名称() }}
                    </a-descriptions-item>
                    <a-descriptions-item label="文档数量">
                      {{ 上传文件列表.length }} 个
                    </a-descriptions-item>
                    <a-descriptions-item label="是否公开">
                      <a-tag :color="知识库配置.是否公开 ? 'green' : 'orange'">
                        {{ 知识库配置.是否公开 ? '公开' : '私有' }}
                      </a-tag>
                    </a-descriptions-item>
                    <!-- 自动编译和版本控制显示移除 -->
                    <!-- MCP工具显示移除 -->
                  </a-descriptions>
                </div>
              </a-tab-pane>

              <!-- 提示词预览 -->
              <a-tab-pane key="prompt" tab="提示词预览">
                <div class="prompt-preview">
                  <a-typography>
                    <a-typography-title :level="5">系统提示词</a-typography-title>
                    <a-typography-paragraph>
                      <pre>{{ 知识库配置.系统提示词 || '未设置系统提示词' }}</pre>
                    </a-typography-paragraph>
                    
                    <a-typography-title :level="5">用户提示词模板</a-typography-title>
                    <a-typography-paragraph>
                      <pre>{{ 知识库配置.用户提示词模板 || '未设置用户提示词模板' }}</pre>
                    </a-typography-paragraph>
                  </a-typography>
                </div>
              </a-tab-pane>

              <!-- 调试测试 -->
              <a-tab-pane key="debug" tab="调试测试">
                <div class="debug-panel">
                  <a-space direction="vertical" style="width: 100%">
                    <a-input
                      v-model:value="测试问题"
                      placeholder="输入测试问题"
                      @press-enter="执行测试"
                    />
                    <a-button
                      type="primary"
                      block
                      @click="执行测试"
                      :loading="测试中"
                      :disabled="!知识库配置.名称"
                    >
                      测试知识库
                    </a-button>
                    
                    <div v-if="测试结果" class="test-result">
                      <a-typography-title :level="5">测试结果</a-typography-title>
                      <a-typography-paragraph>
                        {{ 测试结果 }}
                      </a-typography-paragraph>
                    </div>
                  </a-space>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>

          <!-- 操作历史 -->
          <a-card title="操作历史" class="history-card" style="margin-top: 16px">
            <a-timeline size="small">
              <a-timeline-item
                v-for="(历史, index) in 操作历史"
                :key="index"
                :color="历史.类型 === 'success' ? 'green' : 历史.类型 === 'error' ? 'red' : 'blue'"
              >
                <div class="history-item">
                  <div class="history-time">{{ 历史.时间 }}</div>
                  <div class="history-content">{{ 历史.内容 }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- MCP工具配置弹窗移除 - 应该在智能体配置中 -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  SaveOutlined,
  ClearOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import adminLangchainService from '@/services/adminLangchainService'

// 路由
const router = useRouter()

// 响应式数据
const 知识库表单 = ref()
const 创建中 = ref(false)
const 保存中 = ref(false)
const 模型加载中 = ref(false)
const 嵌入模型加载中 = ref(false)
const 测试中 = ref(false)
const AI模型列表 = ref([])
const 嵌入模型列表 = ref([])
const 上传文件列表 = ref([])
const 预览标签页 = ref('config')
const 测试问题 = ref('')
const 测试结果 = ref('')
const 操作历史 = ref([])

// MCP配置相关代码移除 - 应该在智能体配置中

// 知识库配置数据
const 知识库配置 = reactive({
  名称: '',
  类型: '自定义',
  描述: '',
  嵌入模型id: null,
  是否公开: false
})

// 表单验证规则
const 表单验证规则 = {
  名称: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  类型: [
    { required: true, message: '请选择知识库类型', trigger: 'change' }
  ],
  描述: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  模型id: [
    { required: true, message: '请选择AI对话模型', trigger: 'change' }
  ],
  嵌入模型id: [
    { required: true, message: '请选择嵌入向量模型', trigger: 'change' }
  ],
  系统提示词: [
    { max: 1000, message: '系统提示词不能超过1000个字符', trigger: 'blur' }
  ],
  用户提示词模板: [
    { max: 2000, message: '用户提示词模板不能超过2000个字符', trigger: 'blur' }
  ]
}

// 计算属性
const 获取选中模型名称 = () => {
  const 选中模型 = AI模型列表.value.find(m => m.id === 知识库配置.模型id)
  return 选中模型?.模型名称 || '未选择'
}

const 获取选中嵌入模型名称 = () => {
  const 选中模型 = 嵌入模型列表.value.find(m => m.id === 知识库配置.嵌入模型id)
  return 选中模型?.显示名称 || '未选择'
}

// 方法
const 添加操作历史 = (内容, 类型 = 'info') => {
  操作历史.value.unshift({
    时间: new Date().toLocaleTimeString(),
    内容,
    类型
  })
  // 只保留最近10条记录
  if (操作历史.value.length > 10) {
    操作历史.value = 操作历史.value.slice(0, 10)
  }
}

const 加载AI模型列表 = async () => {
  if (AI模型列表.value.length > 0) return

  模型加载中.value = true
  try {
    const response = await adminLangchainService.获取可用AI模型列表()

    if (response.status === 100) {
      AI模型列表.value = response.data
      添加操作历史('加载AI模型列表成功', 'success')

      // 自动选择默认模型
      nextTick(() => {
        自动选择默认模型()
      })
    } else {
      throw new Error(response.message || '获取AI模型列表失败')
    }
  } catch (error) {
    console.error('加载AI模型列表失败:', error)
    message.error('加载AI模型列表失败')
    添加操作历史('加载AI模型列表失败', 'error')
  } finally {
    模型加载中.value = false
  }
}

const 加载嵌入模型列表 = async () => {
  if (嵌入模型列表.value.length > 0) return

  嵌入模型加载中.value = true
  try {
    const response = await adminLangchainService.获取可用嵌入模型列表()

    if (response.status === 100) {
      嵌入模型列表.value = response.data
      添加操作历史('加载嵌入模型列表成功', 'success')

      // 自动选择默认模型
      nextTick(() => {
        自动选择默认模型()
      })
    } else {
      throw new Error(response.message || '获取嵌入模型列表失败')
    }
  } catch (error) {
    console.error('加载嵌入模型列表失败:', error)
    message.error('加载嵌入模型列表失败')
    添加操作历史('加载嵌入模型列表失败', 'error')
  } finally {
    嵌入模型加载中.value = false
  }
}

const 处理嵌入模型变更 = (嵌入模型id) => {
  const 选中模型 = 嵌入模型列表.value.find(m => m.id === 嵌入模型id)
  if (选中模型) {
    添加操作历史(`选择嵌入模型: ${选中模型.显示名称}`, 'info')
  }
}

// 自动选择默认模型
const 自动选择默认模型 = () => {
  // 自动选择第一个可用的嵌入模型
  if (嵌入模型列表.value.length > 0 && !知识库配置.嵌入模型id) {
    知识库配置.嵌入模型id = 嵌入模型列表.value[0].id
    处理嵌入模型变更(知识库配置.嵌入模型id)
    添加操作历史(`自动选择默认嵌入模型: ${嵌入模型列表.value[0].显示名称}`, 'info')
  }

  // 自动选择第一个可用的AI模型
  if (AI模型列表.value.length > 0 && !知识库配置.模型id) {
    知识库配置.模型id = AI模型列表.value[0].id
    添加操作历史(`自动选择默认AI模型: ${AI模型列表.value[0].模型名称}`, 'info')
  }
}

// MCP相关方法移除 - 应该在智能体配置中

const 处理文件上传前 = (file) => {
  // 验证文件类型和大小
  const 允许的类型 = ['text/plain', 'text/markdown', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  const 文件大小限制 = 10 * 1024 * 1024 // 10MB
  
  if (!允许的类型.includes(file.type)) {
    message.error('不支持的文件类型')
    return false
  }
  
  if (file.size > 文件大小限制) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  return false // 阻止自动上传，我们手动处理
}

const 处理文件变化 = (info) => {
  添加操作历史(`${info.file.status === 'removed' ? '移除' : '添加'}文件: ${info.file.name}`)
}

const 执行测试 = async () => {
  if (!测试问题.value.trim()) {
    message.warning('请输入测试问题')
    return
  }
  
  测试中.value = true
  try {
    // TODO: 调用测试API
    // const response = await knowledgeBaseService.testKnowledgeBase({
    //   配置: 知识库配置,
    //   问题: 测试问题.value
    // })
    
    // 模拟测试结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    测试结果.value = `基于当前配置，AI回答：这是一个测试回答，针对问题"${测试问题.value}"的响应。`
    
    添加操作历史('执行知识库测试成功', 'success')
  } catch (error) {
    console.error('测试失败:', error)
    message.error('测试失败')
    添加操作历史('知识库测试失败', 'error')
  } finally {
    测试中.value = false
  }
}

const 重置表单 = () => {
  Modal.confirm({
    title: '确认重置',
    content: '重置将清空所有已填写的内容，确定要继续吗？',
    onOk() {
      知识库表单.value.resetFields()
      上传文件列表.value = []
      测试结果.value = ''
      添加操作历史('重置表单', 'info')
      message.success('表单已重置')
    }
  })
}

const 保存草稿 = async () => {
  保存中.value = true
  try {
    // TODO: 调用保存草稿API
    await new Promise(resolve => setTimeout(resolve, 1000))
    添加操作历史('保存草稿成功', 'success')
    message.success('草稿保存成功')
  } catch (error) {
    console.error('保存草稿失败:', error)
    message.error('保存草稿失败')
    添加操作历史('保存草稿失败', 'error')
  } finally {
    保存中.value = false
  }
}

const 创建知识库 = async () => {
  try {
    await 知识库表单.value.validate()

    创建中.value = true

    // 调用创建LangChain知识库API - 适配新的后端架构
    const response = await adminLangchainService.创建LangChain知识库({
      知识库名称: 知识库配置.名称,
      知识库描述: 知识库配置.描述,
      嵌入模型id: 知识库配置.嵌入模型id,  // 嵌入模型配置表的ID
      AI模型id: 知识库配置.模型id,        // AI模型配置表的ID
      系统提示词: 知识库配置.系统提示词,
      用户提示词模板: 知识库配置.用户提示词模板,
      是否公开: 知识库配置.是否公开
    })

    if (response.status === 100) {
      添加操作历史('知识库创建成功', 'success')
      message.success('知识库创建成功')

      // 跳转到知识库管理页面
      router.push('/store')
    } else {
      throw new Error(response.message || '创建失败')
    }

  } catch (error) {
    if (error.errorFields) {
      message.error('请完善表单信息')
    } else {
      console.error('创建知识库失败:', error)
      message.error(error.message || '创建知识库失败')
      添加操作历史('知识库创建失败', 'error')
    }
  } finally {
    创建中.value = false
  }
}

// 生命周期
onMounted(async () => {
  添加操作历史('进入知识库创建页面')

  // 自动加载模型列表
  try {
    await Promise.all([
      加载AI模型列表(),
      加载嵌入模型列表()
    ])
    添加操作历史('模型列表加载完成', 'success')
  } catch (error) {
    console.error('模型列表加载失败:', error)
    添加操作历史('模型列表加载失败', 'error')
  }
})
</script>

<style scoped>
.knowledge-base-creation {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.creation-container {
  max-width: 1400px;
  margin: 0 auto;
}

.config-card,
.preview-card,
.history-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.知识库选项 {
  display: flex;
  align-items: center;
  gap: 8px;
}

.知识库描述 {
  color: #999;
  font-size: 12px;
}

.config-preview {
  max-height: 300px;
  overflow-y: auto;
}

.prompt-preview pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.debug-panel {
  padding: 16px 0;
}

.test-result {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-top: 12px;
}

.history-item {
  font-size: 12px;
}

.history-time {
  color: #999;
  margin-bottom: 4px;
}

.history-content {
  color: #333;
}

/* MCP相关样式移除 */

.tool-name {
  font-weight: 500;
  color: #333;
}

.tool-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.统计信息区域 {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}
</style>
