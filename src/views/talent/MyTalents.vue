<template>
  <div class="my-talents">
    <!-- 平台切换器 -->
    <PlatformSwitcher
      v-model="currentPlatform"
      :show-stats="false"
      :show-description="false"
      @platform-change="handlePlatformChange"
      class="platform-switcher-section"
    />



    <!-- 搜索和筛选区域 -->
    <div class="search-filters">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchForm.关键词"
              :placeholder="currentPlatform === 'douyin' ? '搜索抖音号' : '搜索微信号、昵称'"
              enter-button="搜索"
              size="large"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :span="3">
            <a-select
              v-model:value="searchForm.排序字段"
              placeholder="排序方式"
              size="large"
              style="width: 100%"
              @change="handleSearch"
            >
              <a-select-option value="认领时间">认领时间</a-select-option>
              <a-select-option value="粉丝数">粉丝数</a-select-option>
              <a-select-option value="更新时间">更新时间</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model:value="searchForm.排序方式"
              placeholder="排序方向"
              size="large"
              style="width: 100%"
              @change="handleSearch"
            >
              <a-select-option value="desc">降序</a-select-option>
              <a-select-option value="asc">升序</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-button type="default" size="large" @click="handleReset" block>
              <reload-outlined />
              重置
            </a-button>
          </a-col>
          <a-col :span="3">
            <a-button type="primary" size="large" @click="handleAddTalent" block>
              <plus-outlined />
              添加达人
            </a-button>
          </a-col>
          <a-col :span="2">
            <a-button type="default" size="large" @click="handleExport" block>
              <download-outlined />
              导出
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 达人列表 -->
    <div class="talents-list">
      <a-card>
        <template #title>
          <div style="display: flex; align-items: center; gap: 12px;">
            <span>我的达人列表</span>
            <a-tag color="blue" style="margin: 0;">
              总数：{{ pagination.total || 0 }}
            </a-tag>
            <a-tag color="green" style="margin: 0;" v-if="currentPlatform">
              {{ currentPlatform === 'douyin' ? '抖音平台' : '微信平台' }}
            </a-tag>
          </div>
        </template>
        
        <template #extra>
          <a-space>
            <a-button @click="refreshList" :loading="talentsLoading">
              <reload-outlined />
              刷新
            </a-button>
          </a-space>
        </template>

        <!-- 达人卡片列表 -->
        <a-spin :spinning="talentsLoading" tip="正在加载达人信息...">
          <!-- 空状态显示 -->
          <div v-if="talents.length === 0 && !talentsLoading && !showSkeletons" class="empty-state">
            <a-empty description="暂无认领的达人">
              <a-button type="primary" @click="$router.push('/talent/talent-pool')">
                <plus-outlined />
                去达人公海认领
              </a-button>
            </a-empty>
          </div>

          <!-- 骨架屏显示 -->
          <div v-else-if="showSkeletons" class="skeleton-container">
            <a-row :gutter="[20, 20]">
              <a-col
                v-for="index in 6"
                :key="`skeleton-${index}`"
                :xs="24"
                :sm="24"
                :md="24"
                :lg="12"
                :xl="12"
                :xxl="8"
              >
                <a-card class="talent-card skeleton-card">
                  <template #cover>
                    <div class="talent-cover skeleton-cover">
                      <div class="skeleton-avatar"></div>
                      <div class="skeleton-info">
                        <div class="skeleton-name"></div>
                        <div class="skeleton-account"></div>
                        <div class="skeleton-tags">
                          <div class="skeleton-tag"></div>
                          <div class="skeleton-tag"></div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <a-card-meta>
                    <template #description>
                      <div class="skeleton-desc">
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line short"></div>
                      </div>
                    </template>
                  </a-card-meta>
                </a-card>
              </a-col>
            </a-row>
          </div>

          <!-- 实际达人列表 -->
          <a-row v-else :gutter="[20, 20]">
            <a-col
              v-for="talent in talents"
              :key="talent.id"
              :xs="24"
              :sm="24"
              :md="24"
              :lg="12"
              :xl="12"
              :xxl="8"
            >
              <a-card
                class="talent-card"
                hoverable
                @click="handleCardClick($event, talent)"
              >
                <template #cover>
                  <div class="talent-cover">
                    <!-- 认领时间 - 右上角 -->
                    <div class="claim-time-badge">
                      {{ formatDate(talent.认领时间) }}
                    </div>

                    <!-- 使用懒加载头像组件 -->
                    <LazyAvatar
                      :src="talent.avatar || talent.头像 || '/images/default_avatar.svg'"
                      :alt="talent.昵称 || '达人头像'"
                      :size="64"
                      :delay="Math.random() * 200"
                      class="talent-avatar"
                    />
                    <div class="talent-info">
                      <h3 class="talent-name selectable-text" @click.stop>{{ talent.昵称 || '未知昵称' }}</h3>
                      <p class="talent-account selectable-text" @click.stop>
                        {{ currentPlatform === 'wechat'
                            ? (talent.微信号 || '微信达人')
                            : (talent.uid_number || talent.account_douyin || talent.抖音号 || '抖音号未知')
                        }}
                      </p>
                      <div class="talent-stats">
                        <a-tag color="blue">
                          粉丝 {{ currentPlatform === 'wechat'
                                  ? (talent.粉丝数文本 || '未知')
                                  : formatNumber(talent.粉丝数)
                              }}
                        </a-tag>
                        <a-tag v-if="currentPlatform === 'douyin'" color="green">
                          关注 {{ formatNumber(talent.关注数) }}
                        </a-tag>
                        <a-tag v-if="currentPlatform === 'wechat'" color="orange">
                          GMV {{ talent.GMV文本 || '未知' }}
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </template>



                <a-card-meta>
                  <template #description>
                    <div class="talent-meta">
                      <!-- 达人基础介绍 -->
                      <p class="talent-desc selectable-text" @click.stop>{{ talent.introduction || '暂无介绍' }}</p>
                      
                      <!-- 个人管理信息区域 - 这是编辑后展示的核心区域 -->
                      <div class="personal-info-section" v-if="hasPersonalInfo(talent)">
                        <a-divider style="margin: 8px 0;" />
                        <div class="personal-info-title">
                          <span class="info-label">个人管理信息</span>
                        </div>
                        
                        <!-- 合作状态显示 -->
                        <div class="cooperation-status" v-if="talent.合作状态">
                          <a-tag :color="getCooperationStatusColor(talent.合作状态)">
                            {{ talent.合作状态 }}
                          </a-tag>
                        </div>
                        

                        
                        <!-- 个人备注预览 -->
                        <div class="personal-note" v-if="talent.个人备注">
                          <div class="note-content">
                            <span class="note-text">{{ formatNote(talent.个人备注) }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 原有联系方式标识（作为备用显示） -->
                      <div class="talent-contact" v-if="!hasPersonalInfo(talent) && (talent.联系方式 || talent.wechat_id || talent.phone || talent.email)">
                        <a-divider style="margin: 8px 0;" />
                        <a-tag color="orange">
                          <phone-outlined />
                          有联系方式
                        </a-tag>
                      </div>
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-spin>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="talents.length > 0">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`"
            @change="handlePageChange"
            @show-size-change="handlePageSizeChange"
          />
        </div>
      </a-card>
    </div>

    <!-- 达人详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="达人详情"
      :width="1000"
      placement="right"
      @close="handleDetailDrawerClose"
    >
      <talent-detail-component
        v-if="selectedTalent"
        :talent="selectedTalent"
        @update="handleTalentUpdate"
        @unclaim="handleUnclaimFromDetail"
      />
    </a-drawer>



    <!-- 添加达人弹窗 -->
    <a-modal
      v-model:open="addTalentModalVisible"
      title="添加达人"
      :width="600"
      :confirm-loading="addTalentLoading"
      @ok="handleAddTalentConfirm"
      @cancel="handleAddTalentCancel"
    >
      <div class="add-talent-form">
        <!-- 搜索输入框 -->
        <div class="search-section">
          <a-form-item label="达人抖音号" required>
            <a-input-search
              v-model:value="addTalentForm.douyinId"
              placeholder="请输入达人抖音号"
              size="large"
              enter-button="搜索"
              :loading="searchLoading"
              @search="handleSearchTalent"
              allow-clear
            />
          </a-form-item>
          <p class="search-tip">
            <info-circle-outlined style="color: #1890ff; margin-right: 4px;" />
            输入达人的抖音号进行搜索，系统将自动匹配相关达人信息
          </p>
        </div>

        <!-- 搜索结果列表 -->
        <div v-if="searchResults.length > 0" class="search-results">
          <a-divider>搜索结果</a-divider>
          <div class="results-list">
            <div
              v-for="(talent, index) in searchResults"
              :key="index"
              class="talent-item"
              :class="{ 'selected': selectedSearchResult === index }"
              @click="selectSearchResult(index)"
            >
              <div class="talent-avatar">
                <a-avatar :size="48" :src="talent.头像">
                  <template #icon>
                    <user-outlined />
                  </template>
                </a-avatar>
              </div>
              <div class="talent-info">
                <div class="talent-name">{{ talent.昵称 || '未知昵称' }}</div>
                <div class="talent-account">{{ talent.抖音号 || '抖音号未知' }}</div>
                <div class="talent-stats">
                  <a-tag color="blue">
                    <team-outlined />
                    粉丝 {{ formatFollowerCount(talent.粉丝数) }}
                  </a-tag>
                  <a-tag color="orange" v-if="talent.UID">
                    UID: {{ talent.UID }}
                  </a-tag>
                </div>
              </div>
              <div class="select-indicator">
                <a-radio :checked="selectedSearchResult === index" />
              </div>
            </div>
          </div>
        </div>

        <!-- 无搜索结果提示 -->
        <div v-if="searchExecuted && searchResults.length === 0" class="no-results">
          <a-empty
            description="未找到相关达人"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          >
            <template #description>
              <div class="no-results-content">
                <p class="no-results-title">
                  未找到与"{{ addTalentForm.douyinId }}"相关的达人信息
                </p>
                <div class="no-results-suggestions">
                  <p class="suggestions-title">建议您：</p>
                  <ul class="suggestions-list">
                    <li>检查抖音号是否输入正确</li>
                    <li>尝试输入达人的完整抖音号</li>
                    <li>确认该达人是否为公开账号</li>
                    <li>稍后再试，可能是网络问题</li>
                  </ul>
                </div>
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="handleSearchTalent"
                  :loading="searchLoading"
                  style="margin-top: 12px;"
                >
                  <reload-outlined />
                  重新搜索
                </a-button>
              </div>
            </template>
          </a-empty>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Empty } from 'ant-design-vue'
import {
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  PlusOutlined,
  PhoneOutlined,
  InfoCircleOutlined,
  UserOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'
import talentService from '@/services/talentService'
import TalentDetailComponent from '@/components/talent/TalentDetailComponent.vue'
import PlatformSwitcher from '@/components/talent/PlatformSwitcher.vue'
import LazyAvatar from '@/components/common/LazyAvatar.vue'

// 响应式数据
const loading = ref(false)
const talents = ref([])

// 新增：分阶段加载状态控制
const talentsLoading = ref(false)  // 达人列表加载状态
const showSkeletons = ref(true)    // 是否显示骨架屏

// 添加达人相关数据
const addTalentModalVisible = ref(false)
const addTalentLoading = ref(false)
const searchLoading = ref(false)
const searchExecuted = ref(false)
const searchResults = ref([])
const selectedSearchResult = ref(null)
const addTalentForm = reactive({
  douyinId: ''
})

// 平台切换相关数据
const currentPlatform = ref('douyin')
const selectedTalent = ref(null)
const detailDrawerVisible = ref(false)



// 搜索表单
const searchForm = reactive({
  关键词: '',
  排序字段: '认领时间',
  排序方式: 'desc'
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 格式化数字显示
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

/**
 * 检查达人是否有个人管理信息
 * 用于判断是否显示个人信息区域
 * 
 * @param {Object} talent - 达人对象
 * @returns {boolean} 是否有个人管理信息
 */
const hasPersonalInfo = (talent) => {
  return !!(
    talent.微信号 || 
    talent.手机号 || 
    talent.邮箱 || 
    talent.合作状态 || 
    talent.个人备注 || 
    (talent.个人标签 && talent.个人标签.length > 0)
  )
}

/**
 * 获取合作状态对应的颜色
 * 不同状态使用不同颜色便于快速识别
 * 
 * @param {string} status - 合作状态
 * @returns {string} 对应的颜色
 */
const getCooperationStatusColor = (status) => {
  const statusColors = {
    '待联系': 'default',
    '已联系': 'blue',
    '洽谈中': 'orange',
    '合作中': 'green',
    '已结束': 'purple',
    '暂停合作': 'red'
  }
  return statusColors[status] || 'default'
}

/**
 * 格式化手机号显示
 * 隐藏中间4位数字保护隐私
 * 
 * @param {string} phone - 手机号
 * @returns {string} 格式化后的手机号
 */
const formatPhone = (phone) => {
  if (!phone) return ''
  if (phone.length === 11) {
    return `${phone.slice(0, 3)}****${phone.slice(-4)}`
  }
  return phone
}

/**
 * 格式化邮箱显示
 * 隐藏部分字符保护隐私
 * 
 * @param {string} email - 邮箱地址
 * @returns {string} 格式化后的邮箱
 */
const formatEmail = (email) => {
  if (!email) return ''
  const [username, domain] = email.split('@')
  if (username && domain) {
    const hiddenUsername = username.length > 3 
      ? `${username.slice(0, 2)}***${username.slice(-1)}`
      : username
    return `${hiddenUsername}@${domain}`
  }
  return email
}

/**
 * 格式化个人备注显示
 * 限制显示长度，超出部分用省略号表示
 * 
 * @param {string} note - 个人备注
 * @returns {string} 格式化后的备注
 */
const formatNote = (note) => {
  if (!note) return ''
  return note.length > 50 ? `${note.slice(0, 50)}...` : note
}

// 加载我的达人列表 - 优化版：先显示达人基础信息，再异步加载统计数据
const loadMyTalents = async () => {
  try {
    // 第一阶段：加载达人基础信息
    console.log('🚀 开始加载达人基础信息')
    talentsLoading.value = true
    showSkeletons.value = true
    
    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      排序字段: searchForm.排序字段,
      排序方式: searchForm.排序方式,
      筛选条件: null,
      关键词: searchForm.关键词 || null
    }
    
    // 根据当前平台调用不同的API
    let response
    if (currentPlatform.value === 'douyin') {
      response = await talentService.getMyTalents(params)
    } else if (currentPlatform.value === 'wechat') {
      response = await talentService.getMyWechatTalents(params)
    } else {
      throw new Error('不支持的平台类型')
    }

    if (response.status === 100) {
      // 立即显示达人基础数据
      let rawTalents = response.data.达人列表 || response.data.列表 || []

      // 直接使用原始数据，不需要复杂的字段映射
      talents.value = rawTalents

      pagination.total = response.data.总数 || 0
      
      // 立即隐藏达人列表的骨架屏，显示实际内容
      showSkeletons.value = false
      console.log('✅ 达人基础信息加载完成，共', talents.value.length, '个达人')

    } else {
      message.error(response.message || '获取达人列表失败')
      showSkeletons.value = false
    }
  } catch (error) {
    console.error('加载达人列表失败:', error)
    message.error('网络错误，请稍后重试')
    showSkeletons.value = false
  } finally {
    talentsLoading.value = false
  }
}









/**
 * 从达人列表计算统计数据（用于微信平台）
 */


/**
 * 更新平台统计数据
 * 根据当前统计数据更新平台切换器显示的数据
 * 修复：只更新当前平台的数据，不影响其他平台
 */






// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  loadMyTalents()
}

/**
 * 清除所有筛选条件
 * 点击"达人总数"统计项时触发
 */
const clearFilters = () => {
  searchForm.value = {
    关键词: '',
    平台: currentPlatform.value,
    有联系方式: undefined,
    合作状态: undefined,
    标签: undefined
  }
  handleSearch()
  message.info('已清除所有筛选条件')
}





// 处理重置
const handleReset = () => {
  searchForm.关键词 = ''
  searchForm.排序字段 = '认领时间'
  searchForm.排序方式 = 'desc'
  pagination.current = 1
  loadMyTalents()
}

/**
 * 处理平台切换 - 优化版：快速切换并异步加载数据
 * @param {Object} platformInfo - 平台信息
 */
const handlePlatformChange = async (platformInfo) => {
  console.log('🔄 我的达人 - 平台切换:', platformInfo)

  // 重置搜索条件
  searchForm.关键词 = ''
  searchForm.排序字段 = '认领时间'
  searchForm.排序方式 = 'desc'

  // 重置分页
  pagination.current = 1

  // 清除选中状态
  selectedTalent.value = null

  // 立即清空当前数据，显示骨架屏
  talents.value = []
  showSkeletons.value = true

  // 重新加载当前平台的数据
  await loadMyTalents()
}

// 处理导出
const handleExport = () => {
  message.info('导出功能开发中...')
}

// 添加达人相关方法
const handleAddTalent = () => {
  addTalentModalVisible.value = true
  // 重置表单和搜索结果
  addTalentForm.douyinId = ''
  searchResults.value = []
  selectedSearchResult.value = null
  searchExecuted.value = false
}

const handleAddTalentCancel = () => {
  addTalentModalVisible.value = false
  // 重置表单和搜索结果
  addTalentForm.douyinId = ''
  searchResults.value = []
  selectedSearchResult.value = null
  searchExecuted.value = false
}

const handleAddTalentConfirm = async () => {
  if (selectedSearchResult.value === null) {
    message.warning('请先搜索并选择一个达人')
    return
  }

  const selectedTalent = searchResults.value[selectedSearchResult.value]
  console.log('确认添加达人:', selectedTalent)

  // 获取选中达人的UID
  const UID = selectedTalent.UID
  
  if (!UID) {
    message.error('选中的达人缺少UID信息，请重新搜索')
    return
  }

  addTalentLoading.value = true

  try {
    // 调用后端添加达人接口 - 简化版本，只传UID
    const response = await talentService.addTalent({
      UID: UID
    })

    if (response.status === 100) {
      message.success('添加达人成功！')
      // 关闭弹窗
      handleAddTalentCancel()
      // 刷新达人列表
      await loadMyTalents()
    } else {
      message.error(response.message || '添加达人失败')
    }
  } catch (error) {
    console.error('添加达人失败:', error)
    message.error('添加达人失败，请重试')
  } finally {
    addTalentLoading.value = false
  }
}

const handleSearchTalent = async () => {
  if (!addTalentForm.douyinId.trim()) {
    message.warning('请输入达人抖音号')
    return
  }

  console.log('搜索达人:', addTalentForm.douyinId)
  searchLoading.value = true
  searchExecuted.value = true

  try {
    // 调用后端搜索达人接口
    const response = await talentService.searchTalent({
      抖音号: addTalentForm.douyinId.trim()
    })

    if (response.status === 100) {
      const data = response.data

      // 适配新的API返回格式
      if (data.达人信息) {
        // 有达人信息（单个达人或已存在）
        searchResults.value = [data.达人信息]
        selectedSearchResult.value = 0
        
        if (data.已存在) {
          message.success('找到已存在的达人')
        } else {
          message.success('找到匹配的达人')
        }
      } else if (data.达人列表) {
        // 多个达人列表
        searchResults.value = data.达人列表
        
        if (data.需要确认 && data.达人列表.length > 1) {
          selectedSearchResult.value = null
          message.success(`找到 ${data.达人列表.length} 个相关达人，请选择`)
        } else if (data.达人列表.length === 0) {
          selectedSearchResult.value = null
          message.info(`未找到与"${addTalentForm.douyinId}"相关的达人，请检查抖音号是否正确`)
        } else {
          selectedSearchResult.value = 0
          message.success('找到匹配的达人')
        }
      } else {
        // 无结果
        searchResults.value = []
        selectedSearchResult.value = null
        message.info(`未找到与"${addTalentForm.douyinId}"相关的达人，请检查抖音号是否正确`)
      }
    } else {
      searchResults.value = []
      selectedSearchResult.value = null
      message.error(response.message || '搜索失败，请重试')
    }
  } catch (error) {
    console.error('搜索达人失败:', error)
    message.error('搜索失败，请重试')
    searchResults.value = []
  } finally {
    searchLoading.value = false
  }
}

const selectSearchResult = (index) => {
  console.log('选择搜索结果:', index)
  selectedSearchResult.value = index
}

// 格式化粉丝数显示
const formatFollowerCount = (count) => {
  if (!count) return '0'
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  return count.toString()
}

// 刷新列表
const refreshList = () => {
  loadMyTalents()
}

/**
 * 处理卡片点击事件
 * 点击空白区域打开详情，点击文字区域不触发（方便复制文字）
 */
const handleCardClick = (event, talent) => {
  // 检查点击的目标是否是文字区域
  const target = event.target
  const isTextElement = target.classList.contains('selectable-text') ||
                       target.closest('.selectable-text')

  // 如果点击的是文字区域，不打开详情
  if (isTextElement) {
    return
  }

  viewTalentDetail(talent)
}

// 查看达人详情
const viewTalentDetail = async (talent) => {
  try {
    // 先显示抽屉和加载状态
    selectedTalent.value = { ...talent, loading: true }
    detailDrawerVisible.value = true

    // 调用后端接口获取完整详情
    const response = await talentService.getTalentDetail(talent.id)

    if (response.status === 100 && response.data) {
      // 合并原有数据和详情数据
      selectedTalent.value = {
        ...talent,
        ...response.data,
        loading: false
      }
    } else {
      message.error(response.message || '获取达人详情失败')
      selectedTalent.value = { ...talent, loading: false }
    }
  } catch (error) {
    console.error('获取达人详情失败:', error)
    message.error('获取达人详情失败，请稍后重试')
    selectedTalent.value = { ...talent, loading: false }
  }
}




/**
 * 处理详情页抽屉关闭事件
 * 用户手动关闭详情页时清空选中状态，确保下次打开时不会显示错误数据
 */
const handleDetailDrawerClose = () => {
  selectedTalent.value = null
}

/**
 * 处理达人更新事件
 * 重新加载数据并关闭详情页
 */
const handleTalentUpdate = () => {
  loadMyTalents()
  detailDrawerVisible.value = false
}



/**
 * 处理从详情页触发的取消认领操作
 * @param {Object} talent - 要取消认领的达人对象
 */
const handleUnclaimFromDetail = async (talent) => {
  try {
    const response = await talentService.unclaimTalent(talent.id)
    if (response.status === 100) {
      message.success('取消认领成功')
      // 重新加载达人列表
      loadMyTalents()
      // 关闭详情页抽屉
      detailDrawerVisible.value = false
    } else {
      message.error(response.message || '取消认领失败')
    }
  } catch (error) {
    console.error('取消认领失败:', error)
    message.error('操作失败，请稍后重试')
  }
}



// 处理分页变化
const handlePageChange = (page, pageSize) => {
  pagination.current = page
  pagination.pageSize = pageSize
  loadMyTalents()
}

// 处理页面大小变化
const handlePageSizeChange = (current, size) => {
  pagination.current = 1
  pagination.pageSize = size
  loadMyTalents()
}

// 组件挂载时加载数据
onMounted(async () => {
  console.log('🚀 MyTalents组件初始化，当前平台:', currentPlatform.value)
  
  // 只加载当前平台的达人列表和统计数据
  await loadMyTalents()
  
  // 注意：不再自动加载其他平台的统计数据
  // 其他平台的数据只在用户切换平台时才加载，避免不必要的请求
  console.log('✅ 页面初始化完成，仅加载了当前平台数据')
})

defineOptions({
  name: 'MyTalents',
  components: {
    LazyAvatar
  }
})
</script>

<style scoped>
.my-talents {
  padding: 0;
}

/* 平台切换器样式 */
.platform-switcher-section {
  margin-bottom: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}





/* === 动画效果 === */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.rotate-enter-active,
.rotate-leave-active {
  transition: transform 0.3s ease;
}

.rotate-enter-from {
  transform: rotate(180deg);
}

.rotate-leave-to {
  transform: rotate(180deg);
}

.stats-cards .stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.stats-cards .stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stats-cards .stat-card.clickable {
  cursor: pointer;
}

.stats-cards .stat-card.clickable:hover {
  border-color: #1890ff;
}

.stat-tip {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-cards .stat-card.clickable:hover .stat-tip {
  opacity: 1;
}

/* === 增强统计卡片样式 === */
.stat-card.enhanced {
  position: relative;
  overflow: visible;
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
}

.growth-text {
  font-weight: 600;
}

.growth-text.growth-positive {
  color: #52c41a;
}

.growth-text.growth-negative {
  color: #f5222d;
}

.growth-text.growth-neutral {
  color: #8c8c8c;
}

.growth-period {
  color: #8c8c8c;
  font-size: 11px;
}

.comparison-data {
  margin-top: 8px;
  font-size: 12px;
}

.comparison-text {
  color: #8c8c8c;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.detail-text {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
}

.conversion-funnel {
  margin-top: 8px;
}

.funnel-step {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.step-label {
  color: #8c8c8c;
}

.step-value {
  color: #722ed1;
  font-weight: 600;
}

/* === 业务洞察面板样式 === */
.insights-panel {
  margin-top: 16px;
}

.insights-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.insights-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.insights-card .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.insight-item {
  padding: 16px;
  border-radius: 6px;
  background: #fafafa;
  height: 100%;
}

.insight-item h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.insight-text {
  margin: 0 0 12px 0;
  color: #595959;
  font-size: 13px;
  line-height: 1.4;
}

.insight-metrics {
  font-size: 12px;
  color: #8c8c8c;
}



.search-filters {
  margin-bottom: 16px;
}

.talents-list {
  margin-bottom: 16px;
}

.talent-card {
  height: 100%;
  cursor: pointer;
  user-select: none; /* 默认不可选择 */
  min-width: 380px;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* 可选择的文字区域 */
.selectable-text {
  user-select: text !important;
  cursor: text !important;
  position: relative;
  display: inline-block;
  width: fit-content;
  max-width: 100%;
  padding: 1px 2px;
  margin: -1px -2px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.selectable-text:hover {
  background-color: rgba(24, 144, 255, 0.05);
}

.talent-cover {
  display: flex;
  padding: 12px;
  align-items: center;
  position: relative;
}

.claim-time-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  z-index: 1;
  backdrop-filter: blur(4px);
}

.talent-avatar {
  margin-right: 12px;
  flex-shrink: 0;
  width: 64px !important;
  height: 64px !important;
}

.talent-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.talent-name {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  line-height: 1.3;
}

.talent-account {
  margin: 0;
  color: #8c8c8c;
  font-size: 13px;
  line-height: 1.2;
}

.talent-stats {
  display: flex;
  gap: 6px;
  margin-top: 4px;
}

.talent-stats .ant-tag {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
  line-height: 1.2;
}

.talent-meta {
  min-height: 60px;
  padding: 8px 12px 12px 12px;
}

.talent-desc {
  margin: 0 0 6px 0;
  color: #595959;
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* 标准属性，用于兼容性 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.talent-contact {
  display: flex;
  gap: 4px;
  margin-top: 6px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
}

/* 个人管理信息区域样式 - 精简版 */
.personal-info-section {
  margin-top: 6px;
  padding: 8px 10px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.info-label {
  font-size: 11px;
  color: #1890ff;
  font-weight: 600;
  background: #e6f7ff;
  padding: 1px 6px;
  border-radius: 3px;
  border: 1px solid #91d5ff;
}

/* 通用间距样式 */
.personal-info-title,
.cooperation-status,
.contact-preview,
.personal-tags {
  margin-bottom: 6px;
}

.personal-info-title {
  margin-bottom: 4px;
}

/* 布局样式 */
.contact-items,
.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.contact-item {
  display: inline-block;
}

.more-tags {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 4px;
}

/* 备注样式 */
.note-content {
  background: #f6f6f6;
  padding: 6px 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.note-text {
  font-size: 12px;
  color: #595959;
  line-height: 1.4;
  font-style: italic;
}



/* === 响应式设计 === */
/* 防止卡片重叠的基础规则 */
.ant-col {
  display: flex;
  flex-direction: column;
}

.ant-col .talent-card {
  flex: 1;
  width: 100%;
}

/* 考虑侧边栏展开状态的响应式布局 */
@media (max-width: 1800px) {
  .talent-card {
    min-width: 360px;
  }
}

@media (max-width: 1600px) {
  .talent-card {
    min-width: 340px;
  }
}

/* 侧边栏展开时的中等屏幕优化 */
@media (max-width: 1400px) {
  .talent-card {
    min-width: 320px;
  }
}

@media (max-width: 1200px) {
  .stats-row {
    gap: 16px;
  }

  .stat-item {
    padding: 6px 8px;
  }

  .stat-value {
    font-size: 18px;
  }

  .talent-card {
    min-width: 300px;
  }
}

/* 侧边栏展开时的布局优化 */
@media (max-width: 1100px) {
  .talent-card {
    min-width: 280px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 992px) {
  .talent-card {
    min-width: 260px;
  }
}

/* 防止重叠的额外断点 */
@media (max-width: 900px) {
  .talent-card {
    min-width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
    gap: 8px;
  }

  .stat-item {
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }



  .insights-row {
    flex-direction: column;
    gap: 8px;
  }

  .insight-item {
    width: 100%;
  }

  /* 小屏幕隐藏认领时间标签 */
  .claim-time-badge {
    display: none;
  }

  /* 平板和手机优化 */
  .talent-card {
    min-width: 100%;
    max-width: 100%;
  }

  .talent-cover {
    padding: 12px;
  }

  .talent-meta {
    padding: 8px 12px 12px 12px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .claim-time-badge {
    display: none;
  }

  .talent-card {
    min-width: 100%;
    max-width: 100%;
  }

  .talent-cover {
    padding: 10px;
  }

  .talent-avatar {
    margin-right: 10px;
    width: 56px !important;
    height: 56px !important;
  }

  .talent-name {
    font-size: 14px;
  }

  .talent-account {
    font-size: 12px;
  }

  .talent-stats .ant-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  animation: fade-in 0.3s ease;
}

.skeleton-card {
  cursor: default !important;
  pointer-events: none;
}

.skeleton-cover {
  display: flex;
  padding: 12px;
  align-items: center;
}

.skeleton-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-right: 12px;
  flex-shrink: 0;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-name {
  width: 120px;
  height: 16px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-account {
  width: 90px;
  height: 14px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-tags {
  display: flex;
  gap: 6px;
  margin-top: 4px;
}

.skeleton-tag {
  width: 60px;
  height: 20px;
  border-radius: 10px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-desc {
  padding: 8px 12px;
}

.skeleton-line {
  height: 12px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 8px;
}

.skeleton-line.short {
  width: 70%;
  margin-bottom: 0;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 添加达人弹窗样式 */
.add-talent-form {
  .search-section {
    margin-bottom: 24px;
  }

  .search-tip {
    margin-top: 8px;
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
  }

  .search-results {
    margin-top: 16px;
  }

  .results-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .talent-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background-color: #f6f9ff;
    }

    &.selected {
      border-color: #1890ff;
      background-color: #e6f7ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    .talent-avatar {
      margin-right: 12px;
    }

    .talent-info {
      flex: 1;

      .talent-name {
        font-weight: 500;
        font-size: 14px;
        color: #262626;
        margin-bottom: 4px;
      }

      .talent-account {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 8px;
      }

      .talent-stats {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }

    .select-indicator {
      margin-left: 12px;
    }
  }

  .no-results {
    text-align: center;
    padding: 40px 20px;

    .no-results-content {
      max-width: 300px;
      margin: 0 auto;

      .no-results-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 16px;
        line-height: 1.5;
      }

      .no-results-suggestions {
        text-align: left;
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 16px;

        .suggestions-title {
          color: #333;
          font-size: 13px;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .suggestions-list {
          margin: 0;
          padding-left: 16px;
          color: #666;
          font-size: 12px;

          li {
            margin-bottom: 4px;
            line-height: 1.4;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>