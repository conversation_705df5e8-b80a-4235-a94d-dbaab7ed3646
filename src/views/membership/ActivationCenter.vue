<template>
  <div class="activation-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>🔑 激活中心</h1>
      <p>通过激活码或推荐激活，获得更多功能权限</p>
    </div>

    <!-- 用户激活状态卡片 -->
    <div class="activation-status-section">
      <a-row :gutter="24">
        <a-col :xs="24" :md="12">
          <a-card title="当前状态" :bordered="false" class="status-card">
            <div class="status-content">
              <div class="status-info">
                <a-avatar 
                  :size="64" 
                  :style="{ 
                    backgroundColor: userInfo?.是否会员 ? '#52c41a' : '#faad14',
                    fontSize: '24px'
                  }"
                >
                  {{ userInfo?.是否会员 ? '✓' : '?' }}
                </a-avatar>
                <div class="status-text">
                  <h3>{{ userInfo?.会员名称 || '普通用户' }}</h3>
                  <p>{{ userInfo?.会员状态 || '未激活' }}</p>
                  <a-tag 
                    :color="userInfo?.是否会员 ? 'green' : 'orange'"
                    size="large"
                  >
                    {{ userInfo?.是否会员 ? '已激活' : '待激活' }}
                  </a-tag>
                </div>
              </div>
              
              <div v-if="userInfo?.剩余天数" class="remaining-days">
                <a-statistic
                  title="剩余天数"
                  :value="userInfo.剩余天数"
                  suffix="天"
                  :value-style="{ color: '#3f8600' }"
                />
              </div>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :md="12">
          <a-card title="激活方式" :bordered="false" class="methods-card">
            <div class="activation-methods">
              <div class="method-item">
                <div class="method-icon">
                  <key-outlined style="font-size: 24px; color: #1890ff;" />
                </div>
                <div class="method-content">
                  <h4>激活码激活</h4>
                  <p>输入有效的激活码立即获得权限</p>
                </div>
              </div>
              
              <a-divider />
              
              <div class="method-item">
                <div class="method-icon">
                  <user-add-outlined style="font-size: 24px; color: #52c41a;" />
                </div>
                <div class="method-content">
                  <h4>推荐激活</h4>
                  <p>输入推荐人手机号，双方都获得奖励</p>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 激活码输入组件 -->
    <div class="activation-input-section">
      <ActivationCodeInput 
        :show-history="true"
        @activation-success="handleActivationSuccess"
      />
    </div>

    <!-- 激活说明 -->
    <div class="activation-guide-section">
      <a-card title="激活说明" :bordered="false">
        <a-collapse>
          <a-collapse-panel key="1" header="如何获得激活码？">
            <p>您可以通过以下方式获得激活码：</p>
            <ul>
              <li>购买会员套餐时获得</li>
              <li>参与官方活动获得</li>
              <li>联系客服获得试用激活码</li>
            </ul>
          </a-collapse-panel>
          
          <a-collapse-panel key="2" header="推荐激活如何使用？">
            <p>推荐激活是一种互惠的激活方式：</p>
            <ul>
              <li>输入已注册用户的手机号</li>
              <li>系统验证后，您和推荐人都将获得1天邀约时间</li>
              <li>每个用户只能被推荐激活一次</li>
            </ul>
          </a-collapse-panel>
          
          <a-collapse-panel key="3" header="激活后有什么权限？">
            <p>激活后您将获得以下权限：</p>
            <ul>
              <li>更多的每日邀约次数</li>
              <li>高级搜索功能</li>
              <li>数据导出功能</li>
              <li>优先客服支持</li>
            </ul>
          </a-collapse-panel>
        </a-collapse>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  KeyOutlined,
  UserAddOutlined
} from '@ant-design/icons-vue'
import ActivationCodeInput from '@/components/user/ActivationCodeInput.vue'
import { useUserStore } from '@/store/user'

/**
 * 状态管理
 */
const userStore = useUserStore()
const userInfo = ref(userStore.userInfo)

/**
 * 处理激活成功
 */
const handleActivationSuccess = async (response) => {
  console.log('激活成功:', response)
  
  // 更新用户信息
  await userStore.fetchUserInfo()
  userInfo.value = userStore.userInfo
  
  // 显示成功消息
  message.success({
    content: '恭喜！账户激活成功，您现在可以享受更多功能权限！',
    duration: 5
  })
}

/**
 * 组件挂载时初始化
 */
onMounted(async () => {
  // 获取最新用户信息
  await userStore.fetchUserInfo()
  userInfo.value = userStore.userInfo
})
</script>

<style scoped>
.activation-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.activation-status-section {
  margin-bottom: 32px;
}

.status-card,
.methods-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-text h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.status-text p {
  margin: 0 0 8px 0;
  color: #666;
}

.remaining-days {
  text-align: center;
}

.activation-methods {
  padding: 8px 0;
}

.method-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.method-icon {
  flex-shrink: 0;
}

.method-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.method-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.activation-input-section {
  margin-bottom: 32px;
}

.activation-guide-section {
  margin-bottom: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activation-center {
    padding: 16px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .status-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .status-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
