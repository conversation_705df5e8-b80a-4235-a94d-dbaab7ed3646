<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <!-- 登录表单卡片 -->
    <div class="login-card">
      <div class="login-header">
        <h1 class="title">灵邀AI达人管家</h1>
        <p class="subtitle">智能化达人管理系统</p>
      </div>

      <a-form
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        class="login-form"
        layout="vertical"
      >
        <a-form-item
          label="手机号"
          name="username"
          class="form-item"
        >
          <a-input
            v-model:value="loginForm.username"
            size="large"
            placeholder="请输入手机号"
            :prefix="h(PhoneOutlined)"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          class="form-item"
        >
          <a-input-password
            v-model:value="loginForm.password"
            size="large"
            placeholder="请输入密码"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item class="form-item">
          <div class="login-options">
            <a-checkbox v-model:checked="loginForm.remember">
              记住密码
            </a-checkbox>
            <router-link to="/forgot-password" class="forgot-password">忘记密码？</router-link>
          </div>
        </a-form-item>

        <a-form-item class="form-item">
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            class="login-button"
            block
          >
            {{ loading ? '登录中...' : '登录' }}
          </a-button>
        </a-form-item>

        <!-- 注册链接 -->
        <div class="register-link">
          <span>还没有账户？</span>
          <router-link to="/register" class="link">
            立即注册
          </router-link>
        </div>
      </a-form>

      <!-- 版权信息 -->
      <div class="footer-info">
        <p>&copy; 2024 灵邀AI达人管家. All rights reserved.</p>
      </div>
    </div>

    <!-- 网络状态检测组件 -->
    <NetworkStatus ref="networkStatus" />
  </div>
</template>

<script setup>
import { ref, reactive, h, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../store/user'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined, PhoneOutlined } from '@ant-design/icons-vue'
import NetworkStatus from '@/components/common/NetworkStatus.vue'
import customerInvitationService from '../services/customerInvitationService.js'

// 登录页面组件
defineOptions({
  name: 'Login'
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const networkStatus = ref(null) // 网络状态组件引用
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 组件挂载时检查是否有预填参数
onMounted(() => {
  // 预填手机号（如果有）
  if (route.query.phone) {
    loginForm.username = route.query.phone
  }

  // 检查是否是通过邀请链接跳转过来的
  if (route.query.inviteCode && route.query.from === 'invitation') {
    console.log('检测到邀请链接登录，邀请码:', route.query.inviteCode)
  }
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号格式', 
      trigger: 'blur' 
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ]
}

// 处理登录 - 增强错误处理和调试信息
const handleLogin = async (values) => {
  loading.value = true
  
  try {
    // 获取实际使用的API地址 - 与api.js保持一致
    const getActualApiUrl = () => {
      if (import.meta.env.VITE_API_BASE_URL) {
        return import.meta.env.VITE_API_BASE_URL
      }
      return import.meta.env.MODE === 'development' ? 'http://localhost:8000' : ''
    }

    console.log('🚀 开始登录流程:', {
      用户名: values.username,
      密码长度: values.password.length,
      当前环境: import.meta.env.MODE,
      环境变量URL: import.meta.env.VITE_API_BASE_URL || '未设置',
      实际使用URL: getActualApiUrl()
    })
    
    const result = await userStore.login(values)

    if (result.success) {
      message.success('登录成功！')
      console.log('✅ 登录成功，准备处理后续逻辑')

      // 检查是否需要处理邀请关联（已注册用户通过邀请链接登录）
      if (route.query.inviteCode && route.query.from === 'invitation' && result.user?.id) {
        try {
          console.log('处理已注册用户的邀请关联，邀请码:', route.query.inviteCode)
          const invitationResult = await customerInvitationService.processRegistrationInvitation(
            values.username, // 登录的手机号
            result.user.id   // 登录成功后的用户id
          )

          if (invitationResult.success) {
            message.success('邀请关联成功！')
            console.log('已注册用户邀请关联处理成功')
          } else {
            console.warn('已注册用户邀请关联处理失败:', invitationResult.message)
            // 不影响登录流程，只记录警告
          }
        } catch (error) {
          console.error('处理已注册用户邀请关联时发生异常:', error)
          // 不影响登录流程，只记录错误
        }
      }

      // 获取重定向路径
      const redirect = router.currentRoute.value.query.redirect || '/'
      await router.push(redirect)
    } else {
      const errorMsg = result.message || '登录失败，请检查用户名和密码'

      // 只在开发环境记录登录失败的详细信息
      if (import.meta.env.DEV) {
        console.warn('⚠️ 登录失败:', errorMsg)
      }

      message.error(errorMsg)

      // 登录失败时不再检查网络连接状态，避免额外的网络请求
    }
  } catch (error) {
    // 只在开发环境或严重错误时记录详细异常信息
    const isSeriousError = error.code === 'ERR_NETWORK' || error.code === 'ECONNABORTED'

    if (import.meta.env.DEV || isSeriousError) {
      console.error('💥 登录异常详情:', {
        错误类型: error.name,
        错误消息: error.message,
        错误代码: error.code,
        网络状态: navigator.onLine ? '在线' : '离线'
      })
    }
    
    // 根据错误类型提供更具体的错误提示
    let errorMessage = '登录异常，请稍后重试'
    
    if (error.code === 'ERR_NETWORK' || error.code === 'ERR_CONNECTION_REFUSED') {
      errorMessage = '无法连接到服务器，请检查网络连接或联系技术支持'
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络环境后重试'
    } else if (error.message?.includes('Network Error')) {
      errorMessage = '网络连接错误，请检查网络设置'
    }
    
    message.error(errorMessage)
    
    // 网络错误时不再检查连接状态，避免额外的网络请求
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 登录卡片 */
.login-card {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.title {
  font-size: 32px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.login-form {
  margin-top: 24px;
}

.form-item {
  margin-bottom: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.forgot-password:hover {
  color: #764ba2;
}

.register-link {
  text-align: center;
  margin-top: 16px;
  color: #666;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
  transition: color 0.3s;
}

.link:hover {
  color: #764ba2;
}

.login-button {
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.footer-info {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.footer-info p {
  font-size: 12px;
  color: #a0aec0;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    width: 90%;
    margin: 20px;
    padding: 24px;
  }
  
  .title {
    font-size: 28px;
  }
}

/* 表单项样式增强 */
:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #2d3748;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s;
}

:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.ant-checkbox-wrapper) {
  color: #4a5568;
}
</style> 