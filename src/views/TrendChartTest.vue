<template>
  <div class="trend-chart-test">
    <h2>业务趋势分析测试页面</h2>
    
    <a-card title="API测试">
      <a-space direction="vertical" style="width: 100%">
        <div>
          <a-button @click="testMultiMetricAPI" type="primary">测试多指标API</a-button>
          <a-button @click="testSingleMetricAPI" style="margin-left: 8px">测试单指标API</a-button>
        </div>
        
        <div v-if="apiResponse">
          <h4>API响应:</h4>
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>
        
        <div v-if="error">
          <h4 style="color: red">错误信息:</h4>
          <pre style="color: red">{{ error }}</pre>
        </div>
      </a-space>
    </a-card>
    
    <a-card title="图表测试" style="margin-top: 16px">
      <TrendChart
        :loading="loading"
        :time-range="'本周'"
        @metric-change="handleMetricChange"
        @time-dimension-change="handleTimeDimensionChange"
        @visible-metrics-change="handleVisibleMetricsChange"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { workspaceService } from '@/services/workspaceService'
import TrendChart from '@/components/workspace/TrendChart.vue'

const apiResponse = ref(null)
const error = ref(null)
const loading = ref(false)

const testMultiMetricAPI = async () => {
  try {
    error.value = null
    loading.value = true
    
    console.log('🧪 开始测试多指标API')
    
    const response = await workspaceService.getMultiMetricTrends({
      businessModule: 'wechat',
      metricList: ['wechat_accounts', 'total_friends', 'daily_new'],
      timeDimension: 'week',
      timeRange: '本周'
    })
    
    console.log('🧪 多指标API响应:', response)
    apiResponse.value = response
    
  } catch (err) {
    console.error('🧪 多指标API测试失败:', err)
    error.value = err.message || '未知错误'
  } finally {
    loading.value = false
  }
}

const testSingleMetricAPI = async () => {
  try {
    error.value = null
    loading.value = true
    
    console.log('🧪 开始测试单指标API')
    
    const response = await workspaceService.getTrends({
      dataType: 'invitation',
      timeRange: '本周'
    })
    
    console.log('🧪 单指标API响应:', response)
    apiResponse.value = response
    
  } catch (err) {
    console.error('🧪 单指标API测试失败:', err)
    error.value = err.message || '未知错误'
  } finally {
    loading.value = false
  }
}

const handleMetricChange = (metric) => {
  console.log('🧪 指标变更:', metric)
}

const handleTimeDimensionChange = (dimension) => {
  console.log('🧪 时间维度变更:', dimension)
}

const handleVisibleMetricsChange = (metrics) => {
  console.log('🧪 可见指标变更:', metrics)
}
</script>

<style scoped>
.trend-chart-test {
  padding: 24px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
  font-size: 12px;
  line-height: 1.4;
}
</style>
