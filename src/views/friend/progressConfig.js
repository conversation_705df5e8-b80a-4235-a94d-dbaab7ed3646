import progressService from '@/services/friend/progressService';

/**
 * 状态选项，直接从服务中获取
 */
export const overallStatusOptions = progressService.getOverallStatusOptions();

/**
 * 获取状态标签的配置（颜色和文本）
 * @param {string} statusType - 状态类型 (e.g., '回复状态')
 * @param {number} statusValue - 状态值
 * @returns {{color: string, text: string}}
 */
export const getStatusTagConfig = (statusType, statusValue) => {
    return progressService.getStatusTagConfig(statusType, statusValue);
};

/**
 * 获取意向状态选项
 * @returns {Array} 意向状态选项列表
 */
export const getIntentionStatusOptions = () => {
    return progressService.getIntentionStatusOptions();
};

/**
 * 获取样品状态选项
 * @returns {Array} 样品状态选项列表
 */
export const getSampleStatusOptions = () => {
    return progressService.getSampleStatusOptions();
};

/**
 * 获取排期状态选项
 * @returns {Array} 排期状态选项列表
 */
export const getScheduleStatusOptions = () => {
    return progressService.getScheduleStatusOptions();
};

/**
 * 表格列的定义
 */
export const columns = [
    {
        title: '产品信息',
        key: 'product',
        width: 220,
        fixed: 'left',
    },
    {
        title: '回复状态',
        key: 'replyStatus',
        dataIndex: '回复状态',
        width: 120,
    },
    {
        title: '意向状态',
        key: 'intentionStatus',
        dataIndex: '意向状态',
        width: 150,
    },
    {
        title: '样品状态',
        key: 'sampleStatus',
        dataIndex: '样品状态',
        width: 150,
    },
    {
        title: '排期状态',
        key: 'scheduleStatus',
        dataIndex: '排期状态',
        width: 180,
    },
    {
        title: '开播状态',
        key: 'broadcastStatus',
        dataIndex: '开播状态',
        width: 120,
    },
    {
        title: '综合进度',
        key: 'progress',
        width: 180,
    },
    {
        title: '销售额',
        key: 'sales',
        dataIndex: '销售额',
        width: 120,
    },
    {
        title: '操作',
        key: 'action',
        width: 180,
        fixed: 'right',
    },
]; 