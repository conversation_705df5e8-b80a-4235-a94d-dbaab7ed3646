<template>
  <div class="wechat-accounts-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-info">
        <h3>微信账号管理</h3>
        <p>管理您绑定的微信账号，查看账号状态和好友统计</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="showBindModal" :icon="h(PlusOutlined)">
          绑定微信账号
        </a-button>
        <a-button @click="refreshData" :loading="loading" :icon="h(ReloadOutlined)">
          刷新
        </a-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-statistic
            title="绑定账号数"
            :value="stats.绑定账号数"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="好友总数"
            :value="stats.好友总数"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="活跃账号"
            :value="stats.活跃账号数"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="今日新增好友"
            :value="stats.今日新增好友"
            :value-style="{ color: '#722ed1' }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 微信账号列表 -->
    <div class="accounts-list">
      <div class="list-header">
        <h4>微信账号列表</h4>
        <div class="filter-controls">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索微信号或备注"
            style="width: 300px"
            @search="handleSearch"
            @change="handleSearchChange"
          />
          <a-select
            v-model:value="statusFilter"
            placeholder="账号状态"
            style="width: 120px; margin-left: 12px"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="异常">异常</a-select-option>
            <a-select-option value="冻结">冻结</a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 账号卡片列表 -->
      <div class="accounts-grid" v-if="!loading && filteredAccounts.length > 0">
        <div 
          v-for="account in filteredAccounts" 
          :key="account.微信id"
          class="account-card"
        >
          <WeChatAccountCard
            :account="account"
            @edit="handleEditAccount"
            @unbind="handleUnbindAccount"
            @view-friends="handleViewFriends"
            @refresh-stats="处理刷新账号统计"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading && filteredAccounts.length === 0" class="empty-state">
        <a-empty
          :image="h(WechatOutlined)"
          description="暂无微信账号"
        >
          <a-button type="primary" @click="showBindModal">
            立即绑定
          </a-button>
        </a-empty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="large" tip="加载中...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>
    </div>

    <!-- 绑定微信账号弹窗 -->
    <a-modal
      v-model:open="bindModalVisible"
      title="绑定微信账号"
      :width="500"
      @ok="handleBindAccount"
      :confirmLoading="bindLoading"
    >
      <a-form ref="bindFormRef" :model="bindForm" :rules="bindFormRules" layout="vertical">
        <a-form-item label="微信号" name="微信号" required>
          <a-input
            v-model:value="bindForm.微信号"
            placeholder="请输入微信号（6-20位，字母开头）"
            :maxlength="20"
          />
          <div class="form-tip">
            微信号格式：6-20位字符，必须以字母开头，可包含字母、数字、下划线
          </div>
        </a-form-item>
        
        <a-form-item label="备注名称" name="备注">
          <a-input
            v-model:value="bindForm.备注"
            placeholder="为这个微信号添加备注（可选）"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item label="账号类型" name="类型">
          <a-select v-model:value="bindForm.类型" placeholder="请选择账号类型">
            <a-select-option value="个人">个人账号</a-select-option>
            <a-select-option value="企业">企业账号</a-select-option>
            <a-select-option value="工作">工作账号</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑账号弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑微信账号"
      :width="500"
      @ok="handleUpdateAccount"
      :confirmLoading="updateLoading"
    >
      <a-form ref="editFormRef" :model="editForm" :rules="editFormRules" layout="vertical">
        <a-form-item label="微信号" name="微信号">
          <a-input v-model:value="editForm.微信号" disabled />
          <div class="form-tip">微信号不可修改</div>
        </a-form-item>
        
        <a-form-item label="备注名称" name="备注">
          <a-input
            v-model:value="editForm.备注"
            placeholder="为这个微信号添加备注（可选）"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item label="账号类型" name="类型">
          <a-select v-model:value="editForm.类型" placeholder="请选择账号类型">
            <a-select-option value="个人">个人账号</a-select-option>
            <a-select-option value="企业">企业账号</a-select-option>
            <a-select-option value="工作">工作账号</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="账号状态" name="状态">
          <a-select v-model:value="editForm.状态" placeholder="请选择账号状态">
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="异常">异常</a-select-option>
            <a-select-option value="冻结">冻结</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'

// 导入组件和服务
import WeChatAccountCard from '@/components/friend/WeChat/WeChatAccountCard.vue'
import { wechatService } from '@/services/friend'

defineOptions({
  name: 'FriendWechatAccounts'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const accounts = ref([])
const stats = ref({
  绑定账号数: 0,
  好友总数: 0,
  活跃账号数: 0,
  今日新增好友: 0
})

// 搜索和过滤
const searchKeyword = ref('')
const statusFilter = ref('')

// 弹窗状态
const bindModalVisible = ref(false)
const editModalVisible = ref(false)
const bindLoading = ref(false)
const updateLoading = ref(false)

// 表单数据
const bindForm = reactive({
  微信号: '',
  备注: '',
  类型: '个人'
})

const editForm = reactive({
  微信id: null,
  微信号: '',
  备注: '',
  类型: '个人',
  状态: '正常'
})

// 表单验证规则
const bindFormRules = {
  微信号: [
    { required: true, message: '请输入微信号' },
    { min: 6, max: 20, message: '微信号长度为6-20位' },
    { 
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, 
      message: '微信号必须以字母开头，只能包含字母、数字、下划线' 
    }
  ]
}

const editFormRules = {
  备注: [
    { max: 50, message: '备注不能超过50个字符' }
  ]
}

// 计算属性
const filteredAccounts = computed(() => {
  let result = accounts.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(account => 
      account.微信号.toLowerCase().includes(keyword) ||
      (account.备注 && account.备注.toLowerCase().includes(keyword))
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(account => account.状态 === statusFilter.value)
  }

  return result
})

// 组件挂载时初始化
onMounted(() => {
  加载微信账号数据()

  // 监听全局事件
  window.addEventListener('add-wechat-account', showBindModal)
  window.addEventListener('refresh-data', 刷新微信账号数据)
})

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理全局事件监听
  try {
    window.removeEventListener('add-wechat-account', showBindModal)
    window.removeEventListener('refresh-data', 刷新微信账号数据)
  } catch (error) {
    console.warn('清理事件监听器时出现警告:', error)
  }
})

// 方法定义

/**
 * 加载微信账号数据
 */
const 加载微信账号数据 = async () => {
  try {
    loading.value = true
    
    // 并行加载账号列表和统计数据
    const [accountsData, statsData] = await Promise.all([
      wechatService.getWeChatAccounts(),
      wechatService.getWeChatStatistics()
    ])
    
    console.log('前端接收到的账号数据:', accountsData) // 调试日志
    console.log('前端接收到的统计数据:', statsData) // 调试日志

    accounts.value = accountsData.列表 || []
    stats.value = {
      绑定账号数: statsData.data?.微信账号总数 ||  0,
      好友总数: statsData.data?.好友总数 || 0,
      活跃账号数: statsData.data?.活跃账号数 || 0,
      今日新增好友: statsData.data?.今日新增好友 || 0
    }
    
    console.log('设置后的accounts值:', accounts.value) // 调试日志
    console.log('设置后的stats值:', stats.value) // 调试日志
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新微信账号数据
 */
const 刷新微信账号数据 = () => {
  加载微信账号数据()
}

/**
 * 刷新数据 - 模板调用的函数
 */
const refreshData = () => {
  加载微信账号数据()
}

/**
 * 处理搜索
 */
const handleSearch = (value) => {
  searchKeyword.value = value
}

/**
 * 处理搜索输入变化
 */
const handleSearchChange = (e) => {
  const value = e.target.value
  if (value.length === 0 || value.length >= 2) {
    searchKeyword.value = value
  }
}

/**
 * 处理过滤器变化
 */
const handleFilterChange = () => {
  // 过滤逻辑由计算属性自动处理
}

/**
 * 显示绑定弹窗
 */
const showBindModal = () => {
  bindModalVisible.value = true
  // 重置表单
  bindForm.微信号 = ''
  bindForm.备注 = ''
  bindForm.类型 = '个人'
}

/**
 * 处理绑定账号
 */
const handleBindAccount = async () => {
  try {
    // 表单验证
    const bindFormRef = ref()
    await bindFormRef.value?.validate()
    
    bindLoading.value = true
    
    await wechatService.bindWeChatAccount({
      微信号: bindForm.微信号,
      备注: bindForm.备注,
      类型: bindForm.类型
    })
    
    message.success('微信账号绑定成功')
    bindModalVisible.value = false
    
    // 刷新数据
    加载微信账号数据()
    
  } catch (error) {
    console.error('绑定账号失败:', error)
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(error.message || '绑定失败，请重试')
  } finally {
    bindLoading.value = false
  }
}

/**
 * 处理编辑账号
 */
const handleEditAccount = (account) => {
  editForm.微信id = account.微信id
  editForm.微信号 = account.微信号
  editForm.备注 = account.备注 || ''
  editForm.类型 = account.类型 || '个人'
  editForm.状态 = account.状态 || '正常'
  
  editModalVisible.value = true
}

/**
 * 处理更新账号
 */
const handleUpdateAccount = async () => {
  try {
    // 表单验证
    const editFormRef = ref()
    await editFormRef.value?.validate()
    
    updateLoading.value = true
    
    await wechatService.updateWeChatAccount(editForm.微信id, {
      备注: editForm.备注,
      类型: editForm.类型,
      状态: editForm.状态
    })
    
    message.success('账号信息更新成功')
    editModalVisible.value = false
    
    // 刷新数据
    加载微信账号数据()
    
  } catch (error) {
    console.error('更新账号失败:', error)
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(error.message || '更新失败，请重试')
  } finally {
    updateLoading.value = false
  }
}

/**
 * 处理解绑账号
 */
const handleUnbindAccount = (account) => {
  Modal.confirm({
    title: '确认解绑',
    content: `您确定要解绑微信号 "${account.微信号}" 吗？解绑后将无法管理该账号的好友数据。`,
    okText: '确定解绑',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await wechatService.unbindWeChatAccount(account.微信id)
        message.success('账号解绑成功')
        加载微信账号数据()
      } catch (error) {
        console.error('解绑账号失败:', error)
        message.error(error.message || '解绑失败，请重试')
      }
    }
  })
}

/**
 * 查看好友列表
 */
const handleViewFriends = (account) => {
  router.push({
    name: 'FriendList',
    query: { 微信id: account.微信id }
  })
}

/**
 * 刷新账号统计
 */
const 处理刷新账号统计 = (account) => {
  // 重新加载数据以更新统计信息
  加载微信账号数据()
}
</script>

<style scoped>
.wechat-accounts-page {
  padding: 24px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.header-info p {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 账号列表 */
.accounts-list {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.filter-controls {
  display: flex;
  align-items: center;
}

/* 账号网格布局 */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 24px;
}

.account-card {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state :deep(.ant-empty-image) {
  font-size: 64px;
  color: #d9d9d9;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-placeholder {
  height: 200px;
  background: #f5f5f5;
  border-radius: 8px;
}

/* 表单提示 */
.form-tip {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .accounts-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .wechat-accounts-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .stats-overview {
    padding: 16px;
  }
  
  .accounts-list {
    padding: 16px;
  }
  
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-controls .ant-input-search {
    width: 100% !important;
  }
  
  .filter-controls .ant-select {
    width: 100% !important;
    margin-left: 0 !important;
  }
  
  .accounts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style> 