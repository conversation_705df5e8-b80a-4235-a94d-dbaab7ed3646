<template>
  <div class="workspace-dashboard">
    <!-- 欢迎区域已移除 -->

    <!-- 核心业务指标 Core Business Metrics -->
    <div class="metrics-section">
      <div class="section-header">
        <h3 class="section-title">核心业务指标</h3>
        <div class="time-range-selector">
          <a-radio-group v-model:value="currentTimeRange" @change="handleTimeRangeChange">
            <a-radio-button value="昨日">昨日</a-radio-button>
            <a-radio-button value="今日">今日</a-radio-button>
            <a-radio-button value="本周">本周</a-radio-button>
            <a-radio-button value="上周">上周</a-radio-button>
            <a-radio-button value="本月">本月</a-radio-button>
            <a-radio-button value="上月">上月</a-radio-button>
            <a-radio-button value="本季度">本季度</a-radio-button>
            <a-radio-button value="上季度">上季度</a-radio-button>
          </a-radio-group>
        </div>
      </div>
      
      <!-- 新的卡片分组指标布局 -->
      <div class="metrics-cards-container">
          
        <template v-for="module in businessModules" :key="module.key">
          <div class="metric-module-card" v-if="module.metrics && module.metrics.length > 0">
            <!-- 模块头部 -->
            <div class="module-header">
              <div class="module-icon-container">
                <div class="module-icon" :style="{ background: module.color }">
                  <component :is="getModuleIcon(module.key)" />
                </div>
              </div>
              <div class="module-info">
                <h4 class="module-title">{{ module.title }}</h4>
                <p class="module-subtitle">{{ module.metrics.length }}项核心指标</p>
              </div>
              <!-- 活跃数显示方块，鼠标悬停显示详情 -->
              <a-tooltip placement="top" :title="getActiveCountTooltip(module)">
                <div class="module-badge">
                  <span class="active-count">{{ module.metrics.filter(m => m.数值 > 0).length }}</span>
                  <span class="badge-label">活跃</span>
                </div>
              </a-tooltip>
            </div>
            
            <!-- 指标列表 -->
            <div class="metrics-list">
              <div 
                v-for="(metric, index) in module.metrics" 
                :key="`${module.key}-${index}`"
                class="metric-item"
                @click="handleMetricDetail(module.key, metric)"
              >
                <div class="metric-info">
                  <div class="metric-icon-wrapper">
                    <component 
                      v-if="metric.图标"
                      :is="getMetricIcon(metric.图标)" 
                      class="metric-icon"
                      :style="{ color: module.color }"
                    />
                  </div>
                  <div class="metric-details">
                    <span class="metric-name">{{ metric.标题 }}</span>
                    <span v-if="metric.描述" class="metric-description">{{ metric.描述 }}</span>
                  </div>
                </div>
                
                <div class="metric-value-section">
                  <div class="value-display">
                    <span class="value-number" :style="{ color: module.color }">{{ metric.格式化数值 }}</span>
                    <span class="value-unit" v-if="metric.单位">{{ metric.单位 }}</span>
                  </div>
                  
                  <div v-if="metric.趋势" class="trend-display" :class="getTrendClass(metric.趋势类型)">
                    <component :is="getTrendIcon(metric.趋势类型)" class="trend-icon" />
                    <span class="trend-percentage">{{ metric.趋势 }}</span>
                  </div>
                  <div v-else class="trend-display no-trend">
                    <span class="trend-text">暂无变化</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态模块卡片 -->
          <div class="metric-module-card empty-card" v-else>
            <div class="module-header">
              <div class="module-icon-container">
                <div class="module-icon empty" :style="{ background: '#f1f5f9' }">
                  <component :is="getModuleIcon(module.key)" style="color: #94a3b8;" />
                </div>
              </div>
              <div class="module-info">
                <h4 class="module-title">{{ module.title }}</h4>
                <p class="module-subtitle">暂无数据</p>
              </div>
            </div>
            <div class="empty-state">
              <p class="empty-text">该模块暂无指标数据</p>
              <a-button type="link" size="small" @click="handleMetricClick(module.key)">配置指标</a-button>
            </div>
          </div>
        </template>
        
        <!-- 加载状态 -->
        <div v-if="Object.values(loading).some(l => l)" class="metrics-loading">
          <a-spin size="large" tip="正在加载业务指标数据..." />
        </div>
        
        <!-- 空状态 -->
        <div v-else-if="businessModules.length === 0" class="metrics-empty">
          <a-empty description="暂无业务指标数据">
            <a-button type="primary" @click="loadWorkspaceData">
              刷新数据
            </a-button>
          </a-empty>
        </div>
      </div>
    </div>

    <!-- 业务趋势图表 Business Trends Charts -->
    <div class="trends-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="16">
          <a-card title="业务趋势分析" :bordered="false" class="trends-card">
            <TrendChart
              :data="trendsData[selectedTrendType]"
              :loading="loading.trends"
              :height="480"
              :business-modules="businessModules"
              @metric-change="handleMetricChange"
              @time-dimension-change="handleTimeDimensionChange"
              @visible-metrics-change="handleVisibleMetricsChange"
            />
          </a-card>
        </a-col>
        <a-col :xs="24" :lg="8">
          <a-card title="团队数据概览" :bordered="false" class="team-card">
            <TeamOverview
              :data="teamData"
              :loading="loading.team"
              @view-details="handleViewTeamDetails"
              @join-team="handleJoinTeam"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 移除待办事项和快捷操作模块 -->
  </div>
</template>

<script setup>
import {
  AppstoreOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
  ContactsOutlined,
  InfoCircleOutlined,
  SwapOutlined,
  TeamOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

// 导入组件 Import Components
import TeamOverview from '../components/workspace/TeamOverview.vue'
import TrendChart from '../components/workspace/TrendChart.vue'
// 移除 MetricCard、TodosList 和 QuickActions 组件导入

// 导入服务 Import Services
import workspaceService from '../services/workspaceService.js'
import { useUserStore } from '../store/user.js'

// 路由和用户状态 Router and User Store
const router = useRouter()
const userStore = useUserStore()

// 响应式数据 Reactive Data
const loading = reactive({
  wechat: false,
  invitation: false,
  talent: false,
  team: false,
  trends: false
  // 移除 todos 和 cooperation 加载状态
})

const refreshing = ref(false)
const selectedTrendType = ref('invitation')
const currentTimeRange = ref('本周')

// 用户信息 User Info
const userInfo = reactive({
  nickname: userStore.userInfo?.nickname || userStore.userName || '用户',
  phone: userStore.userInfo?.phone || '',
  avatar: userStore.userInfo?.avatar || userStore.userAvatar || null
})

// 当前时间 Current Time
const currentTime = ref('')

// 今日概览数据 Today's Overview
const todayStats = reactive({
  invitations: 0,
  newFriends: 0
  // 移除 todos 字段
})

// 业务模块数据 Business Modules Data
const businessModules = ref([])

// 趋势数据 Trends Data
const trendsData = reactive({
  invitation: null
  // 移除 cooperation 趋势数据
})

// 团队数据 Team Data
const teamData = ref(null)

// 移除待办事项数据和快捷操作数据

// 时间范围选项 Time Range Options
const timeRanges = [
  { label: '昨日', value: '昨日' },
  { label: '今日', value: '今日' },
  { label: '本周', value: '本周' },
  { label: '上周', value: '上周' },
  { label: '本月', value: '本月' },
  { label: '上月', value: '上月' },
  { label: '本季度', value: '本季度' },
  { label: '上季度', value: '上季度' }
]

// 计算属性 Computed Properties
const hasData = computed(() => {
  return businessModules.value.length > 0
})

const timeRangeTitle = computed(() => {
  // 直接返回中文时间范围描述
  return currentTimeRange.value || '今日'
})

// 方法 Methods
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    weekday: 'long'
  })
}

const loadWorkspaceData = async () => {
  try {
    // 设置加载状态 Set loading states
    Object.keys(loading).forEach(key => {
      loading[key] = true
    })

    // 并行加载数据以提高性能 Load data in parallel for better performance
    console.log('🚀 开始并行加载工作台数据，时间范围:', currentTimeRange.value)
    const [dashboardResponse, wechatMetricsResponse, talentByPlatformResponse, sampleMetricsResponse] = await Promise.allSettled([
      // 获取完整工作台数据 Get complete workspace data
      workspaceService.getDashboard({
        timeRange: currentTimeRange.value
      }),
      // 获取微信运营核心指标数据 Get WeChat core metrics data
      workspaceService.getWechatCoreMetrics({
        timeRange: currentTimeRange.value
      }),
      // 获取分平台达人管理统计数据 Get talent management statistics by platform
      workspaceService.getTalentManagementByPlatform({
        timeRange: currentTimeRange.value
      }),
      // 获取寄样统计数据 Get sample metrics data
      workspaceService.getSampleMetrics({
        timeRange: currentTimeRange.value
      })
    ])
    console.log('📊 数据加载完成，响应状态:', {
      dashboard: dashboardResponse.status,
      wechatMetrics: wechatMetricsResponse.status,
      talentByPlatform: talentByPlatformResponse.status,
      sampleMetrics: sampleMetricsResponse.status
    })

    // 处理工作台数据 Process dashboard data
    let data = null
    if (dashboardResponse.status === 'fulfilled') {
      const response = dashboardResponse.value
      if (response && response.status === 100 && response.data) {
        // 标准API响应格式 Standard API response format
        data = response.data
      } else if (response && typeof response === 'object' && response.微信运营) {
        // 直接的业务数据格式（API拦截器已解包）Direct business data format (unpacked by API interceptor)
        data = response
      }
    }

    // 处理微信运营核心指标数据 Process WeChat core metrics data
    let wechatCoreMetrics = null
    if (wechatMetricsResponse.status === 'fulfilled') {
      const response = wechatMetricsResponse.value
      if (response && response.status === 100 && response.data) {
        wechatCoreMetrics = response.data
      } else if (response && typeof response === 'object' && response.指标卡片) {
        wechatCoreMetrics = response
      }
    }

    // 处理分平台达人统计数据 Process talent management by platform data
    let talentByPlatform = null
    if (talentByPlatformResponse.status === 'fulfilled') {
      const response = talentByPlatformResponse.value
      console.log('🎯 分平台达人管理统计响应:', response)
      if (response && response.status === 100 && response.data) {
        talentByPlatform = response.data
        console.log('✅ 分平台达人管理统计数据获取成功:', talentByPlatform)
      } else if (response && typeof response === 'object' && response.指标卡片) {
        talentByPlatform = response
        console.log('✅ 分平台达人管理统计数据(备用格式):', talentByPlatform)
      } else {
        console.warn('⚠️ 分平台达人管理统计数据格式异常:', response)
      }
    } else {
      console.error('❌ 分平台达人管理统计接口调用失败:', talentByPlatformResponse)
    }

    // 处理寄样统计数据 Process sample metrics data
    let sampleMetrics = null
    if (sampleMetricsResponse.status === 'fulfilled') {
      const response = sampleMetricsResponse.value
      if (response && response.status === 100 && response.data) {
        sampleMetrics = response.data
      } else if (response && typeof response === 'object' && response.指标卡片) {
        sampleMetrics = response
      }
    }

    // 合并数据 Merge data
    if (data || wechatCoreMetrics || talentByPlatform || sampleMetrics) {
      // 如果有微信核心指标数据，优先使用它替换原有的微信运营数据
      // If WeChat core metrics data is available, use it to replace the original WeChat operations data
      if (wechatCoreMetrics) {
        if (!data) data = {}
        data.微信运营核心指标 = wechatCoreMetrics

        // 为了兼容现有代码，也将核心指标数据放到微信运营字段中
        // For compatibility with existing code, also put core metrics data in the WeChat operations field
        data.微信运营 = {
          ...data.微信运营,
          指标卡片: wechatCoreMetrics.指标卡片 || [],
          今日新增: wechatCoreMetrics.今日新增 || 0,
          当前新增好友: wechatCoreMetrics.今日新增 || 0
        }
      }

      // 如果有分平台达人统计数据，使用它替换原有的达人管理数据
      // If talent management by platform data is available, use it to replace the original talent management data
      if (talentByPlatform) {
        if (!data) data = {}
        data.分平台达人管理 = talentByPlatform
        console.log('🔄 正在合并分平台达人管理数据:', talentByPlatform)
        console.log('🔍 分平台达人管理数据指标卡片:', talentByPlatform.指标卡片)

        // 替换原有的达人管理数据，使用分平台统计的指标卡片
        // Replace original talent management data with platform-specific statistics
        data.达人管理 = {
          ...data.达人管理,
          指标卡片: talentByPlatform.指标卡片 || [],
          微信平台: talentByPlatform.微信平台 || {},
          抖音平台: talentByPlatform.抖音平台 || {},
          汇总数据: talentByPlatform.汇总数据 || {}
        }
        console.log('✅ 达人管理数据合并完成:', data.达人管理)
        console.log('🎯 达人管理指标卡片数量:', data.达人管理.指标卡片?.length || 0)
        if (data.达人管理.指标卡片?.length > 0) {
          console.log('🏆 第一个指标:', data.达人管理.指标卡片[0])
        }
      } else {
        console.warn('⚠️ 没有分平台达人管理数据，使用原有数据')
      }

      // 如果有寄样统计数据，添加到数据中
      // If sample metrics data is available, add it to the data
      if (sampleMetrics) {
        if (!data) data = {}
        data.寄样统计 = sampleMetrics
      }

      // 更新业务模块数据 Update business modules data
      updateBusinessModules(data)

      // 更新今日概览 Update today's overview
      updateTodayStats(data)

      // 更新趋势数据 Update trends data
      if (data.趋势数据) {
        updateTrendsData(data)
      }

      // 更新团队数据 Update team data
      teamData.value = data.团队数据 || null

      // 移除待办事项和快捷操作的数据更新逻辑

      message.success('工作台数据加载成功')
    } else {
      throw new Error('无法获取工作台数据')
    }
  } catch (error) {
    console.error('Failed to load workspace data:', error)
    message.error('加载工作台数据失败: ' + error.message)
  } finally {
    // 清除加载状态 Clear loading states
    Object.keys(loading).forEach(key => {
      loading[key] = false
    })
  }
}

const updateBusinessModules = (data) => {
  const modules = []

  // 优先使用微信运营核心指标数据，如果没有则使用原有的微信运营数据
  // Prioritize WeChat core metrics data, fallback to original WeChat operations data if not available
  if (data.微信运营核心指标 || data.微信运营) {
    const wechatData = data.微信运营核心指标 || data.微信运营
    modules.push({
      key: 'wechat',
      title: '微信运营核心指标',
      metrics: wechatData.指标卡片 || [],
      color: '#1890ff'
    })
  }

  // 移除邀约业务模块 - 总邀约数已移至达人管理模块

  if (data.达人管理) {
    console.log('📊 正在处理达人管理模块数据:', data.达人管理)
    console.log('📈 达人管理指标卡片:', data.达人管理.指标卡片)
    modules.push({
      key: 'talent',
      title: '达人管理',
      metrics: data.达人管理.指标卡片 || [],
      color: '#fa8c16'
    })
    console.log('✅ 达人管理模块已添加，指标数量:', data.达人管理.指标卡片?.length || 0)
  }

  // 移除合作项目模块
  // if (data.合作项目) {
  //   modules.push({
  //     key: 'cooperation',
  //     title: '合作项目',
  //     metrics: data.合作项目.指标卡片 || [],
  //     color: '#722ed1'
  //   })
  // }

  // 寄样模块
  if (data.寄样统计) {
    modules.push({
      key: 'sample',
      title: '寄样模块',
      metrics: data.寄样统计.指标卡片 || [],
      color: '#eb2f96'
    })
  }

  businessModules.value = modules
}

const updateTodayStats = (data) => {
  // 根据当前时间范围显示对应的数据
  // 邀约数据现在从达人管理模块的总邀约数指标获取
  const 总邀约数指标 = data.达人管理?.指标卡片?.find(item => item.标题 === '总邀约数')
  
  if (currentTimeRange.value === '今日') {
    // 今日数据
    todayStats.invitations = 总邀约数指标?.趋势数值 || 0
    todayStats.newFriends = data.微信运营?.今日新增好友 || 0
  } else if (currentTimeRange.value === '昨日') {
    // 昨日数据
    todayStats.invitations = 总邀约数指标?.趋势数值 || 0
    todayStats.newFriends = data.微信运营?.昨日新增好友 || 0
  } else if (currentTimeRange.value === '本周') {
    // 本周数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else if (currentTimeRange.value === '上周') {
    // 上周数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else if (currentTimeRange.value === '本月') {
    // 本月数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else if (currentTimeRange.value === '上月') {
    // 上月数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else if (currentTimeRange.value === '本季度') {
    // 本季度数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else if (currentTimeRange.value === '上季度') {
    // 上季度数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  } else {
    // 默认显示当前时间范围的数据
    todayStats.invitations = 总邀约数指标?.数值 || 0
    todayStats.newFriends = data.微信运营?.当前新增好友 || 0
  }

  // 移除待办事项统计
}

const updateTrendsData = (data) => {
  console.log('📊 updateTrendsData - 输入数据:', data)

  // 处理邀约趋势数据
  if (data.邀约趋势) {
    trendsData.invitation = data.邀约趋势.邀约趋势 || data.邀约趋势 || null
    console.log('📈 邀约趋势数据已更新:', trendsData.invitation)
  } else {
    console.log('⚠️ 未找到邀约趋势数据')
    trendsData.invitation = null
  }

  // 处理趋势数据字段（可能的数据结构）
  if (data.趋势数据) {
    if (data.趋势数据.邀约趋势) {
      trendsData.invitation = data.趋势数据.邀约趋势
      console.log('📈 从趋势数据中获取邀约趋势:', trendsData.invitation)
    }
  }

  console.log('📊 最终趋势数据状态:', trendsData)
}

const handleMetricClick = (moduleKey) => {
  console.log('Metric clicked:', moduleKey)
  // 处理指标卡片点击事件 Handle metric card click
  // 可以跳转到详细页面 Can navigate to detail page
}

const handleMetricDetail = (moduleKey, metric) => {
  console.log('Metric detail clicked:', moduleKey, metric)
  // 暂时移除路由跳转，因为MetricDetail路由不存在
  // TODO: 待详情页面开发完成后恢复路由跳转
  // router.push({ name: 'MetricDetail', params: { moduleKey: moduleKey, metricKey: metric.key } })
}

const handleTrendTypeChange = (value) => {
  selectedTrendType.value = value
  // 可以重新加载对应的趋势数据 Can reload corresponding trend data
}

// 新增：处理指标变更事件
const handleMetricChange = (metricKey) => {
  console.log('指标变更:', metricKey)
  // 这里可以根据选择的指标重新加载对应的趋势数据
  // 暂时使用现有的邀约趋势数据
}

// 处理业务趋势分析的时间维度变更事件（独立于核心业务指标）
const handleTimeDimensionChange = (dimension) => {
  console.log('📊 业务趋势分析时间维度变更:', dimension)
  // 业务趋势分析有自己的时间维度控制，不影响核心业务指标
  // 这里只需要记录日志，不需要修改 currentTimeRange
}

// 新增：处理可见指标变更事件
const handleVisibleMetricsChange = (visibleMetrics) => {
  console.log('可见指标变更:', visibleMetrics)
  // 这里可以根据可见指标调整图表显示
}

// 团队概览相关事件处理 Team Overview Event Handlers
const handleViewTeamDetails = (teamData) => {
  console.log('查看团队详情:', teamData)

  // 优先使用主要团队id
  const teamId = teamData?.主要团队id || teamData?.团队id

  if (teamId) {
    // 跳转到具体团队详情页面
    router.push({ name: 'TeamDetail', params: { teamId: String(teamId) } })
  } else if (teamData?.参与团队数 > 0) {
    // 如果有参与团队但没有具体ID，跳转到团队列表页面
    message.info('请在团队列表中选择要查看的团队')
    router.push({ name: 'MyTeams' })
  } else {
    // 没有参与任何团队，跳转到团队列表页面
    message.info('您还没有加入任何团队，请先加入团队')
    router.push({ name: 'MyTeams' })
  }
}

const handleJoinTeam = () => {
  console.log('加入团队')
  // 跳转到团队列表页面，用户可以查看可加入的团队
  router.push({ name: 'MyTeams' })
}

// 移除待办事项和快捷操作的事件处理函数

const handleTimeRangeChange = (e) => {
  const range = e.target ? e.target.value : e
  currentTimeRange.value = range
  loadWorkspaceData()
}

const refreshData = async () => {
  refreshing.value = true
  try {
    await loadWorkspaceData()
  } finally {
    refreshing.value = false
  }
}

// 生命周期 Lifecycle
onMounted(() => {
  updateCurrentTime()
  loadWorkspaceData()
  
  // 每分钟更新时间 Update time every minute
  setInterval(updateCurrentTime, 60000)
})

// 辅助函数 Helper Functions
const getModuleIcon = (moduleKey) => {
  switch (moduleKey) {
    case 'wechat':
      return WechatOutlined
    case 'invitation':
      return ContactsOutlined
    case 'talent':
      return TeamOutlined
    case 'sample':
      return SwapOutlined
    default:
      return AppstoreOutlined
  }
}

const getMetricIcon = (iconName) => {
  // 图标映射 - 支持多种格式
  const iconMap = {
    'wechat': WechatOutlined,
    'contacts': ContactsOutlined,
    'team': TeamOutlined,
    'swap': SwapOutlined,
    'user-add': TeamOutlined,
    'fire': InfoCircleOutlined,
    'project': AppstoreOutlined,
    'dollar': InfoCircleOutlined,
    'send': InfoCircleOutlined,
    'database': InfoCircleOutlined,
    'message': ContactsOutlined,
    'interaction': TeamOutlined,
    'usergroup-add': TeamOutlined,
    'video-camera': InfoCircleOutlined
  }
  
  return iconMap[iconName] || InfoCircleOutlined
}

const getTrendClass = (trendType) => {
  switch (trendType) {
    case 'up':
    case '上升':
      return 'trend-up'
    case 'down':
    case '下降':
      return 'trend-down'
    case 'stable':
    case '持平':
      return 'trend-flat'
    default:
      return ''
  }
}

// 获取活跃数详情提示信息
const getActiveCountTooltip = (module) => {
  // 根据不同模块返回相应的详情说明
  switch (module.key) {
    case 'wechat':
      return `统计数值大于0的微信管理指标\n包括：账号数、好友数、沟通数等`

    case 'invitation':
      return `统计数值大于0的邀约管理指标\n包括：邀约数、认领达人数等`

    case 'talent':
      return `活跃达人基于多维度评分：\n• 沟通活跃度（消息互动）\n• 业务参与度（寄样申请）\n• 信息完整度（资料完善）\n• 认领时效性（认领时间）\n\n≥40分为活跃达人`

    case 'sample':
      return `统计数值大于0的样品管理指标\n包括：合作项目、销售额、开播率等`

    default:
      return `显示数值大于0的指标数量`
  }
}

const getTrendIcon = (trendType) => {
  switch (trendType) {
    case 'up':
    case '上升':
      return ArrowUpOutlined
    case 'down':
    case '下降':
      return ArrowDownOutlined
    case 'stable':
    case '持平':
      return SwapOutlined
    default:
      return InfoCircleOutlined
  }
}
</script>

<style scoped>
.workspace-dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  min-height: 100vh;
  height: auto; /* 允许根据内容自动扩展高度 */
  position: relative;
}

/* welcome-section 样式已移除 */

/* welcome-card 相关样式已移除 */

/* user-info 相关样式已移除 */

/* today-overview-card 样式已移除 */

/* overview 相关样式已移除 */

/* stat 相关样式已移除 */

.metrics-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 0 4px 24px 4px;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.8) 0%, 
    rgba(147, 51, 234, 0.6) 50%, 
    rgba(59, 130, 246, 0.3) 100%
  );
  border-radius: 1px;
}

.section-title {
  margin: 0;
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.time-range-selector {
  display: flex;
  align-items: center;
}

.time-range-selector :deep(.ant-radio-group) {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.time-range-selector :deep(.ant-radio-button-wrapper) {
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.time-range-selector :deep(.ant-radio-button-wrapper-checked) {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.trends-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

/* 移除 bottom-section 样式 */

.trends-card,
.team-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.06);
  border-radius: 24px;
  height: auto; /* 允许根据内容自动调整高度 */
  min-height: 580px; /* 设置最小高度确保有足够空间 */
  overflow: visible; /* 允许内容溢出显示 */
}

/* 移除待办事项和快捷操作相关样式 */

/* 全局卡片样式优化 */
:deep(.ant-card) {
  border-radius: 24px;
  overflow: hidden;
  border: none;
}

:deep(.ant-card-head) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.08);
  padding: 20px 24px;
}

:deep(.ant-card-head-title) {
  font-weight: 700;
  color: #0f172a;
  font-size: 18px;
}

:deep(.ant-card-body) {
  padding: 24px;
}

/* 修复z-index层级问题 */
:deep(.ant-select-dropdown) {
  z-index: 1050;
}

:deep(.ant-tooltip) {
  z-index: 1060;
}

:deep(.ant-message) {
  z-index: 1070;
}

/* 新的卡片分组样式 */
.metrics-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  position: relative;
  min-height: 400px;
}

.metric-module-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.metric-module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.metric-module-card.empty-card {
  opacity: 0.7;
  background: rgba(248, 250, 252, 0.8);
}

.module-header {
  display: flex;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.08);
  position: relative;
}

.module-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.3) 0%, transparent 100%);
}

.module-icon-container {
  margin-right: 16px;
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.module-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
}

.module-icon.empty {
  background: #f1f5f9 !important;
  box-shadow: none;
  border: 2px solid #e2e8f0;
}

.module-info {
  flex: 1;
}

.module-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.module-subtitle {
  font-size: 13px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.module-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.active-count {
  font-size: 16px;
  font-weight: 800;
  color: #16a34a;
  line-height: 1;
}

.badge-label {
  font-size: 10px;
  color: #16a34a;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metrics-list {
  padding: 0;
}

.metric-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  transform: translateX(4px);
}

.metric-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-item:hover::before {
  width: 3px;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.metric-icon-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.metric-icon {
  font-size: 18px;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-name {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
}

.metric-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.2;
}

.metric-value-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value-number {
  font-size: 20px;
  font-weight: 800;
  line-height: 1;
}

.value-unit {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.trend-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-icon {
  font-size: 12px;
}

.trend-display.trend-up {
  color: #16a34a;
  background: rgba(34, 197, 94, 0.1);
}

.trend-display.trend-down {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

.trend-display.trend-flat {
  color: #0891b2;
  background: rgba(6, 182, 212, 0.1);
}

.trend-display.no-trend {
  color: #64748b;
  background: rgba(148, 163, 184, 0.1);
  font-style: italic;
}

.empty-state {
  padding: 40px 24px;
  text-align: center;
  color: #64748b;
}

.empty-text {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.module-count {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.metric-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.metric-icon {
  font-size: 18px;
}

.value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value-number {
  font-size: 24px;
  font-weight: 800;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.value-unit {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.trend-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  font-size: 16px;
}

.trend-up {
  color: #52c41a; /* 上升趋势颜色 */
}

.trend-down {
  color: #f5222d; /* 下降趋势颜色 */
}

.trend-flat {
  color: #1890ff; /* 持平趋势颜色 */
}

.no-trend {
  color: #94a3b8;
  font-style: italic;
}

.placeholder-text {
  color: #94a3b8;
  font-style: italic;
}

.metrics-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  z-index: 10;
}

.metrics-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  z-index: 10;
}

@media (max-width: 768px) {
  .workspace-dashboard::before {
    height: 120px;
    border-radius: 0 0 24px 24px;
  }

  .dashboard-content {
    padding: 16px;
  }

  /* welcome-section 响应式样式已移除 */

  .section-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
    padding: 0 8px 20px 8px;
    margin-bottom: 28px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .section-title::before {
    width: 3px;
    height: 24px;
  }

  /* today-stats 响应式样式已移除 */

  .stat-value {
    font-size: 24px;
  }

  .trends-card,
  .team-card {
    height: auto;
    min-height: 620px;
  }

  /* 移除待办事项和快捷操作的响应式样式 */

  .user-info {
    gap: 16px;
  }

  .user-details h2 {
    font-size: 20px;
  }

  /* 移动端卡片响应式样式 */
  .metrics-cards-container {
    grid-template-columns: 1fr; /* 移动端单列布局 */
    gap: 16px;
  }
  
  .metric-module-card {
    border-radius: 16px;
  }

  .module-header {
    padding: 20px;
  }

  .module-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .module-title {
    font-size: 16px;
  }

  .module-subtitle {
    font-size: 12px;
  }

  .module-badge {
    padding: 6px 10px;
  }

  .active-count {
    font-size: 14px;
  }

  .badge-label {
    font-size: 9px;
  }

  .metric-item {
    padding: 16px 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .metric-info {
    width: 100%;
  }

  .metric-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .metric-icon {
    font-size: 16px;
  }

  .metric-name {
    font-size: 14px;
  }

  .metric-description {
    font-size: 11px;
  }

  .metric-value-section {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .value-number {
    font-size: 18px;
  }

  .value-unit {
    font-size: 11px;
  }

  .trend-display {
    font-size: 11px;
  }

  .trend-icon {
    font-size: 10px;
  }

  .empty-state {
    padding: 32px 20px;
  }

  .empty-text {
    font-size: 13px;
  }
}
</style>
