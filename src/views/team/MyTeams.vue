<template>
  <div class="my-teams">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h2>团队总览</h2>
          <p>管理您参与的所有团队，查看团队统计和详细信息</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateTeamModal = true">
            <template #icon>
              <PlusOutlined />
            </template>
            创建团队
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <a-card class="filter-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="8" :md="6">
          <a-select
            v-model:value="filters.团队关系类型"
            placeholder="团队关系"
            style="width: 100%"
          >
            <a-select-option value="">全部团队</a-select-option>
            <a-select-option value="created">我创建的</a-select-option>
            <a-select-option value="managed">我管理的</a-select-option>
            <a-select-option value="joined">我参与的</a-select-option>
          </a-select>
        </a-col>
        
        <a-col :xs="24" :sm="10" :md="12">
          <a-input-search
            v-model:value="filters.搜索关键词"
            placeholder="搜索团队名称"
          />
        </a-col>
        
        <a-col :xs="24" :sm="6" :md="6">
          <a-button @click="resetFilters">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 团队列表卡片 -->
    <a-card class="teams-card">
      <TeamList
        :teams="teams"
        :loading="loading"
        :pagination="pagination"
        @team-click="handleTeamClick"
      />
    </a-card>

    <!-- 弹窗组件 -->
    <CreateTeamModal
      v-model:open="showCreateTeamModal"
      @success="handleCreateSuccess"
    />

  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { useTeamManagement } from '../../composables/useTeamManagement'

// 异步加载子组件 - 添加错误处理
const TeamList = defineAsyncComponent({
  loader: () => import('../../components/team/TeamList.vue'),
  errorComponent: { template: '<div>组件加载失败</div>' },
  loadingComponent: { template: '<div>组件加载中...</div>' }
})
const CreateTeamModal = defineAsyncComponent({
  loader: () => import('../../components/team/CreateTeamModal.vue'),
  errorComponent: { template: '<div>创建团队模态框加载失败</div>' },
  loadingComponent: { template: '<div>加载中...</div>' }
})

defineOptions({
  name: 'TeamOverview'
})

const router = useRouter()

// 从Composable获取团队列表相关的数据和方法
const { loading, teams, filters, pagination, loadTeams, resetFilters } = useTeamManagement({ autoLoad: false })

// 状态管理
const showCreateTeamModal = ref(false)

// 事件处理
const handleTeamClick = (team) => {
  // 确保团队id存在且有效
  const teamId = team.团队id || team.id
  if (!teamId || isNaN(parseInt(teamId))) {
    message.error('团队id无效，无法进入详情页')
    console.error('无效的团队数据:', team)
    return
  }
  router.push({ name: 'TeamDetail', params: { teamId: String(teamId) } })
}

// 弹窗成功回调
const handleCreateSuccess = () => {
  // 子组件已经显示了成功提示，这里只需要刷新数据
  loadTeams()
}


onMounted(() => {
  loadTeams()
})

</script>

<style scoped>
.my-teams {
  padding: 24px;
}
.page-header {
  margin-bottom: 24px;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}
.header-title p {
  margin: 0;
  color: #666;
}
.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
}
.teams-card {
  border-radius: 8px;
}

@media (max-width: 768px) {
  .my-teams {
    padding: 16px;
  }
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
}
</style>