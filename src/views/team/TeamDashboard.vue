<template>
  <div class="team-dashboard">
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">团队数据看板</h1>
        <a-space>
          <a-select
            v-model:value="selectedTeamId"
            style="width: 200px"
            placeholder="选择团队"
            :loading="teamsLoading"
            @change="handleTeamChange"
          >
            <a-select-option v-for="team in teams" :key="team.团队id" :value="team.团队id">
              {{ team.团队名称 }}
            </a-select-option>
          </a-select>
        </a-space>
      </div>

      <div class="header-right">
        <a-space>
          <a-dropdown>
            <a-button>
              <template #icon><SettingOutlined /></template>
              设置
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="export">
                  <ExportOutlined />
                  <span>导出数据</span>
                </a-menu-item>
                <a-menu-item key="print">
                  <PrinterOutlined />
                  <span>打印报表</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>
    
    <!-- 团队数据看板内容 -->
    <div v-if="selectedTeamId" class="dashboard-content">
      <!-- 数据加载中 -->
      <div v-if="dataLoading" class="data-loading">
        <a-spin size="large">
          <div style="text-align: center; padding: 100px;">
            <p>正在加载团队数据...</p>
          </div>
        </a-spin>
      </div>
      
      <!-- 数据加载完成，显示完整看板 -->
      <template v-else-if="dashboardData || businessModulesData">
        <!-- 核心业务指标 Core Business Metrics（参考工作台设计）-->
        <div class="metrics-section">
          <div class="section-header">
            <h3 class="section-title">核心业务指标</h3>
            <div class="time-range-selector">
              <a-radio-group v-model:value="currentTimeRange" @change="handleTimeRangeChange">
                <a-radio-button value="昨日">昨日</a-radio-button>
                <a-radio-button value="今日">今日</a-radio-button>
                <a-radio-button value="本周">本周</a-radio-button>
                <a-radio-button value="上周">上周</a-radio-button>
                <a-radio-button value="本月">本月</a-radio-button>
                <a-radio-button value="上月">上月</a-radio-button>
                <a-radio-button value="本季度">本季度</a-radio-button>
                <a-radio-button value="上季度">上季度</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          
          <!-- 业务模块指标卡片容器（参考工作台设计）-->
          <div class="metrics-cards-container">
            <template v-for="module in businessModules" :key="module.key">
              <div class="metric-module-card" v-if="module.metrics && module.metrics.length > 0">
                <!-- 模块头部 -->
                <div class="module-header">
                  <div class="module-icon-container">
                    <div class="module-icon" :style="{ background: module.color }">
                      <component :is="getModuleIcon(module.key)" />
                    </div>
                  </div>
                  <div class="module-info">
                    <h4 class="module-title">{{ module.title }}</h4>
                    <p class="module-subtitle">{{ module.metrics.length }}项核心指标</p>
                  </div>
                  <!-- 活跃数显示方块，鼠标悬停显示详情 -->
                  <a-tooltip placement="top" :title="getActiveCountTooltip(module)">
                    <div class="module-badge">
                      <span class="active-count">{{ module.metrics.filter(m => m.数值 > 0).length }}</span>
                      <span class="badge-label">活跃</span>
                    </div>
                  </a-tooltip>
                </div>
                
                <!-- 指标列表 -->
                <div class="metrics-list">
                  <div 
                    v-for="(metric, index) in module.metrics" 
                    :key="`${module.key}-${index}`"
                    class="metric-item"
                    @click="handleMetricDetail(module.key, metric)"
                  >
                    <div class="metric-info">
                      <div class="metric-icon-wrapper">
                        <component 
                          v-if="metric.图标"
                          :is="getMetricIcon(metric.图标)" 
                          class="metric-icon"
                          :style="{ color: module.color }"
                        />
                      </div>
                      <div class="metric-details">
                        <span class="metric-name">{{ metric.标题 }}</span>
                        <span v-if="metric.描述" class="metric-description">{{ metric.描述 }}</span>
                      </div>
                    </div>
                    
                    <div class="metric-value-section">
                      <div class="value-display">
                        <span class="value-number" :style="{ color: module.color }">{{ metric.格式化数值 }}</span>
                        <span class="value-unit" v-if="metric.单位">{{ metric.单位 }}</span>
                      </div>
                      
                      <div v-if="metric.趋势" class="trend-display" :class="getTrendClass(metric.趋势类型)">
                        <component :is="getTrendIcon(metric.趋势类型)" class="trend-icon" />
                        <span class="trend-percentage">{{ metric.趋势 }}</span>
                      </div>
                      <div v-else class="trend-display no-trend">
                        <span class="trend-text">暂无变化</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态模块卡片 -->
              <div class="metric-module-card empty-card" v-else>
                <div class="module-header">
                  <div class="module-icon-container">
                    <div class="module-icon empty" :style="{ background: '#f1f5f9' }">
                      <component :is="getModuleIcon(module.key)" style="color: #94a3b8;" />
                    </div>
                  </div>
                  <div class="module-info">
                    <h4 class="module-title">{{ module.title }}</h4>
                    <p class="module-subtitle">暂无数据</p>
                  </div>
                </div>
                <div class="empty-state">
                  <p class="empty-text">该模块暂无指标数据</p>
                  <a-button type="link" size="small" @click="handleMetricClick(module.key)">配置指标</a-button>
                </div>
              </div>
            </template>
            
            <!-- 加载状态 -->
            <div v-if="businessModulesLoading" class="metrics-loading">
              <a-spin size="large" tip="正在加载业务指标数据..." />
            </div>
            
            <!-- 空状态 -->
            <div v-else-if="businessModules.length === 0" class="metrics-empty">
              <a-empty description="暂无业务指标数据" />
            </div>
          </div>
        </div>

        <!-- 详细数据面板 Detailed Data Panels -->
        <div class="detailed-panels">
          
          <!-- 成员绩效面板 -->
          <member-performance-panel
            :teamId="selectedTeamId"
            :sharedData="dashboardData"
            :memberRanking="memberRankingData"
          />

          <!-- 业务流程面板 -->
          <business-process-panel
            :teamId="selectedTeamId"
            :sharedData="dashboardData"
          />
        </div>
      </template>
      
      <!-- 数据获取失败 -->
      <div v-else class="data-error">
        <a-empty description="数据获取失败">
          <a-button type="primary" @click="() => fetchDashboardData(selectedTeamId)">
            重新加载
          </a-button>
        </a-empty>
      </div>
    </div>
    
    <!-- 无团队选择状态 -->
    <div v-else-if="!teamsLoading" class="no-team-selected">
      <a-empty description="请选择一个团队查看数据看板" />
    </div>
    
    <!-- 团队列表加载中状态 -->
    <div v-else class="teams-loading">
      <a-spin size="large">
        <div style="text-align: center; padding: 50px;">
          <p>正在加载团队列表...</p>
        </div>
      </a-spin>
    </div>
    

  </div>
</template>

<script setup>
import {
    AppstoreOutlined,
    ArrowDownOutlined,
    ArrowUpOutlined,
    CheckCircleOutlined,
    ContactsOutlined,
    DownOutlined,
    ExportOutlined,
    GiftOutlined,
    InfoCircleOutlined,
    MailOutlined,
    PrinterOutlined,
    SettingOutlined,
    StarOutlined,
    SwapOutlined,
    TeamOutlined,
    WechatOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, provide, ref } from 'vue'
import { teamBasicService } from '../../services/team/teamBasic'
import { teamDashboardService } from '../../services/team/teamDashboard'


// 导入面板组件
import BusinessProcessPanel from '../../components/team/dashboard/BusinessProcessPanel.vue'
import MemberPerformancePanel from '../../components/team/dashboard/MemberPerformancePanel.vue'



// 响应式数据
const teams = ref([])
const teamsLoading = ref(false)
const selectedTeamId = ref(null)

// 新增：统一数据管理
const dashboardData = ref(null)
const dataLoading = ref(false)

// 业务模块数据管理（参考工作台架构）
const businessModulesData = ref(null)
const businessModules = ref([])
const businessModulesLoading = ref(false)
const memberRankingData = ref(null)
const currentTimeRange = ref('本周')

// 获取团队列表
const fetchTeams = async () => {
  teamsLoading.value = true
  try {
    const response = await teamBasicService.getUserTeams()
    console.log('团队列表API响应:', response) // 添加调试日志
    
    if (response.status === 100) {
      // 修复：正确获取团队列表数据 - 团队数据在response.data.团队列表中
      teams.value = response.data?.团队列表 || []
      
      console.log('解析后的团队列表:', teams.value) // 添加调试日志
      
      // 如果有团队，默认选择第一个并获取数据
      if (teams.value.length > 0 && !selectedTeamId.value) {
        const firstTeamId = teams.value[0].团队id
        selectedTeamId.value = firstTeamId
        console.log('自动选择团队id:', firstTeamId) // 添加调试日志
        
        // 并行获取第一个团队的核心业务指标和看板数据
        const [, 核心指标结果] = await Promise.allSettled([
          fetchDashboardData(firstTeamId, false),
          fetchCoreMetricsData(firstTeamId)
        ])

        // 处理核心业务指标数据
        if (核心指标结果.status === 'fulfilled' && 核心指标结果.value) {
          const { 核心指标模块列表, 核心指标数据 } = 核心指标结果.value

          // 设置业务模块数据
          if (核心指标模块列表 && 核心指标模块列表.length > 0) {
            businessModules.value = 核心指标模块列表
            businessModulesData.value = {
              businessModules: 核心指标模块列表,
              核心指标数据: 核心指标数据,
              timeRange: currentTimeRange.value,
              teamId: firstTeamId
            }

            console.log('TeamDashboard: 初始化核心业务指标模块已加载', {
              核心指标模块数量: 核心指标模块列表.length,
              模块列表: 核心指标模块列表.map(模块 => 模块.title)
            })
          }
        }
      }
    } else {
      console.warn('获取团队列表失败，状态码:', response.status, '错误信息:', response.message)
      message.error(response.message || '获取团队列表失败')
    }
  } catch (error) {
    console.error('获取团队列表失败:', error)
    message.error('获取团队列表失败，请稍后重试')
  } finally {
    teamsLoading.value = false
  }
}

// 获取团队基础数据（简化版本，主要用于设置加载状态）
const fetchDashboardData = async (teamId, setLoading = true) => {
  if (!teamId) return null

  if (setLoading) {
    dataLoading.value = true
  }

  try {
    console.log('TeamDashboard: 获取团队基础数据', {
      teamId,
      时间范围: currentTimeRange.value
    })

    // 现在主要依赖核心业务指标数据，这里只是设置基础状态
    dashboardData.value = {
      团队id: teamId,
      时间范围: currentTimeRange.value,
      更新时间: new Date().toISOString()
    }

    console.log('TeamDashboard: 团队基础数据设置完成')
    return dashboardData.value
  } catch (error) {
    console.error('TeamDashboard: 团队基础数据设置异常', error)
    dashboardData.value = null
    return null
  } finally {
    if (setLoading) {
      dataLoading.value = false
    }
  }
}

// 获取团队核心业务指标聚合数据 - 与工作台核心指标完全对标
const fetchCoreMetricsData = async (团队id) => {
  if (!团队id) return null

  businessModulesLoading.value = true
  try {
    console.log('TeamDashboard: 获取团队核心业务指标聚合数据', {
      团队id,
      时间范围: currentTimeRange.value
    })

    const 响应数据 = await teamDashboardService.getTeamCoreMetricsAggregated(团队id, {
      timeRange: currentTimeRange.value
    })

    if (响应数据 && 响应数据.status === 100) {
      console.log('TeamDashboard: 核心业务指标聚合数据获取成功', {
        微信运营指标: !!响应数据.data?.微信运营指标,
        达人管理指标: !!响应数据.data?.达人管理指标,
        寄样管理指标: !!响应数据.data?.寄样管理指标,
        团队汇总: !!响应数据.data?.团队汇总,

      })

      // 直接使用后端返回的指标卡片数据，按模块分组
      const 核心指标模块列表 = []

      // 微信运营核心指标模块 - 显示所有微信运营指标
      if (响应数据.data.微信运营指标) {
        const 微信指标卡片 = []

        // 直接从微信运营指标数据构建所有指标卡片
        Object.entries(响应数据.data.微信运营指标).forEach(([指标名称, 指标数值]) => {
          微信指标卡片.push({
            标题: 指标名称,
            数值: 指标数值 || 0,
            格式化数值: String(指标数值 || 0),
            单位: 指标名称.includes('数量') ? '个' : '人',
            图标: 'wechat',
            颜色: '#1890ff',
            趋势: `当前${指标数值 || 0}`,
            趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
          })
        })

        console.log('🔍 微信运营指标构建结果:', {
          指标数量: 微信指标卡片.length,
          指标列表: 微信指标卡片.map(卡片 => ({ 标题: 卡片.标题, 数值: 卡片.数值 }))
        })

        核心指标模块列表.push({
          key: 'wechat_core',
          title: '微信运营核心指标',
          metrics: 微信指标卡片,
          color: '#1890ff',
          汇总数据: 响应数据.data.微信运营指标
        })
      }

      // 达人管理核心指标模块 - 显示所有达人管理指标
      if (响应数据.data.达人管理指标) {
        const 达人指标卡片 = []

        // 直接从达人管理指标数据构建所有指标卡片
        Object.entries(响应数据.data.达人管理指标).forEach(([指标名称, 指标数值]) => {
          达人指标卡片.push({
            标题: 指标名称,
            数值: 指标数值 || 0,
            格式化数值: String(指标数值 || 0),
            单位: 指标名称.includes('数量') || 指标名称.includes('总数') ? '个' : '人',
            图标: 'star',
            颜色: '#fa8c16',
            趋势: `当前${指标数值 || 0}`,
            趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
          })
        })

        console.log('🔍 达人管理指标构建结果:', {
          指标数量: 达人指标卡片.length,
          指标列表: 达人指标卡片.map(卡片 => ({ 标题: 卡片.标题, 数值: 卡片.数值 }))
        })

        核心指标模块列表.push({
          key: 'talent_core',
          title: '达人管理核心指标',
          metrics: 达人指标卡片,
          color: '#fa8c16',
          汇总数据: 响应数据.data.达人管理指标
        })
      }

      // 寄样管理核心指标模块 - 显示所有寄样管理指标
      if (响应数据.data.寄样管理指标) {
        const 寄样指标卡片 = []

        // 直接从寄样管理指标数据构建所有指标卡片
        Object.entries(响应数据.data.寄样管理指标).forEach(([指标名称, 指标数值]) => {
          寄样指标卡片.push({
            标题: 指标名称,
            数值: 指标数值 || 0,
            格式化数值: String(指标数值 || 0),
            单位: '件',
            图标: 'gift',
            颜色: '#eb2f96',
            趋势: `当前${指标数值 || 0}`,
            趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
          })
        })

        console.log('🔍 寄样管理指标构建结果:', {
          指标数量: 寄样指标卡片.length,
          指标列表: 寄样指标卡片.map(卡片 => ({ 标题: 卡片.标题, 数值: 卡片.数值 }))
        })

        核心指标模块列表.push({
          key: 'sample_core',
          title: '寄样管理核心指标',
          metrics: 寄样指标卡片,
          color: '#eb2f96',
          汇总数据: 响应数据.data.寄样管理指标
        })
      }

      console.log('🎯 核心指标模块列表构建完成:', {
        模块总数: 核心指标模块列表.length,
        模块详情: 核心指标模块列表.map(模块 => ({
          标题: 模块.title,
          指标数量: 模块.metrics.length,
          指标列表: 模块.metrics.map(指标 => ({ 标题: 指标.标题, 数值: 指标.数值 }))
        }))
      })

      return {
        核心指标模块列表,
        核心指标数据: 响应数据.data
      }
    } else {
      console.warn('TeamDashboard: 核心业务指标聚合数据获取失败', 响应数据)
      return null
    }
  } catch (error) {
    console.error('TeamDashboard: 核心业务指标聚合数据获取异常', error)
    return null
  } finally {
    businessModulesLoading.value = false
  }
}



// 切换团队
const handleTeamChange = async (团队id) => {
  selectedTeamId.value = 团队id
  // 切换团队时立即获取数据（直接使用核心业务指标聚合）
  if (团队id) {
    // 并行加载核心业务指标和看板数据
    const [核心指标结果, 看板数据结果] = await Promise.allSettled([
      fetchCoreMetricsData(团队id),
      fetchDashboardData(团队id)
    ])

    // 处理核心业务指标数据
    if (核心指标结果.status === 'fulfilled' && 核心指标结果.value) {
      const { 核心指标模块列表, 核心指标数据 } = 核心指标结果.value

      // 设置业务模块数据
      if (核心指标模块列表 && 核心指标模块列表.length > 0) {
        businessModules.value = 核心指标模块列表
        businessModulesData.value = {
          businessModules: 核心指标模块列表,
          核心指标数据: 核心指标数据,
          timeRange: currentTimeRange.value,
          teamId: 团队id
        }

        console.log('TeamDashboard: 核心业务指标模块已加载', {
          核心指标模块数量: 核心指标模块列表.length,
          模块列表: 核心指标模块列表.map(模块 => 模块.title),
          团队汇总: 核心指标数据?.团队汇总
        })
      }
    }

    console.log('TeamDashboard: 团队数据并行加载完成', {
      团队id,
      核心指标: 核心指标结果.status,
      看板数据: 看板数据结果.status
    })
  } else {
    // 清空数据
    dashboardData.value = null
    businessModules.value = []
    memberRankingData.value = null
  }
}

// 处理时间范围变化（参考工作台逻辑）
const handleTimeRangeChange = async (e) => {
  const range = e.target ? e.target.value : e
  console.log('TeamDashboard: 时间范围变化', {
    原值: currentTimeRange.value,
    新值: range,
    团队id: selectedTeamId.value
  })

  currentTimeRange.value = range

  // 时间范围变化时重新获取所有数据
  if (selectedTeamId.value) {
    console.log('TeamDashboard: 开始重新获取所有数据', {
      团队id: selectedTeamId.value,
      时间范围: range
    })

    // 并行重新获取核心业务指标和看板数据
    const [核心指标结果, 看板数据结果] = await Promise.allSettled([
      fetchCoreMetricsData(selectedTeamId.value),
      fetchDashboardData(selectedTeamId.value)
    ])

    // 处理核心业务指标数据
    if (核心指标结果.status === 'fulfilled' && 核心指标结果.value) {
      const { 核心指标模块列表, 核心指标数据 } = 核心指标结果.value

      // 设置业务模块数据
      if (核心指标模块列表 && 核心指标模块列表.length > 0) {
        businessModules.value = 核心指标模块列表
        businessModulesData.value = {
          businessModules: 核心指标模块列表,
          核心指标数据: 核心指标数据,
          timeRange: range,
          teamId: selectedTeamId.value
        }

        console.log('TeamDashboard: 时间范围变化后核心业务指标模块已更新', {
          核心指标模块数量: 核心指标模块列表.length,
          时间范围: range,
          团队汇总: 核心指标数据?.团队汇总
        })
      }
    }

    console.log('TeamDashboard: 时间范围变化数据重新加载完成', {
      时间范围: range,
      核心指标: 核心指标结果.status,
      看板数据: 看板数据结果.status
    })
  }
}









// 提供数据给子组件使用的计算属性
const provideDashboardData = computed(() => dashboardData.value)

// 提供给子组件使用的数据
provide('dashboardData', provideDashboardData)
provide('dataLoading', computed(() => dataLoading.value))

// 辅助函数（参考工作台逻辑）
const getModuleIcon = (moduleKey) => {
  switch (moduleKey) {
    case 'wechat':
    case 'wechat_core':
      return WechatOutlined
    case 'talent':
    case 'talent_core':
      return TeamOutlined
    case 'sample':
    case 'sample_core':
      return GiftOutlined
    case 'invitation':
      return MailOutlined
    default:
      return AppstoreOutlined
  }
}

const getMetricIcon = (iconName) => {
  // 图标映射 - 支持多种格式
  const iconMap = {
    'wechat': WechatOutlined,
    'contacts': ContactsOutlined,
    'team': TeamOutlined,
    'swap': SwapOutlined,
    'user-add': TeamOutlined,
    'fire': InfoCircleOutlined,
    'project': AppstoreOutlined,
    'dollar': InfoCircleOutlined,
    'send': InfoCircleOutlined,
    'database': InfoCircleOutlined,
    'message': ContactsOutlined,
    'interaction': TeamOutlined,
    'usergroup-add': TeamOutlined,
    'video-camera': InfoCircleOutlined,
    'gift': GiftOutlined,
    'mail': MailOutlined,
    'star': StarOutlined,
    'check-circle': CheckCircleOutlined
  }

  return iconMap[iconName] || InfoCircleOutlined
}

const getTrendClass = (trendType) => {
  switch (trendType) {
    case 'up':
    case '上升':
      return 'trend-up'
    case 'down':
    case '下降':
      return 'trend-down'
    case 'stable':
    case '持平':
      return 'trend-flat'
    default:
      return ''
  }
}

const getTrendIcon = (trendType) => {
  switch (trendType) {
    case 'up':
    case '上升':
      return ArrowUpOutlined
    case 'down':
    case '下降':
      return ArrowDownOutlined
    case 'stable':
    case '持平':
      return SwapOutlined
    default:
      return InfoCircleOutlined
  }
}

// 获取活跃数详情提示信息
const getActiveCountTooltip = (module) => {
  // 根据不同模块返回相应的详情说明
  switch (module.key) {
    case 'wechat':
      return `统计数值大于0的微信管理指标\n包括：账号数、好友数、沟通数等`

    case 'invitation':
      return `统计数值大于0的邀约管理指标\n包括：邀约数、认领达人数等`

    case 'talent':
      return `活跃达人基于多维度评分：\n• 沟通活跃度（消息互动）\n• 业务参与度（寄样申请）\n• 信息完整度（资料完善）\n• 认领时效性（认领时间）\n\n≥40分为活跃达人`

    case 'sample':
      return `统计数值大于0的样品管理指标\n包括：合作项目、销售额、开播率等`

    default:
      return `显示数值大于0的指标数量`
  }
}

// 处理指标点击事件
const handleMetricClick = (moduleKey) => {
  console.log('Metric clicked:', moduleKey)
  // 处理指标卡片点击事件 Handle metric card click
  // 可以跳转到详细页面 Can navigate to detail page
}

const handleMetricDetail = (moduleKey, metric) => {
  console.log('Metric detail clicked:', moduleKey, metric)
  // 暂时移除路由跳转，因为MetricDetail路由不存在
  // TODO: 待详情页面开发完成后恢复路由跳转
}

// 生命周期钩子
onMounted(() => {
  fetchTeams()
})


</script>

<style scoped>
.team-dashboard {
  padding: 16px;
  min-height: 100%;
  background-color: #f0f2f5;
  /* 添加容器查询支持 */
  container-type: inline-size;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  /* 改进响应式布局 */
  flex-wrap: wrap;
  gap: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  /* 确保在小屏幕上不会被压缩 */
  min-width: 0;
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  /* 响应式字体大小 */
  white-space: nowrap;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  /* 改进容器适配 */
  width: 100%;
  max-width: 100%;
}

.detailed-panels {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  /* 改进响应式网格 */
  width: 100%;
}



/* 核心业务指标样式（优化响应式设计）*/
.metrics-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  /* 确保在所有屏幕尺寸下都能正常显示 */
  width: 100%;
  max-width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 0 4px 24px 4px;
  position: relative;
  /* 改进响应式布局 */
  flex-wrap: wrap;
  gap: 16px;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(147, 51, 234, 0.6) 50%,
    rgba(59, 130, 246, 0.3) 100%
  );
  border-radius: 1px;
}

.section-title {
  margin: 0;
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  /* 响应式字体大小 */
  white-space: nowrap;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  /* 响应式调整 */
  flex-shrink: 0;
}

.time-range-selector {
  display: flex;
  align-items: center;
  /* 改进响应式布局 */
  flex-shrink: 0;
}

.time-range-selector :deep(.ant-radio-group) {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
  /* 响应式调整 */
  flex-wrap: wrap;
}

.time-range-selector :deep(.ant-radio-button-wrapper) {
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 响应式字体大小 */
  font-size: 14px;
  white-space: nowrap;
}

.time-range-selector :deep(.ant-radio-button-wrapper-checked) {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 改进指标卡片容器的响应式布局 */
.metrics-cards-container {
  display: grid;
  /* 更智能的网格布局 - 减小最小宽度 */
  grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
  gap: 24px;
  position: relative;
  min-height: 400px;
  width: 100%;
  max-width: 100%;
}

/* 容器查询支持 */
@container (max-width: 700px) {
  .metrics-cards-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@container (min-width: 701px) and (max-width: 1200px) {
  .metrics-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (min-width: 1201px) {
  .metrics-cards-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

.metric-module-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  /* 改进响应式适配 */
  min-width: 0;
  max-width: 100%;
}

.metric-module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.metric-module-card.empty-card {
  opacity: 0.7;
  background: rgba(248, 250, 252, 0.8);
}

.module-header {
  display: flex;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.08);
  position: relative;
  /* 改进响应式布局 */
  flex-wrap: wrap;
  gap: 12px;
}

.module-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.3) 0%, transparent 100%);
}

.module-icon-container {
  margin-right: 16px;
  /* 响应式调整 */
  flex-shrink: 0;
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  /* 响应式调整 */
  flex-shrink: 0;
}

.module-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
}

.module-icon.empty {
  background: #f1f5f9 !important;
  box-shadow: none;
  border: 2px solid #e2e8f0;
}

.module-info {
  flex: 1;
  /* 确保文本不会溢出 */
  min-width: 0;
}

.module-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 4px 0;
  line-height: 1.3;
  /* 改进文本溢出处理 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.module-subtitle {
  font-size: 13px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  /* 改进文本溢出处理 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.module-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  /* 响应式调整 */
  flex-shrink: 0;
}

.active-count {
  font-size: 16px;
  font-weight: 800;
  color: #16a34a;
  line-height: 1;
}

.badge-label {
  font-size: 10px;
  color: #16a34a;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metrics-list {
  padding: 0;
}

.metric-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  /* 改进响应式布局 */
  flex-wrap: wrap;
  gap: 12px;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  transform: translateX(4px);
}

.metric-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-item:hover::before {
  width: 3px;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  /* 确保不会溢出 */
  min-width: 0;
}

.metric-icon-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(226, 232, 240, 0.6);
  /* 响应式调整 */
  flex-shrink: 0;
}

.metric-icon {
  font-size: 18px;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  /* 确保不会溢出 */
  min-width: 0;
  flex: 1;
}

.metric-name {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  /* 改进文本溢出处理 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.metric-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.2;
  /* 改进文本溢出处理 */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.metric-value-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  /* 响应式调整 */
  flex-shrink: 0;
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  /* 响应式调整 */
  flex-wrap: wrap;
}

.value-number {
  font-size: 20px;
  font-weight: 800;
  line-height: 1;
}

.value-unit {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.trend-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  /* 响应式调整 */
  white-space: nowrap;
}

.trend-icon {
  font-size: 12px;
}

.trend-display.trend-up {
  color: #16a34a;
  background: rgba(34, 197, 94, 0.1);
}

.trend-display.trend-down {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
}

.trend-display.trend-flat {
  color: #64748b;
  background: rgba(100, 116, 139, 0.1);
}

.trend-display.no-trend {
  color: #9ca3af;
  background: transparent;
  padding: 0;
}

.empty-card {
  opacity: 0.7;
}

.empty-state {
  padding: 40px 24px;
  text-align: center;
  color: #64748b;
}

.empty-text {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.metrics-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  z-index: 10;
}

.metrics-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  z-index: 10;
}

.no-team-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
}

.teams-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
}

.data-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  grid-column: 1 / -1; /* 占据整行 */
}

.data-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  grid-column: 1 / -1; /* 占据整行 */
}



.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-name {
  font-weight: 500;
}

/* 改进响应式设计 - 更精细的断点控制 */

/* 大屏幕 (1400px+) */
@media (min-width: 1400px) {
  .team-dashboard {
    padding: 24px;
  }
  
  .dashboard-header {
    margin-bottom: 32px;
  }
  
  .page-title {
    font-size: 28px;
  }
  
  .section-title {
    font-size: 26px;
  }
  
  .metrics-cards-container {
    gap: 32px;
  }
}

/* 中等屏幕 (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .time-range-selector {
    align-self: flex-end;
  }
}

/* 平板屏幕 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .team-dashboard {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .page-title {
    font-size: 22px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .time-range-selector {
    width: 100%;
    justify-content: center;
  }
  
  .time-range-selector :deep(.ant-radio-group) {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .time-range-selector :deep(.ant-radio-button-wrapper) {
    font-size: 13px;
    padding: 8px 12px;
  }

  .metrics-cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .module-header {
    padding: 20px;
  }

  .module-icon {
    width: 44px;
    height: 44px;
    font-size: 22px;
  }

  .module-title {
    font-size: 16px;
  }

  .module-subtitle {
    font-size: 12px;
  }

  .metric-item {
    padding: 16px 20px;
  }

  .metric-name {
    font-size: 14px;
  }

  .value-number {
    font-size: 18px;
  }
}

/* 移动端 (最大767px) */
@media (max-width: 767px) {
  .team-dashboard {
    padding: 12px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
  }

  .header-left {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-right {
    width: 100%;
  }

  .page-title {
    font-size: 20px;
  }
  
  .section-title {
    font-size: 18px;
  }
  
  .section-title::before {
    width: 3px;
    height: 24px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 0 2px 16px 2px;
  }
  
  .time-range-selector {
    width: 100%;
    overflow-x: auto;
  }
  
  .time-range-selector :deep(.ant-radio-group) {
    flex-wrap: nowrap;
    min-width: max-content;
    padding: 4px;
  }
  
  .time-range-selector :deep(.ant-radio-button-wrapper) {
    font-size: 12px;
    padding: 6px 10px;
    white-space: nowrap;
  }

  /* 移动端指标卡片响应式样式 */
  .metrics-cards-container {
    grid-template-columns: 1fr; /* 移动端单列布局 */
    gap: 16px;
  }

  .metric-module-card {
    border-radius: 16px;
    /* 移动端卡片最小宽度优化 */
    min-width: 280px;
  }

  .module-header {
    padding: 14px;
    /* 移动端头部保持水平布局，但允许换行 */
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .module-icon-container {
    margin-right: 0;
    flex-shrink: 0;
  }

  .module-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .module-info {
    flex: 1;
    min-width: 120px;
  }

  .module-title {
    font-size: 15px;
  }

  .module-subtitle {
    font-size: 11px;
  }

  .module-badge {
    padding: 4px 8px;
    flex-shrink: 0;
  }

  .active-count {
    font-size: 12px;
  }

  .badge-label {
    font-size: 8px;
  }

  .metric-item {
    padding: 12px 14px;
    /* 移动端指标项保持水平布局 */
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .metric-info {
    flex: 1;
    min-width: 0;
  }

  .metric-value-section {
    flex-shrink: 0;
    align-items: flex-end;
    flex-direction: column;
    gap: 2px;
  }

  .metric-icon-wrapper {
    width: 28px;
    height: 28px;
  }

  .metric-icon {
    font-size: 14px;
  }

  .metric-name {
    font-size: 13px;
  }

  .metric-description {
    font-size: 10px;
  }

  .value-number {
    font-size: 15px;
  }

  .value-unit {
    font-size: 10px;
  }

  .trend-display {
    font-size: 10px;
  }

  .empty-state {
    padding: 20px 14px;
  }

  .empty-text {
    font-size: 12px;
  }
  
  .detailed-panels {
    gap: 12px;
  }
}

/* 小屏幕移动端 (最大479px) */
@media (max-width: 479px) {
  .team-dashboard {
    padding: 8px;
  }
  
  .dashboard-header {
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .section-header {
    margin-bottom: 16px;
    padding: 0 2px 12px 2px;
  }
  
  .time-range-selector :deep(.ant-radio-button-wrapper) {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .metric-module-card {
    /* 小屏幕下进一步减小最小宽度 */
    min-width: 260px;
  }
  
  .module-header {
    padding: 12px;
    gap: 8px;
  }
  
  .module-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .module-info {
    min-width: 100px;
  }
  
  .module-title {
    font-size: 14px;
  }
  
  .module-subtitle {
    font-size: 10px;
  }
  
  .module-badge {
    padding: 3px 6px;
  }
  
  .active-count {
    font-size: 11px;
  }
  
  .badge-label {
    font-size: 7px;
  }
  
  .metric-item {
    padding: 10px 12px;
    gap: 6px;
  }
  
  .metric-icon-wrapper {
    width: 24px;
    height: 24px;
  }
  
  .metric-icon {
    font-size: 12px;
  }
  
  .metric-name {
    font-size: 12px;
  }
  
  .metric-description {
    font-size: 9px;
  }
  
  .value-number {
    font-size: 14px;
  }
  
  .value-unit {
    font-size: 9px;
  }
  
  .trend-display {
    font-size: 9px;
  }
}

/* 侧边栏适配优化 */
@media (min-width: 768px) {
  /* 当侧边栏展开时，调整内容区域 */
  .team-dashboard {
    /* 为侧边栏留出空间 */
    transition: all 0.3s ease;
  }
  
  /* 当侧边栏收起时，内容区域可以使用更多空间 */
  .sidebar-collapsed .team-dashboard {
    /* 可以使用更多的水平空间 */
    max-width: calc(100vw - 80px);
  }
}

/* 针对容器查询的回退方案 */
@supports not (container-type: inline-size) {
  .metrics-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  }
  
  @media (max-width: 700px) {
    .metrics-cards-container {
      grid-template-columns: 1fr;
    }
  }
  
  @media (min-width: 701px) and (max-width: 1200px) {
    .metrics-cards-container {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (min-width: 1201px) {
    .metrics-cards-container {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
</style> 