<template>
  <div class="store-page sample-management-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <experiment-outlined class="store-title-icon" />
        样品管理
      </h1>
      <p class="store-page-description">管理产品样品申请、审核和寄送状态</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <SampleManagementModule />
    </div>
  </div>
</template>

<script setup>
import { ExperimentOutlined } from '@ant-design/icons-vue'
import SampleManagementModule from '@/components/store/SampleManagementModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'SampleManagement'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}

/* 样品管理特有样式 */
.page-content :deep(.sample-status-pending) {
  color: #faad14;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.page-content :deep(.sample-status-approved) {
  color: #52c41a;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.page-content :deep(.sample-status-rejected) {
  color: #ff4d4f;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.page-content :deep(.sample-status-shipped) {
  color: #1890ff;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.page-content :deep(.sample-info-card) {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fafafa;
}

.page-content :deep(.sample-product-info) {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.page-content :deep(.sample-product-image) {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
  background: #f0f0f0;
}

.page-content :deep(.sample-product-details) {
  flex: 1;
}

.page-content :deep(.sample-product-name) {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.page-content :deep(.sample-product-spec) {
  font-size: 12px;
  color: #666;
}

.page-content :deep(.sample-actions) {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}
</style>
