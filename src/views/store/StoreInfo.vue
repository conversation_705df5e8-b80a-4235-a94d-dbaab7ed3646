<template>
  <div class="store-page store-info-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <shop-outlined class="store-title-icon" />
        店铺信息
      </h1>
      <p class="store-page-description">管理您的店铺基本信息和绑定状态</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <StoreInfoModule />
    </div>
  </div>
</template>

<script setup>
import { ShopOutlined } from '@ant-design/icons-vue'
import StoreInfoModule from '@/components/store/StoreInfoModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'StoreInfo'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}
</style>
