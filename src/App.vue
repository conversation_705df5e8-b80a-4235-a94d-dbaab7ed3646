<template>
  <router-view />
  <!-- AI智能助手组件 - 全局悬浮 -->
  <AIAssistant />
</template>

<script setup>
import AIAssistant from '@/components/common/AIAssistant.vue'

// 应用根组件
defineOptions({
  name: 'App'
})
</script>

<style>
#app {
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
</style> 