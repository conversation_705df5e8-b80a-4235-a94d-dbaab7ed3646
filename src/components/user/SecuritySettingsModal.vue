<template>
  <a-modal
    :open="open"
    title="安全设置"
    @cancel="handleCancel"
    @update:open="$emit('update:open', $event)"
  >
    <a-empty description="安全设置功能开发中..." />
  </a-modal>
</template>

<script setup>
defineOptions({
  name: 'SecuritySettingsModal'
})

const props = defineProps({
  open: Boolean,
  userInfo: Object
})

const emit = defineEmits(['update:open', 'success'])

const handleCancel = () => {
  emit('update:open', false)
}
</script>