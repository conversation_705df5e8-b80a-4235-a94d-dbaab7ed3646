<template>
  <a-modal
    :open="open"
    title="修改密码"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:open', $event)"
    width="450px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="当前密码" name="oldPassword">
        <a-input-password
          v-model:value="formData.oldPassword"
          placeholder="请输入当前密码"
          :prefix="h(LockOutlined)"
        />
      </a-form-item>

      <a-form-item label="新密码" name="newPassword">
        <a-input-password
          v-model:value="formData.newPassword"
          placeholder="请输入新密码"
          :prefix="h(KeyOutlined)"
        />
        <div class="password-strength">
          <div class="strength-bar">
            <div
              class="strength-fill"
              :class="passwordStrengthClass"
              :style="{ width: passwordStrengthWidth }"
            ></div>
          </div>
          <span class="strength-text">{{ passwordStrengthText }}</span>
        </div>
      </a-form-item>

      <a-form-item label="确认新密码" name="confirmPassword">
        <a-input-password
          v-model:value="formData.confirmPassword"
          placeholder="请再次输入新密码"
          :prefix="h(SafetyOutlined)"
        />
      </a-form-item>
    </a-form>

    <div class="password-tips">
      <h4>密码安全建议：</h4>
      <ul>
        <li>密码长度至少6个字符</li>
        <li>建议包含大小写字母、数字和特殊字符</li>
        <li>不要使用生日、手机号等个人信息</li>
        <li>定期更换密码，保障账户安全</li>
      </ul>
    </div>
  </a-modal>
</template>

<script setup>
import { reactive, ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import { userAPI } from '../../services/user'
import {
  LockOutlined,
  KeyOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'

defineOptions({
  name: 'PasswordChangeModal'
})

const props = defineProps({
  open: Boolean
})

const emit = defineEmits(['update:open', 'success'])

// 表单引用和加载状态
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码强度计算
const passwordStrength = computed(() => {
  const password = formData.newPassword
  if (!password) return 0

  let strength = 0

  // 长度检查
  if (password.length >= 6) strength += 1
  if (password.length >= 8) strength += 1

  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1

  // 包含数字
  if (/\d/.test(password)) strength += 1

  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1

  return Math.min(strength, 4)
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 1) return 'weak'
  if (strength <= 2) return 'fair'
  if (strength <= 3) return 'good'
  return 'strong'
})

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return ''
  if (strength <= 1) return '弱'
  if (strength <= 2) return '一般'
  if (strength <= 3) return '良好'
  return '强'
})

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6个字符', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && value === formData.oldPassword) {
          return Promise.reject('新密码不能与当前密码相同')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && value !== formData.newPassword) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 提交表单
const handleOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    loading.value = true
    console.log('🔄 开始修改密码...')

    // 调用修改密码API
    const response = await userAPI.changePassword({
      old_password: formData.oldPassword,
      new_password: formData.newPassword
    })

    console.log('📥 密码修改响应:', response)

    // 检查响应状态
    if (response?.status === 100) {
      message.success('密码修改成功，请重新登录')
      emit('success')
      emit('update:open', false)

      // 清空表单
      Object.assign(formData, {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    } else {
      // 处理业务错误
      const errorMsg = response?.message || '密码修改失败'
      message.error(errorMsg)
      console.warn('⚠️ 密码修改业务错误:', response)
    }

  } catch (error) {
    console.error('❌ 修改密码失败:', error)

    // 区分表单验证错误和API错误
    if (error.errorFields && Array.isArray(error.errorFields)) {
      // 表单验证错误
      const firstError = error.errorFields[0]
      const errorMsg = firstError?.errors?.[0] || '请检查表单填写是否正确'
      message.error(errorMsg)
    } else if (error.message) {
      // API错误
      message.error(error.message)
    } else {
      // 未知错误
      message.error('修改失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  // 清空表单
  Object.assign(formData, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  emit('update:open', false)
}
</script>

<style scoped>
.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background-color: #ff4d4f;
}

.strength-fill.fair {
  background-color: #faad14;
}

.strength-fill.good {
  background-color: #1890ff;
}

.strength-fill.strong {
  background-color: #52c41a;
}

.strength-text {
  font-size: 12px;
  color: #666;
}

.password-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.password-tips h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

.password-tips ul {
  margin: 0;
  padding-left: 16px;
}

.password-tips li {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
}

:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #374151;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 8px;
}

:deep(.ant-input-affix-wrapper:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}
</style>