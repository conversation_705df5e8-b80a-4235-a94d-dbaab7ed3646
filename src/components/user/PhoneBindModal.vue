<template>
  <a-modal
    :open="open"
    title="手机号绑定"
    @cancel="handleCancel"
    @update:open="$emit('update:open', $event)"
  >
    <a-empty description="手机号绑定功能开发中..." />
  </a-modal>
</template>

<script setup>
defineOptions({
  name: 'PhoneBindModal'
})

const props = defineProps({
  open: Boolean,
  currentPhone: String
})

const emit = defineEmits(['update:open', 'success'])

const handleCancel = () => {
  emit('update:open', false)
}
</script>