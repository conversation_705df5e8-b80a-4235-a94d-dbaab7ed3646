<template>
  <a-modal
    :open="open"
    title="编辑个人信息"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:open', $event)"
    width="500px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="昵称" name="nickname">
        <a-input
          v-model:value="formData.nickname"
          placeholder="请输入昵称"
          :prefix="h(UserOutlined)"
        />
      </a-form-item>

      <a-form-item label="手机号" name="phone">
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入手机号"
          :prefix="h(PhoneOutlined)"
          disabled
        />
        <div class="form-tip">手机号不可修改，如需更换请联系客服</div>
      </a-form-item>

      <a-form-item label="邮箱" name="email">
        <a-input
          v-model:value="formData.email"
          placeholder="请输入邮箱地址"
          :prefix="h(MailOutlined)"
        />
      </a-form-item>

      <a-form-item label="部门" name="department">
        <a-input
          v-model:value="formData.department"
          placeholder="请输入所属部门"
        />
      </a-form-item>

      <a-form-item label="职位" name="position">
        <a-input
          v-model:value="formData.position"
          placeholder="请输入职位"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { reactive, ref, watch, h } from 'vue'
import { message } from 'ant-design-vue'
import { userAPI } from '../../services/user'
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined
} from '@ant-design/icons-vue'

defineOptions({
  name: 'PersonalInfoModal'
})

const props = defineProps({
  open: Boolean,
  userInfo: Object
})

const emit = defineEmits(['update:open', 'success'])

// 表单引用和加载状态
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  nickname: '',
  phone: '',
  email: '',
  department: '',
  position: ''
})

// 表单验证规则
const rules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度应在2-20个字符之间', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 监听用户信息变化，同步到表单
watch(() => props.userInfo, (newUserInfo) => {
  if (newUserInfo && props.open) {
    Object.assign(formData, {
      nickname: newUserInfo.name || '',
      phone: newUserInfo.phone || '',
      email: newUserInfo.email || ''
    })
  }
}, { immediate: true, deep: true })

// 提交表单
const handleOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    loading.value = true
    console.log('🔄 开始更新个人信息...', formData)

    // 调用设置昵称API（目前后端只支持昵称更新）
    if (formData.nickname !== props.userInfo?.name) {
      const nicknameResponse = await userAPI.setNickname({
        昵称: formData.nickname
      })

      console.log('📥 昵称更新响应:', nicknameResponse)

      // 检查响应状态 - 统一响应格式处理
      if (nicknameResponse && nicknameResponse.status === 100) {
        // 成功响应处理
        const successMsg = nicknameResponse.message || '个人信息更新成功'
        message.success(successMsg)

        // 返回更新后的数据
        const updatedData = {
          ...props.userInfo,
          name: formData.nickname,
          nickname: formData.nickname,
          昵称: formData.nickname,
          email: formData.email,
          department: formData.department,
          position: formData.position
        }

        emit('success', updatedData)
        emit('update:open', false)
      } else {
        // 处理错误响应
        let errorMsg = '更新失败，请重试'

        if (nicknameResponse) {
          if (nicknameResponse.message) {
            errorMsg = nicknameResponse.message
          } else if (nicknameResponse.msg) {
            errorMsg = nicknameResponse.msg
          }
        }

        console.warn('⚠️ 昵称更新失败:', errorMsg)
        message.error(errorMsg)
      }
    } else {
      message.success('个人信息更新成功')
      emit('update:open', false)
    }

  } catch (error) {
    console.error('❌ 更新个人信息失败:', error)
    if (error.errorFields) {
      message.error('请检查表单填写是否正确')
    } else {
      message.error('更新失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:open', false)
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #374151;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 8px;
}

:deep(.ant-input-affix-wrapper:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}
</style>