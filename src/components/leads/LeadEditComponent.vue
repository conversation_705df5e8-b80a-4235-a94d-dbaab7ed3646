<template>
  <div class="lead-edit">
    <a-form
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
      ref="formRef"
    >
      <!-- 基础信息 -->
      <a-card title="基础信息" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="达人名称" name="名称">
              <a-input 
                v-model:value="formData.名称" 
                placeholder="请输入达人名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="类别" name="类别">
              <a-select 
                v-model:value="formData.类别" 
                placeholder="请选择类别"
                allow-clear
              >
                <a-select-option 
                  v-for="category in categoryOptions" 
                  :key="category" 
                  :value="category"
                >
                  {{ category }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="省份" name="省份">
              <a-input 
                v-model:value="formData.省份" 
                placeholder="请输入省份"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="城市" name="城市">
              <a-input 
                v-model:value="formData.城市" 
                placeholder="请输入城市"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="粉丝数" name="抖音粉丝数">
              <a-input-number 
                v-model:value="formData.抖音粉丝数" 
                placeholder="请输入粉丝数"
                :min="0"
                style="width: 100%;"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
      
      <!-- 联系方式信息 -->
      <a-card title="联系方式" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="联系方式类型" name="联系方式类型">
              <a-select 
                v-model:value="formData.联系方式类型" 
                placeholder="请选择联系方式类型"
                allow-clear
              >
                <a-select-option value="微信">微信</a-select-option>
                <a-select-option value="电话">电话</a-select-option>
                <a-select-option value="QQ">QQ</a-select-option>
                <a-select-option value="邮箱">邮箱</a-select-option>
                <a-select-option value="其他">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="联系方式内容" name="联系方式内容">
              <a-input 
                v-model:value="formData.联系方式内容" 
                placeholder="请输入联系方式"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
      
      <!-- 其他信息 -->
      <a-card title="其他信息" style="margin-bottom: 16px;">
        <a-form-item label="线索来源" name="线索来源">
          <a-input 
            v-model:value="formData.线索来源" 
            placeholder="请输入线索来源"
            disabled
          />
        </a-form-item>
        
        <a-form-item label="备注信息" name="备注">
          <a-textarea 
            v-model:value="formData.备注" 
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-card>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="saving">
            <save-outlined />
            保存
          </a-button>
          <a-button @click="resetForm">
            <reload-outlined />
            重置
          </a-button>
          <a-button @click="$emit('cancel')">
            取消
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

// 定义props
const props = defineProps({
  lead: {
    type: Object,
    required: true
  }
})

// 定义emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  名称: '',
  类别: '',
  省份: '',
  城市: '',
  抖音粉丝数: 0,
  联系方式类型: '',
  联系方式内容: '',
  线索来源: '',
  备注: ''
})

// 表单验证规则
const formRules = {
  名称: [
    { required: true, message: '请输入达人名称', trigger: 'blur' }
  ],
  类别: [
    { required: true, message: '请选择类别', trigger: 'change' }
  ]
}

// 类别选项
const categoryOptions = [
  '美妆', '时尚', '美食', '旅游', '科技', '娱乐', '文化', '社会',
  '健身', '宠物', '母婴', '教育', '汽车', '房产', '金融', '其他'
]

// 监听props变化，初始化表单数据
watch(() => props.lead, (newLead) => {
  if (newLead) {
    initFormData(newLead)
  }
}, { immediate: true })

// 初始化表单数据
function initFormData(lead) {
  const parsedInfo = parseLeadInfo(lead.信息)
  
  Object.assign(formData, {
    名称: parsedInfo.名称 || parsedInfo.昵称 || '',
    类别: parsedInfo.类别 || parsedInfo.分类 || '',
    省份: parsedInfo.省份 || '',
    城市: parsedInfo.城市 || '',
    抖音粉丝数: parsedInfo.抖音粉丝数 || parsedInfo.粉丝数 || 0,
    联系方式类型: lead.关联_联系方式类型 || '',
    联系方式内容: lead.关联_联系方式内容 || '',
    线索来源: lead.线索来源 || '',
    备注: parsedInfo.备注 || ''
  })
}

// 解析线索信息
function parseLeadInfo(info) {
  if (!info) return {}
  
  if (typeof info === 'string') {
    try {
      return JSON.parse(info)
    } catch (e) {
      return {}
    }
  }
  
  return info
}

// 处理表单提交
async function handleSubmit() {
  try {
    // 验证表单
    await formRef.value.validate()
    
    saving.value = true
    
    // 构建更新数据
    const updateData = {
      线索id: props.lead.id,
      更新数据: {
        信息: JSON.stringify({
          名称: formData.名称,
          类别: formData.类别,
          省份: formData.省份,
          城市: formData.城市,
          抖音粉丝数: formData.抖音粉丝数,
          备注: formData.备注
        }),
        联系方式类型: formData.联系方式类型,
        联系方式内容: formData.联系方式内容
      }
    }
    
    // 触发保存事件
    emit('save', updateData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    message.error('请检查表单填写是否正确')
  } finally {
    saving.value = false
  }
}

// 重置表单
function resetForm() {
  initFormData(props.lead)
  message.info('表单已重置')
}
</script>

<style scoped>
.lead-edit {
  padding: 0;
}

.action-buttons {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 表单样式优化 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-input-number) {
  border-radius: 4px;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-input-number:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 卡片样式 */
:deep(.ant-card) {
  border-radius: 6px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 4px;
}
</style>
