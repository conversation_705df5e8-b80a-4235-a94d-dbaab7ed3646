<template>
  <div class="product-spec-editor">
    <!-- 产品规格标签 -->
    <a-card title="产品规格" size="small">
      <div class="spec-description">
        <p style="color: #666; margin-bottom: 16px;">
          <info-circle-outlined style="margin-right: 8px;" />
          添加产品的规格信息，如颜色、尺寸、型号等。每个规格作为一个标签显示。
        </p>
      </div>

      <a-form-item label="规格标签">
        <div class="tag-input-container">
          <a-tag
            v-for="(spec, index) in specList"
            :key="index"
            closable
            color="blue"
            @close="removeSpec(index)"
            class="spec-tag"
          >
            {{ spec }}
          </a-tag>

          <a-input
            v-if="showSpecInput"
            ref="specInput"
            v-model:value="newSpec"
            size="small"
            style="width: 120px;"
            placeholder="输入规格"
            @blur="handleSpecInputConfirm"
            @keyup.enter="handleSpecInputConfirm"
            @keyup.esc="cancelSpecInput"
          />

          <a-tag
            v-else
            style="background: #fff; border-style: dashed; cursor: pointer;"
            @click="showSpecInputField"
          >
            <plus-outlined /> 添加规格
          </a-tag>
        </div>
      </a-form-item>

      <!-- 常用规格快捷添加 -->
      <div class="quick-specs">
        <div class="quick-specs-title">
          <span style="color: #666; font-size: 12px;">常用规格：</span>
        </div>
        <div class="quick-specs-tags">
          <a-tag
            v-for="quickSpec in commonSpecs"
            :key="quickSpec"
            style="cursor: pointer; margin: 2px;"
            @click="addQuickSpec(quickSpec)"
            :disabled="specList.includes(quickSpec)"
          >
            <plus-outlined style="font-size: 10px;" /> {{ quickSpec }}
          </a-tag>
        </div>
      </div>

      <!-- 规格预览 -->
      <div class="spec-preview" v-if="specList.length > 0">
        <a-divider style="margin: 16px 0;" />
        <div class="preview-title">
          <span style="color: #666; font-size: 12px;">
            <eye-outlined style="margin-right: 4px;" />
            预览 ({{ specList.length }} 个规格)：
          </span>
        </div>
        <div class="preview-content">
          <span style="font-family: monospace; color: #1890ff;">
            {{ JSON.stringify(specList) }}
          </span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { PlusOutlined, InfoCircleOutlined, EyeOutlined } from '@ant-design/icons-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  value: {
    type: [Array, Object],
    default: () => []
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:value', 'change'])

// 响应式数据
const specList = ref([])
const showSpecInput = ref(false)
const newSpec = ref('')
const specInput = ref(null)

// 常用规格选项
const commonSpecs = [
  '黑色', '白色', '红色', '蓝色', '绿色',
  '小号', '中号', '大号', 'XL', 'XXL',
  '500g', '1kg', '2kg',
  '防水', '防尘', '抗震',
  '无线', '蓝牙', 'USB',
  '塑料', '金属', '玻璃', '皮革'
]

// 初始化数据
const initializeData = () => {
  if (Array.isArray(props.value)) {
    specList.value = [...props.value]
  } else if (props.value && typeof props.value === 'object') {
    // 兼容旧的复杂格式，提取所有值转为数组
    const extractedSpecs = []
    const extractValues = (obj) => {
      for (const key in obj) {
        const value = obj[key]
        if (Array.isArray(value)) {
          extractedSpecs.push(...value)
        } else if (typeof value === 'string' && value.trim()) {
          extractedSpecs.push(value)
        } else if (typeof value === 'object' && value !== null) {
          extractValues(value)
        }
      }
    }
    extractValues(props.value)
    specList.value = [...new Set(extractedSpecs)] // 去重
  } else {
    specList.value = []
  }
}

// 监听props变化
watch(() => props.value, initializeData, { immediate: true })

/**
 * 处理数据变化
 */
const handleChange = () => {
  emit('update:value', [...specList.value])
  emit('change', [...specList.value])
}

/**
 * 显示规格输入框
 */
const showSpecInputField = () => {
  showSpecInput.value = true
  nextTick(() => {
    specInput.value?.focus()
  })
}

/**
 * 确认添加规格
 */
const handleSpecInputConfirm = () => {
  const trimmedSpec = newSpec.value.trim()
  if (trimmedSpec && !specList.value.includes(trimmedSpec)) {
    specList.value.push(trimmedSpec)
    handleChange()
  }
  newSpec.value = ''
  showSpecInput.value = false
}

/**
 * 取消输入
 */
const cancelSpecInput = () => {
  newSpec.value = ''
  showSpecInput.value = false
}

/**
 * 移除规格
 */
const removeSpec = (index) => {
  specList.value.splice(index, 1)
  handleChange()
}

/**
 * 快速添加常用规格
 */
const addQuickSpec = (spec) => {
  if (!specList.value.includes(spec)) {
    specList.value.push(spec)
    handleChange()
  }
}

// 组件名称定义
defineOptions({
  name: 'ProductSpecEditor'
})
</script>

<style scoped>
.product-spec-editor {
  width: 100%;
}

.tag-input-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
}

.spec-tag {
  margin: 2px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.quick-specs {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.quick-specs-title {
  margin-bottom: 8px;
}

.quick-specs-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.quick-specs-tags .ant-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s;
}

.quick-specs-tags .ant-tag:hover:not(.ant-tag-disabled) {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.quick-specs-tags .ant-tag.ant-tag-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spec-preview {
  margin-top: 8px;
}

.preview-title {
  margin-bottom: 8px;
}

.preview-content {
  padding: 8px 12px;
  background: #f6f8fa;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
  font-size: 12px;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-input-container {
    gap: 4px;
  }

  .spec-tag {
    font-size: 11px;
    padding: 2px 6px;
  }

  .quick-specs-tags .ant-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}
</style>
