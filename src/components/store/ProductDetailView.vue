<template>
  <div class="product-detail-view">
    <div v-if="product" class="product-content">
      <!-- 产品基本信息 -->
      <div class="basic-info-section">
        <div class="section-header">
          <h3>
            <appstore-outlined />
            基本信息
          </h3>
          <a-space>
            <a-button size="small" @click="handleEdit">
              <template #icon>
                <edit-outlined />
              </template>
              编辑
            </a-button>
            <a-button size="small" danger @click="handleDelete">
              <template #icon>
                <delete-outlined />
              </template>
              删除
            </a-button>
          </a-space>
        </div>

        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="产品名称">
            {{ product.产品名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="产品分类">
            {{ product.产品分类 || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="产品状态">
            <a-tag :color="getStatusColor(product.状态)">
              {{ getStatusText(product.状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(product.创建时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="产品描述" :span="2">
            {{ product.产品描述 || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 详细信息 -->
      <div class="detail-info-section">
        <div class="section-header">
          <h3>
            <info-circle-outlined />
            详细信息
          </h3>
          <a-button
            v-if="product.产品信息 && Object.keys(product.产品信息).length > 0"
            size="small"
            @click="toggleJsonView"
          >
            {{ showJsonView ? '结构化视图' : 'JSON视图' }}
          </a-button>
        </div>

        <!-- 有产品信息时显示 -->
        <div v-if="product.产品信息 && Object.keys(product.产品信息).length > 0">
          <!-- 结构化显示 -->
          <div v-if="!showJsonView" class="structured-info">
            <a-descriptions :column="2" bordered size="small">
              <a-descriptions-item
                v-for="(value, key) in product.产品信息"
                :key="key"
                :label="key"
                :span="shouldSpanFullWidth(value) ? 2 : 1"
              >
                <span v-if="Array.isArray(value)">
                  <a-tag
                    v-for="(item, index) in value"
                    :key="index"
                    :color="getTagColor(index)"
                  >
                    {{ item }}
                  </a-tag>
                </span>
                <span v-else-if="typeof value === 'object'">
                  <pre class="nested-object">{{ JSON.stringify(value, null, 2) }}</pre>
                </span>
                <span v-else>{{ value }}</span>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- JSON视图 -->
          <div v-else class="json-view">
            <pre class="json-content">{{ formattedProductInfo }}</pre>
          </div>
        </div>

        <!-- 无产品信息时显示 -->
        <div v-else class="empty-product-info">
          <a-empty
            description="暂无详细产品信息"
            :image="false"
          >
            <template #description>
              <span class="empty-description">
                <info-circle-outlined style="margin-right: 8px; color: #999;" />
                暂无详细产品信息，您可以通过编辑功能添加产品详细信息
              </span>
            </template>
            <a-button type="primary" size="small" @click="handleEdit">
              <template #icon>
                <edit-outlined />
              </template>
              编辑产品信息
            </a-button>
          </a-empty>
        </div>
      </div>

      <!-- 产品规格 -->
      <div class="spec-info-section">
        <div class="section-header">
          <h3>
            <setting-outlined />
            产品规格
          </h3>
          <a-button
            v-if="product.产品规格 && Object.keys(product.产品规格).length > 0"
            size="small"
            @click="toggleSpecJsonView"
          >
            {{ showSpecJsonView ? '结构化视图' : 'JSON视图' }}
          </a-button>
        </div>

        <!-- 有产品规格时显示 -->
        <div v-if="hasProductSpecs">
          <!-- 数组格式规格显示 -->
          <div v-if="Array.isArray(product.产品规格) || (product.产品规格?.规格列表 && Array.isArray(product.产品规格.规格列表))" class="spec-tags-container">
            <div class="spec-tags-header">
              <h4>产品规格</h4>
              <div class="spec-actions">
                <a-tooltip title="复制所有规格">
                  <a-button
                    type="text"
                    size="small"
                    @click="copyAllSpecs"
                    class="copy-btn"
                  >
                    <copy-outlined />
                  </a-button>
                </a-tooltip>
              </div>
            </div>
            <div class="spec-tags-content">
              <a-tag
                v-for="(spec, index) in (Array.isArray(product.产品规格) ? product.产品规格 : product.产品规格.规格列表)"
                :key="index"
                color="blue"
                class="spec-tag"
              >
                {{ spec }}
              </a-tag>
            </div>
          </div>

          <!-- 兼容旧的对象格式规格显示 -->
          <div v-else-if="!showSpecJsonView" class="spec-cards-container">
            <!-- 规格卡片网格 -->
            <div class="spec-grid">
              <div
                v-for="(value, key) in product.产品规格"
                :key="key"
                class="spec-card"
                :class="getSpecCardClass(key, value)"
              >
                <div class="spec-card-header">
                  <div class="spec-icon">
                    <component :is="getSpecIcon(key)" />
                  </div>
                  <div class="spec-label-container">
                    <div class="spec-label-main">{{ getSpecDisplayName(key) }}</div>
                    <div v-if="getSpecCategory(key)" class="spec-label-category">
                      {{ getSpecCategory(key) }}
                    </div>
                  </div>
                  <div class="spec-actions">
                    <a-tooltip title="复制规格值">
                      <a-button
                        type="text"
                        size="small"
                        @click="copySpecValue(key, value)"
                        class="copy-btn"
                      >
                        <copy-outlined />
                      </a-button>
                    </a-tooltip>
                  </div>
                </div>

                <div class="spec-content">
                  <!-- 数组类型 -->
                  <div v-if="Array.isArray(value)" class="spec-array">
                    <a-tag
                      v-for="(item, index) in value"
                      :key="index"
                      :color="getSpecTagColor(key, index)"
                      class="spec-tag"
                    >
                      {{ item }}
                    </a-tag>
                  </div>

                  <!-- 对象类型 -->
                  <div v-else-if="typeof value === 'object'" class="spec-object">
                    <div class="nested-specs">
                      <div
                        v-for="(subValue, subKey) in value"
                        :key="subKey"
                        class="nested-spec-item"
                      >
                        <span class="nested-spec-key">{{ subKey }}:</span>
                        <span class="nested-spec-value">{{ subValue }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 基础类型 -->
                  <div v-else class="spec-value">
                    <span class="value-text" :class="getValueClass(key, value)">
                      {{ formatSpecValue(key, value) }}
                    </span>
                    <span v-if="getSpecUnit(key)" class="value-unit">
                      {{ getSpecUnit(key) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- JSON视图 -->
          <div v-else class="json-view">
            <pre class="json-content">{{ formattedProductSpec }}</pre>
          </div>
        </div>

        <!-- 无产品规格时显示 -->
        <div v-else class="empty-product-spec">
          <a-empty
            description="暂无产品规格信息"
            :image="false"
          >
            <template #description>
              <span class="empty-description">
                <setting-outlined style="margin-right: 8px; color: #999;" />
                暂无产品规格信息，您可以通过编辑功能添加产品规格
              </span>
            </template>
            <a-button type="primary" size="small" @click="handleEdit">
              <template #icon>
                <edit-outlined />
              </template>
              编辑产品规格
            </a-button>
          </a-empty>
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="history-section">
        <div class="section-header">
          <h3>
            <history-outlined />
            操作历史
          </h3>
        </div>

        <a-timeline size="small">
          <a-timeline-item
            v-for="record in operationHistory"
            :key="record.id"
            :color="getTimelineColor(record.操作类型)"
          >
            <template #dot>
              <component :is="getOperationIcon(record.操作类型)" />
            </template>
            <div class="timeline-content">
              <div class="operation-title">{{ record.操作描述 }}</div>
              <div class="operation-meta">
                <span>操作人：{{ record.操作人 }}</span>
                <span>时间：{{ formatDateTime(record.操作时间) }}</span>
              </div>
              <div v-if="record.备注" class="operation-note">
                备注：{{ record.备注 }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>

    <a-empty v-else description="暂无产品信息" />
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Modal, message } from 'ant-design-vue'
import {
  AppstoreOutlined,
  InfoCircleOutlined,
  HistoryOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  CopyOutlined,
  TagOutlined,
  NumberOutlined,
  FontSizeOutlined,
  BgColorsOutlined,
  ToolOutlined,
  DatabaseOutlined,
  ThunderboltOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  product: {
    type: Object,
    default: () => null
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['edit', 'delete'])

// 是否显示JSON视图
const showJsonView = ref(false)
// 是否显示产品规格JSON视图
const showSpecJsonView = ref(false)

// 模拟操作历史数据
const operationHistory = ref([
  {
    id: 1,
    操作类型: '创建',
    操作描述: '创建产品',
    操作人: '张三',
    操作时间: '2024-01-15 10:30:00',
    备注: '初始创建产品信息'
  },
  {
    id: 2,
    操作类型: '编辑',
    操作描述: '更新产品信息',
    操作人: '李四',
    操作时间: '2024-01-16 15:20:00',
    备注: '修改了产品描述和分类'
  },
  {
    id: 3,
    操作类型: '同步',
    操作描述: '同步产品数据',
    操作人: '系统',
    操作时间: '2024-01-17 09:00:00',
    备注: '自动同步产品状态'
  }
])

/**
 * 格式化产品详细信息为JSON
 */
const formattedProductInfo = computed(() => {
  if (!props.product?.产品信息) return '{}'
  return JSON.stringify(props.product.产品信息, null, 2)
})

/**
 * 格式化产品规格信息为JSON
 */
const formattedProductSpec = computed(() => {
  if (!props.product?.产品规格) return '{}'
  return JSON.stringify(props.product.产品规格, null, 2)
})

/**
 * 检查是否有产品规格
 */
const hasProductSpecs = computed(() => {
  if (!props.product?.产品规格) return false

  if (Array.isArray(props.product.产品规格)) {
    return props.product.产品规格.length > 0
  }

  if (typeof props.product.产品规格 === 'object') {
    // 检查新的字典格式 {规格列表: [...]}
    if (props.product.产品规格.规格列表 && Array.isArray(props.product.产品规格.规格列表)) {
      return props.product.产品规格.规格列表.length > 0
    }
    // 兼容旧的复杂格式
    return Object.keys(props.product.产品规格).length > 0
  }

  return false
})

/**
 * 复制所有规格
 */
const copyAllSpecs = async () => {
  try {
    let specsText = ''

    if (Array.isArray(props.product.产品规格)) {
      specsText = props.product.产品规格.join(', ')
    } else if (props.product.产品规格?.规格列表 && Array.isArray(props.product.产品规格.规格列表)) {
      specsText = props.product.产品规格.规格列表.join(', ')
    }

    if (specsText) {
      await navigator.clipboard.writeText(specsText)
      message.success('规格已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

/**
 * 获取状态对应的颜色
 * @param {number|string} status - 状态值
 * @returns {string} 颜色值
 */
const getStatusColor = (status) => {
  const statusNum = Number(status)
  return statusNum === 1 ? 'green' : 'red'
}

/**
 * 获取状态文本
 * @param {number|string} status - 状态值
 * @returns {string} 状态文本
 */
const getStatusText = (status) => {
  const statusNum = Number(status)
  return statusNum === 1 ? '正常' : '停用'
}

/**
 * 获取时间线颜色
 * @param {string} type - 操作类型
 * @returns {string} 颜色值
 */
const getTimelineColor = (type) => {
  const colorMap = {
    '创建': 'green',
    '编辑': 'blue',
    '同步': 'orange',
    '删除': 'red'
  }
  return colorMap[type] || 'gray'
}

/**
 * 获取操作图标
 * @param {string} type - 操作类型
 * @returns {Component} 图标组件
 */
const getOperationIcon = (type) => {
  const iconMap = {
    '创建': PlusOutlined,
    '编辑': EditOutlined,
    '同步': ReloadOutlined,
    '删除': DeleteOutlined
  }
  return iconMap[type] || ExclamationCircleOutlined
}

/**
 * 获取标签颜色
 * @param {number} index - 索引
 * @returns {string} 颜色值
 */
const getTagColor = (index) => {
  const colors = ['blue', 'green', 'orange', 'red', 'purple', 'cyan']
  return colors[index % colors.length]
}

/**
 * 判断是否应该占满整行
 * @param {any} value - 值
 * @returns {boolean} 是否占满整行
 */
const shouldSpanFullWidth = (value) => {
  if (typeof value === 'string' && value.length > 50) return true
  if (typeof value === 'object') return true
  if (Array.isArray(value) && value.length > 3) return true
  return false
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间
 */
const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return `${date.toLocaleDateString('zh-CN')} ${date.toLocaleTimeString('zh-CN')}`
}

/**
 * 切换JSON视图
 */
const toggleJsonView = () => {
  showJsonView.value = !showJsonView.value
}

/**
 * 切换产品规格JSON视图
 */
const toggleSpecJsonView = () => {
  showSpecJsonView.value = !showSpecJsonView.value
}

/**
 * 获取规格图标
 * @param {string} key - 规格键名
 * @returns {Component} 图标组件
 */
const getSpecIcon = (key) => {
  const keyLower = key.toLowerCase()

  // 尺寸相关
  if (keyLower.includes('尺寸') || keyLower.includes('大小') || keyLower.includes('长') ||
      keyLower.includes('宽') || keyLower.includes('高') || keyLower.includes('size')) {
    return FontSizeOutlined
  }
  // 重量相关
  if (keyLower.includes('重量') || keyLower.includes('重') || keyLower.includes('weight')) {
    return DatabaseOutlined
  }
  // 颜色相关
  if (keyLower.includes('颜色') || keyLower.includes('色') || keyLower.includes('color')) {
    return BgColorsOutlined
  }
  // 材质相关
  if (keyLower.includes('材质') || keyLower.includes('材料') || keyLower.includes('material')) {
    return ToolOutlined
  }
  // 性能相关
  if (keyLower.includes('性能') || keyLower.includes('功率') || keyLower.includes('速度') ||
      keyLower.includes('performance') || keyLower.includes('power')) {
    return ThunderboltOutlined
  }
  // 安全相关
  if (keyLower.includes('安全') || keyLower.includes('认证') || keyLower.includes('标准') ||
      keyLower.includes('safety') || keyLower.includes('standard')) {
    return SafetyOutlined
  }
  // 数量相关
  if (keyLower.includes('数量') || keyLower.includes('个数') || keyLower.includes('count') ||
      keyLower.includes('quantity')) {
    return NumberOutlined
  }

  // 默认图标
  return TagOutlined
}

/**
 * 获取规格显示名称（主要名称）
 * @param {string} key - 规格键名
 * @returns {string} 显示名称
 */
const getSpecDisplayName = (key) => {
  // 规格名称标准化映射 - 更简洁直接的名称
  const nameMap = {
    // 基础属性
    'size': '尺寸',
    'weight': '重量',
    'color': '颜色',
    'material': '材质',
    'brand': '品牌',
    'model': '型号',
    'name': '名称',
    'type': '类型',

    // 技术参数
    'power': '功率',
    'voltage': '电压',
    'current': '电流',
    'frequency': '频率',
    'capacity': '容量',
    'performance': '性能',
    'speed': '速度',
    'temperature': '温度',
    'humidity': '湿度',
    'pressure': '压力',

    // 规格信息
    'dimension': '外形尺寸',
    'length': '长度',
    'width': '宽度',
    'height': '高度',
    'diameter': '直径',
    'thickness': '厚度',
    'volume': '体积',
    'area': '面积',

    // 包装信息
    'package': '包装',
    'quantity': '数量',
    'packing': '包装方式',
    'carton': '外箱',

    // 认证信息
    'certification': '认证',
    'standard': '标准',
    'safety': '安全等级',
    'warranty': '保修',
    'origin': '产地',

    // 功能特性
    'feature': '特性',
    'function': '功能',
    'application': '应用',
    'usage': '用途',
    'advantage': '优势',

    // 中文直接映射
    '尺寸': '尺寸',
    '大小': '尺寸',
    '重量': '重量',
    '重': '重量',
    '颜色': '颜色',
    '色彩': '颜色',
    '材质': '材质',
    '材料': '材质',
    '品牌': '品牌',
    '型号': '型号',
    '规格': '规格',
    '功率': '功率',
    '电压': '电压',
    '容量': '容量',
    '性能': '性能',
    '温度': '温度',
    '湿度': '湿度',
    '包装': '包装',
    '数量': '数量',
    '认证': '认证',
    '标准': '标准',
    '产地': '产地',
    '保修': '保修',
    '质保': '保修',
    '特性': '特性',
    '功能': '功能',
    '用途': '用途',
    '应用': '应用'
  }

  // 精确匹配
  const exactMatch = nameMap[key.toLowerCase()]
  if (exactMatch) return exactMatch

  // 包含匹配
  const keyLower = key.toLowerCase()
  for (const [pattern, name] of Object.entries(nameMap)) {
    if (keyLower.includes(pattern) && pattern.length > 2) {
      return name
    }
  }

  // 智能处理自定义名称
  return processCustomName(key)
}

/**
 * 获取规格分类标签
 * @param {string} key - 规格键名
 * @returns {string} 分类标签
 */
const getSpecCategory = (key) => {
  const keyLower = key.toLowerCase()

  // 基础属性分类
  if (['size', 'weight', 'color', 'material', 'brand', 'model', 'name', 'type'].some(k => keyLower.includes(k)) ||
      ['尺寸', '重量', '颜色', '材质', '品牌', '型号', '名称', '类型'].some(k => keyLower.includes(k))) {
    return '基础属性'
  }

  // 技术参数分类
  if (['power', 'voltage', 'current', 'frequency', 'capacity', 'performance', 'speed', 'temperature', 'humidity', 'pressure'].some(k => keyLower.includes(k)) ||
      ['功率', '电压', '电流', '频率', '容量', '性能', '速度', '温度', '湿度', '压力'].some(k => keyLower.includes(k))) {
    return '技术参数'
  }

  // 规格尺寸分类
  if (['dimension', 'length', 'width', 'height', 'diameter', 'thickness', 'volume', 'area'].some(k => keyLower.includes(k)) ||
      ['外形', '长度', '宽度', '高度', '直径', '厚度', '体积', '面积'].some(k => keyLower.includes(k))) {
    return '规格尺寸'
  }

  // 包装信息分类
  if (['package', 'quantity', 'packing', 'carton'].some(k => keyLower.includes(k)) ||
      ['包装', '数量', '装箱', '外箱'].some(k => keyLower.includes(k))) {
    return '包装信息'
  }

  // 认证信息分类
  if (['certification', 'standard', 'safety', 'warranty', 'origin'].some(k => keyLower.includes(k)) ||
      ['认证', '标准', '安全', '保修', '质保', '产地'].some(k => keyLower.includes(k))) {
    return '认证信息'
  }

  // 功能特性分类
  if (['feature', 'function', 'application', 'usage', 'advantage'].some(k => keyLower.includes(k)) ||
      ['特性', '功能', '应用', '用途', '优势', '特点'].some(k => keyLower.includes(k))) {
    return '功能特性'
  }

  return '' // 不显示分类
}

/**
 * 处理自定义名称
 * @param {string} key - 原始键名
 * @returns {string} 处理后的名称
 */
const processCustomName = (key) => {
  let processed = key.trim()

  // 移除常见的冗余词汇
  processed = processed
    .replace(/^(产品|商品|物品|设备)/, '')
    .replace(/(信息|数据|参数|规格|属性|详情|说明)$/, '')
    .trim()

  // 如果是英文，进行格式化
  if (/^[a-zA-Z_\-\s]+$/.test(processed)) {
    processed = processed
      .replace(/[_-]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
      .trim()
  }

  // 如果处理后为空或太短，返回原始值
  if (!processed || processed.length < 2) {
    return key
  }

  return processed
}

/**
 * 格式化规格值
 * @param {string} key - 规格键名
 * @param {any} value - 规格值
 * @returns {string} 格式化后的值
 */
const formatSpecValue = (key, value) => {
  if (value === null || value === undefined) {
    return '未设置'
  }

  const keyLower = key.toLowerCase()

  if (typeof value === 'number') {
    // 重量相关 - 保留2位小数
    if (keyLower.includes('重量') || keyLower.includes('weight')) {
      return value.toFixed(2)
    }

    // 尺寸相关 - 保留1位小数
    if (keyLower.includes('尺寸') || keyLower.includes('长') || keyLower.includes('宽') ||
        keyLower.includes('高') || keyLower.includes('size') || keyLower.includes('dimension')) {
      return value.toFixed(1)
    }

    // 功率相关 - 整数显示
    if (keyLower.includes('功率') || keyLower.includes('power')) {
      return Math.round(value).toString()
    }

    // 电压相关 - 整数显示
    if (keyLower.includes('电压') || keyLower.includes('voltage')) {
      return Math.round(value).toString()
    }

    // 温度相关 - 整数显示
    if (keyLower.includes('温度') || keyLower.includes('temperature')) {
      return Math.round(value).toString()
    }

    // 容量相关 - 根据大小决定小数位数
    if (keyLower.includes('容量') || keyLower.includes('capacity')) {
      return value >= 1000 ? Math.round(value).toString() : value.toFixed(1)
    }

    // 数量相关 - 整数显示
    if (keyLower.includes('数量') || keyLower.includes('quantity') || keyLower.includes('个数')) {
      return Math.round(value).toString()
    }

    // 百分比相关 - 保留1位小数
    if (keyLower.includes('率') || keyLower.includes('percent') || keyLower.includes('%')) {
      return value.toFixed(1)
    }

    // 默认数值格式化
    if (value >= 1000) {
      return value.toLocaleString()
    } else if (value % 1 === 0) {
      return value.toString()
    } else {
      return value.toFixed(2)
    }
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  if (typeof value === 'string') {
    // 处理特殊字符串值
    const trimmed = value.trim()

    if (trimmed === '' || trimmed === 'null' || trimmed === 'undefined') {
      return '未设置'
    }

    // URL链接处理
    if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
      return '查看链接'
    }

    // 长文本截断
    if (trimmed.length > 50) {
      return trimmed.substring(0, 47) + '...'
    }

    // 颜色值处理
    if (keyLower.includes('颜色') || keyLower.includes('color')) {
      return formatColorValue(trimmed)
    }

    // 材质处理
    if (keyLower.includes('材质') || keyLower.includes('material')) {
      return formatMaterialValue(trimmed)
    }

    return trimmed
  }

  return String(value)
}

/**
 * 格式化颜色值
 * @param {string} color - 颜色值
 * @returns {string} 格式化后的颜色
 */
const formatColorValue = (color) => {
  const colorMap = {
    'red': '红色',
    'blue': '蓝色',
    'green': '绿色',
    'yellow': '黄色',
    'black': '黑色',
    'white': '白色',
    'gray': '灰色',
    'grey': '灰色',
    'pink': '粉色',
    'purple': '紫色',
    'orange': '橙色',
    'brown': '棕色'
  }

  return colorMap[color.toLowerCase()] || color
}

/**
 * 格式化材质值
 * @param {string} material - 材质值
 * @returns {string} 格式化后的材质
 */
const formatMaterialValue = (material) => {
  const materialMap = {
    'plastic': '塑料',
    'metal': '金属',
    'wood': '木材',
    'glass': '玻璃',
    'ceramic': '陶瓷',
    'fabric': '布料',
    'leather': '皮革',
    'rubber': '橡胶',
    'steel': '钢材',
    'aluminum': '铝合金',
    'copper': '铜材',
    'iron': '铁材'
  }

  return materialMap[material.toLowerCase()] || material
}

/**
 * 获取规格单位
 * @param {string} key - 规格键名
 * @returns {string} 单位
 */
const getSpecUnit = (key) => {
  const keyLower = key.toLowerCase()

  // 重量单位
  if (keyLower.includes('重量') || keyLower.includes('weight')) {
    return 'kg'
  }

  // 尺寸单位
  if (keyLower.includes('尺寸') || keyLower.includes('长') || keyLower.includes('宽') ||
      keyLower.includes('高') || keyLower.includes('size') || keyLower.includes('dimension')) {
    return 'cm'
  }

  // 面积单位
  if (keyLower.includes('面积') || keyLower.includes('area')) {
    return 'cm²'
  }

  // 体积单位
  if (keyLower.includes('体积') || keyLower.includes('容积') || keyLower.includes('volume')) {
    return 'L'
  }

  // 功率单位
  if (keyLower.includes('功率') || keyLower.includes('power')) {
    return 'W'
  }

  // 电压单位
  if (keyLower.includes('电压') || keyLower.includes('voltage')) {
    return 'V'
  }

  // 电流单位
  if (keyLower.includes('电流') || keyLower.includes('current')) {
    return 'A'
  }

  // 频率单位
  if (keyLower.includes('频率') || keyLower.includes('frequency')) {
    return 'Hz'
  }

  // 温度单位
  if (keyLower.includes('温度') || keyLower.includes('temperature')) {
    return '°C'
  }

  // 湿度单位
  if (keyLower.includes('湿度') || keyLower.includes('humidity')) {
    return '%RH'
  }

  // 压力单位
  if (keyLower.includes('压力') || keyLower.includes('pressure')) {
    return 'Pa'
  }

  // 速度单位
  if (keyLower.includes('速度') || keyLower.includes('speed')) {
    return 'm/s'
  }

  // 时间单位
  if (keyLower.includes('时间') || keyLower.includes('time') || keyLower.includes('duration')) {
    return 's'
  }

  // 数量单位
  if (keyLower.includes('数量') || keyLower.includes('quantity') || keyLower.includes('个数')) {
    return '个'
  }

  // 百分比单位
  if (keyLower.includes('率') || keyLower.includes('percent') || keyLower.includes('比例')) {
    return '%'
  }

  // 容量单位（存储）
  if (keyLower.includes('存储') || keyLower.includes('内存') || keyLower.includes('storage') || keyLower.includes('memory')) {
    return 'GB'
  }

  // 距离单位
  if (keyLower.includes('距离') || keyLower.includes('distance') || keyLower.includes('范围') || keyLower.includes('range')) {
    return 'm'
  }

  return ''
}

/**
 * 获取规格卡片样式类
 * @param {string} key - 规格键名
 * @param {any} value - 规格值
 * @returns {string} 样式类名
 */
const getSpecCardClass = (key, value) => {
  const classes = []
  const keyLower = key.toLowerCase()

  // 重要性分级
  const highPriorityKeys = [
    '产品名称', '型号', '规格', '尺寸', '重量', '材质', '颜色', '品牌',
    'name', 'model', 'size', 'weight', 'material', 'color', 'brand'
  ]

  const mediumPriorityKeys = [
    '功率', '电压', '容量', '性能', '认证', '标准', '产地',
    'power', 'voltage', 'capacity', 'performance', 'certification', 'origin'
  ]

  // 检查重要性
  const isHighPriority = highPriorityKeys.some(keyword =>
    keyLower.includes(keyword) || keyword.includes(keyLower)
  )

  const isMediumPriority = mediumPriorityKeys.some(keyword =>
    keyLower.includes(keyword) || keyword.includes(keyLower)
  )

  if (isHighPriority || keyLower.includes('重要') || keyLower.includes('主要') || keyLower.includes('核心')) {
    classes.push('spec-card-important')
  } else if (isMediumPriority) {
    classes.push('spec-card-medium')
  }

  // 根据值类型添加样式
  if (Array.isArray(value)) {
    classes.push('spec-card-array')
  } else if (typeof value === 'object' && value !== null) {
    classes.push('spec-card-object')
  } else if (typeof value === 'number') {
    classes.push('spec-card-number')
  } else if (typeof value === 'boolean') {
    classes.push('spec-card-boolean')
  }

  // 根据规格类别添加样式
  if (keyLower.includes('尺寸') || keyLower.includes('size') || keyLower.includes('dimension')) {
    classes.push('spec-card-dimension')
  } else if (keyLower.includes('颜色') || keyLower.includes('color')) {
    classes.push('spec-card-color')
  } else if (keyLower.includes('材质') || keyLower.includes('material')) {
    classes.push('spec-card-material')
  } else if (keyLower.includes('性能') || keyLower.includes('performance') || keyLower.includes('功率')) {
    classes.push('spec-card-performance')
  }

  return classes.join(' ')
}

/**
 * 获取规格标签颜色
 * @param {string} key - 规格键名
 * @param {number} index - 索引
 * @returns {string} 颜色值
 */
const getSpecTagColor = (key, index) => {
  const keyLower = key.toLowerCase()

  // 根据规格类型返回不同颜色
  if (keyLower.includes('颜色') || keyLower.includes('color')) {
    const colors = ['red', 'orange', 'gold', 'green', 'blue', 'purple']
    return colors[index % colors.length]
  }

  if (keyLower.includes('尺寸') || keyLower.includes('size')) {
    return 'geekblue'
  }

  if (keyLower.includes('材质') || keyLower.includes('material')) {
    return 'volcano'
  }

  // 默认颜色循环
  const defaultColors = ['blue', 'green', 'orange', 'red', 'purple', 'cyan']
  return defaultColors[index % defaultColors.length]
}

/**
 * 获取值的样式类
 * @param {string} key - 规格键名
 * @param {any} value - 规格值
 * @returns {string} 样式类名
 */
const getValueClass = (key, value) => {
  const classes = []

  if (typeof value === 'number') {
    classes.push('value-number')
  }

  const keyLower = key.toLowerCase()
  if (keyLower.includes('重要') || keyLower.includes('主要')) {
    classes.push('value-important')
  }

  return classes.join(' ')
}

/**
 * 复制规格值
 * @param {string} key - 规格键名
 * @param {any} value - 规格值
 */
const copySpecValue = async (key, value) => {
  try {
    let textToCopy = ''

    if (Array.isArray(value)) {
      textToCopy = value.join(', ')
    } else if (typeof value === 'object') {
      textToCopy = JSON.stringify(value, null, 2)
    } else {
      textToCopy = String(value)
    }

    await navigator.clipboard.writeText(textToCopy)
    message.success(`已复制 "${key}" 的规格值`)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

/**
 * 处理编辑操作
 */
const handleEdit = () => {
  emit('edit', props.product)
}

/**
 * 处理删除操作
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `您确定要删除产品"${props.product.产品名称}"吗？删除后无法恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      emit('delete', props.product)
    }
  })
}

// 组件名称定义
defineOptions({
  name: 'ProductDetailView'
})
</script>

<style scoped>
/* 产品详情整体样式 */
.product-detail-view {
  padding: 0;
}

.product-content {
  padding: 0;
}

/* 区块样式 */
.basic-info-section,
.detail-info-section,
.spec-info-section,
.history-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.history-section {
  border-bottom: none;
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1d;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 结构化信息显示 */
.structured-info {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.nested-object {
  margin: 0;
  padding: 8px;
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* JSON视图样式 */
.json-view {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.json-content {
  margin: 0;
  padding: 16px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

/* 时间线样式 */
.timeline-content {
  padding-left: 8px;
}

.operation-title {
  font-weight: 500;
  color: #1d1d1d;
  margin-bottom: 4px;
}

.operation-meta {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.operation-meta span {
  margin-right: 16px;
}

.operation-note {
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}

/* 描述列表样式优化 */
.basic-info-section :deep(.ant-descriptions-item-label),
.structured-info :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #333;
  min-width: 100px;
}

.basic-info-section :deep(.ant-descriptions-item-content),
.structured-info :deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  margin: 2px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 图标样式 */
.section-header .anticon {
  color: #1890ff;
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 20px;
}

/* 产品规格卡片样式 */
.spec-cards-container {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.spec-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.spec-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.spec-card-important {
  border-left: 4px solid #ff4d4f;
  background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
  position: relative;
}

.spec-card-important::before {
  content: '重要';
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.spec-card-medium {
  border-left: 4px solid #faad14;
  background: linear-gradient(135deg, #fff 0%, #fffbe6 100%);
}

.spec-card-array {
  border-left: 4px solid #52c41a;
}

.spec-card-object {
  border-left: 4px solid #722ed1;
}

.spec-card-number {
  border-left: 4px solid #1890ff;
}

.spec-card-boolean {
  border-left: 4px solid #13c2c2;
}

.spec-card-dimension {
  border-left: 4px solid #2f54eb;
}

.spec-card-color {
  border-left: 4px solid #eb2f96;
}

.spec-card-material {
  border-left: 4px solid #fa8c16;
}

.spec-card-performance {
  border-left: 4px solid #f5222d;
}

.spec-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.spec-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #f0f0f0;
  color: #666;
  font-size: 14px;
}

.spec-card-important .spec-icon {
  background: #ffebee;
  color: #ff4d4f;
}

.spec-card-medium .spec-icon {
  background: #fffbe6;
  color: #faad14;
}

.spec-card-array .spec-icon {
  background: #f6ffed;
  color: #52c41a;
}

.spec-card-object .spec-icon {
  background: #f9f0ff;
  color: #722ed1;
}

.spec-card-number .spec-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.spec-card-boolean .spec-icon {
  background: #e6fffb;
  color: #13c2c2;
}

.spec-card-dimension .spec-icon {
  background: #f0f5ff;
  color: #2f54eb;
}

.spec-card-color .spec-icon {
  background: #fff0f6;
  color: #eb2f96;
}

.spec-card-material .spec-icon {
  background: #fff7e6;
  color: #fa8c16;
}

.spec-card-performance .spec-icon {
  background: #fff1f0;
  color: #f5222d;
}

.spec-label-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.spec-label-main {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  line-height: 1.2;
}

.spec-label-category {
  font-size: 11px;
  color: #999;
  font-weight: 400;
  line-height: 1;
  opacity: 0.8;
}

.spec-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.spec-card:hover .spec-actions {
  opacity: 1;
}

.copy-btn {
  padding: 4px;
  border-radius: 4px;
}

.copy-btn:hover {
  background: #f0f0f0;
}

.spec-content {
  min-height: 32px;
  display: flex;
  align-items: center;
}

.spec-array {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.spec-tag {
  margin: 0;
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.spec-object {
  width: 100%;
}

.nested-specs {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
}

.nested-spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  font-size: 12px;
}

.nested-spec-item:not(:last-child) {
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 4px;
  padding-bottom: 4px;
}

.nested-spec-key {
  color: #666;
  font-weight: 500;
}

.nested-spec-value {
  color: #333;
}

.spec-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.value-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1890ff;
}

.value-important {
  color: #ff4d4f;
  font-weight: 600;
}

.value-unit {
  font-size: 12px;
  color: #999;
  font-weight: normal;
}

/* 空产品信息样式 */
.empty-product-info,
.empty-product-spec {
  background: #fafafa;
  border-radius: 6px;
  padding: 24px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}

.empty-product-info .ant-empty,
.empty-product-spec .ant-empty {
  padding: 20px;
}

.empty-description {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .operation-meta span {
    display: block;
    margin-right: 0;
    margin-bottom: 2px;
  }

  .structured-info,
  .json-view {
    padding: 12px;
  }

  .json-content {
    padding: 12px;
    font-size: 12px;
  }

  /* 规格卡片响应式 */
  .spec-cards-container {
    padding: 16px;
  }

  .spec-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .spec-card {
    padding: 12px;
  }

  .spec-card-header {
    margin-bottom: 8px;
  }

  .spec-label-main {
    font-size: 13px;
  }

  .spec-label-category {
    font-size: 10px;
  }

  .value-text {
    font-size: 14px;
  }

  .nested-specs {
    padding: 6px;
  }

  .nested-spec-item {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .spec-grid {
    gap: 8px;
  }

  .spec-card {
    padding: 10px;
  }

  .spec-array {
    gap: 4px;
  }

  .spec-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
}

/* 新的数组格式规格样式 */
.spec-tags-container {
  margin-bottom: 24px;
}

.spec-tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.spec-tags-header h4 {
  margin: 0;
  color: #1d1d1d;
  font-size: 16px;
  font-weight: 600;
}

.spec-tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.spec-tags-content .spec-tag {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 6px;
  margin: 0;
  font-weight: 500;
  transition: all 0.2s;
}

.spec-tags-content .spec-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 滚动条样式 */
.json-content::-webkit-scrollbar,
.nested-object::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.json-content::-webkit-scrollbar-track,
.nested-object::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb,
.nested-object::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb:hover,
.nested-object::-webkit-scrollbar-thumb:hover {
  background: #999;
}
</style> 