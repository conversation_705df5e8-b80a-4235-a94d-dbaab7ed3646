<template>
  <div class="product-info-editor">
    <!-- 编辑模式切换 -->
    <div class="editor-header">
      <div class="mode-switcher">
        <a-radio-group v-model:value="editMode" button-style="solid" size="small">
          <a-radio-button value="visual">可视化编辑</a-radio-button>
          <a-radio-button value="code">代码编辑</a-radio-button>
        </a-radio-group>
      </div>
      <div class="editor-actions">
        <a-tooltip title="重置为默认结构">
          <a-button size="small" @click="resetToDefault">
            <template #icon>
              <reload-outlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="验证JSON格式">
          <a-button size="small" @click="validateJson">
            <template #icon>
              <check-outlined />
            </template>
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- 可视化编辑模式 -->
    <div v-if="editMode === 'visual'" class="visual-editor">
      <a-form layout="vertical" :model="visualData">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品型号">
              <a-input v-model:value="visualData.型号" placeholder="请输入产品型号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品品牌">
              <a-input v-model:value="visualData.品牌" placeholder="请输入产品品牌" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品重量">
              <a-input-number
                v-model:value="visualData.重量"
                placeholder="重量"
                :min="0"
                :precision="2"
                style="width: 100%"
                addon-after="kg"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品尺寸">
              <a-input v-model:value="visualData.尺寸" placeholder="长×宽×高 (cm)" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="材质">
              <a-input v-model:value="visualData.材质" placeholder="请输入产品材质" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="颜色">
              <a-select
                v-model:value="visualData.颜色"
                mode="tags"
                placeholder="选择或输入颜色"
                style="width: 100%"
              >
                <a-select-option value="黑色">黑色</a-select-option>
                <a-select-option value="白色">白色</a-select-option>
                <a-select-option value="红色">红色</a-select-option>
                <a-select-option value="蓝色">蓝色</a-select-option>
                <a-select-option value="绿色">绿色</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="产品特性">
          <a-select
            v-model:value="visualData.特性"
            mode="tags"
            placeholder="添加产品特性标签"
            style="width: 100%"
          >
            <a-select-option value="防水">防水</a-select-option>
            <a-select-option value="防尘">防尘</a-select-option>
            <a-select-option value="耐磨">耐磨</a-select-option>
            <a-select-option value="环保">环保</a-select-option>
            <a-select-option value="轻便">轻便</a-select-option>
            <a-select-option value="耐用">耐用</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="使用场景">
          <a-checkbox-group v-model:value="visualData.使用场景">
            <a-row>
              <a-col :span="8"><a-checkbox value="室内">室内</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="户外">户外</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="办公">办公</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="运动">运动</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="旅行">旅行</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="日常">日常</a-checkbox></a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="产品描述">
          <a-textarea
            v-model:value="visualData.描述"
            :rows="4"
            placeholder="请输入详细的产品描述"
          />
        </a-form-item>

        <!-- 自定义字段 -->
        <div class="custom-fields">
          <div class="custom-fields-header">
            <span>自定义字段</span>
            <a-button size="small" @click="addCustomField">
              <template #icon>
                <plus-outlined />
              </template>
              添加字段
            </a-button>
          </div>
          <div v-for="(field, index) in customFields" :key="index" class="custom-field-item">
            <a-row :gutter="8" align="middle">
              <a-col :span="6">
                <a-input
                  v-model:value="field.key"
                  placeholder="字段名称"
                  @change="updateCustomField"
                />
              </a-col>
              <a-col :span="16">
                <a-input
                  v-model:value="field.value"
                  placeholder="字段值"
                  @change="updateCustomField"
                />
              </a-col>
              <a-col :span="2">
                <a-button size="small" danger @click="removeCustomField(index)">
                  <template #icon>
                    <delete-outlined />
                  </template>
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form>
    </div>

    <!-- 代码编辑模式 -->
    <div v-else class="code-editor">
      <div class="code-editor-toolbar">
        <span class="editor-label">JSON 代码编辑器</span>
        <a-space size="small">
          <a-button size="small" @click="formatJson">
            <template #icon>
              <format-painter-outlined />
            </template>
            格式化
          </a-button>
          <a-button size="small" @click="compressJson">
            <template #icon>
              <compress-outlined />
            </template>
            压缩
          </a-button>
        </a-space>
      </div>
      <a-textarea
        v-model:value="jsonCode"
        :rows="15"
        class="json-textarea"
        placeholder="请输入 JSON 格式的产品信息"
        @change="handleJsonChange"
      />
      <div v-if="jsonError" class="json-error">
        <exclamation-circle-outlined />
        {{ jsonError }}
      </div>
    </div>

    <!-- 预览区域 -->
    <div class="preview-section">
      <a-collapse>
        <a-collapse-panel key="preview" header="预览最终数据">
          <pre class="json-preview">{{ formattedPreview }}</pre>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, defineProps, defineEmits } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  CheckOutlined,
  PlusOutlined,
  DeleteOutlined,
  FormatPainterOutlined,
  CompressOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  value: {
    type: [Object, String],
    default: () => ({})
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:value', 'change'])

// 编辑模式：visual（可视化）或 code（代码）
const editMode = ref('visual')

// JSON 错误信息
const jsonError = ref('')

// 可视化编辑数据
const visualData = reactive({
  型号: '',
  品牌: '',
  重量: null,
  尺寸: '',
  材质: '',
  颜色: [],
  特性: [],
  使用场景: [],
  描述: ''
})

// 自定义字段
const customFields = ref([])

// JSON 代码字符串
const jsonCode = ref('')

/**
 * 格式化预览数据
 */
const formattedPreview = computed(() => {
  try {
    const data = getCurrentData()
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return '数据格式错误'
  }
})

/**
 * 获取当前数据
 * 根据编辑模式返回对应的数据
 */
const getCurrentData = () => {
  if (editMode.value === 'visual') {
    const result = { ...visualData }
    
    // 添加自定义字段
    customFields.value.forEach(field => {
      if (field.key && field.value) {
        result[field.key] = field.value
      }
    })
    
    return result
  } else {
    try {
      return JSON.parse(jsonCode.value || '{}')
    } catch (error) {
      return {}
    }
  }
}

/**
 * 监听 props.value 变化，同步到内部状态
 */
watch(() => props.value, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    // 同步到可视化编辑数据
    Object.assign(visualData, {
      型号: newValue.型号 || '',
      品牌: newValue.品牌 || '',
      重量: newValue.重量 || null,
      尺寸: newValue.尺寸 || '',
      材质: newValue.材质 || '',
      颜色: newValue.颜色 || [],
      特性: newValue.特性 || [],
      使用场景: newValue.使用场景 || [],
      描述: newValue.描述 || ''
    })
    
    // 同步自定义字段
    const knownFields = new Set(['型号', '品牌', '重量', '尺寸', '材质', '颜色', '特性', '使用场景', '描述'])
    customFields.value = Object.keys(newValue)
      .filter(key => !knownFields.has(key))
      .map(key => ({ key, value: newValue[key] }))
    
    // 同步到 JSON 代码
    jsonCode.value = JSON.stringify(newValue, null, 2)
    jsonError.value = ''
  }
}, { immediate: true, deep: true })

/**
 * 监听可视化数据变化，发送更新事件
 */
watch(() => visualData, () => {
  if (editMode.value === 'visual') {
    emitUpdate()
  }
}, { deep: true })

/**
 * 监听自定义字段变化
 */
watch(() => customFields.value, () => {
  if (editMode.value === 'visual') {
    emitUpdate()
  }
}, { deep: true })

/**
 * 发送更新事件
 */
const emitUpdate = () => {
  const data = getCurrentData()
  emit('update:value', data)
  emit('change', data)
}

/**
 * 处理 JSON 代码变化
 */
const handleJsonChange = () => {
  try {
    const data = JSON.parse(jsonCode.value || '{}')
    jsonError.value = ''
    emit('update:value', data)
    emit('change', data)
  } catch (error) {
    jsonError.value = `JSON 格式错误: ${error.message}`
  }
}

/**
 * 添加自定义字段
 */
const addCustomField = () => {
  customFields.value.push({ key: '', value: '' })
}

/**
 * 删除自定义字段
 * @param {number} index - 字段索引
 */
const removeCustomField = (index) => {
  customFields.value.splice(index, 1)
  emitUpdate()
}

/**
 * 更新自定义字段
 */
const updateCustomField = () => {
  emitUpdate()
}

/**
 * 重置为默认结构
 */
const resetToDefault = () => {
  const defaultData = {
    型号: '',
    品牌: '',
    重量: null,
    尺寸: '',
    材质: '',
    颜色: [],
    特性: [],
    使用场景: [],
    描述: ''
  }
  
  Object.assign(visualData, defaultData)
  customFields.value = []
  jsonCode.value = JSON.stringify(defaultData, null, 2)
  jsonError.value = ''
  
  message.success('已重置为默认结构')
  emitUpdate()
}

/**
 * 验证 JSON 格式
 */
const validateJson = () => {
  try {
    const data = getCurrentData()
    if (Object.keys(data).length === 0) {
      message.warning('数据为空')
    } else {
      message.success('JSON 格式正确')
    }
  } catch (error) {
    message.error(`JSON 格式错误: ${error.message}`)
  }
}

/**
 * 格式化 JSON
 */
const formatJson = () => {
  try {
    const data = JSON.parse(jsonCode.value || '{}')
    jsonCode.value = JSON.stringify(data, null, 2)
    jsonError.value = ''
    message.success('JSON 格式化完成')
  } catch (error) {
    message.error(`格式化失败: ${error.message}`)
  }
}

/**
 * 压缩 JSON
 */
const compressJson = () => {
  try {
    const data = JSON.parse(jsonCode.value || '{}')
    jsonCode.value = JSON.stringify(data)
    jsonError.value = ''
    message.success('JSON 压缩完成')
  } catch (error) {
    message.error(`压缩失败: ${error.message}`)
  }
}

// 组件名称定义
defineOptions({
  name: 'ProductInfoEditor'
})
</script>

<style scoped>
/* 编辑器整体样式 */
.product-info-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* 编辑器头部 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.mode-switcher .ant-radio-group {
  font-size: 12px;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

/* 可视化编辑器样式 */
.visual-editor {
  padding: 16px;
}

/* 自定义字段样式 */
.custom-fields {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.custom-fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1d1d1d;
}

.custom-field-item {
  margin-bottom: 8px;
}

/* 代码编辑器样式 */
.code-editor {
  padding: 16px;
}

.code-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.editor-label {
  font-weight: 500;
  color: #1d1d1d;
}

.json-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
}

.json-error {
  margin-top: 8px;
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 预览区域样式 */
.preview-section {
  border-top: 1px solid #d9d9d9;
}

.preview-section .ant-collapse {
  border: none;
  background: transparent;
}

.preview-section :deep(.ant-collapse-item) {
  border-bottom: none;
}

.preview-section :deep(.ant-collapse-header) {
  padding: 12px 16px;
  background: #fafafa;
  font-size: 12px;
  color: #666;
}

.preview-section :deep(.ant-collapse-content-box) {
  padding: 12px 16px;
  background: #f8f9fa;
}

.json-preview {
  margin: 0;
  padding: 12px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 表单样式优化 */
.visual-editor .ant-form-item {
  margin-bottom: 16px;
}

.visual-editor .ant-form-item-label {
  font-weight: 500;
}

/* 输入框样式 */
.visual-editor .ant-input,
.visual-editor .ant-select,
.visual-editor .ant-input-number {
  border-radius: 4px;
}

/* 复选框组样式 */
.visual-editor .ant-checkbox-group .ant-row {
  width: 100%;
}

.visual-editor .ant-checkbox-wrapper {
  margin-bottom: 8px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 4px;
  font-weight: 500;
}

.ant-btn-sm {
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .custom-fields-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .code-editor-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 滚动条样式 */
.json-textarea::-webkit-scrollbar,
.json-preview::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.json-textarea::-webkit-scrollbar-track,
.json-preview::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.json-textarea::-webkit-scrollbar-thumb,
.json-preview::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 3px;
}

.json-textarea::-webkit-scrollbar-thumb:hover,
.json-preview::-webkit-scrollbar-thumb:hover {
  background: #999;
}
</style> 