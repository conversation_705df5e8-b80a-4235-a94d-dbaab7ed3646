<template>
  <div class="douyin-products-module">
    <!-- 抖音商品概览 -->
    <div class="overview-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="stat in productStats" :key="stat.key">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              :title="stat.title"
              :value="stat.value"
              :suffix="stat.suffix"
              :prefix="stat.icon ? h(stat.icon) : null"
              :value-style="{ color: stat.color }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 商品管理主区域 -->
    <a-card class="products-card" title="抖音商品管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showSyncModal = true">
            <sync-outlined />
            同步商品
          </a-button>
          <a-button @click="showImportModal = true">
            <import-outlined />
            批量导入
          </a-button>
          <a-button @click="exportProducts">
            <export-outlined />
            导出商品
          </a-button>
          <a-button @click="refreshProducts">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索筛选区域 -->
      <div class="filter-section">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <a-input-search
              v-model:value="searchText"
              placeholder="搜索商品名称"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="5">
            <a-select
              v-model:value="filterPlatform"
              placeholder="商品平台"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option value="抖音商城">抖音商城</a-select-option>
              <a-select-option value="抖音小店">抖音小店</a-select-option>
              <a-select-option value="抖音直播">抖音直播</a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="5">
            <a-select
              v-model:value="filterStatus"
              placeholder="商品状态"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option value="1">上架</a-select-option>
              <a-select-option value="0">下架</a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="5">
            <a-range-picker
              v-model:value="dateRange"
              :placeholder="['开始日期', '结束日期']"
              style="width: 100%"
              @change="handleFilter"
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="3">
            <a-button type="default" @click="resetFilters" block>
              重置筛选
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 批量操作区域 -->
      <div class="batch-actions" v-if="selectedRowKeys.length > 0">
        <a-alert
          :message="`已选中 ${selectedRowKeys.length} 个商品`"
          type="info"
          show-icon
          class="selection-info"
        >
          <template #action>
            <a-space>
              <a-button size="small" @click="batchUpdateStatus(1)">
                批量上架
              </a-button>
              <a-button size="small" @click="batchUpdateStatus(0)">
                批量下架
              </a-button>
              <a-popconfirm
                title="确定要批量删除选中的商品吗？"
                @confirm="batchDeleteProducts"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button size="small" danger>
                  批量删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-alert>
      </div>

      <!-- 商品列表 -->
      <a-table
        :columns="columns"
        :data-source="filteredProducts"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        @change="handleTableChange"
        row-key="id"
        class="products-table"
      >
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 商品信息列 -->
          <template v-if="column.key === 'productInfo'">
            <div class="product-info-cell">
              <div class="product-main">
                <strong>{{ record.商品名称 || '未知商品' }}</strong>
                <div class="product-id">商品ID: {{ record.商品id }}</div>
              </div>
              <div class="product-platform">
                <a-tag color="orange">{{ record.商品平台 || '抖音' }}</a-tag>
              </div>
            </div>
          </template>
          
          <!-- 价格信息列 -->
          <template v-else-if="column.key === 'priceInfo'">
            <div class="price-info-cell">
              <div class="current-price">
                ¥{{ record.当前价格 || '0.00' }}
              </div>
              <div class="original-price" v-if="record.原价 && record.原价 !== record.当前价格">
                原价: ¥{{ record.原价 }}
              </div>
            </div>
          </template>
          
          <!-- 销量信息列 -->
          <template v-else-if="column.key === 'salesInfo'">
            <div class="sales-info-cell">
              <div class="sales-count">
                销量: {{ record.销量 || 0 }}
              </div>
              <div class="stock-count">
                库存: {{ record.库存 || 0 }}
              </div>
            </div>
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.状态 === 1 ? 'green' : 'red'">
              {{ record.状态 === 1 ? '上架' : '下架' }}
            </a-tag>
          </template>
          
          <!-- 更新时间列 -->
          <template v-else-if="column.key === 'updateTime'">
            {{ formatDate(record.更新时间) }}
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewProduct(record)">
                <eye-outlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="editProduct(record)">
                <edit-outlined />
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  <ellipsis-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="syncSingleProduct(record)">
                      <sync-outlined />
                      同步数据
                    </a-menu-item>
                    <a-menu-item @click="copyProductLink(record)">
                      <copy-outlined />
                      复制链接
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteProduct(record)" danger>
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 同步商品弹窗 -->
    <a-modal
      v-model:open="showSyncModal"
      title="同步抖音商品"
      width="600px"
      @ok="handleSyncProducts"
      @cancel="showSyncModal = false"
      :confirm-loading="syncLoading"
    >
      <div class="sync-form">
        <a-form layout="vertical">
          <a-form-item label="同步方式">
            <a-radio-group v-model:value="syncMethod">
              <a-radio value="auto">自动同步(推荐)</a-radio>
              <a-radio value="manual">手动选择店铺</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="同步范围" v-if="syncMethod === 'manual'">
            <a-select
              v-model:value="selectedStores"
              mode="multiple"
              placeholder="选择要同步的店铺"
              style="width: 100%"
            >
              <a-select-option v-for="store in storeList" :key="store.id" :value="store.id">
                {{ store.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="同步选项">
            <a-checkbox-group v-model:value="syncOptions">
              <a-checkbox value="basic">基础信息</a-checkbox>
              <a-checkbox value="price">价格信息</a-checkbox>
              <a-checkbox value="stock">库存信息</a-checkbox>
              <a-checkbox value="images">商品图片</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
        
        <a-alert
          message="同步说明"
          description="同步过程可能需要几分钟时间，请耐心等待。同步期间请勿关闭页面。"
          type="info"
          show-icon
        />
      </div>
    </a-modal>

    <!-- 批量导入弹窗 -->
    <a-modal
      v-model:open="showImportModal"
      title="批量导入商品"
      width="500px"
      @ok="handleImportProducts"
      @cancel="showImportModal = false"
      :confirm-loading="importLoading"
    >
      <div class="import-form">
        <a-upload-dragger
          v-model:fileList="importFileList"
          name="file"
          :multiple="false"
          accept=".xlsx,.xls,.csv"
          :before-upload="beforeUpload"
          @remove="handleRemoveFile"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持 Excel(.xlsx, .xls) 和 CSV 格式文件
          </p>
        </a-upload-dragger>
        
        <div class="import-tips">
          <a-typography-title :level="5">导入说明:</a-typography-title>
          <ul>
            <li>文件大小不超过10MB</li>
            <li>单次最多导入1000个商品</li>
            <li>必填字段：商品名称、商品ID</li>
            <li><a href="#" @click="downloadTemplate">下载模板文件</a></li>
          </ul>
        </div>
      </div>
    </a-modal>

    <!-- 商品详情抽屉 -->
    <a-drawer
      v-model:open="showDetailDrawer"
      title="商品详情"
      width="800px"
      :footer-style="{ textAlign: 'right' }"
    >
      <DouyinProductDetail
        v-if="selectedProduct"
        :product="selectedProduct"
        @edit="editProduct"
        @sync="syncSingleProduct"
      />
      <template #footer>
        <a-button @click="showDetailDrawer = false">关闭</a-button>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  SyncOutlined,
  ImportOutlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  EllipsisOutlined,
  CopyOutlined,
  DeleteOutlined,
  InboxOutlined,
  ShoppingOutlined,
  RiseOutlined,
  DollarOutlined,
  FireOutlined
} from '@ant-design/icons-vue'

// 导入子组件
import DouyinProductDetail from './DouyinProductDetail.vue'

// 导入API服务
import douyinService from '@/services/douyinService'

// 响应式数据
const loading = ref(false)
const syncLoading = ref(false)
const importLoading = ref(false)
const showSyncModal = ref(false)
const showImportModal = ref(false)
const showDetailDrawer = ref(false)
const selectedProduct = ref(null)
const searchText = ref('')
const filterPlatform = ref(undefined)
const filterStatus = ref(undefined)
const dateRange = ref([])

// 同步相关数据
const syncMethod = ref('auto')
const selectedStores = ref([])
const syncOptions = ref(['basic', 'price', 'stock'])

// 导入相关数据
const importFileList = ref([])

// 表格选择
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    console.log('全选状态:', selected, selectedRows, changeRows)
  }
}

// 商品列表数据
const productList = ref([])

// 店铺列表（用于同步选择）
const storeList = ref([
  { id: 1, name: '格恩利geenli母婴旗舰店' },
  { id: 2, name: '小蓉姑娘农水果蔬菜专卖店' },
  { id: 3, name: '极臣个人护理企业店' }
])

// 商品统计数据
const productStats = computed(() => [
  {
    key: 'total',
    title: '商品总数',
    value: productList.value.length,
    color: '#1890ff',
    icon: ShoppingOutlined
  },
  {
    key: 'onSale',
    title: '在售商品',
    value: productList.value.filter(p => p.状态 === 1).length,
    color: '#52c41a',
    icon: RiseOutlined
  },
  {
    key: 'totalSales',
    title: '总销量',
    value: productList.value.reduce((sum, p) => sum + (p.销量 || 0), 0),
    color: '#fa8c16',
    icon: FireOutlined
  },
  {
    key: 'totalValue',
    title: '商品总价值',
    value: productList.value.reduce((sum, p) => sum + ((p.当前价格 || 0) * (p.库存 || 0)), 0).toFixed(2),
    color: '#722ed1',
    icon: DollarOutlined,
    suffix: '元'
  }
])

// 表格列配置
const columns = [
  {
    title: '商品信息',
    key: 'productInfo',
    width: 250,
    ellipsis: true
  },
  {
    title: '价格信息',
    key: 'priceInfo',
    width: 120,
    align: 'center'
  },
  {
    title: '销量/库存',
    key: 'salesInfo',
    width: 120,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    align: 'center'
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 150,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
})

/**
 * 计算过滤后的商品列表
 */
const filteredProducts = computed(() => {
  let result = [...productList.value]
  
  // 根据搜索文本过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(product => 
      product.商品名称?.toLowerCase().includes(search) ||
      product.商品id?.toString().includes(search)
    )
  }
  
  // 根据平台过滤
  if (filterPlatform.value) {
    result = result.filter(product => product.商品平台 === filterPlatform.value)
  }
  
  // 根据状态过滤
  if (filterStatus.value !== undefined) {
    result = result.filter(product => product.状态 === Number(filterStatus.value))
  }
  
  // 根据日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(product => {
      const updateTime = new Date(product.更新时间)
      return updateTime >= startDate && updateTime <= endDate
    })
  }
  
  // 更新分页总数
  paginationConfig.total = result.length
  
  // 应用分页
  const start = (paginationConfig.current - 1) * paginationConfig.pageSize
  const end = start + paginationConfig.pageSize
  
  return result.slice(start, end)
})

/**
 * 格式化日期显示
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 加载商品列表
 */
const loadProducts = async () => {
  try {
    loading.value = true

    // 调用抖音商品API
    const response = await douyinService.getProductList({
      页码: paginationConfig.current,
      每页条数: paginationConfig.pageSize,
      商品名称: searchText.value,
      平台: filterPlatform.value,
      状态: filterStatus.value
    })

    if (response.status === 100) {
      const data = response.data || {}
      productList.value = data.列表 || data.商品列表 || []
      paginationConfig.total = data.总数 || 0

      console.log('✅ 抖音商品列表加载成功:', {
        商品数量: productList.value.length,
        总数: paginationConfig.total,
        当前页: paginationConfig.current,
        每页数量: paginationConfig.pageSize
      })
    } else {
      console.warn('⚠️ 抖音商品列表加载失败:', response.message)
      message.error(response.message || '加载抖音商品列表失败')
      productList.value = []
      paginationConfig.total = 0
    }

  } catch (error) {
    console.error('❌ 加载抖音商品列表失败:', error)
    message.error('加载抖音商品列表失败，请稍后重试')
    productList.value = []
    paginationConfig.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = (value) => {
  searchText.value = value
  paginationConfig.current = 1
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  paginationConfig.current = 1
}

/**
 * 重置筛选条件
 */
const resetFilters = () => {
  searchText.value = ''
  filterPlatform.value = undefined
  filterStatus.value = undefined
  dateRange.value = []
  paginationConfig.current = 1
}

/**
 * 处理表格变化
 */
const handleTableChange = (pagination) => {
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
}

/**
 * 查看商品详情
 */
const viewProduct = (product) => {
  selectedProduct.value = product
  showDetailDrawer.value = true
}

/**
 * 编辑商品
 */
const editProduct = (product) => {
  // TODO: 实现商品编辑功能
  message.info('编辑功能开发中...')
}

/**
 * 删除商品
 */
const deleteProduct = async (product) => {
  try {
    const response = await douyinService.deleteProduct(product.商品id)

    if (response.status === 100) {
      const index = productList.value.findIndex(p => p.id === product.id)
      if (index !== -1) {
        productList.value.splice(index, 1)
      }
      message.success('删除成功')
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('❌ 删除抖音商品失败:', error)
    message.error('删除失败，请稍后重试')
  }
}

/**
 * 同步单个商品
 */
const syncSingleProduct = async (product) => {
  try {
    message.loading('正在同步商品数据...', 0)

    const response = await douyinService.syncProducts({
      商品列表: [product.商品id],
      同步选项: ['basic', 'price', 'stock']
    })

    message.destroy()

    if (response.status === 100) {
      message.success('商品数据同步成功')
      // 刷新商品列表
      loadProducts()
    } else {
      message.error(response.message || '同步失败')
    }
  } catch (error) {
    message.destroy()
    console.error('❌ 同步商品失败:', error)
    message.error('同步失败，请稍后重试')
  }
}

/**
 * 复制商品链接
 */
const copyProductLink = (product) => {
  const link = `https://haohuo.jinritemai.com/views/product/detail?id=${product.商品id}`
  navigator.clipboard.writeText(link).then(() => {
    message.success('商品链接已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败，请手动复制')
  })
}

/**
 * 批量更新状态
 */
const batchUpdateStatus = async (status) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的商品')
    return
  }

  try {
    const statusText = status === 1 ? '上架' : '下架'

    // 获取选中商品的商品ID
    const selectedProducts = productList.value.filter(p => selectedRowKeys.value.includes(p.id))
    const productIds = selectedProducts.map(p => p.商品id)

    // 调用批量更新API
    const response = await douyinService.batchOperate({
      操作类型: 'updateStatus',
      商品列表: productIds,
      状态: status
    })

    if (response.status === 100) {
      // 更新本地数据
      selectedRowKeys.value.forEach(id => {
        const product = productList.value.find(p => p.id === id)
        if (product) {
          product.状态 = status
        }
      })
      message.success(`批量${statusText}成功`)
      selectedRowKeys.value = []
    } else {
      message.error(response.message || `批量${statusText}失败`)
    }
  } catch (error) {
    console.error('❌ 批量更新状态失败:', error)
    message.error('批量操作失败，请稍后重试')
  }
}

/**
 * 批量删除商品
 */
const batchDeleteProducts = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的商品')
    return
  }

  try {
    // 获取选中商品的商品ID
    const selectedProducts = productList.value.filter(p => selectedRowKeys.value.includes(p.id))
    const productIds = selectedProducts.map(p => p.商品id)

    // 调用批量删除API
    const response = await douyinService.batchOperate({
      操作类型: 'delete',
      商品列表: productIds
    })

    if (response.status === 100) {
      // 从本地数据中移除
      selectedRowKeys.value.forEach(id => {
        const index = productList.value.findIndex(p => p.id === id)
        if (index !== -1) {
          productList.value.splice(index, 1)
        }
      })
      message.success('批量删除成功')
      selectedRowKeys.value = []
    } else {
      message.error(response.message || '批量删除失败')
    }
  } catch (error) {
    console.error('❌ 批量删除失败:', error)
    message.error('批量删除失败，请稍后重试')
  }
}

/**
 * 处理同步商品
 */
const handleSyncProducts = async () => {
  try {
    syncLoading.value = true

    // 调用抖音商品同步API
    const response = await douyinService.syncProducts({
      同步方式: syncMethod.value,
      店铺列表: selectedStores.value,
      同步选项: syncOptions.value
    })

    if (response.status === 100) {
      message.success('商品同步成功')
      showSyncModal.value = false
      loadProducts()
    } else {
      message.error(response.message || '同步失败')
    }

  } catch (error) {
    console.error('❌ 同步商品失败:', error)
    message.error('同步失败，请稍后重试')
  } finally {
    syncLoading.value = false
  }
}

/**
 * 文件上传前检查
 */
const beforeUpload = (file) => {
  const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel' ||
                      file.type === 'text/csv'
  if (!isValidType) {
    message.error('只能上传 Excel 或 CSV 格式的文件!')
    return false
  }
  
  const isValidSize = file.size / 1024 / 1024 < 10
  if (!isValidSize) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  
  return false // 阻止自动上传
}

/**
 * 移除文件
 */
const handleRemoveFile = () => {
  importFileList.value = []
}

/**
 * 处理导入商品
 */
const handleImportProducts = async () => {
  if (importFileList.value.length === 0) {
    message.error('请先选择要导入的文件')
    return
  }

  try {
    importLoading.value = true

    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', importFileList.value[0].originFileObj)

    // 调用导入API
    const response = await douyinService.importProducts(formData)

    if (response.status === 100) {
      message.success('商品导入成功')
      showImportModal.value = false
      importFileList.value = []
      loadProducts()
    } else {
      message.error(response.message || '导入失败')
    }

  } catch (error) {
    console.error('❌ 导入商品失败:', error)
    message.error('导入失败，请稍后重试')
  } finally {
    importLoading.value = false
  }
}

/**
 * 下载模板文件
 */
const downloadTemplate = () => {
  // TODO: 实现模板文件下载
  message.info('模板文件下载功能开发中...')
}

/**
 * 导出商品
 */
const exportProducts = async () => {
  try {
    const response = await douyinService.exportProducts({
      筛选条件: {
        商品名称: searchText.value,
        平台: filterPlatform.value,
        状态: filterStatus.value
      }
    })

    if (response.status === 100) {
      // 处理文件下载
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `抖音商品_${new Date().toISOString().slice(0, 10)}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
    } else {
      message.error(response.message || '导出失败')
    }
  } catch (error) {
    console.error('❌ 导出商品失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

/**
 * 刷新商品列表
 */
const refreshProducts = () => {
  paginationConfig.current = 1
  selectedRowKeys.value = []
  loadProducts()
}

// 组件挂载时加载数据
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
/* 模块整体样式 */
.douyin-products-module {
  padding: 0;
}

/* 概览区域样式 */
.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 产品卡片样式 */
.products-card {
  border-radius: 12px;
  overflow: hidden;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

/* 批量操作样式 */
.batch-actions {
  margin-bottom: 16px;
}

.selection-info {
  border-radius: 6px;
}

/* 表格样式 */
.products-table {
  margin-top: 16px;
}

.products-table :deep(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
}

/* 商品信息单元格 */
.product-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-main {
  font-size: 14px;
}

.product-id {
  font-size: 12px;
  color: #999;
}

.product-platform {
  align-self: flex-start;
}

/* 价格信息单元格 */
.price-info-cell {
  text-align: center;
}

.current-price {
  font-weight: 600;
  color: #f5222d;
  font-size: 14px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

/* 销量信息单元格 */
.sales-info-cell {
  text-align: center;
}

.sales-count {
  color: #52c41a;
  font-size: 12px;
}

.stock-count {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

/* 同步表单样式 */
.sync-form {
  padding: 16px 0;
}

/* 导入表单样式 */
.import-form {
  padding: 16px 0;
}

.import-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 6px;
}

.import-tips ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.import-tips li {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    padding: 12px;
  }
  
  .products-table :deep(.ant-table-thead > tr > th),
  .products-table :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }
  
  .batch-actions {
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .product-info-cell {
    gap: 2px;
  }
  
  .price-info-cell,
  .sales-info-cell {
    font-size: 12px;
  }
}
</style> 