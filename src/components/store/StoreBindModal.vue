<template>
  <a-modal
    :open="visible"
    title="绑定店铺"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="submitting"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="店铺类型" name="店铺类型">
        <a-select
          v-model:value="formData.店铺类型"
          placeholder="请选择店铺类型"
          allow-clear
        >
          <a-select-option value="抖音店铺">抖音店铺</a-select-option>
          <a-select-option value="淘宝店铺">淘宝店铺</a-select-option>
          <a-select-option value="京东店铺">京东店铺</a-select-option>
          <a-select-option value="小红书店铺">小红书店铺</a-select-option>
          <a-select-option value="其他平台">其他平台</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="店铺id/链接" name="店铺标识">
        <a-input
          v-model:value="formData.店铺标识"
          placeholder="请输入店铺id或店铺链接"
          allow-clear
        />
        <div class="form-tip">
          可以输入店铺id（如：18037896）或完整的店铺链接
        </div>
      </a-form-item>

      <a-form-item label="店铺名称" name="店铺名称">
        <a-input
          v-model:value="formData.店铺名称"
          placeholder="请输入店铺名称"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="店铺分类" name="店铺分类">
        <a-select
          v-model:value="formData.店铺分类"
          placeholder="请选择店铺分类"
          allow-clear
        >
          <a-select-option value="母婴用品">母婴用品</a-select-option>
          <a-select-option value="食品饮料">食品饮料</a-select-option>
          <a-select-option value="数码科技">数码科技</a-select-option>
          <a-select-option value="服装鞋帽">服装鞋帽</a-select-option>
          <a-select-option value="美妆护肤">美妆护肤</a-select-option>
          <a-select-option value="家居生活">家居生活</a-select-option>
          <a-select-option value="运动户外">运动户外</a-select-option>
          <a-select-option value="汽车用品">汽车用品</a-select-option>
          <a-select-option value="其他">其他</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注信息" name="备注">
        <a-textarea
          v-model:value="formData.备注"
          :rows="3"
          placeholder="请输入备注信息（选填）"
          allow-clear
        />
      </a-form-item>

      <!-- 自动识别信息显示区域 -->
      <div v-if="检测到的店铺信息" class="detected-info">
        <a-divider>自动识别信息</a-divider>
        <a-descriptions :column="1" size="small" bordered>
          <a-descriptions-item label="识别的店铺名称">
            {{ 检测到的店铺信息.店铺名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="识别的分类">
            {{ 检测到的店铺信息.分类 }}
          </a-descriptions-item>
          <a-descriptions-item label="店铺状态">
            <a-tag :color="检测到的店铺信息.状态 === '正常' ? 'green' : 'red'">
              {{ 检测到的店铺信息.状态 }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        <div class="auto-fill-actions">
          <a-button size="small" @click="应用自动识别信息">
            应用识别信息
          </a-button>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 检测到的店铺信息
const 检测到的店铺信息 = ref(null)

// 表单数据
const formData = reactive({
  店铺类型: '',
  店铺标识: '',
  店铺名称: '',
  店铺分类: '',
  备注: ''
})

// 表单验证规则
const formRules = {
  店铺类型: [
    { required: true, message: '请选择店铺类型' }
  ],
  店铺标识: [
    { required: true, message: '请输入店铺id或链接' },
    { min: 3, message: '店铺标识至少3个字符' }
  ],
  店铺名称: [
    { required: true, message: '请输入店铺名称' },
    { min: 2, message: '店铺名称至少2个字符' }
  ],
  店铺分类: [
    { required: true, message: '请选择店铺分类' }
  ]
}

/**
 * 监听店铺标识变化，自动识别店铺信息
 */
watch(() => formData.店铺标识, (newValue) => {
  if (newValue && newValue.length > 5) {
    识别店铺信息(newValue)
  } else {
    检测到的店铺信息.value = null
  }
}, { debounce: 500 })

/**
 * 识别店铺信息
 * 根据输入的店铺id或链接自动识别店铺信息
 * @param {string} 店铺标识 - 店铺id或链接
 */
const 识别店铺信息 = async (店铺标识) => {
  try {
    console.log('开始识别店铺信息:', 店铺标识)
    
    // 这里应该调用后端API进行店铺信息识别
    // const response = await storeAPI.detectStoreInfo(店铺标识)
    
    // 模拟店铺信息识别结果
    if (店铺标识.includes('18037896') || 店铺标识.includes('格恩利')) {
      检测到的店铺信息.value = {
        店铺名称: '格恩利geenli母婴旗舰店',
        分类: '母婴用品',
        状态: '正常',
        平台: '抖音'
      }
    } else if (店铺标识.includes('63176420') || 店铺标识.includes('小蓉')) {
      检测到的店铺信息.value = {
        店铺名称: '小蓉姑娘农水果蔬菜专卖店',
        分类: '食品饮料',
        状态: '正常',
        平台: '抖音'
      }
    } else {
      // 模拟无法识别的情况
      检测到的店铺信息.value = null
      message.warning('无法自动识别店铺信息，请手动填写')
    }
    
  } catch (error) {
    console.error('识别店铺信息失败:', error)
    检测到的店铺信息.value = null
    message.error('识别店铺信息失败')
  }
}

/**
 * 应用自动识别的信息到表单
 */
const 应用自动识别信息 = () => {
  if (!检测到的店铺信息.value) return
  
  const info = 检测到的店铺信息.value
  
  // 如果表单字段为空，则自动填充
  if (!formData.店铺名称) {
    formData.店铺名称 = info.店铺名称
  }
  if (!formData.店铺分类) {
    formData.店铺分类 = info.分类
  }
  if (!formData.店铺类型) {
    formData.店铺类型 = info.平台 + '店铺'
  }
  
  message.success('已应用识别信息')
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value.validate()
    
    submitting.value = true
    
    console.log('提交店铺绑定数据:', formData)
    
    // 这里调用实际的API接口进行店铺绑定
    // const response = await storeAPI.bindStore(formData)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('店铺绑定成功')
    
    // 触发成功事件，通知父组件刷新数据
    emit('success', {
      店铺id: Date.now(), // 模拟返回的店铺id
      ...formData,
      绑定时间: new Date().toISOString(),
      店铺状态: '正常'
    })
    
    // 关闭弹窗
    handleCancel()
    
  } catch (error) {
    console.error('店铺绑定失败:', error)
    message.error('店铺绑定失败，请检查输入信息')
  } finally {
    submitting.value = false
  }
}

/**
 * 处理取消操作
 */
const handleCancel = () => {
  // 重置表单
  formRef.value?.resetFields()
  检测到的店铺信息.value = null
  
  // 重置表单数据
  Object.assign(formData, {
    店铺类型: '',
    店铺标识: '',
    店铺名称: '',
    店铺分类: '',
    备注: ''
  })
  
  // 关闭弹窗
  emit('update:visible', false)
}

// 组件名称定义
defineOptions({
  name: 'StoreBindModal'
})
</script>

<style scoped>
/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

/* 检测信息区域样式 */
.detected-info {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.detected-info .ant-divider {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1d;
}

/* 自动填充操作按钮 */
.auto-fill-actions {
  margin-top: 12px;
  text-align: right;
}

.auto-fill-actions .ant-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

/* 描述列表样式优化 */
.detected-info .ant-descriptions {
  background: #fff;
  border-radius: 4px;
}

.detected-info :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #333;
  width: 100px;
}

.detected-info :deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 表单项样式优化 */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label {
  font-weight: 500;
}

/* 输入框样式优化 */
.ant-input,
.ant-select,
.ant-input-number {
  border-radius: 6px;
}

.ant-input:focus,
.ant-select:focus,
.ant-input-number:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detected-info {
    padding: 12px;
  }
  
  .detected-info :deep(.ant-descriptions-item-label) {
    width: 80px;
    font-size: 12px;
  }
  
  .detected-info :deep(.ant-descriptions-item-content) {
    font-size: 12px;
  }
}
</style> 