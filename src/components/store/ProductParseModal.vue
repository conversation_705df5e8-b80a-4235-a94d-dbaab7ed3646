<template>
  <a-modal
    v-model:open="visible"
    title="智能产品解析"
    width="900px"
    :confirm-loading="parsing"
    @ok="handleParse"
    @cancel="handleCancel"
    :ok-text="parsing ? '解析中...' : '开始解析'"
    cancel-text="取消"
  >
    <div class="product-parse-modal">
      <!-- 解析说明 -->
      <a-alert
        message="智能产品解析"
        description="请输入产品信息文本，AI将自动解析产品的详细信息，包括名称、分类、规格参数等，并可选择上传到知识库供AI助手学习使用。"
        type="info"
        show-icon
        class="parse-alert"
      />

      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-header">
          <h4>
            <file-text-outlined />
            产品信息输入
          </h4>
          <a-space>
            <a-tooltip title="上传文件">
              <a-button size="small" @click="triggerFileUpload">
                <template #icon>
                  <upload-outlined />
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="清空内容">
              <a-button size="small" @click="clearInput">
                <template #icon>
                  <clear-outlined />
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="粘贴剪贴板内容">
              <a-button size="small" @click="pasteFromClipboard">
                <template #icon>
                  <copy-outlined />
                </template>
              </a-button>
            </a-tooltip>
          </a-space>
        </div>

        <!-- 文件拖放区域 -->
        <div
          class="file-drop-zone"
          :class="{ 'drag-over': isDragOver, 'has-content': productText }"
          @drop="handleFileDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter"
          @dragleave="handleDragLeave"
          @click="triggerFileUpload"
        >
          <div v-if="!productText && !fileProcessing" class="drop-zone-content">
            <div class="drop-icon">
              <cloud-upload-outlined />
            </div>
            <div class="drop-text">
              <p class="primary-text">拖放文件到此处或点击上传</p>
              <p class="secondary-text">
                支持格式：TXT、DOC、DOCX、PDF、XLS、XLSX、CSV、JSON
              </p>
              <p class="hint-text">
                也可以直接在下方文本框中输入产品信息
              </p>
            </div>
          </div>

          <div v-if="fileProcessing" class="processing-content">
            <a-spin size="large">
              <template #indicator>
                <loading-outlined style="font-size: 24px" spin />
              </template>
            </a-spin>
            <p class="processing-text">正在解析文件内容...</p>
          </div>
        </div>

        <!-- 文件信息显示 -->
        <div v-if="uploadedFile" class="file-info">
          <a-alert
            :message="`已上传文件：${uploadedFile.name}`"
            :description="`文件大小：${formatFileSize(uploadedFile.size)} | 文件类型：${uploadedFile.type || '未知'}`"
            type="success"
            show-icon
            closable
            @close="clearUploadedFile"
          />
        </div>

        <a-textarea
          v-model:value="productText"
          placeholder="请输入产品信息文本，例如：
产品名称：蓝牙耳机Pro
品牌：TechPro
型号：TP-BT001
颜色：黑色、白色、蓝色
功能：降噪、防水、长续航
价格：299元
适用场景：运动、通勤、办公

支持多种格式：纯文本、表格数据、产品描述等
或者拖放文件到上方区域自动解析文件内容"
          :rows="12"
          :maxlength="10000"
          show-count
          class="product-input"
        />

        <!-- 隐藏的文件输入 -->
        <input
          ref="fileInput"
          type="file"
          accept=".txt,.doc,.docx,.pdf,.xls,.xlsx,.csv,.json"
          style="display: none"
          @change="handleFileSelect"
        />
      </div>

      <!-- 解析选项 -->
      <div class="options-section">
        <div class="section-header">
          <h4>
            <setting-outlined />
            解析选项
          </h4>
        </div>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="上传到知识库">
              <a-switch
                v-model:checked="uploadToKnowledge"
                checked-children="是"
                un-checked-children="否"
              />
              <div class="option-desc">解析后自动上传到知识库供AI学习</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="知识库套餐" v-if="uploadToKnowledge">
              <a-select
                v-model:value="selectedPackage"
                placeholder="选择知识库套餐"
                :loading="loadingPackages"
                @focus="loadKnowledgePackages"
              >
                <a-select-option
                  v-for="pkg in knowledgePackages"
                  :key="pkg.id"
                  :value="pkg.id"
                >
                  {{ pkg.name }}
                </a-select-option>
              </a-select>
              <div class="option-desc">选择要上传的知识库套餐</div>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 解析结果 -->
      <div v-if="parseResult" class="result-section">
        <div class="section-header">
          <h4>
            <check-circle-outlined />
            解析结果
          </h4>
          <a-space>
            <a-button size="small" @click="copyResult">
              <template #icon>
                <copy-outlined />
              </template>
              复制结果
            </a-button>
            <a-button size="small" type="primary" @click="saveProduct">
              <template #icon>
                <save-outlined />
              </template>
              保存产品
            </a-button>
          </a-space>
        </div>

        <!-- 基础信息 -->
        <div class="basic-info">
          <h5>基础信息</h5>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="产品名称">
              {{ parseResult.产品名称 || '未识别' }}
            </a-descriptions-item>
            <a-descriptions-item label="产品分类">
              {{ parseResult.产品分类 || '未分类' }}
            </a-descriptions-item>
            <a-descriptions-item label="产品描述" :span="2">
              {{ parseResult.产品描述 || '暂无描述' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 详细信息 -->
        <div class="detail-info">
          <h5>详细信息</h5>
          <div class="info-display">
            <a-tabs size="small">
              <a-tab-pane key="structured" tab="结构化显示">
                <a-descriptions :column="2" bordered size="small">
                  <a-descriptions-item
                    v-for="(value, key) in parseResult.产品信息"
                    :key="key"
                    :label="key"
                    :span="shouldSpanFullWidth(value) ? 2 : 1"
                  >
                    <span v-if="Array.isArray(value)">
                      <a-tag
                        v-for="(item, index) in value"
                        :key="index"
                        :color="getTagColor(index)"
                      >
                        {{ item }}
                      </a-tag>
                    </span>
                    <span v-else-if="typeof value === 'object'">
                      <pre class="nested-object">{{ JSON.stringify(value, null, 2) }}</pre>
                    </span>
                    <span v-else>{{ value }}</span>
                  </a-descriptions-item>
                </a-descriptions>
              </a-tab-pane>
              <a-tab-pane key="json" tab="JSON格式">
                <pre class="json-display">{{ JSON.stringify(parseResult.产品信息, null, 2) }}</pre>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>

        <!-- 缺失信息提示 -->
        <div v-if="parseResult.缺失信息 && parseResult.缺失信息.length > 0" class="missing-info">
          <h5>
            <exclamation-circle-outlined />
            缺失信息提示
          </h5>
          <a-alert
            message="以下信息未能从输入文本中识别，建议手动补充："
            type="warning"
            show-icon
          >
            <template #description>
              <ul class="missing-list">
                <li v-for="(item, index) in parseResult.缺失信息" :key="index">
                  {{ item }}
                </li>
              </ul>
            </template>
          </a-alert>
        </div>
      </div>

      <!-- 解析进度 -->
      <div v-if="parsing" class="parsing-progress">
        <a-spin size="large">
          <template #indicator>
            <loading-outlined style="font-size: 24px" spin />
          </template>
        </a-spin>
        <div class="progress-text">
          <p>AI正在解析产品信息...</p>
          <p class="progress-desc">这可能需要几秒钟时间，请耐心等待</p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  FileTextOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClearOutlined,
  CopyOutlined,
  SaveOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import productService from '@/services/productService'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'parsed', 'saved'])

// 响应式数据
const productText = ref('')
const parsing = ref(false)
const parseResult = ref(null)
const uploadToKnowledge = ref(false)
const selectedPackage = ref(null)
const loadingPackages = ref(false)
const knowledgePackages = ref([])

/**
 * 清空输入
 */
const clearInput = () => {
  productText.value = ''
}

/**
 * 从剪贴板粘贴
 */
const pasteFromClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText()
    productText.value = text
    message.success('已粘贴剪贴板内容')
  } catch (error) {
    message.error('无法访问剪贴板，请手动粘贴')
  }
}

/**
 * 加载知识库套餐列表
 */
const loadKnowledgePackages = async () => {
  if (knowledgePackages.value.length > 0) return

  try {
    loadingPackages.value = true
    const response = await productService.getKnowledgePackages()

    if (response.status === 100) {
      knowledgePackages.value = response.data || []
    } else {
      message.error(response.message || '加载知识库套餐失败')
    }
  } catch (error) {
    console.error('加载知识库套餐失败:', error)
    message.error('加载知识库套餐失败')
  } finally {
    loadingPackages.value = false
  }
}

/**
 * 开始解析
 */
const handleParse = async () => {
  if (!productText.value.trim()) {
    message.warning('请输入产品信息文本')
    return
  }

  try {
    parsing.value = true
    parseResult.value = null

    // 调用产品解析API
    const response = await productService.parseProduct({
      productText: productText.value
    })

    if (response.status === 100) {
      parseResult.value = response.data
      message.success('产品信息解析成功')

      // 如果选择上传到知识库
      if (uploadToKnowledge.value && selectedPackage.value && response.data.产品id) {
        await uploadToKnowledgeBase(response.data.产品id)
      }

      emit('parsed', response.data)
    } else {
      message.error(response.message || '解析失败')
    }
  } catch (error) {
    console.error('产品解析失败:', error)
    message.error('解析失败，请稍后重试')
  } finally {
    parsing.value = false
  }
}

/**
 * 上传到知识库
 */
const uploadToKnowledgeBase = async (productId) => {
  try {
    const response = await productService.updateKnowledge({
      产品id: productId,
      套餐关联id: selectedPackage.value
    })

    if (response.status === 100) {
      message.success('已上传到知识库')
    } else {
      message.warning(response.message || '上传知识库失败')
    }
  } catch (error) {
    console.error('上传知识库失败:', error)
    message.error('上传知识库失败')
  }
}

/**
 * 复制解析结果
 */
const copyResult = async () => {
  try {
    const resultText = JSON.stringify(parseResult.value, null, 2)
    await navigator.clipboard.writeText(resultText)
    message.success('解析结果已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

/**
 * 保存产品
 */
const saveProduct = () => {
  if (parseResult.value) {
    emit('saved', parseResult.value)
    handleCancel()
  }
}

/**
 * 取消
 */
const handleCancel = () => {
  emit('update:visible', false)
  // 重置数据
  productText.value = ''
  parseResult.value = null
  uploadToKnowledge.value = false
  selectedPackage.value = null
}

/**
 * 判断是否应该跨列显示
 */
const shouldSpanFullWidth = (value) => {
  if (typeof value === 'string' && value.length > 20) return true
  if (Array.isArray(value) && value.length > 3) return true
  if (typeof value === 'object') return true
  return false
}

/**
 * 获取标签颜色
 */
const getTagColor = (index) => {
  const colors = ['blue', 'green', 'orange', 'red', 'purple', 'cyan']
  return colors[index % colors.length]
}
</script>

<style scoped>
.product-parse-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.parse-alert {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4,
.section-header h5 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
}

.input-section {
  margin-bottom: 24px;
}

.product-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.options-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.option-desc {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.result-section {
  margin-bottom: 24px;
}

.basic-info,
.detail-info,
.missing-info {
  margin-bottom: 20px;
}

.basic-info h5,
.detail-info h5,
.missing-info h5 {
  margin-bottom: 12px;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-display {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.json-display {
  background: #f5f5f5;
  padding: 12px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
}

.nested-object {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin: 0;
}

.missing-list {
  margin: 0;
  padding-left: 20px;
}

.missing-list li {
  margin-bottom: 4px;
}

.parsing-progress {
  text-align: center;
  padding: 40px 20px;
}

.progress-text {
  margin-top: 16px;
}

.progress-text p {
  margin: 4px 0;
}

.progress-desc {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-parse-modal {
    max-height: 80vh;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
