<template>
  <div class="team-settings-tab">
    <a-result
      status="info"
      title="团队设置"
      sub-title="团队设置功能正在开发中，敬请期待。"
    >
      <template #extra>
        <a-button type="primary" @click="$emit('refresh')">
          返回
        </a-button>
      </template>
    </a-result>
  </div>
</template>

<script setup>
defineOptions({
  name: 'TeamSettingsTab'
})

const props = defineProps({
  team: {
    type: Object,
    required: true
  }
})

defineEmits(['refresh', 'team-updated'])
</script>

<style scoped>
.team-settings-tab {
  padding: 40px 0;
}
</style> 