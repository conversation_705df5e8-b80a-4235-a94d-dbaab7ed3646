<template>
  <a-dropdown placement="bottomRight">
    <template #overlay>
      <a-menu>
        <a-menu-item key="edit-role" @click="$emit('editRole')">
          <UserOutlined />
          设置角色
        </a-menu-item>
        <a-menu-item key="edit-permissions" @click="$emit('editPermissions')">
          <SettingOutlined />
          编辑权限
        </a-menu-item>
        <a-menu-divider v-if="canTransferOwnership" />
        <a-menu-item
          v-if="canTransferOwnership"
          key="transfer-ownership"
          @click="handleTransferOwnership"
        >
          <CrownOutlined />
          转移所有权
        </a-menu-item>
        <a-menu-divider v-if="canRemoveMember" />
        <a-menu-item
          v-if="canRemoveMember"
          key="remove"
          danger
          @click="handleRemoveMember"
        >
          <DeleteOutlined />
          移除成员
        </a-menu-item>
      </a-menu>
    </template>
    <a-button size="small">
      操作 <DownOutlined />
    </a-button>
  </a-dropdown>
</template>

<script setup>
import { computed } from 'vue'
import { Modal } from 'ant-design-vue'
import {
  UserOutlined,
  SettingOutlined,
  CrownOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { ROLE_TYPES, getRoleLevel } from '../../utils/roleUtils'

defineOptions({
  name: 'MemberActionDropdown'
})

const props = defineProps({
  member: {
    type: Object,
    required: true
  },
  currentUser: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits([
  'editRole',
  'editPermissions',
  'transferOwnership',
  'removeMember'
])

// 当前用户的角色级别
const currentUserLevel = computed(() => {
  return getRoleLevel(props.currentUser.角色类型 || ROLE_TYPES.MEMBER)
})

// 目标成员的角色级别
const memberLevel = computed(() => {
  return getRoleLevel(props.member.角色类型 || ROLE_TYPES.MEMBER)
})

// 是否可以转移所有权
const canTransferOwnership = computed(() => {
  // 只有创始人可以转移所有权
  return props.currentUser.角色类型 === ROLE_TYPES.FOUNDER &&
         props.member.角色类型 !== ROLE_TYPES.FOUNDER &&
         props.member.用户id !== props.currentUser.用户id
})

// 是否可以移除成员
const canRemoveMember = computed(() => {
  // 不能移除自己
  if (props.member.用户id === props.currentUser.用户id) {
    return false
  }
  
  // 创始人不能被移除
  if (props.member.角色类型 === ROLE_TYPES.FOUNDER) {
    return false
  }
  
  // 只有角色级别更高的用户才能移除成员
  return currentUserLevel.value > memberLevel.value
})

// 处理转移所有权
const handleTransferOwnership = () => {
  Modal.confirm({
    title: '确认转移所有权？',
    content: `将团队所有权转移给"${props.member.昵称 || props.member.手机号}"后，您将失去团队管理权限。此操作不可撤销。`,
    okText: '确认转移',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      emit('transferOwnership')
    }
  })
}

// 处理移除成员
const handleRemoveMember = () => {
  Modal.confirm({
    title: '确认移除成员？',
    content: `确定要将"${props.member.昵称 || props.member.手机号}"从团队中移除吗？移除后该成员将失去所有团队权限。`,
    okText: '确认移除',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      emit('removeMember')
    }
  })
}
</script>

<style scoped>
.ant-dropdown-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ant-dropdown-menu-item-danger {
  color: #ff4d4f;
}

.ant-dropdown-menu-item-danger:hover {
  background-color: #fff2f0;
}
</style> 