<template>
  <a-modal
    v-model:open="visible"
    :title="`设置成员权限 - ${member?.昵称 || member?.手机号 || '未知成员'}`"
    width="900"
    :confirm-loading="loading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <div class="member-permission-modal">
      <!-- 成员信息展示 -->
      <div v-if="member" class="member-info-section">
        <a-card size="small" class="member-info-card">
          <template #title>
            <UserOutlined />
            成员信息
          </template>
          <div class="member-info">
            <a-avatar
              :size="48"
              :style="{ backgroundColor: getAvatarColor(member.昵称 || member.手机号 || '用户') }"
            >
              {{ getAvatarText(member.昵称 || member.手机号 || '用户') }}
            </a-avatar>
            <div class="member-details">
              <div class="member-name">{{ member.昵称 || '未注册用户' }}</div>
              <div class="member-phone">{{ member.手机号 }}</div>
              <div class="member-role">
                <a-tag :color="getRoleColor(member.角色)">
                  {{ member.角色 || '成员' }}
                </a-tag>
                <a-tag v-if="member.状态" :color="getStatusColor(member.状态)" size="small">
                  {{ member.状态 }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 权限配置区域 -->
      <div class="permission-config-section">
        <a-card size="small" title="权限配置">
          <template #extra>
            <a-space>
              <a-button 
                size="small" 
                @click="loadCurrentPermissions"
                :loading="loadingPermissions"
              >
                <ReloadOutlined />
                重新加载当前权限
              </a-button>
              <a-button 
                size="small" 
                @click="resetPermissions"
              >
                <ClearOutlined />
                重置权限
              </a-button>
            </a-space>
          </template>

          <!-- 权限状态提示 -->
          <div class="permission-status">
            <a-alert
              v-if="selectedPermissions.length === 0"
              message="该成员暂无特殊权限"
              description="成员将只拥有其角色对应的基础权限"
              type="info"
              show-icon
              style="margin-bottom: 16px;"
            />
            <a-alert
              v-else
              :message="`已配置 ${selectedPermissions.length} 项特殊权限`"
              description="这些权限将在角色基础权限之上额外授予"
              type="success"
              show-icon
              style="margin-bottom: 16px;"
            />
          </div>

          <!-- 权限配置组件 -->
          <PermissionConfig
            v-model="selectedPermissions"
            :team="team"
            :show-mode-selector="false"
            :default-mode="'custom'"
            :show-summary="true"
            :show-description="true"
            @permissions-change="handlePermissionsChange"
          />
        </a-card>
      </div>

      <!-- 变更说明 -->
      <div class="change-summary">
        <a-card size="small" title="权限变更说明">
          <div v-if="permissionChanges.added.length > 0" class="change-item">
            <h4 style="color: #52c41a;">
              <PlusCircleOutlined />
              新增权限 ({{ permissionChanges.added.length }}项)
            </h4>
            <a-tag 
              v-for="permission in permissionChanges.added" 
              :key="permission"
              color="green"
              style="margin: 2px;"
            >
              {{ getPermissionName(permission) }}
            </a-tag>
          </div>

          <div v-if="permissionChanges.removed.length > 0" class="change-item">
            <h4 style="color: #ff4d4f;">
              <MinusCircleOutlined />
              移除权限 ({{ permissionChanges.removed.length }}项)
            </h4>
            <a-tag 
              v-for="permission in permissionChanges.removed" 
              :key="permission"
              color="red"
              style="margin: 2px;"
            >
              {{ getPermissionName(permission) }}
            </a-tag>
          </div>

          <div v-if="permissionChanges.added.length === 0 && permissionChanges.removed.length === 0">
            <a-empty 
              description="暂无权限变更" 
              :image="false"
              style="margin: 20px 0;"
            />
          </div>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  UserOutlined, 
  ReloadOutlined, 
  ClearOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue'
import PermissionConfig from './PermissionConfig.vue'
import { teamPermissionService } from '../../services/team/teamPermission'
import { teamMemberService } from '../../services/team/teamMember'
import { getRoleColor } from '../../utils/roleUtils'

defineOptions({
  name: 'MemberPermissionModal'
})

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  member: {
    type: Object,
    default: null
  },
  team: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:open', 'success'])

// 响应式数据
const loading = ref(false)
const loadingPermissions = ref(false)
const selectedPermissions = ref([])
const originalPermissions = ref([])
const allPermissions = ref([])
const submitting = ref(false)

// 计算属性
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const teamId = computed(() => props.team?.团队id || props.team?.id)
const memberId = computed(() => props.member?.用户id || props.member?.id)

// 权限变更对比
const permissionChanges = computed(() => {
  const original = new Set(originalPermissions.value)
  const current = new Set(selectedPermissions.value)
  
  return {
    added: selectedPermissions.value.filter(p => !original.has(p)),
    removed: originalPermissions.value.filter(p => !current.has(p))
  }
})

// 方法
const getAvatarColor = (name) => {
  if (!name) return '#1890ff'
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return colors[hash % colors.length]
}

const getAvatarText = (name) => {
  if (!name) return '用'
  return name.length > 1 ? name.slice(-2) : name
}

const getStatusColor = (status) => {
  const colorMap = {
    '正常': 'success',
    '已禁用': 'error',
    '待激活': 'warning'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取权限的显示名称
 * 根据权限代码查找对应的权限描述，用于UI显示
 * 
 * @param {string} permissionCode - 权限代码，如 'team_view'
 * @returns {string} 权限的显示名称，如果找不到则返回权限代码本身
 */
const getPermissionName = (permissionCode) => {
  // 如果权限列表未加载完成，直接返回权限代码
  if (!allPermissions.value || allPermissions.value.length === 0) {
    return permissionCode;
  }
  
  // 在权限列表中查找匹配的权限
  const permission = allPermissions.value.find(
    p => p.权限名称 === permissionCode
  );
  
  // 返回权限描述或权限名称，如果都没有则返回原始代码
  if (permission) {
    return permission.权限描述 || permission.权限名称;
  }
  
  // 找不到对应权限时返回原始代码
  return permissionCode;
}

// 加载成员当前权限
const loadCurrentPermissions = async () => {
  if (!teamId.value || !memberId.value) return

  try {
    loadingPermissions.value = true
    
    // 获取成员详细权限信息
    const response = await teamMemberService.getMemberDetailedPermissions({
      团队id: teamId.value,
      用户id: memberId.value
    })

    if ([100, 0, 1].includes(response.status)) {
      const permissionData = response.data || response.message
      const memberPermissions = permissionData?.权限列表 || []
      selectedPermissions.value = [...memberPermissions]
      originalPermissions.value = [...memberPermissions]
      
      console.log('成员权限加载成功:', {
        成员id: memberId.value,
        权限数量: memberPermissions.length,
        权限列表: memberPermissions
      })
    } else {
      throw new Error(response.message || '获取成员权限失败')
    }
  } catch (error) {
    console.error('加载成员权限失败:', error)
    
    // 检查是否是权限问题
    if (error.message?.includes('权限') || error.message?.includes('403')) {
      message.error('您没有查看该成员权限的权限')
      emit('update:open', false)
      return
    }
    
    // 其他错误：降级处理
    selectedPermissions.value = []
    originalPermissions.value = []
    message.warning('加载成员当前权限失败，将显示空权限列表')
  } finally {
    loadingPermissions.value = false
  }
}

/**
 * 加载所有权限列表
 * 从后端获取权限数据并转换为扁平结构，用于权限名称显示
 */
const loadAllPermissions = async () => {
  try {
    // 调用API获取权限列表（按分类组织）
    const response = await teamPermissionService.getPermissionList()
    
    // 只处理成功响应
    if (response.status === 100) {
      // 获取分类权限数据
      const permissionsByCategory = response.data || {};
      
      // 将按分类组织的权限转换为扁平结构数组
      // 这样便于在UI中展示和查找权限信息
      const allPermissionsList = [];
      
      // 遍历所有权限分类
      Object.keys(permissionsByCategory).forEach(category => {
        // 获取该分类下的所有权限
        const permissions = permissionsByCategory[category] || [];
        
        // 将每个权限添加到扁平列表中，并记录其所属分类
        permissions.forEach(permission => {
          allPermissionsList.push({
            ...permission,       // 保留原有属性（权限名称、权限描述）
            权限分类: category    // 添加所属分类信息
          });
        });
      });
      
      // 更新权限列表状态
      allPermissions.value = allPermissionsList;
      console.log(`权限列表加载成功，共 ${allPermissionsList.length} 项权限`);
    } else {
      // 处理API返回的业务错误
      console.error('权限列表API返回错误:', response.message);
      message.warning('加载权限信息失败，某些功能可能受限');
    }
  } catch (error) {
    // 处理网络或其他异常
    console.error('加载权限列表失败:', error);
    message.warning('加载权限列表失败，权限管理功能可能受限');
  }
}

// 重置权限
const resetPermissions = () => {
  selectedPermissions.value = []
}

// 权限变更处理
const handlePermissionsChange = (permissions) => {
  // 权限列表直接传入，不再是对象
  selectedPermissions.value = permissions || []
}

/**
 * 提交权限更新
 * 将用户选择的权限列表发送到后端保存
 * @returns {Promise<boolean>} 更新是否成功
 */
const handleUpdatePermissions = async () => {
  // 避免重复提交
  if (submitting.value) {
    return false;
  }
  
  // 设置提交状态
  submitting.value = true;
  
  try {
    // 准备请求参数
    const params = {
      团队id: teamId.value,
      用户id: memberId.value,
      权限列表: selectedPermissions.value
    };
    
    console.log('提交权限更新:', params);
    
    // 调用API更新成员权限
    const response = await teamPermissionService.updateMemberPermissions(params);
    
    // 处理API响应
    if (response.status === 100) {
      // 更新成功，显示成功提示
      message.success('权限设置已保存');
      
      // 关闭模态窗并通知父组件更新
      visible.value = false;
      emit('success', {
        memberId: memberId.value,
        permissions: selectedPermissions.value
      });
      
      return true; // 更新成功
    } else {
      // 处理业务逻辑错误
      message.error(response.message || '权限更新失败');
      console.error('权限更新失败:', response);
      return false; // 更新失败
    }
  } catch (error) {
    // 处理网络或系统异常
    message.error('保存权限失败: ' + (error.message || '未知错误'));
    console.error('保存权限异常:', error);
    throw error; // 向上抛出错误，让调用者处理
  } finally {
    // 无论成功失败都恢复提交状态
    submitting.value = false;
  }
}

/**
 * 保存权限设置
 * 验证参数并执行权限更新操作
 */
const handleSave = async () => {
  // 参数验证
  if (!teamId.value || !memberId.value) {
    message.error('缺少必要参数：团队id或成员id');
    return;
  }

  // 检查是否有权限变更
  if (permissionChanges.value.added.length === 0 && permissionChanges.value.removed.length === 0) {
    message.info('权限未发生变更，无需保存');
    handleCancel(); // 关闭窗口
    return;
  }

  // 避免重复提交
  if (submitting.value || loading.value) {
    return;
  }

  // 设置加载状态
  loading.value = true;

  try {
    // 记录权限变更信息用于调试
    console.log('保存权限设置:', {
      团队id: teamId.value,
      用户id: memberId.value,
      角色: props.member?.角色 || 'member',
      权限列表: selectedPermissions.value,
      变更统计: {
        新增: permissionChanges.value.added.length,
        移除: permissionChanges.value.removed.length
      }
    });

    // 调用权限更新方法
    await handleUpdatePermissions();
  } catch (error) {
    // 错误处理
    console.error('更新成员权限失败:', error);
    
    // 根据错误类型提供友好提示
    if (error.message?.includes('403') || error.message?.includes('权限')) {
      message.error('您没有管理该成员权限的权限');
    } else if (error.message?.includes('404')) {
      message.error('成员不存在或已离开团队');
    } else {
      message.error('更新权限失败: ' + (error.message || '未知错误'));
    }
  } finally {
    // 恢复加载状态
    loading.value = false;
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  // 重置状态
  selectedPermissions.value = [...originalPermissions.value]
}

// 监听弹窗打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen && props.member) {
    loadCurrentPermissions()
    loadAllPermissions()
  }
})

// 监听成员变化
watch(() => props.member, (newMember) => {
  if (newMember && props.open) {
    loadCurrentPermissions()
  }
})
</script>

<style scoped>
.member-permission-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.member-info-section {
  margin-bottom: 20px;
}

.member-info-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.member-phone {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.member-role {
  display: flex;
  gap: 8px;
  align-items: center;
}

.permission-config-section {
  margin-bottom: 20px;
}

.permission-status {
  margin-bottom: 16px;
}

.change-summary {
  margin-top: 20px;
}

.change-item {
  margin-bottom: 16px;
}

.change-item h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-info {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .member-details {
    width: 100%;
  }
  
  .member-role {
    justify-content: center;
  }
}

/* 自定义滚动条 */
.member-permission-modal::-webkit-scrollbar {
  width: 6px;
}

.member-permission-modal::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.member-permission-modal::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.member-permission-modal::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 