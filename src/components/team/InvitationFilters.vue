<template>
  <div class="invitation-filters">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="left-actions">
        <a-button type="primary" @click="handleRefresh" :loading="refreshLoading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        
        <!-- 状态筛选 -->
        <a-select
          v-model:value="filters.状态"
          placeholder="筛选状态"
          style="width: 120px"
          @change="handleStatusFilter"
          allowClear
        >
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="邀请待处理">待处理</a-select-option>
          <a-select-option value="正常">已接受</a-select-option>
          <a-select-option value="已拒绝邀请">已拒绝</a-select-option>
          <a-select-option value="已移除">已撤销</a-select-option>
        </a-select>
      </div>
      
      <div class="right-actions">
        <a-statistic
          title="邀请总数"
          :value="total"
          :value-style="{ color: '#1890ff' }"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'

defineOptions({
  name: 'InvitationFilters'
})

const props = defineProps({
  // 刷新按钮加载状态
  refreshLoading: {
    type: Boolean,
    default: false
  },
  // 邀请总数
  total: {
    type: Number,
    default: 0
  },
  // 初始筛选条件
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['refresh', 'filter-change'])

// 筛选条件
const filters = reactive({
  状态: props.initialFilters.状态 || '',
  ...props.initialFilters
})

/**
 * 处理刷新按钮点击
 */
const handleRefresh = () => {
  emit('refresh')
}

/**
 * 处理状态筛选变化
 */
const handleStatusFilter = () => {
  emit('filter-change', { ...filters })
}

// 监听筛选条件变化
watch(filters, (newFilters) => {
  emit('filter-change', { ...newFilters })
}, { deep: true })

// 监听外部筛选条件变化
watch(() => props.initialFilters, (newInitialFilters) => {
  Object.assign(filters, newInitialFilters)
}, { deep: true })
</script>

<style scoped>
.invitation-filters {
  margin-bottom: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.left-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.right-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .left-actions {
    justify-content: center;
  }
  
  .right-actions {
    justify-content: center;
  }
}
</style> 