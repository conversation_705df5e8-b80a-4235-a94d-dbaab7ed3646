<template>
  <a-list
    :data-source="teams"
    :loading="loading"
    :pagination="pagination"
    item-layout="vertical"
    size="large"
  >
    <template #renderItem="{ item }">
      <a-list-item
        key="item.团队id"
        class="team-item clickable-team-item"
        @click="emit('teamClick', item)"
      >
        <!-- 简化操作：移除下拉菜单，点击卡片直接进入详情页 -->

        <a-list-item-meta>
          <template #title>
            <div class="team-title">
              <span class="team-name">{{ item.团队名称 }}</span>
              <a-tag v-if="item.我的角色" :color="getRoleColor(item.我的角色)">
                {{ item.我的角色 }}
              </a-tag>
              <a-tag v-if="item.团队状态" :color="getTeamStatusColor(item.团队状态)">
                {{ item.团队状态 }}
              </a-tag>
            </div>
          </template>
          
          <template #description>
            <div class="team-description">
              <p v-if="item.团队描述">{{ item.团队描述 }}</p>
              <div class="team-meta">
                <span class="meta-item">
                  <UserOutlined />
                  成员: {{ item.当前成员数 || item.成员数量 || 0 }}/{{ item.最大成员数 || 100 }}
                </span>
                <span class="meta-item" v-if="item.公司名称">
                  <BankOutlined />
                  {{ item.公司名称 }}
                </span>
                <span class="meta-item">
                  <CalendarOutlined />
                  创建于: {{ formatDate(item.创建时间) }}
                </span>
              </div>
            </div>
          </template>
          
          <template #avatar>
            <a-avatar
              :size="64"
              :style="{ backgroundColor: getTeamAvatarColor(item.团队名称 || '团队') }"
            >
              {{ (item.团队名称 || '团队').charAt(0) }}
            </a-avatar>
          </template>
        </a-list-item-meta>

        <!-- 团队成员预览 -->
        <div class="team-members-preview" v-if="item.成员列表 && item.成员列表.length > 0">
          <span class="preview-label">成员:</span>
          <a-avatar-group :max-count="5" :size="32">
            <a-avatar
              v-for="member in item.成员列表.slice(0, 5)"
              :key="member.用户id || member.id"
              :title="member.昵称 || member.手机号 || '成员'"
            >
              {{ (member.昵称 || member.手机号 || '成员').charAt(0) }}
            </a-avatar>
          </a-avatar-group>
                          <span v-if="(item.当前成员数 || item.成员数量) > 5" class="more-count">
                  +{{ (item.当前成员数 || item.成员数量) - 5 }}
          </span>
        </div>
      </a-list-item>
    </template>
  </a-list>
</template>

<script setup>
import { 
  UserOutlined, BankOutlined, CalendarOutlined
} from '@ant-design/icons-vue';
import { getRoleColor } from '../../utils/roleUtils';
import { formatDate, getTeamAvatarColor, getTeamStatusColor } from '../../utils/teamUtils';

// 定义组件的props和emits
const props = defineProps({
  teams: { type: Array, required: true },
  loading: { type: Boolean, default: false },
  pagination: { type: Object, required: true },
});

const emit = defineEmits(['teamClick']);

// 简化后的组件专注于显示团队列表，所有操作都在团队详情页面进行
</script>

<style scoped>
.team-item {
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.3s;
}
.team-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.clickable-team-item {
  cursor: pointer;
  position: relative;
}
.clickable-team-item:hover {
  background-color: #fafafa;
  transform: translateY(-2px);
}
.team-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.team-name {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}
.team-description {
  margin-top: 8px;
}
.team-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}
.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.team-members-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
.preview-label {
  color: #666;
  font-size: 14px;
}
.more-count {
  color: #666;
  font-size: 12px;
}
</style> 