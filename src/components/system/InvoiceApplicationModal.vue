<template>
  <a-modal
    :visible="visible"
    title="申请开票"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    @update:visible="$emit('update:visible', $event)"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="发票类型" name="invoiceType">
        <a-radio-group v-model:value="formData.invoiceType">
          <a-radio value="personal">个人发票</a-radio>
          <a-radio value="company">企业发票</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="发票抬头" name="invoiceTitle">
        <a-input v-model:value="formData.invoiceTitle" placeholder="请输入发票抬头" />
      </a-form-item>

      <a-form-item v-if="formData.invoiceType === 'company'" label="纳税人识别号" name="taxNumber">
        <a-input v-model:value="formData.taxNumber" placeholder="请输入纳税人识别号" />
      </a-form-item>

      <a-form-item label="开票金额" name="amount">
        <a-input-number
          v-model:value="formData.amount"
          :min="0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入开票金额"
        />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息（可选）" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'

defineOptions({
  name: 'InvoiceApplicationModal'
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const loading = ref(false)
const formRef = ref()

const formData = reactive({
  invoiceType: 'personal',
  invoiceTitle: '',
  taxNumber: '',
  amount: null,
  remark: ''
})

const rules = {
  invoiceType: [{ required: true, message: '请选择发票类型' }],
  invoiceTitle: [{ required: true, message: '请输入发票抬头' }],
  taxNumber: [
    { 
      required: true, 
      message: '请输入纳税人识别号',
      validator: (rule, value) => {
        if (formData.invoiceType === 'company' && !value) {
          return Promise.reject('请输入纳税人识别号')
        }
        return Promise.resolve()
      }
    }
  ],
  amount: [
    { required: true, message: '请输入开票金额' },
    { type: 'number', min: 0.01, message: '金额必须大于0' }
  ]
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 这里应该调用API提交发票申请
    // await submitInvoiceApplication(formData)
    
    message.success('发票申请提交成功')
    emit('success')
    handleCancel()
  } catch (error) {
    console.error('发票申请失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  Object.assign(formData, {
    invoiceType: 'personal',
    invoiceTitle: '',
    taxNumber: '',
    amount: null,
    remark: ''
  })
}

// 监听发票类型变化，清空纳税人识别号
watch(() => formData.invoiceType, (newType) => {
  if (newType === 'personal') {
    formData.taxNumber = ''
  }
})
</script>

<style scoped>
/* 这里可以添加特定的样式 */
</style> 