<template>
  <div class="payment-management">
    <a-card title="付费管理" :bordered="false" class="main-card">
      
      <!-- 会员状态展示区域 -->
      <div class="membership-status-section">
        <h3 class="section-title">
          <CrownOutlined class="title-icon" />
          我的会员状态
        </h3>
        
        <!-- 当前生效会员卡片 -->
        <CurrentMembershipCard 
          :membership="currentMembership"
          @showActivation="toggleActivationInput"
        />
        
        <!-- 即将生效会员卡片 -->
        <UpcomingMembershipCard 
          v-if="upcomingMemberships.length > 0"
          :memberships="upcomingMemberships"
          :current-membership="currentMembership"
        />
        
        <!-- 会员时间轴详细记录 - 只有多个会员时才显示 -->
        <MembershipTimelineCard
          v-if="hasMultipleMemberships"
          :all-memberships="allMemberships"
          :current-memberships="currentMembership ? [currentMembership] : []"
          :upcoming-memberships="upcomingMemberships"
          :membership-history="membershipHistory"
        />
      </div>
        
      <!-- 激活码输入区域 -->
      <div v-if="showActivationInput" class="activation-input-section">
        <a-card :bordered="false" class="activation-card">
          <div class="activation-content">
            <!-- 激活说明 -->
            <a-alert
              :message="membershipStatus === 'active' ? '使用激活码延长会员时间' : '使用激活码快速激活会员'"
              :description="membershipStatus === 'active' ?
                '输入有效的激活码可以延长您的会员时间，激活码对应的天数将添加到您当前的到期时间。' :
                '支持两种激活方式：① 输入16位激活码直接激活 ② 输入推荐人手机号激活（推荐人需为会员）'"
              type="info"
              show-icon
              style="margin-bottom: 24px;"
            />

            <!-- 激活码输入表单 -->
            <a-form
              ref="activationFormRef"
              :model="activationForm"
              :rules="activationRules"
              layout="vertical"
              @finish="handleActivation"
            >
              <a-form-item
                label="激活码/推荐人手机号"
                name="code"
              >
                <a-input
                  v-model:value="activationForm.code"
                  size="large"
                  placeholder="请输入16位激活码或11位手机号"
                  :maxlength="16"
                  allow-clear
                  @input="onActivationInputChange"
                >
                  <template #prefix>
                    <key-outlined />
                  </template>
                </a-input>
                <!-- 输入提示 -->
                <div class="input-hint" v-if="activationForm.code">
                  <a-tag 
                    :color="activationInputType === '激活码' ? 'blue' : activationInputType === '手机号' ? 'green' : 'red'"
                    size="small"
                    style="margin-top: 8px;"
                  >
                    {{ activationInputHintText }}
                  </a-tag>
                </div>
              </a-form-item>

              <a-form-item style="margin-bottom: 0;">
                <a-space style="width: 100%; justify-content: flex-end;">
                  <a-button @click="showActivationInput = false">
                    取消
                  </a-button>
                  <a-button
                    type="primary"
                    html-type="submit"
                    :loading="activationLoading"
                    :disabled="!activationInputValid"
                  >
                    <template #icon>
                      <rocket-outlined />
                    </template>
                    {{ activationButtonText }}
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </a-card>
      </div>

      <!-- Tab容器 -->
      <a-tabs
        v-model:activeKey="activeTab"
        class="payment-tabs"
        size="large"
        type="card"
      >
        <!-- 套餐选择Tab -->
        <a-tab-pane key="plans" tab="套餐选择">
          <template #tab>
            <span>
              <ShoppingCartOutlined />
              套餐选择
            </span>
          </template>

          <!-- 套餐选择区域 -->
          <div class="plans-section">
            <div class="section-header">
              <h3 class="section-title">
                <ShoppingCartOutlined class="title-icon" />
                选择适合您的套餐
              </h3>
              <p class="section-subtitle">根据您的业务需求，选择最适合的会员套餐</p>
            </div>

            <!-- 计费周期切换 -->
            <div class="billing-switch">
              <div class="billing-tips">
                <span class="tip-text">选择计费周期：</span>
                <a-tag color="green" v-if="billingCycle === 'yearly'">年付更优惠</a-tag>
              </div>
              <a-radio-group v-model:value="billingCycle" button-style="solid" size="large">
                <a-radio-button value="monthly">按月付费</a-radio-button>
                <a-radio-button value="yearly">
                  按年付费
                  <span class="discount-tag">(立省{{ getYearlyDiscount() }}%)</span>
                </a-radio-button>
              </a-radio-group>
            </div>

            <!-- 套餐卡片列表 -->
            <div v-if="membershipPlans.length === 0" class="no-data-tip">
              <a-empty description="正在加载套餐信息..." />
            </div>
            <a-row :gutter="24" class="plans-grid" v-else>
              <a-col :xs="24" :md="12" :lg="6" v-for="plan in membershipPlans" :key="plan.套餐ID">
                <a-card
                  class="plan-item-card"
                  :class="{ 'recommended': plan.推荐 }"
                  hoverable
                >
                  <!-- 推荐标签 -->
                  <div v-if="plan.推荐" class="recommended-badge">推荐</div>

                  <!-- 套餐信息 -->
                  <div class="plan-content">
                    <!-- 主要内容区域 -->
                    <div class="plan-main-content">
                      <h4 class="plan-title">{{ plan.套餐名称 }}</h4>
                      <p class="plan-desc">{{ plan.套餐描述 }}</p>

                      <!-- 价格显示 -->
                      <div class="price-section">
                        <div class="current-price">
                          <span class="price">¥{{ billingCycle === 'yearly' ? plan.年价格 : plan.月价格 }}</span>
                          <span class="period">{{ billingCycle === 'yearly' ? '/年' : '/月' }}</span>
                        </div>

                        <!-- 年付显示原价对比 -->
                        <div v-if="billingCycle === 'yearly'" class="original-price">
                          <span class="original">原价: ¥{{ plan.月价格 * 12 }}</span>
                          <span class="save">节省{{ plan.年费优惠比例 }}%</span>
                        </div>
                      </div>

                      <!-- 特色功能列表 -->
                      <div class="features-list">
                        <div v-for="feature in plan.特色功能.slice(0, 3)" :key="feature" class="feature-item">
                          <CheckOutlined class="check-icon" />
                          <span>{{ feature }}</span>
                        </div>
                        <div v-if="plan.特色功能.length > 3" class="more-features">
                          还有{{ plan.特色功能.length - 3 }}项功能...
                        </div>
                      </div>
                    </div>

                    <!-- 选择按钮容器 -->
                    <div class="select-btn-container">
                      <a-button
                        type="primary"
                        size="large"
                        block
                        :loading="getPlanLoadingState(plan.套餐ID)"
                        @click="handleSelectPlan(plan)"
                        class="select-btn"
                      >
                        {{ getSelectButtonText(plan) }}
                      </a-button>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 订单历史Tab -->
        <a-tab-pane key="orders" tab="订单历史">
          <template #tab>
            <span>
              <HistoryOutlined />
              订单历史
            </span>
          </template>

          <!-- 订单历史区域 -->
          <div class="history-section">
            <h3 class="section-title">
              <HistoryOutlined class="title-icon" />
              订单历史
            </h3>

            <a-table
              :columns="orderColumns"
              :data-source="userOrders"
              :loading="loadingHistory"
              :pagination="{ pageSize: 5 }"
              row-key="订单号"
            >
              <!-- 状态列自定义渲染 -->
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getOrderStatusColor(record.订单状态)">
                    {{ getOrderStatusText(record.订单状态) }}
                  </a-tag>
                </template>

                <!-- 操作列自定义渲染 -->
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button
                      v-if="record.订单状态 === 'pending'"
                      type="link"
                      @click="handlePayOrder(record)"
                    >
                      继续支付
                    </a-button>
                    <a-button
                      v-if="record.订单状态 === 'pending'"
                      type="link"
                      danger
                      @click="handleCancelOrder(record)"
                    >
                      取消订单
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 微信支付弹窗 -->
    <a-modal
      :open="showPaymentModal"
      :title="paymentStatus === 'success' ? '支付成功' : '微信支付'"
      :footer="null"
      @update:open="showPaymentModal = $event"
      width="450px"
      centered
      :closable="paymentStatus !== 'checking'"
      :maskClosable="paymentStatus !== 'checking'"
    >
      <div class="payment-modal-content">
        <!-- 支付信息 -->
        <div class="payment-info" v-if="paymentStatus !== 'success'">
          <div class="amount-display">
            <span class="amount-label">支付金额</span>
            <span class="amount-value">¥{{ paymentAmount }}</span>
          </div>
          <div class="plan-info">
            <span class="plan-name">{{ selectedPlan.套餐名称 }}</span>
            <span class="plan-period">{{ billingCycle === 'yearly' ? '年付套餐' : '月付套餐' }}</span>
            <span v-if="billingCycle === 'yearly'" class="discount-info">
              (相比月付节省¥{{ (selectedPlan.月价格 * 12 - selectedPlan.年价格) }})
            </span>
          </div>
        </div>
        
        <!-- 二维码显示 -->
        <div class="qrcode-container" v-if="paymentQrCode && paymentStatus !== 'success'">
          <div class="qrcode-wrapper">
            <!-- 二维码加载状态 -->
            <div v-if="generatingQrCode" class="qr-loading">
              <a-spin size="large" />
              <p>正在生成支付二维码...</p>
            </div>

            <!-- 二维码显示 -->
            <div v-else-if="paymentQrCode && !qrCodeError" class="qr-display">
              <qrcode-vue :value="paymentQrCode" :size="200" level="M"></qrcode-vue>
              <div class="qr-info">
                <p class="order-number">订单号：{{ currentOrderNumber }}</p>
                <p class="timeout-info">
                  <ClockCircleOutlined />
                  订单将在 <span class="countdown">{{ formatTime(paymentTimeout) }}</span> 后过期
                </p>
              </div>
            </div>

            <!-- 二维码生成失败 -->
            <div v-else-if="qrCodeError" class="qr-error">
              <ExclamationCircleOutlined style="font-size: 48px; color: #ff4d4f;" />
              <p class="error-title">二维码生成失败</p>
              <p class="error-message">{{ qrCodeError }}</p>
              <a-button type="primary" @click="retryGenerateQrCode" :loading="generatingQrCode">
                <ReloadOutlined />
                重新生成
              </a-button>
            </div>
          </div>

          <div class="scan-instructions">
            <div class="instruction-item">
              <MobileOutlined class="instruction-icon" />
              <div class="instruction-text">
                <p class="instruction-title">使用微信扫码支付</p>
                <p class="instruction-desc">打开微信 → 扫一扫 → 扫描二维码</p>
              </div>
            </div>
            <div class="instruction-item">
              <SafetyCertificateOutlined class="instruction-icon" />
              <div class="instruction-text">
                <p class="instruction-title">安全保障</p>
                <p class="instruction-desc">支付安全由微信支付保障</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 支付状态 -->
        <div class="payment-status">
          <a-spin v-if="paymentStatus === 'checking'" :spinning="true" size="large">
            <div class="checking-content">
              <p class="checking-title">正在确认支付结果...</p>
              <p class="checking-subtitle">支付完成后会自动跳转，请耐心等待</p>
              <div class="checking-tips">
                <p>• 支付遇到问题？请检查微信支付设置</p>
                <p>• 长时间未响应？请联系客服协助</p>
              </div>
            </div>
          </a-spin>
          
          <a-result 
            v-else-if="paymentStatus === 'success'"
            status="success"
            title="支付成功"
            sub-title="恭喜您成为会员，享受专属权益"
          >
            <template #extra>
              <div class="success-info">
                <p><strong>套餐名称：</strong>{{ selectedPlan.套餐名称 }}</p>
                <p><strong>有效期：</strong>1年</p>
                <p><strong>开通时间：</strong>{{ new Date().toLocaleString() }}</p>
              </div>
            </template>
          </a-result>
        </div>
        
        <!-- 操作按钮 -->
        <div class="payment-actions">
          <a-button 
            v-if="paymentStatus === 'checking'" 
            @click="closePaymentModal"
            type="default"
          >
            稍后再试
          </a-button>
          <a-button 
            v-else
            @click="closePaymentModal"
            :type="paymentStatus === 'success' ? 'primary' : 'default'"
          >
            {{ paymentStatus === 'success' ? '开始使用' : '关闭' }}
          </a-button>
        </div>
      </div>
    </a-modal>


  </div>
</template>

<script setup>
import {
    CheckOutlined,
    ClockCircleOutlined,
    CrownOutlined,
    ExclamationCircleOutlined,
    HistoryOutlined,
    KeyOutlined,
    MobileOutlined,
    ReloadOutlined,
    RocketOutlined,
    SafetyCertificateOutlined,
    ShoppingCartOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import QrcodeVue from 'qrcode.vue'
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue'
import { authAPI } from '../../services'
import orderService from '../../services/order'
import CurrentMembershipCard from './CurrentMembershipCard.vue'
import MembershipTimelineCard from './MembershipTimelineCard.vue'
import UpcomingMembershipCard from './UpcomingMembershipCard.vue'

// 付费管理组件 - 处理用户套餐购买、会员管理等功能
defineOptions({
  name: 'PaymentManagement',
  components: {
    QrcodeVue
  }
})

// ========================= 响应式数据定义 =========================

// Tab页面状态管理
const activeTab = ref('plans')         // 当前激活的Tab页：plans/orders

// 套餐相关数据
const membershipPlans = ref([])        // 会员套餐列表
const selectedPlan = ref({})           // 当前选择的套餐
const billingCycle = ref('monthly')    // 计费周期：monthly/yearly

// 用户会员信息
const userMembership = ref({})         // 用户会员详情（兼容原有逻辑）
const currentPlan = ref({})            // 当前套餐信息
const membershipStatus = ref('inactive') // 会员状态：active/inactive

// 多会员状态数据
const currentMembership = ref(null)    // 当前生效的会员
const upcomingMemberships = ref([])    // 即将生效的会员列表
const membershipHistory = ref([])      // 历史会员记录
const allMemberships = ref([])         // 所有会员记录
const showMembershipDetails = ref(false) // 是否显示详细会员记录

// 订单相关数据
const userOrders = ref([])             // 用户订单列表

// 支付相关数据
const showPaymentModal = ref(false)    // 是否显示支付弹窗
const paymentQrCode = ref('')          // 支付二维码
const paymentAmount = ref(0)           // 支付金额
const paymentStatus = ref('waiting')   // 支付状态：waiting/checking/success
const paymentTimer = ref(null)         // 支付状态检查定时器
const countdownTimer = ref(null)       // 倒计时定时器
const currentOrderNumber = ref('')     // 当前订单号
const paymentTimeout = ref(0)          // 支付剩余时间（秒）
const generatingQrCode = ref(false)    // 是否正在生成二维码
const qrCodeError = ref('')            // 二维码生成错误信息
const cancelingPayment = ref(false)    // 是否正在取消支付
const checkingPayment = ref(false)     // 是否正在检查支付状态

// 加载状态
const loadingPay = ref(false)          // 支付按钮加载状态（保留用于兼容性）
const loadingStates = ref({})          // 各套餐按钮的独立加载状态
const loadingHistory = ref(false)     // 订单历史加载状态
const loadingPlans = ref(false)       // 套餐列表加载状态
const activationLoading = ref(false)  // 激活码激活加载状态

// 激活码相关数据
const showActivationInput = ref(false) // 是否显示激活码输入区域
const activationFormRef = ref()        // 激活码表单引用
const activationForm = reactive({      // 激活码表单数据
  code: ''
})

// 激活码表单验证规则
const activationRules = {
  code: [
    { required: true, message: '请输入激活码或推荐人手机号', trigger: 'blur' },
    { 
      validator: (rule, value) => {
        const trimmedValue = value.trim()
        
        // 16位激活码验证
        if (trimmedValue.length === 16) {
          if (!/^[A-Za-z0-9]{16}$/.test(trimmedValue)) {
            return Promise.reject('激活码应为16位数字和字母组合')
          }
          return Promise.resolve()
        }
        
        // 11位手机号验证
        if (trimmedValue.length === 11) {
          if (!/^1[3-9]\d{9}$/.test(trimmedValue)) {
            return Promise.reject('手机号格式错误，应为11位数字，以1开头，第二位为3-9')
          }
          return Promise.resolve()
        }
        
        return Promise.reject('请输入11位手机号或16位激活码')
      },
      trigger: 'blur'
    }
  ]
}

// ========================= 计算属性 =========================

/**
 * 获取指定套餐的加载状态
 * 为每个套餐按钮提供独立的加载状态管理
 */
const getPlanLoadingState = (planId) => {
  return loadingStates.value[planId] || false
}

// 获取年付折扣百分比
const getYearlyDiscount = () => {
  if (membershipPlans.value.length === 0) return 0
  const plan = membershipPlans.value.find(p => p.年费优惠比例 > 0)
  return plan ? Math.round(plan.年费优惠比例) : 15
}

// 注意：getStatusColor 和 getStatusText 函数已移至 CurrentMembershipCard 组件中

// 激活码输入类型判断
const activationInputType = computed(() => {
  const code = activationForm.code.trim()
  if (!code) return ''
  
  // 判断是否为16位激活码（数字和字母组合）
  if (code.length === 16 && /^[A-Za-z0-9]{16}$/.test(code)) {
    return '激活码'
  }
  
  // 判断是否为11位手机号（1开头，第二位是3-9）
  if (code.length === 11 && /^1[3-9]\d{9}$/.test(code)) {
    return '手机号'
  }
  
  return '无效'
})

// 激活码输入提示文本
const activationInputHintText = computed(() => {
  const code = activationForm.code.trim()
  if (!code) return ''
  
  switch (activationInputType.value) {
    case '激活码':
      return '识别为：16位激活码'
    case '手机号':
      return '识别为：推荐人手机号'
    case '无效':
      if (code.length < 11) {
        return '输入长度不足'
      } else if (code.length > 16) {
        return '输入过长，请检查格式'
      } else if (code.length === 11) {
        return '手机号格式错误（应为1开头，第二位3-9）'
      } else if (code.length === 16) {
        return '激活码格式错误（应为数字和字母组合）'
      } else {
        return '格式错误，请重新输入'
      }
    default:
      return ''
  }
})

// 激活按钮文本
const activationButtonText = computed(() => {
  switch (activationInputType.value) {
    case '激活码':
      return '使用激活码激活'
    case '手机号':
      return '通过推荐人激活'
    default:
      return '立即激活'
  }
})

// 输入是否有效
const activationInputValid = computed(() => {
  return activationInputType.value === '激活码' || activationInputType.value === '手机号'
})

// 判断用户是否有多个会员（包括当前、即将生效和历史记录）
const hasMultipleMemberships = computed(() => {
  // 统计所有会员记录：当前生效 + 即将生效 + 历史记录
  const currentCount = currentMembership.value ? 1 : 0
  const upcomingCount = upcomingMemberships.value.length
  const historyCount = membershipHistory.value.length
  const totalMemberships = currentCount + upcomingCount + historyCount

  return totalMemberships > 1
})

// ========================= 数据加载方法 =========================

/**
 * 获取默认套餐列表
 * 当API调用失败时使用的备用套餐数据
 */
const getDefaultPlans = () => {
  return [
    {
      套餐ID: 1,
      套餐名称: '基础版',
      套餐描述: '适合个人用户的基础功能',
      月价格: 99,
      年价格: 999,
      年费优惠比例: 15,
      推荐: false,
      特色功能: ['基础搜索', '数据导出', '客户管理']
    },
    {
      套餐ID: 2,
      套餐名称: '专业版',
      套餐描述: '适合小团队的专业功能',
      月价格: 299,
      年价格: 2999,
      年费优惠比例: 16,
      推荐: true,
      特色功能: ['高级搜索', '批量操作', '数据分析', '团队协作']
    },
    {
      套餐ID: 3,
      套餐名称: '企业版',
      套餐描述: '适合大型企业的全功能版本',
      月价格: 599,
      年价格: 5999,
      年费优惠比例: 17,
      推荐: false,
      特色功能: ['无限搜索', '高级分析', '定制功能', 'API接口', '专属客服']
    }
  ]
}

/**
 * 获取会员套餐列表
 * 从后端获取所有可用的会员套餐信息
 */
const loadMembershipPlans = async () => {
  try {
    console.log('🔄 开始加载会员套餐列表...')

    const result = await orderService.getMembershipPlans()
    console.log('📋 套餐列表API响应:', result)

    if (result && result.status === 100) {
      membershipPlans.value = result.data || []
      console.log('✅ 套餐列表加载成功:', result.data)
    } else {
      console.warn('⚠️ 套餐列表加载失败:', result)
      // 不显示错误消息，使用默认套餐
      membershipPlans.value = getDefaultPlans()
      console.log('🔄 使用默认套餐列表')
    }
  } catch (error) {
    console.error('❌ 获取套餐列表失败:', error)

    // 不抛出错误，使用默认套餐避免页面崩溃
    membershipPlans.value = getDefaultPlans()
    console.log('🔄 API调用失败，使用默认套餐列表')
  }
}

/**
 * 会员状态分类处理
 * 将会员列表按状态分类：当前生效、即将生效、历史记录
 */
const processMembershipData = (会员列表) => {
  const now = new Date()
  
  // 按状态分类会员
  const 当前生效 = 会员列表.filter(会员 => {
    const 到期时间 = new Date(会员.到期时间)
    const 开通时间 = new Date(会员.开通时间)
    return 会员.会员状态 === '有效' && 到期时间 > now && 开通时间 <= now
  }).sort((a, b) => (b.会员级别 || 0) - (a.会员级别 || 0)) // 按级别排序，取最高级别
  
  const 即将生效 = 会员列表.filter(会员 => {
    const 开通时间 = new Date(会员.开通时间)
    return (会员.会员状态 === '待生效' || 
           (会员.会员状态 === '有效' && 开通时间 > now))
  }).sort((a, b) => new Date(a.开通时间) - new Date(b.开通时间)) // 按开通时间排序
  
  const 历史记录 = 会员列表.filter(会员 => {
    const 到期时间 = new Date(会员.到期时间)
    return 会员.会员状态 === '已过期' || 
           (会员.会员状态 === '有效' && 到期时间 <= now)
  }).sort((a, b) => new Date(b.到期时间) - new Date(a.到期时间)) // 按到期时间倒序
  
  return {
    当前生效: 当前生效[0] || null, // 取最高级别的有效会员
    即将生效,
    历史记录,
    全部会员: 会员列表
  }
}

/**
 * 获取用户会员信息
 * 获取当前用户的会员状态和套餐详情，支持多会员状态显示
 */
const loadUserMembership = async () => {
  try {
    const result = await orderService.getUserMembership()

    if (result.status === 100) {
      const data = result.data || {}

      // 处理会员列表数据
      const 会员列表 = data.会员列表 || []
      const 会员数据 = processMembershipData(会员列表)
      
      // 设置分类后的会员数据
      currentMembership.value = 会员数据.当前生效
      upcomingMemberships.value = 会员数据.即将生效
      membershipHistory.value = 会员数据.历史记录
      allMemberships.value = 会员数据.全部会员

      // 设置总体会员状态
      const 会员状态 = 会员数据.当前生效 ? '有效' : '未开通'
      membershipStatus.value = 会员状态 === '有效' ? 'active' : 'inactive'

      // 设置当前用户会员信息（兼容原有逻辑）
      if (会员数据.当前生效) {
        userMembership.value = {
          到期时间: 会员数据.当前生效.到期时间 || '',
          剩余天数: 会员数据.当前生效.剩余天数 || 0,
          会员名称: 会员数据.当前生效.会员名称 || '',
          是否会员: true
        }

        // 设置当前套餐信息
        currentPlan.value = {
          套餐名称: 会员数据.当前生效.会员名称 || '免费体验版',
          套餐描述: 会员数据.当前生效.会员名称 ? `${会员数据.当前生效.会员名称}套餐` : '体验基础功能，升级享受更多权益',
          套餐ID: 会员数据.当前生效.会员id || null,
          月价格: 会员数据.当前生效.每月费用 || 0,
          年价格: 会员数据.当前生效.每年费用 || 0
        }
      } else {
        userMembership.value = {
          到期时间: '',
          剩余天数: 0,
          会员名称: '',
          是否会员: false
        }
        currentPlan.value = {
          套餐名称: '免费体验版',
          套餐描述: '体验基础功能，升级享受更多权益',
          套餐ID: null,
          月价格: 0,
          年价格: 0
        }
      }

    } else {
      membershipStatus.value = 'inactive'
      userMembership.value = {}
      currentPlan.value = {}
      currentMembership.value = null
      upcomingMemberships.value = []
      membershipHistory.value = []
      allMemberships.value = []
    }
  } catch (error) {
    console.error('获取用户会员信息失败:', error)
    membershipStatus.value = 'inactive'
    userMembership.value = {}
    currentPlan.value = {}
    currentMembership.value = null
    upcomingMemberships.value = []
    membershipHistory.value = []
    allMemberships.value = []
  }
}

/**
 * 获取用户订单历史
 * 获取用户的所有付费订单记录
 */
const loadUserOrders = async () => {
  loadingHistory.value = true
  try {
    const result = await orderService.getUserOrders()
    if (result.status === 100) {
      userOrders.value = result.data || []
      console.log('用户订单历史加载成功:', result.data)
    } else {
      message.error(result.message || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    message.error('获取订单历史失败')
  } finally {
    loadingHistory.value = false
  }
}

// ========================= 业务逻辑方法 =========================

/**
 * 切换激活码输入显示
 */
const toggleActivationInput = () => {
  showActivationInput.value = !showActivationInput.value
  if (showActivationInput.value) {
    // 清空表单
    activationForm.code = ''
    activationFormRef.value?.resetFields()
  }
}

/**
 * 激活码输入变化处理
 */
const onActivationInputChange = () => {
  // 自动清除非法字符
  const code = activationForm.code
  if (code.length <= 11) {
    // 可能是手机号，只允许数字
    activationForm.code = code.replace(/[^\d]/g, '')
  } else {
    // 可能是激活码，只允许数字和字母
    activationForm.code = code.replace(/[^A-Za-z0-9]/g, '')
  }
}

/**
 * 处理激活失败的错误信息
 * 将技术性错误转换为用户友好的提示
 */
const handleActivationError = (response) => {
  const errorMessage = response.message || '激活失败'

  // 根据不同的错误信息提供用户友好的提示
  if (errorMessage.includes('一次性激活码已被他人使用') || errorMessage.includes('激活码已被他人使用')) {
    message.error('该激活码已被使用，请联系客服获取新的激活码')
  } else if (errorMessage.includes('一次性激活码已被本人使用') || errorMessage.includes('激活码已被本人使用')) {
    message.info('您已使用过该激活码，无需重复激活')
  } else if (errorMessage.includes('您已使用过该永久激活码')) {
    message.info('您已使用过该激活码，无需重复激活')
  } else if (errorMessage.includes('无效的激活码') || errorMessage.includes('激活码不存在')) {
    message.error('激活码无效，请检查输入是否正确')
  } else if (errorMessage.includes('激活码类型不存在')) {
    message.error('激活码类型异常，请联系客服处理')
  } else if (errorMessage.includes('激活码类型未绑定会员权限') || errorMessage.includes('激活类型未绑定权限')) {
    message.error('激活码配置异常，请联系客服处理')
  } else if (errorMessage.includes('激活码类型未绑定会员天数') || errorMessage.includes('激活类型未绑定时间')) {
    message.error('激活码配置异常，请联系客服处理')
  } else if (errorMessage.includes('设置用户会员时间失败')) {
    message.error('激活处理失败，请稍后重试或联系客服')
  } else if (errorMessage.includes('请输入16位激活码或11位手机号')) {
    message.error('请输入正确格式的激活码（16位）或推荐人手机号（11位）')
  } else {
    // 其他未知错误，显示通用提示
    message.error('激活失败，请检查激活码是否正确或联系客服')
  }
}

/**
 * 处理激活码激活
 * Handle activation code activation
 */
const handleActivation = async () => {
  try {
    activationLoading.value = true

    // 检查用户认证状态
    const token = localStorage.getItem('crm_token') || document.cookie.split('; ').find(row => row.startsWith('token='))?.split('=')[1]
    if (!token) {
      message.error('请先登录后再使用激活码')
      showActivationInput.value = false
      return
    }

    console.log('开始激活码激活，输入内容:', activationForm.code)

    // 调用激活接口
    const response = await authAPI.activateAccount({
      code: activationForm.code.trim()
    })

    console.log('激活接口响应:', response)

    // 检查激活结果
    if (response.status === 200 || response.status === 100) {
      // 根据不同的激活类型和用户状态显示不同的成功消息
      let successMessage = '激活成功！'

      if (response.data?.激活类型 === '手机号激活') {
        successMessage = '手机号激活成功！您获得了会员试用时间'
      } else if (membershipStatus.value === 'active') {
        successMessage = '激活码激活成功！您的会员时间已延长'
      } else {
        successMessage = '激活码激活成功！您的会员权限已生效'
      }

      message.success(successMessage)

      // 关闭输入框
      showActivationInput.value = false

      // 清空表单
      activationForm.code = ''
      activationFormRef.value?.resetFields()

      // 重新加载用户会员信息
      await loadUserMembership()

    } else {
      // 不抛出异常，而是直接处理激活失败的情况
      handleActivationError(response)
      return
    }

  } catch (error) {
    // 处理网络错误和其他异常情况
    console.error('激活请求异常:', error)

    const errorMessage = error.response?.data?.message || error.消息 || error.message || '网络异常'

    // 针对网络错误和系统异常的用户友好提示
    if (errorMessage.includes('网络') || errorMessage.includes('Network') || errorMessage.includes('timeout')) {
      message.error('网络连接异常，请检查网络后重试')
    } else if (errorMessage.includes('登录') || errorMessage.includes('认证') || errorMessage.includes('token') || errorMessage.includes('401')) {
      message.error('登录状态已过期，请重新登录后再试')
      showActivationInput.value = false
    } else if (errorMessage.includes('服务器') || errorMessage.includes('500')) {
      message.error('服务器暂时繁忙，请稍后重试')
    } else if (errorMessage.includes('手机号不存在') || errorMessage.includes('推荐人手机号不存在')) {
      message.error('推荐人手机号不存在，请检查输入是否正确')
    } else if (errorMessage.includes('不是会员') || errorMessage.includes('不是付费会员')) {
      message.error('推荐人不是付费会员，无法通过此手机号激活')
    } else if (errorMessage.includes('已经被邀请') || errorMessage.includes('无法再次邀请')) {
      message.info('您已经被其他推荐人邀请过，每个用户只能通过一个推荐人激活')
    } else if (errorMessage.includes('今日邀请次数已达上限')) {
      message.info('推荐人今日邀请次数已达上限，请明天再试')
    } else {
      // 对于其他未知错误，使用通用提示
      message.error('激活请求失败，请稍后重试或联系客服')
    }
  } finally {
    activationLoading.value = false
  }
}

/**
 * 获取选择按钮的显示文本
 * 根据用户当前套餐状态决定按钮文案
 */
const getSelectButtonText = (plan) => {
  if (!currentPlan.value.套餐ID) {
    return '立即开通'
  }
  if (plan.月价格 > currentPlan.value.月价格) {
    return '升级套餐'
  }
  return '选择套餐'
}

/**
 * 处理套餐选择
 * 用户点击套餐后创建订单并显示支付界面
 */
const handleSelectPlan = async (plan) => {
  // 检查是否需要升级确认
  if (currentPlan.value.套餐ID && plan.月价格 < currentPlan.value.月价格) {
    message.warning('您当前的套餐级别更高，无需降级')
    return
  }

  selectedPlan.value = plan
  paymentAmount.value = billingCycle.value === 'yearly' ? plan.年价格 : plan.月价格

  // 设置当前套餐的加载状态
  loadingStates.value[plan.套餐ID] = true
  loadingPay.value = true  // 保留用于兼容性
  try {
    // 检测是否在微信环境中，决定支付方式
    const isWechat = /MicroMessenger/i.test(navigator.userAgent)
    const paymentType = isWechat ? 'JSAPI' : 'NATIVE'
    
    // 调用后端接口创建支付订单
    const result = await orderService.createOrder(
      plan.套餐ID,
      billingCycle.value === 'yearly' ? 'yearly' : 'monthly',
      paymentType
    )
    
    if (result.status === 100) {
      if (paymentType === 'JSAPI') {
        // JSAPI支付：直接调用微信支付
        await handleWechatJSAPIPay(result.data)
      } else {
        // NATIVE支付：显示二维码
        paymentQrCode.value = result.data.支付二维码
        showPaymentModal.value = true
        startPaymentCheck(result.data.订单号)
        message.success('订单创建成功，请使用微信扫码支付')
      }
    } else {
      message.error(result.message || '创建订单失败，请稍后重试')
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    // 提供更友好的错误提示
    if (error.message.includes('503')) {
      message.error('服务暂时不可用，请稍后重试或联系客服')
    } else if (error.message.includes('网络')) {
      message.error('网络连接异常，请检查网络后重试')
    } else {
      message.error('创建订单失败，请联系客服协助处理')
    }
  } finally {
    // 清除当前套餐的加载状态
    loadingStates.value[plan.套餐ID] = false
    loadingPay.value = false  // 保留用于兼容性
  }
}

/**
 * 处理微信JSAPI支付
 * 在微信内浏览器中直接调用微信支付
 */
const handleWechatJSAPIPay = async (orderData) => {
  try {
    // 检查是否有支付参数
    if (!orderData.支付参数 || orderData.支付参数.错误) {
      message.error('JSAPI支付参数获取失败，请刷新页面重试')
      return
    }
    
    // 调用微信JSAPI支付
    if (typeof WeixinJSBridge !== 'undefined') {
      paymentStatus.value = 'checking'
      showPaymentModal.value = true
      
      WeixinJSBridge.invoke(
        'getBrandWCPayRequest',
        {
          appId: orderData.支付参数.appId,
          timeStamp: orderData.支付参数.timeStamp,
          nonceStr: orderData.支付参数.nonceStr,
          package: orderData.支付参数.package,
          signType: orderData.支付参数.signType,
          paySign: orderData.支付参数.paySign
        },
        function(res) {
          if (res.err_msg === "get_brand_wcpay_request:ok") {
            // 支付成功
            paymentStatus.value = 'success'
            message.success('支付成功！会员权益已激活')
            setTimeout(() => {
              closePaymentModal()
            }, 2000)
          } else {
            // 支付失败或取消
            paymentStatus.value = 'waiting'
            message.warning('支付已取消或失败')
            closePaymentModal()
          }
        }
      )
    } else {
      // 不在微信环境或微信接口未准备好，使用扫码支付
      message.info('请在微信中打开此页面进行支付，或使用扫码支付')
      // 创建NATIVE支付订单
      await handleSelectPlan(selectedPlan.value)
    }
  } catch (error) {
    console.error('JSAPI支付失败:', error)
    message.error('微信支付调用失败，请重试')
    paymentStatus.value = 'waiting'
  }
}

/**
 * 处理继续支付
 * 用户点击继续支付未完成的订单
 */
const handlePayOrder = async (order) => {
  const plan = membershipPlans.value.find(p => p.套餐名称 === order.套餐名称)
  if (plan) {
    selectedPlan.value = plan
    paymentAmount.value = order.支付金额
    
    // 如果订单已有二维码，直接使用；否则重新创建
    if (order.支付二维码) {
      paymentQrCode.value = order.支付二维码
      showPaymentModal.value = true
      startPaymentCheck(order.订单号)
    } else {
      await handleSelectPlan(plan)
    }
  }
}

/**
 * 取消订单
 * 取消用户的待支付订单
 */
const handleCancelOrder = async (order) => {
  try {
    const result = await orderService.cancelOrder(order.订单号)
    
    if (result.status === 100) {
      message.success('订单已取消')
      await loadUserOrders() // 刷新订单列表
    } else {
      message.error(result.message || '取消订单失败')
    }
  } catch (error) {
    console.error('取消订单失败:', error)
    message.error('取消订单失败')
  }
}

/**
 * 开始支付状态检查
 * 定时查询订单支付状态，确认支付结果
 */
const startPaymentCheck = (orderNumber) => {
  paymentStatus.value = 'checking'
  currentOrderNumber.value = orderNumber

  // 设置支付超时时间为15分钟（900秒）
  paymentTimeout.value = 900

  // 每2秒检查一次支付状态，最多检查15分钟
  let checkCount = 0
  const maxChecks = 450 // 15分钟 (900秒 / 2秒)

  console.log(`🔄 开始轮询支付状态: 订单号=${orderNumber}`)

  // 启动倒计时定时器
  startCountdown()

  paymentTimer.value = setInterval(async () => {
    checkCount++
    console.log(`🔄 第${checkCount}次检查支付状态`)

    try {
      const result = await orderService.queryOrder(orderNumber)
      console.log(`💳 支付状态查询结果:`, result)

      if (result.status === 100 && result.data) {
        const orderStatus = result.data.订单状态
        console.log(`💳 当前订单状态: ${orderStatus}`)

        if (orderStatus === 'paid') {
          // 支付成功
          console.log('🎉 支付成功！')
          paymentStatus.value = 'success'
          clearInterval(paymentTimer.value)
          clearInterval(countdownTimer.value)
          message.success('支付成功！会员权益已激活')

          // 延迟关闭弹窗并刷新数据
          setTimeout(() => {
            closePaymentModal()
          }, 2000)
          return
        }

        // 更新支付状态为检查中（显示动画）
        if (paymentStatus.value === 'waiting') {
          paymentStatus.value = 'checking'
        }
      }

      // 检查是否超时
      if (checkCount >= maxChecks) {
        console.log('⏰ 支付状态检查超时')
        clearInterval(paymentTimer.value)
        clearInterval(countdownTimer.value)
        paymentStatus.value = 'timeout'
        message.warning('支付状态检查超时，请手动刷新页面确认支付结果')
      }
    } catch (error) {
      console.error('❌ 查询支付状态失败:', error)
      if (checkCount >= maxChecks) {
        clearInterval(paymentTimer.value)
        clearInterval(countdownTimer.value)
        paymentStatus.value = 'error'
        message.error('支付状态查询失败，请手动刷新页面确认')
      }
    }
  }, 2000) // 改为2秒检查一次，更及时
}

/**
 * 启动倒计时
 * 每秒更新一次剩余时间
 */
const startCountdown = () => {
  // 清除之前的倒计时定时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }

  countdownTimer.value = setInterval(() => {
    if (paymentTimeout.value > 0) {
      paymentTimeout.value--
    } else {
      // 倒计时结束
      clearInterval(countdownTimer.value)
      clearInterval(paymentTimer.value)
      paymentStatus.value = 'timeout'
      message.warning('订单已过期，请重新创建订单')
    }
  }, 1000)
}

/**
 * 关闭支付弹窗
 * 重置支付相关状态并关闭弹窗
 */
const closePaymentModal = () => {
  // 记录是否支付成功
  const wasSuccessful = paymentStatus.value === 'success'
  
  showPaymentModal.value = false
  paymentQrCode.value = ''
  paymentStatus.value = 'waiting'
  selectedPlan.value = {}
  
  // 清除定时器
  if (paymentTimer.value) {
    clearInterval(paymentTimer.value)
    paymentTimer.value = null
  }

  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  
  // 如果支付成功，刷新页面数据
  if (wasSuccessful) {
    setTimeout(() => {
      // 重新加载用户会员信息和订单历史
      Promise.all([
        loadUserMembership(),
        loadUserOrders()
      ]).then(() => {
        // 显示成功提示
        window.$message?.success('会员服务已成功开通，享受专属权益！')
      }).catch(error => {
        console.error('刷新数据失败:', error)
      })
    }, 500)
  }
}

// ========================= 表格配置 =========================

// 订单历史表格列定义
const orderColumns = [
  {
    title: '订单号',
    dataIndex: '订单号',
    key: 'orderNumber',
    width: 180
  },
  {
    title: '套餐名称',
    dataIndex: '套餐名称',
    key: 'planName'
  },
  {
    title: '支付周期',
    dataIndex: '支付周期',
    key: 'cycle',
    width: 100
  },
  {
    title: '金额',
    dataIndex: '支付金额',
    key: 'amount',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: '创建时间',
    key: 'createTime',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150
  }
]

/**
 * 获取订单状态对应的颜色
 */
const getOrderStatusColor = (status) => {
  const colorMap = {
    'pending': 'orange',
    'paid': 'green',
    'cancelled': 'red',
    'refunded': 'purple'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取订单状态的中文文本
 */
const getOrderStatusText = (status) => {
  const textMap = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return textMap[status] || status
}

/**
 * 格式化时间显示
 * 将秒数转换为 mm:ss 格式
 */
const formatTime = (seconds) => {
  if (!seconds || seconds <= 0) return '00:00'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// ========================= 生命周期钩子 =========================

/**
 * 组件挂载时初始化数据
 * 延迟执行避免初始化错误导致页面跳转
 */
onMounted(async () => {
  setTimeout(async () => {
    try {
      await Promise.all([
        loadMembershipPlans(),
        loadUserMembership(),
        loadUserOrders()
      ])
    } catch (error) {
      console.error('初始化数据失败:', error)
    }
  }, 100)
})

/**
 * 组件卸载时清理资源
 * 确保定时器被正确清理
 */
onUnmounted(() => {
  if (paymentTimer.value) {
    clearInterval(paymentTimer.value)
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
/* ========================= 主体布局样式 ========================= */
.payment-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.main-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* ========================= Tab容器样式 ========================= */
.payment-tabs {
  margin-top: 24px;
}

.payment-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 24px;
}

.payment-tabs :deep(.ant-tabs-tab) {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

.payment-tabs :deep(.ant-tabs-tab-active) {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white !important;
  border-color: #1890ff;
}

.payment-tabs :deep(.ant-tabs-tab-active .anticon) {
  color: white;
}

.payment-tabs :deep(.ant-tabs-content-holder) {
  padding: 0;
}

.payment-tabs :deep(.ant-tabs-tabpane) {
  padding: 0;
}

/* ========================= 区域标题样式 ========================= */
.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.title-icon {
  margin-right: 8px;
  color: #1890ff;
}

/* ========================= 会员状态展示区域 ========================= */
.membership-status-section {
  margin-bottom: 32px;
}

/* 新的会员状态组件样式在各自的组件文件中定义 */

/* ========================= 套餐选择区域 ========================= */
.plans-section {
  margin-bottom: 0; /* Tab内容不需要底部边距 */
}

.billing-switch {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.discount-tag {
  color: #ff4d4f;
  font-size: 12px;
  margin-left: 4px;
}

.plans-grid {
  margin-top: 16px;
}

.plan-item-card {
  border-radius: 12px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 580px; /* 设置最小高度，让所有卡片至少这么高 */
}

/* 恢复Card默认样式 */
.plan-item-card .ant-card-body {
  padding: 16px !important;
}

.plan-item-card:hover {
  border-color: #1890ff;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.plan-item-card.recommended {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #f0fff0 100%);
}

.recommended-badge {
  position: absolute;
  top: 12px;
  right: -24px;
  background: #52c41a;
  color: white;
  padding: 4px 32px;
  font-size: 12px;
  font-weight: 500;
  transform: rotate(45deg);
  z-index: 1;
}

.plan-content {
  padding: 8px;
  position: relative;
  min-height: 520px; /* 确保内容区域有足够高度 */
}

/* 主要内容区域 */
.plan-main-content {
  flex: 1; /* 占据除按钮外的所有空间 */
}

.plan-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  text-align: center;
}

.plan-desc {
  color: #666;
  text-align: center;
  margin: 0 0 16px 0;
  font-size: 14px;
}

.price-section {
  text-align: center;
  margin-bottom: 16px;
}

.current-price {
  margin-bottom: 4px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
}

.period {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.original-price {
  font-size: 12px;
}

.original {
  color: #999;
  text-decoration: line-through;
  margin-right: 8px;
}

.save {
  color: #ff4d4f;
  font-weight: 500;
}

.features-list {
  margin-bottom: 20px;
  flex: 1; /* 占据剩余空间，将按钮推到底部 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.check-icon {
  color: #52c41a;
  margin-right: 8px;
}

.more-features {
  color: #999;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
}

/* 按钮容器样式 - 绝对定位到底部 */
.select-btn-container {
  position: absolute;
  bottom: 16px;
  left: 8px;
  right: 8px;
}

.select-btn {
  border-radius: 8px;
  height: 44px;
  font-weight: 500;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

/* ========================= 订单历史区域 ========================= */
.history-section {
  margin-bottom: 0; /* Tab内容不需要底部边距 */
}

/* ========================= 支付弹窗样式 ========================= */
.payment-modal-content {
  text-align: center;
}

.payment-info {
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f2ff 100%);
  border-radius: 12px;
  border: 1px solid #e3f2fd;
}

.amount-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.amount-label {
  color: #666;
  font-size: 14px;
}

.amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #ff6b35;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.plan-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.plan-period {
  color: #666;
  font-size: 14px;
}

.discount-info {
  color: #52c41a;
  font-size: 12px;
  font-weight: 500;
}

.qrcode-container {
  margin: 24px 0;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
}

.qrcode-wrapper {
  display: inline-block;
  padding: 16px;
  background: white;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 16px;
}

.scan-instructions {
  text-align: left;
  max-width: 280px;
  margin: 0 auto;
}

.scan-tip {
  display: flex;
  align-items: center;
  margin: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.step {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
  flex-shrink: 0;
}

.payment-status {
  margin: 24px 0;
}

.checking-content {
  padding: 20px;
}

.checking-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.checking-subtitle {
  color: #666;
  font-size: 14px;
  margin: 0 0 16px 0;
}

.checking-tips {
  text-align: left;
  background: #f6f9fc;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 3px solid #1890ff;
}

.checking-tips p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}

.success-info {
  text-align: left;
  background: #f6ffed;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
  margin-top: 16px;
}

.success-info p {
  margin: 8px 0;
  color: #333;
  font-size: 14px;
}

.payment-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* ========================= 响应式设计 ========================= */
@media (max-width: 768px) {
  .payment-management {
    padding: 16px;
  }
  
  .plan-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .plan-status {
    margin-top: 12px;
  }
  
  .membership-details .ant-col {
    margin-bottom: 12px;
  }
  
  .billing-switch {
    margin-bottom: 16px;
  }
  
  .plans-grid .ant-col {
    margin-bottom: 16px;
  }
}

/* 激活码输入区域样式 */
.activation-input-section {
  margin-top: 16px;
}

.activation-card {
  border: 2px solid #e6f7ff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.activation-content {
  padding: 8px 0;
}

.input-hint {
  margin-top: 4px;
}
</style> 