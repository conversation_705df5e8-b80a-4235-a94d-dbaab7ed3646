<template>
  <div class="customer-invitation">
    <a-card title="推广管理" :bordered="false">
      <!-- 推广统计卡片 -->
      <div class="stats-section" style="margin-bottom: 24px;">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="推广链接状态"
              :value="promotionData.推广链接 ? '已生成' : '未生成'"
              :value-style="{ color: promotionData.推广链接 ? '#52c41a' : '#faad14' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="我的邀请码"
              :value-style="{ color: '#1890ff' }"
            >
              <template #formatter>
                <div class="invite-code-display">
                  <a-tooltip v-if="!hasValidInviteCode" placement="top" title="点击前往个人信息页面设置自定义邀请码">
                    <span 
                      class="invite-code-empty"
                      @click="goToPersonalSettings"
                    >
                      <SettingOutlined style="margin-right: 4px;" />
                      未设置
                    </span>
                  </a-tooltip>
                  <a-tooltip v-else placement="top" :title="`点击复制推广链接：${promotionData.自定义邀请码}`">
                    <span 
                      class="invite-code-value"
                      @click="copyPromotionLink"
                    >
                      <CopyOutlined style="margin-right: 4px; flex-shrink: 0;" />
                      <span class="invite-code-text">{{ promotionData.自定义邀请码 }}</span>
                    </span>
                  </a-tooltip>
                </div>
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="推广注册数"
              :value="promotionStats.注册用户数"
              :value-style="{ color: '#722ed1' }"
              suffix="人"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 推广记录 - 暂时隐藏，因为已改为推广链接模式 -->
      <a-card title="推广记录" size="small" v-if="false">
        <!-- 状态说明 -->
        <div class="status-guidance" style="margin-bottom: 16px;">
          <a-alert
            v-if="listFilter.状态筛选 === '待处理'"
            message="待处理推广记录"
            description="这些推广对象尚未注册。您可以复制推广链接再次发送，或者撤销不需要的邀请。"
            type="info"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已注册'"
            message="已注册推广记录"
            description="这些推广对象已成功注册并建立了邀请关系。您可以查看详细信息。"
            type="success"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已过期'"
            message="已过期推广记录"
            description="这些推广邀请已过期。您可以重新发送邀请来延续推广。"
            type="warning"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已拒绝'"
            message="已拒绝推广记录"
            description="这些推广对象拒绝了邀请。您可以重新发送邀请或联系对方了解情况。"
            type="error"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 筛选器 -->
        <div class="filter-section" style="margin-bottom: 16px;">
          <a-row :gutter="16" align="middle">
            <a-col :span="18">
              <!-- 快速筛选按钮 -->
              <a-button-group>
                <a-button
                  :type="listFilter.状态筛选 === '待处理' ? 'primary' : 'default'"
                  @click="handleQuickFilter('待处理')"
                  size="small"
                >
                  待处理 ({{ statistics.待处理 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已注册' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已注册')"
                  size="small"
                >
                  已注册 ({{ statistics.已注册 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已过期' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已过期')"
                  size="small"
                >
                  已过期 ({{ statistics.已过期 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已拒绝' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已拒绝')"
                  size="small"
                >
                  已拒绝 ({{ statistics.已拒绝 }})
                </a-button>
              </a-button-group>
            </a-col>
            <a-col :span="6">
              <a-button @click="loadInvitationList" :loading="listLoading">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 推广列表表格 -->
        <a-table
          :columns="columns"
          :data-source="invitationList"
          :loading="listLoading"
          :pagination="paginationConfig"
          @change="handleTableChange"
          :scroll="{ x: 800 }"
          size="middle"
        >
          <!-- 使用新的插槽语法 -->
          <template #bodyCell="{ column, record }">
            <!-- 推广对象信息 -->
            <template v-if="column.key === 'inviteeInfo'">
              <div class="invitee-info">
                <div class="phone">{{ record.被邀请人手机号 }}</div>
                <div class="name" v-if="record.被邀请人姓名 && record.被邀请人姓名 !== '未注册'">
                  {{ record.被邀请人姓名 }}
                </div>
              </div>
            </template>

            <!-- 推广状态 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.邀请状态)">
                {{ record.邀请状态 }}
              </a-tag>
            </template>

            <!-- 时间信息 -->
            <template v-else-if="column.key === 'timeInfo'">
              <div class="time-info">
                <div class="invite-time">
                  推广：{{ formatDate(record.邀请时间) }}
                </div>
                <div class="expire-time" v-if="record.过期时间">
                  过期：{{ formatDate(record.过期时间) }}
                </div>
                <div class="register-time" v-if="record.注册时间">
                  注册：{{ formatDate(record.注册时间) }}
                </div>
              </div>
            </template>

            <!-- 操作按钮 - 根据状态显示不同操作 -->
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <!-- 待处理状态的操作 -->
                <template v-if="record.邀请状态 === '待处理'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInviteLink(record)"
                  >
                    <template #icon>
                      <LinkOutlined />
                    </template>
                    查看推广链接
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleCopyLink(record)"
                  >
                    <template #icon>
                      <CopyOutlined />
                    </template>
                    复制推广链接
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    danger
                    @click="handleCancelInvitation(record)"
                  >
                    <template #icon>
                      <CloseOutlined />
                    </template>
                    撤销邀请
                  </a-button>
                </template>

                <!-- 已注册状态的操作 -->
                <template v-else-if="record.邀请状态 === '已注册'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInviteeInfo(record)"
                  >
                    <template #icon>
                      <UserOutlined />
                    </template>
                    查看被邀请人
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>

                <!-- 已过期状态的操作 -->
                <template v-else-if="record.邀请状态 === '已过期'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleResendInvitation(record)"
                  >
                    <template #icon>
                      <RedoOutlined />
                    </template>
                    重新发送邀请
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>

                <!-- 已拒绝状态的操作 -->
                <template v-else-if="record.邀请状态 === '已拒绝'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleResendInvitation(record)"
                  >
                    <template #icon>
                      <RedoOutlined />
                    </template>
                    重新发送邀请
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 推广列表 -->
      <a-card title="推广列表" size="small" style="margin-bottom: 24px;">
        <div class="promotion-list-section">
          <!-- 搜索和筛选 -->
          <div class="list-filters" style="margin-bottom: 16px;">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-input-search
                  v-model:value="listFilter.searchKeyword"
                  placeholder="搜索用户昵称或手机号"
                  @search="handleSearch"
                  @change="handleSearchChange"
                  allow-clear
                />
              </a-col>
              <a-col :span="16">
                <div class="list-actions">
                  <a-button @click="loadPromotedUsersList" :loading="listLoading">
                    <template #icon>
                      <ReloadOutlined />
                    </template>
                    刷新
                  </a-button>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 推广用户列表 -->
          <a-table
            :columns="promotedUsersColumns"
            :data-source="promotedUsersList"
            :loading="listLoading"
            :pagination="paginationConfig"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'userInfo'">
                <div class="user-info">
                  <div class="user-name">{{ record.昵称 || '未设置昵称' }}</div>
                  <div class="user-phone">{{ record.手机号 || '未知' }}</div>
                </div>
              </template>
              <template v-else-if="column.key === 'registrationTime'">
                <div class="time-info">
                  <div class="register-time">{{ formatDate(record.registration_time) }}</div>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-card>

      <!-- 推广记录 - 暂时隐藏，因为已改为推广链接模式 -->
      <a-card title="推广记录" size="small" v-if="false">
        <!-- 状态说明 -->
        <div class="status-guidance" style="margin-bottom: 16px;">
          <a-alert
            v-if="listFilter.状态筛选 === '待处理'"
            message="待处理推广记录"
            description="这些推广对象尚未注册。您可以复制推广链接再次发送，或者撤销不需要的邀请。"
            type="info"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已注册'"
            message="已注册推广记录"
            description="这些推广对象已成功注册并建立了邀请关系。您可以查看详细信息。"
            type="success"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已过期'"
            message="已过期推广记录"
            description="这些推广邀请已过期。您可以重新发送邀请来延续推广。"
            type="warning"
            show-icon
            :closable="false"
          />
          <a-alert
            v-else-if="listFilter.状态筛选 === '已拒绝'"
            message="已拒绝推广记录"
            description="这些推广对象拒绝了邀请。您可以重新发送邀请或联系对方了解情况。"
            type="error"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 筛选器 -->
        <div class="filter-section" style="margin-bottom: 16px;">
          <a-row :gutter="16" align="middle">
            <a-col :span="18">
              <!-- 快速筛选按钮 -->
              <a-button-group>
                <a-button
                  :type="listFilter.状态筛选 === '待处理' ? 'primary' : 'default'"
                  @click="handleQuickFilter('待处理')"
                  size="small"
                >
                  待处理 ({{ statistics.待处理 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已注册' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已注册')"
                  size="small"
                >
                  已注册 ({{ statistics.已注册 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已过期' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已过期')"
                  size="small"
                >
                  已过期 ({{ statistics.已过期 }})
                </a-button>
                <a-button
                  :type="listFilter.状态筛选 === '已拒绝' ? 'primary' : 'default'"
                  @click="handleQuickFilter('已拒绝')"
                  size="small"
                >
                  已拒绝 ({{ statistics.已拒绝 }})
                </a-button>
              </a-button-group>
            </a-col>
            <a-col :span="6">
              <a-button @click="loadInvitationList" :loading="listLoading">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 推广列表表格 -->
        <a-table
          :columns="columns"
          :data-source="invitationList"
          :loading="listLoading"
          :pagination="paginationConfig"
          @change="handleTableChange"
          :scroll="{ x: 800 }"
          size="middle"
        >
          <!-- 使用新的插槽语法 -->
          <template #bodyCell="{ column, record }">
            <!-- 推广对象信息 -->
            <template v-if="column.key === 'inviteeInfo'">
              <div class="invitee-info">
                <div class="phone">{{ record.被邀请人手机号 }}</div>
                <div class="name" v-if="record.被邀请人姓名 && record.被邀请人姓名 !== '未注册'">
                  {{ record.被邀请人姓名 }}
                </div>
              </div>
            </template>

            <!-- 推广状态 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.邀请状态)">
                {{ record.邀请状态 }}
              </a-tag>
            </template>

            <!-- 时间信息 -->
            <template v-else-if="column.key === 'timeInfo'">
              <div class="time-info">
                <div class="invite-time">
                  推广：{{ formatDate(record.邀请时间) }}
                </div>
                <div class="expire-time" v-if="record.过期时间">
                  过期：{{ formatDate(record.过期时间) }}
                </div>
                <div class="register-time" v-if="record.注册时间">
                  注册：{{ formatDate(record.注册时间) }}
                </div>
              </div>
            </template>

            <!-- 操作按钮 - 根据状态显示不同操作 -->
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <!-- 待处理状态的操作 -->
                <template v-if="record.邀请状态 === '待处理'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInviteLink(record)"
                  >
                    <template #icon>
                      <LinkOutlined />
                    </template>
                    查看推广链接
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleCopyLink(record)"
                  >
                    <template #icon>
                      <CopyOutlined />
                    </template>
                    复制推广链接
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    danger
                    @click="handleCancelInvitation(record)"
                  >
                    <template #icon>
                      <CloseOutlined />
                    </template>
                    撤销邀请
                  </a-button>
                </template>

                <!-- 已注册状态的操作 -->
                <template v-else-if="record.邀请状态 === '已注册'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInviteeInfo(record)"
                  >
                    <template #icon>
                      <UserOutlined />
                    </template>
                    查看被邀请人
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>

                <!-- 已过期状态的操作 -->
                <template v-else-if="record.邀请状态 === '已过期'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleResendInvitation(record)"
                  >
                    <template #icon>
                      <RedoOutlined />
                    </template>
                    重新发送邀请
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>

                <!-- 已拒绝状态的操作 -->
                <template v-else-if="record.邀请状态 === '已拒绝'">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleResendInvitation(record)"
                  >
                    <template #icon>
                      <RedoOutlined />
                    </template>
                    重新发送邀请
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewInvitationDetail(record)"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    查看详情
                  </a-button>
                </template>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </a-card>

    <!-- 推广链接详情弹窗 -->
    <a-modal
      v-model:open="linkModalVisible"
      title="推广链接详情"
      :footer="null"
      width="600px"
      centered
    >
      <div v-if="selectedInvitation" class="invite-link-detail">
        <a-descriptions :column="2" size="small" style="margin-bottom: 16px;">
          <a-descriptions-item label="推广对象">
            {{ selectedInvitation.被邀请人手机号 }}
          </a-descriptions-item>
          <a-descriptions-item label="推广状态">
            <a-tag :color="getStatusColor(selectedInvitation.邀请状态)">
              {{ selectedInvitation.邀请状态 }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推广时间">
            {{ formatDate(selectedInvitation.邀请时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="过期时间">
            {{ formatDate(selectedInvitation.过期时间) }}
          </a-descriptions-item>
        </a-descriptions>

        <div class="invite-message" v-if="selectedInvitation.邀请消息" style="margin-bottom: 16px;">
          <h4>推广消息：</h4>
          <p>{{ selectedInvitation.邀请消息 }}</p>
        </div>

        <div class="invite-link-section">
          <h4>推广链接：</h4>
          <div class="link-display">
            <a-input
              :value="selectedInvitation.邀请链接"
              readonly
              size="large"
              style="font-family: monospace; margin-bottom: 8px;"
            >
              <template #suffix>
                <a-button
                  type="link"
                  @click="copyInviteLink"
                  :loading="copyLoading"
                >
                  <CopyOutlined />
                  复制
                </a-button>
              </template>
            </a-input>
          </div>

          <div class="link-info">
            <a-alert
              message="分享说明"
              description="请将此推广链接发送给推广对象，他们可以通过此链接注册并加入系统。"
              type="info"
              show-icon
              style="margin-bottom: 16px;"
            />
          </div>

          <div class="link-actions">
            <a-space style="width: 100%; justify-content: flex-end;">
              <a-button @click="linkModalVisible = false">关闭</a-button>
              <a-button type="primary" @click="copyAndCloseLink">
                复制推广链接并关闭
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {
  CloseOutlined,
  CopyOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  LinkOutlined,
  RedoOutlined,
  ReloadOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import customerInvitationService from '../../services/customerInvitationService.js'
import { userAPI } from '../../services/user.js'

// 组件名称
defineOptions({
  name: 'CustomerInvitation'
})

// 路由实例
const router = useRouter()

// 响应式数据
const listLoading = ref(false)
const linkModalVisible = ref(false)
const selectedInvitation = ref(null)
const copyLoading = ref(false)

// 推广链接相关数据
const promotionData = reactive({
  推广链接: '',
  自定义邀请码: '',
  生成时间: ''
})

// 推广统计数据
const promotionStats = reactive({
  注册用户数: 0,
  本月注册数: 0,
  总推广收益: 0
})



// 列表筛选 - 默认显示待处理状态
const listFilter = reactive({
  状态筛选: '待处理',  // 修改：默认显示待处理状态
  searchKeyword: ''   // 新增：搜索关键词
})

// 推广统计
const statistics = reactive({
  总邀请数: 0,
  已注册: 0,
  待处理: 0,
  已过期: 0,
  已拒绝: 0
})

// 推广列表
const invitationList = ref([])

// 推广用户列表（新增）
const promotedUsersList = ref([])

// 推广用户列表表格列配置
const promotedUsersColumns = [
  {
    title: '用户信息',
    key: 'userInfo'
  },
  {
    title: '注册时间',
    key: 'registrationTime'
  }
]

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '推广对象',
    dataIndex: 'inviteeInfo',
    key: 'inviteeInfo',
    width: 150
  },
  {
    title: '推广状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '推广消息',
    dataIndex: '邀请消息',
    key: '邀请消息',
    ellipsis: true,
    width: 200
  },
  {
    title: '时间信息',
    dataIndex: 'timeInfo',
    key: 'timeInfo',
    width: 200
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 计算属性
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadInvitationList()
  },
  onShowSizeChange: (_, size) => {
    pagination.current = 1
    pagination.pageSize = size
    loadInvitationList()
  }
}))

// 计算属性 - 邀请码状态判断
const hasValidInviteCode = computed(() => {
  const code = promotionData.自定义邀请码
  return code && code.trim() !== ''
})

// 工具函数
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const getStatusColor = (status) => {
  const colorMap = {
    '待处理': 'processing',
    '已注册': 'success',
    '已过期': 'default',
    '已拒绝': 'error'
  }
  return colorMap[status] || 'default'
}

  // 推广链接相关方法
  const generatePromotionLink = async () => {
    try {
      const response = await userAPI.generatePromotionLink()

      if (response && response.status === 100) {
        Object.assign(promotionData, response.data)
        message.success('推广链接生成成功')
      } else {
        const errorMsg = response?.message || '生成推广链接失败'
        message.error(errorMsg)
      }
    } catch (error) {
      console.error('生成推广链接失败:', error)
      message.error('生成推广链接失败，请稍后重试')
    }
  }

  // 跳转到个人信息设置页面
  const goToPersonalSettings = async () => {
  try {
    console.log('🔄 开始跳转到个人信息设置页面...')

    // 跳转到系统设置页面的个人信息tab
    await router.push({
      path: '/settings',
      query: { tab: 'personal' }
    })

    console.log('✅ 成功跳转到个人信息设置页面')
    message.success({
      content: '已跳转到个人信息设置页面',
      duration: 2
    })
  } catch (error) {
    console.error('❌ 跳转失败:', error)
    message.error('跳转失败，请手动前往系统设置页面')
  }
}

// 复制推广链接（点击邀请码时调用）
const copyPromotionLink = async () => {
  if (!hasValidInviteCode.value) {
    message.warning('邀请码为空，无法复制推广链接')
    return
  }

  try {
    // 如果推广链接不存在，先生成
    if (!promotionData.推广链接) {
      message.loading('正在生成推广链接...', 0.5)
      await generatePromotionLink()
    }

    // 复制推广链接
    await navigator.clipboard.writeText(promotionData.推广链接)
    message.success({
      content: '推广链接已复制到剪贴板',
      duration: 2
    })
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 加载推广统计数据
const loadPromotionStats = async () => {
  try {
    console.log('开始加载推广统计数据')

    const response = await userAPI.getPromotionStats()

    if (response && response.status === 100) {
      const 统计数据 = response.data.统计数据
      promotionStats.注册用户数 = 统计数据.总推广注册数 || 0
      promotionStats.本月注册数 = 统计数据.本月推广注册数 || 0
      promotionStats.总推广收益 = 0 // 暂时设为0，后续可以添加收益计算

      console.log('推广统计数据加载成功:', promotionStats)
    } else {
      console.warn('推广统计数据加载失败:', response)
      // 使用默认值
      promotionStats.注册用户数 = 0
      promotionStats.本月注册数 = 0
      promotionStats.总推广收益 = 0
    }
  } catch (error) {
    console.error('加载推广统计数据失败:', error)
    // 使用默认值
    promotionStats.注册用户数 = 0
    promotionStats.本月注册数 = 0
    promotionStats.总推广收益 = 0
  }
}

// 快速筛选处理函数
const handleQuickFilter = (status) => {
  listFilter.状态筛选 = status
  pagination.current = 1
  loadInvitationList()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadInvitationList()
}

const handleViewInviteLink = (record) => {
  selectedInvitation.value = record
  linkModalVisible.value = true
}

const handleCopyLink = async (record) => {
  try {
    await navigator.clipboard.writeText(record.邀请链接)
    message.success('推广链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制推广链接')
  }
}

// 优化的复制推广链接功能
const copyInviteLink = async () => {
  try {
    copyLoading.value = true
    await navigator.clipboard.writeText(selectedInvitation.value.邀请链接)
    message.success('推广链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：创建临时输入框进行复制
    const textArea = document.createElement('textarea')
    textArea.value = selectedInvitation.value.邀请链接
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      // 使用已弃用的 execCommand 作为降级处理，确保在旧浏览器中的兼容性
      document.execCommand('copy')
      message.success('推广链接已复制到剪贴板')
    } catch (fallbackError) {
      message.error('复制失败，请手动复制推广链接')
    }
    document.body.removeChild(textArea)
  } finally {
    copyLoading.value = false
  }
}

// 新增：复制推广链接并关闭弹窗
const copyAndCloseLink = async () => {
  await copyInviteLink()
  linkModalVisible.value = false
}

// 新增：状态特定的处理函数
const handleCancelInvitation = async (record) => {
  try {
    // 显示确认对话框
    const confirmed = await new Promise((resolve) => {
      message.confirm({
        title: '确认撤销邀请',
        content: `确定要撤销对 ${record.被邀请人手机号} 的邀请吗？`,
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (confirmed) {
      // 调用撤销邀请的API
      await customerInvitationService.cancelInvitation(record.id)
      message.success('邀请已撤销')

      // 刷新数据
      await Promise.all([
        loadInvitationList(),
        loadStatistics()
      ])
    }
  } catch (error) {
    console.error('撤销邀请失败:', error)
    message.error('撤销邀请失败，请重试')
  }
}

const handleViewInviteeInfo = (record) => {
  // 显示被邀请人信息
  message.info({
    content: `被邀请人：${record.被邀请人手机号}${record.被邀请人姓名 ? ` (${record.被邀请人姓名})` : ''}`,
    duration: 3
  })
}

const handleViewInvitationDetail = (record) => {
  selectedInvitation.value = record
  linkModalVisible.value = true
}

const handleResendInvitation = async (record) => {
  try {
    // 显示确认对话框
    const confirmed = await new Promise((resolve) => {
      message.confirm({
        title: '确认重新发送邀请',
        content: `确定要重新发送邀请给 ${record.被邀请人手机号} 吗？`,
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (confirmed) {
      // 调用重新发送邀请的API
      const result = await customerInvitationService.resendInvitation(record.id)

      if (result.success) {
        message.success('邀请已重新发送')

        // 刷新数据
        await Promise.all([
          loadInvitationList(),
          loadStatistics()
        ])
      } else {
        message.error(result.message || '重新发送邀请失败')
      }
    }
  } catch (error) {
    console.error('重新发送邀请失败:', error)
    message.error('重新发送邀请失败，请重试')
  }
}

// 数据加载函数
const loadStatistics = async () => {
  try {
    // 调用获取统计信息的API
    const result = await customerInvitationService.getInvitationStatistics()

    if (result.success) {
      Object.assign(statistics, result.data)
    } else {
      console.error('获取统计信息失败:', result.message)
      // 使用默认数据
      Object.assign(statistics, {
        总邀请数: 0,
        已注册: 0,
        待处理: 0,
        已过期: 0,
        已拒绝: 0
      })
    }

  } catch (error) {
    console.error('加载统计信息失败:', error)
    // 使用默认数据
    Object.assign(statistics, {
      总邀请数: 0,
      已注册: 0,
      待处理: 0,
      已过期: 0,
      已拒绝: 0
    })
  }
}

const loadInvitationList = async () => {
  try {
    listLoading.value = true

    // 调用获取推广列表的API
    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      状态筛选: listFilter.状态筛选
    }

    const result = await customerInvitationService.getInvitationList(params)

    if (result.success) {
      invitationList.value = result.data.邀请列表 || []
      pagination.total = result.data.总数 || 0
    } else {
      console.error('获取推广列表失败:', result.message)
      message.error(result.message || '加载推广列表失败')
      invitationList.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('加载推广列表失败:', error)
    message.error(error.message || '加载推广列表失败')
    invitationList.value = []
    pagination.total = 0
  } finally {
    listLoading.value = false
  }
}

// 加载推广用户列表
const loadPromotedUsersList = async () => {
  try {
    listLoading.value = true
    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      搜索关键词: listFilter.searchKeyword
    }
    const result = await userAPI.getPromotedUsers(params)

    if (result.status === 100) {
      promotedUsersList.value = result.data.用户列表 || []
      pagination.total = result.data.total_count || 0
    } else {
      console.error('获取推广用户列表失败:', result.message)
      message.error(result.message || '加载推广用户列表失败')
      promotedUsersList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载推广用户列表失败:', error)
    message.error(error.message || '加载推广用户列表失败')
    promotedUsersList.value = []
    pagination.total = 0
  } finally {
    listLoading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  pagination.current = 1
  loadPromotedUsersList()
}

const handleSearchChange = () => {
  // 当搜索框内容变化时，不立即触发搜索，而是等待用户停止输入
  // 例如，用户输入 'a'，然后输入 'ab'，此时应该触发搜索
  // 可以通过 debounce 或 throttle 实现
  // 这里简单处理，直接触发搜索
  handleSearch()
}

// 组件挂载时加载数据
onMounted(async () => {
  // 优化：统一加载用户信息和推广数据，避免重复请求
  await Promise.all([
    loadUserInfoAndGenerateLink(),
    loadPromotionStats(),
    loadPromotedUsersList()
  ])
})

// 统一加载用户信息和生成推广链接
const loadUserInfoAndGenerateLink = async () => {
  try {
    console.log('🔄 开始加载用户信息和生成推广链接...')

    // 首先获取用户完整信息
    const userResponse = await userAPI.getUserFullInfo()

    if (userResponse && userResponse.status === 100 && userResponse.data) {
      // 安全地访问邀请码信息
      const inviteCode = userResponse.data?.用户自定义邀请码 || ''
      promotionData.自定义邀请码 = inviteCode

      console.log('✅ 用户信息加载成功，邀请码:', inviteCode || '未设置')

      // 如果有邀请码，生成推广链接
      if (inviteCode) {
        const linkResponse = await userAPI.generatePromotionLink()
        
        if (linkResponse && linkResponse.status === 100) {
          Object.assign(promotionData, linkResponse.data)
          console.log('✅ 推广链接生成成功')
        } else {
          console.warn('⚠️ 推广链接生成失败:', linkResponse?.message)
        }
      } else {
        console.info('ℹ️ 用户暂未设置自定义邀请码，跳过推广链接生成')
      }
    } else {
      console.warn('⚠️ 获取用户信息失败:', userResponse?.message)
      promotionData.自定义邀请码 = ''
    }
  } catch (error) {
    console.error('❌ 加载用户信息和生成推广链接失败:', error)
    promotionData.自定义邀请码 = ''
  }
}

// 暴露给父组件的方法
defineExpose({
  refresh: async () => {
    await Promise.all([
      loadStatistics(),
      loadInvitationList()
    ])
  }
})
</script>

<style scoped>
.customer-invitation {
  background: #fff;
  border-radius: 8px;
}

.stats-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
}

.stats-section .ant-statistic {
  text-align: center;
}

.stats-section .ant-statistic-title {
  margin-bottom: 8px;
  font-size: 14px;
  color: #8c8c8c;
}

.stats-section .ant-statistic-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 32px;
}

/* 邀请码显示样式 */
.invite-code-display {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
}

.invite-code-empty {
  color: #faad14;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: underline;
  text-decoration-style: dashed;
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(250, 173, 20, 0.1);
  border: 1px dashed #faad14;
  white-space: nowrap;
}

.invite-code-empty:hover {
  color: #ff7a45;
  background: rgba(255, 122, 69, 0.15);
  border-color: #ff7a45;
  text-decoration-style: solid;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(250, 173, 20, 0.3);
}

.invite-code-value {
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid rgba(24, 144, 255, 0.3);
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.invite-code-value:hover {
  color: #40a9ff;
  background: rgba(24, 144, 255, 0.2);
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 邀请码文本部分 */
.invite-code-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
  display: inline-block;
  font-size: 13px;
}

.invitee-info .phone {
  font-weight: 500;
  color: #262626;
}

.invitee-info .name {
  font-size: 12px;
  color: #8c8c8c;
}

/* 推广用户列表样式 */
.promotion-list-section {
  margin-top: 8px;
}

.list-filters {
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
}

.list-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.user-phone {
  font-size: 12px;
  color: #8c8c8c;
}

.time-info {
  font-size: 12px;
}

.time-info .invite-time {
  color: #262626;
}

.time-info .expire-time,
.time-info .register-time {
  color: #8c8c8c;
  margin-top: 2px;
}

.invite-link-detail h4 {
  margin-bottom: 8px;
  color: #262626;
}

.invite-message p {
  background: #f6f6f6;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 0;
}

.link-actions {
  text-align: right;
}

.filter-section {
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .invite-code-display {
    font-size: 13px;
  }

  .invite-code-value,
  .invite-code-empty {
    padding: 6px 10px;
    font-size: 12px;
  }

  .invite-code-text {
    max-width: 120px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .invite-code-display {
    font-size: 12px;
  }

  .invite-code-value,
  .invite-code-empty {
    padding: 4px 8px;
    font-size: 11px;
  }

  .invite-code-text {
    max-width: 90px;
    font-size: 11px;
  }
}
</style>
