<template>
  <div class="personal-info-settings">
    <!-- 个人信息卡片 -->
    <a-card title="个人信息" :bordered="false" class="settings-card">
      <div class="profile-section">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <a-avatar
              :size="80"
              :src="userInfo.avatar"
              class="user-avatar"
            >
              <template #icon>
                <UserOutlined />
              </template>
              {{ !userInfo.avatar ? (userInfo.name?.charAt(0) || 'U') : '' }}
            </a-avatar>
            <a-button 
              type="text" 
              size="small" 
              class="avatar-edit-btn"
              @click="showAvatarUpload = true"
            >
              <template #icon>
                <CameraOutlined />
              </template>
              更换头像
            </a-button>
          </div>
        </div>

        <!-- 基本信息表单 -->
        <div class="info-form-section">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
            class="personal-form"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="昵称" name="nickname">
                  <a-input
                    v-model:value="formData.nickname"
                    placeholder="请输入昵称"
                    :prefix="h(UserOutlined)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="角色" name="roleName">
                  <a-input
                    v-model:value="formData.roleName"
                    placeholder="用户角色"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="手机号" name="phone">
                  <a-input
                    v-model:value="formData.phone"
                    placeholder="请输入手机号"
                    :prefix="h(PhoneOutlined)"
                    disabled
                  />
                  <div class="form-tip">手机号不可修改，如需更换请联系客服</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮箱" name="email">
                  <a-input
                    v-model:value="formData.email"
                    placeholder="请输入邮箱地址"
                    :prefix="h(MailOutlined)"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 用户自定义邀请码 - 仅代理用户显示 -->
            <a-row :gutter="16" v-if="userInfo.代理类型表id">
              <a-col :span="12">
                <a-form-item label="自定义邀请码" name="customInviteCode">
                  <a-input
                    v-model:value="formData.customInviteCode"
                    placeholder="请输入自定义邀请码"
                    :prefix="h(KeyOutlined)"
                    :maxlength="200"
                    show-count
                    :class="{ 'success-input': saveSuccess && !hasUnsavedChanges }"
                  />
                  <div class="form-tip">
                    自定义邀请码用于推广，最多200个字符
                    <br>
                    <span style="color: #ff4d4f;">注意：邀请码必须唯一，不能与其他用户重复</span>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <a-space>
                <a-button
                  type="primary"
                  :loading="loading"
                  @click="handleSave"
                  :class="{ 'save-success': saveSuccess }"
                >
                  <template #icon>
                    <SaveOutlined v-if="!saveSuccess" />
                    <CheckOutlined v-else />
                  </template>
                  {{ saveButtonText }}
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-space>

              <!-- 保存状态提示 -->
              <div v-if="hasUnsavedChanges" class="save-status unsaved">
                <ExclamationCircleOutlined />
                有未保存的更改
              </div>
              <div v-else-if="saveSuccess" class="save-status saved">
                <CheckCircleOutlined />
                所有更改已保存
              </div>
            </div>
          </a-form>
        </div>
      </div>
    </a-card>

    <!-- 账户安全卡片 -->
    <a-card title="账户安全" :bordered="false" class="settings-card security-card">
      <div class="security-section">
        <!-- 密码安全 -->
        <div class="security-item">
          <div class="security-info">
            <LockOutlined class="security-icon" />
            <div class="security-content">
              <h4>登录密码</h4>
              <p>定期更换密码，保障账户安全</p>
            </div>
          </div>
          <a-button type="primary" ghost @click="showPasswordModal = true">
            修改密码
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 账户统计卡片 -->
    <a-card title="账户统计" :bordered="false" class="settings-card stats-card">
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="stat-item">
              <div class="stat-icon">
                <ThunderboltOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ userInfo.算力值 || 0 }}</div>
                <div class="stat-label">算力值</div>
              </div>
            </div>
          </a-col>
          <!-- 移除每日邀约次数显示，但保留后端业务逻辑 -->
          <a-col :span="12">
            <div class="stat-item">
              <div class="stat-icon">
                <CalendarOutlined />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatDate(userInfo.创建时间) }}</div>
                <div class="stat-label">注册时间</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 样品管理偏好设置卡片 -->
    <a-card title="样品管理偏好" :bordered="false" class="settings-card sample-preferences-card">
      <div class="sample-preferences-section">
        <div class="preference-item">
          <div class="preference-info">
            <div class="preference-icon">
              <GiftOutlined />
            </div>
            <div class="preference-content">
              <h4>样品自动审核</h4>
              <p>开启后，申请自己产品的样品时将自动通过审核，无需等待</p>
              <div class="preference-note">
                <ExclamationCircleOutlined style="color: #faad14; margin-right: 4px;" />
                仅对您自己的产品生效，不能审核其他用户的申请
              </div>
            </div>
          </div>
          <div class="preference-control">
            <a-switch
              v-model:checked="autoAuditEnabled"
              :loading="autoAuditLoading"
              @change="handleAutoAuditChange"
            />
            <div class="switch-label">
              {{ autoAuditEnabled ? '已开启' : '已关闭' }}
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 头像上传弹窗 -->
    <a-modal
      v-model:open="showAvatarUpload"
      title="更换头像"
      @ok="handleAvatarUpload"
      @cancel="showAvatarUpload = false"
    >
      <a-upload
        v-model:file-list="avatarFileList"
        name="avatar"
        list-type="picture-card"
        class="avatar-uploader"
        :show-upload-list="false"
        :before-upload="beforeUpload"
        @change="handleAvatarChange"
      >
        <div v-if="imageUrl">
          <img :src="imageUrl" alt="avatar" style="width: 100%" />
        </div>
        <div v-else>
          <loading-outlined v-if="avatarLoading"></loading-outlined>
          <plus-outlined v-else></plus-outlined>
          <div class="ant-upload-text">上传头像</div>
        </div>
      </a-upload>
    </a-modal>

    <!-- 修改密码弹窗 -->
    <PasswordChangeModal
      v-model:open="showPasswordModal"
      @success="handlePasswordChangeSuccess"
    />
  </div>
</template>

<script setup>
import {
    CalendarOutlined,
    CameraOutlined,
    CheckCircleOutlined,
    CheckOutlined,
    ExclamationCircleOutlined,
    GiftOutlined,
    KeyOutlined,
    LoadingOutlined,
    LockOutlined,
    MailOutlined,
    PhoneOutlined,
    PlusOutlined,
    ReloadOutlined,
    SaveOutlined,
    ThunderboltOutlined,
    UserOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { userAPI } from '../../services/user'
import { useUserStore } from '../../store/user'

// 导入子组件
import PasswordChangeModal from '../user/PasswordChangeModal.vue'

// 个人信息设置组件 - 系统设置模块的子组件
defineOptions({
  name: 'PersonalInfoSettings'
})

const emit = defineEmits(['save'])
const userStore = useUserStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const showAvatarUpload = ref(false)
const showPasswordModal = ref(false)

const avatarLoading = ref(false)
const avatarFileList = ref([])
const imageUrl = ref('')

// 样品自动审核相关状态
const autoAuditEnabled = ref(false)
const autoAuditLoading = ref(false)

// 新增：保存状态相关
const saveSuccess = ref(false)
const hasUnsavedChanges = ref(false)
const originalFormData = ref({})  // 用于比较是否有变更

// 用户信息
const userInfo = reactive({
  id: '',
  username: '',
  name: '',
  phone: '',
  email: '',
  avatar: '',
  roleName: '',
  status: 1,
  createTime: '',
  lastLoginTime: '',
  算力值: 0,
  每日邀约次数: 0,
  代理类型表id: null,  // 新增：代理类型表id字段
  用户自定义邀请码: ''  // 新增：用户自定义邀请码字段
})

// 表单数据
const formData = reactive({
  nickname: '',
  phone: '',
  email: '',
  roleName: '',
  customInviteCode: ''  // 新增：自定义邀请码表单字段
})

// 表单验证规则
const rules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在2到20个字符', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
      message: '昵称只能包含中文、英文、数字、下划线和短横线',
      trigger: 'blur'
    }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  customInviteCode: [
    { max: 200, message: '自定义邀请码长度不能超过200个字符', trigger: 'blur' }
  ]
}

// 计算属性
const saveButtonText = computed(() => {
  if (loading.value) return '保存中...'
  if (saveSuccess.value) return '已保存'
  return '保存信息'
})

// 监听表单数据变化

watch(
  () => [formData.nickname, formData.email, formData.customInviteCode],
  () => {
    // 检查是否有未保存的更改
    const hasChanges =
      formData.nickname !== originalFormData.value.nickname ||
      formData.email !== originalFormData.value.email ||
      formData.customInviteCode !== originalFormData.value.customInviteCode

    hasUnsavedChanges.value = hasChanges

    // 如果有变更，清除保存成功状态
    if (hasChanges && saveSuccess.value) {
      saveSuccess.value = false
    }
  },
  { deep: true }
)

// 初始化用户信息
const initUserInfo = async () => {
  try {
    console.log('🔄 获取用户信息...')

    // 首先尝试从store获取缓存的用户信息
    const storeUserInfo = userStore.userInfo
    if (storeUserInfo && storeUserInfo.id) {
      console.log('📦 从store获取用户信息:', storeUserInfo)
      Object.assign(userInfo, {
        id: storeUserInfo.id,
        username: storeUserInfo.username || '',
        name: storeUserInfo.nickname || storeUserInfo.name || '未设置昵称',
        phone: storeUserInfo.phone,
        email: storeUserInfo.email || '',
        avatar: storeUserInfo.avatar || '',
        roleName: storeUserInfo.role || '普通用户',
        status: storeUserInfo.status || 1,
        createTime: storeUserInfo.createTime || '',
        lastLoginTime: storeUserInfo.lastLoginTime || '',
        算力值: storeUserInfo.算力值 || 0,
        每日邀约次数: storeUserInfo.每日邀约次数 || 30
      })

      // 同步到表单数据
      syncFormData()
    }

    // 然后尝试从API获取最新信息
    const response = await userAPI.getUserFullInfo()

    // 检查统一响应格式
    if (response?.status === 100 && response?.data) {
      const 用户数据 = response.data

      Object.assign(userInfo, {
        用户id: 用户数据.用户id,
        用户名: 用户数据.用户名 || '',
        昵称: 用户数据.昵称 || '未设置昵称',
        手机号: 用户数据.手机号,
        邮箱: 用户数据.邮箱 || '',
        头像: 用户数据.头像 || '',
        角色名称: 用户数据.角色名称 || '普通用户',
        状态: 用户数据.状态 || 1,
        等级: 用户数据.等级 || 1,
        经验值: 用户数据.经验值 || 0,
        创建时间: 用户数据.创建时间 || '',
        上次登录时间: 用户数据.上次登录时间 || '',
        算力值: 用户数据.算力值 || 0,
        每日邀约次数: 用户数据.每日邀约次数 || 30,
        代理类型表id: 用户数据.代理类型表id,  // 新增：代理类型表id字段
        用户自定义邀请码: 用户数据.用户自定义邀请码 || ''  // 新增：用户自定义邀请码字段
      })

      // 同步到表单数据
      syncFormData()
    } else {
      throw new Error('获取用户信息失败')
    }
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error)
    message.error('获取用户信息失败，请刷新页面重试')
  }
}

// 同步表单数据
const syncFormData = () => {
  const newFormData = {
    nickname: userInfo.昵称 || '',
    phone: userInfo.手机号 || '',
    email: userInfo.邮箱 || '',
    roleName: userInfo.角色名称 || '',
    customInviteCode: userInfo.用户自定义邀请码 || ''  // 新增：同步自定义邀请码
  }

  Object.assign(formData, newFormData)

  // 保存原始数据用于比较变更
  originalFormData.value = { ...newFormData }

  // 重置状态
  hasUnsavedChanges.value = false
  saveSuccess.value = false
}

// 更新结果收集器
const updateResults = {
  nickname: { success: false, message: '', isFirstTime: false },
  inviteCode: { success: false, message: '' },
  hasChanges: false
}

// 保存个人信息
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    loading.value = true
    console.log('🔄 开始更新个人信息...', formData)

    // 重置更新结果
    updateResults.nickname = { success: false, message: '', isFirstTime: false }
    updateResults.inviteCode = { success: false, message: '' }
    updateResults.hasChanges = false

    // 检查昵称是否有变化
    if (formData.nickname !== userInfo.昵称) {
      console.log('🔄 昵称有变化，调用API更新:', {
        原昵称: userInfo.昵称,
        新昵称: formData.nickname
      })

      const nicknameResponse = await userAPI.setNickname({
        昵称: formData.nickname
      })

      // 检查响应状态 - 统一响应格式处理
      if (nicknameResponse && nicknameResponse.status === 100) {
        // 成功响应处理
        updateResults.nickname.success = true
        updateResults.nickname.message = nicknameResponse.message || '昵称修改成功'
        updateResults.hasChanges = true

        // 检查是否包含首次设置奖励信息
        if (nicknameResponse.message && nicknameResponse.message.includes('首次设置昵称')) {
          updateResults.nickname.isFirstTime = true
        }

        // 更新本地用户信息
        userInfo.昵称 = formData.nickname

        // 更新用户store中的信息
        userStore.updateUserInfo({
          昵称: formData.nickname
        })
      } else {
        // 处理API返回的错误信息
        console.warn('⚠️ 昵称更新失败:', nicknameResponse)

        // 提取错误信息
        let errorMsg = '昵称更新失败，请重试'

        if (nicknameResponse) {
          // 优先使用message字段
          if (nicknameResponse.message) {
            errorMsg = nicknameResponse.message
          }
          // 兼容旧的msg字段
          else if (nicknameResponse.msg) {
            errorMsg = nicknameResponse.msg
          }
          // 处理字段验证错误
          else if (nicknameResponse.data && nicknameResponse.data.errors) {
            const fieldErrors = nicknameResponse.data.errors
            if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
              errorMsg = fieldErrors[0].msg || fieldErrors[0].message || errorMsg
            }
          }
        }

        // 记录错误信息
        updateResults.nickname.success = false
        updateResults.nickname.message = errorMsg

        // 显示错误消息并中断后续操作
        message.error(errorMsg)
        return
      }
    }

    // 检查自定义邀请码是否有变化（仅代理用户）
    if (userInfo.代理类型表id && formData.customInviteCode !== userInfo.用户自定义邀请码) {
      console.log('🔄 自定义邀请码有变化，调用API更新:', {
        原邀请码: userInfo.用户自定义邀请码,
        新邀请码: formData.customInviteCode
      })

      const inviteCodeResponse = await userAPI.updateCustomInviteCode({
        用户自定义邀请码: formData.customInviteCode
      })

      // 检查响应状态
      if (inviteCodeResponse && inviteCodeResponse.status === 100) {
        // 更新本地用户信息
        userInfo.用户自定义邀请码 = formData.customInviteCode

        // 记录成功信息
        updateResults.inviteCode.success = true
        updateResults.inviteCode.message = '自定义邀请码更新成功'
        updateResults.hasChanges = true

        console.log('✅ 自定义邀请码更新成功')
      } else {
        console.warn('⚠️ 自定义邀请码更新失败:', inviteCodeResponse)
        const errorMsg = inviteCodeResponse?.message || '自定义邀请码更新失败，请重试'

        // 记录错误信息
        updateResults.inviteCode.success = false
        updateResults.inviteCode.message = errorMsg

        // 如果是邀请码重复错误，显示更长时间的提示
        if (errorMsg.includes('已被其他用户使用')) {
          message.error({
            content: errorMsg,
            duration: 5  // 显示5秒
          })
        } else {
          message.error(errorMsg)
        }

        // 重置保存成功状态
        saveSuccess.value = false
        return  // 如果邀请码更新失败，不继续执行后续逻辑
      }
    }

    // 生成智能合并提示
    generateSmartSuccessMessage()

    // 设置保存成功状态
    saveSuccess.value = true
    hasUnsavedChanges.value = false

    // 更新原始数据
    originalFormData.value = {
      nickname: formData.nickname,
      email: formData.email,
      customInviteCode: formData.customInviteCode
    }

    // 3秒后自动清除成功状态
    setTimeout(() => {
      saveSuccess.value = false
    }, 3000)

    // 通知父组件保存完成
    emit('save', 'personal', {
      nickname: formData.nickname,
      email: formData.email,
      customInviteCode: formData.customInviteCode
    })

  } catch (error) {
    console.error('❌ 更新个人信息失败:', error)

    // 区分不同类型的错误
    if (error.errorFields && Array.isArray(error.errorFields)) {
      // 表单验证错误
      const firstError = error.errorFields[0]
      const errorMsg = firstError?.errors?.[0] || '请检查表单填写是否正确'
      message.error(errorMsg)
    } else if (error.response) {
      // HTTP响应错误
      const errorMsg = error.response.data?.message ||
                      error.response.data?.msg ||
                      `请求失败 (${error.response.status})`
      message.error(errorMsg)
    } else if (error.message) {
      // 其他API错误
      message.error(error.message)
    } else {
      // 未知错误
      message.error('更新失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 生成智能合并提示消息
const generateSmartSuccessMessage = () => {
  // 如果没有任何变化，显示默认消息
  if (!updateResults.hasChanges) {
    console.log('ℹ️ 个人信息无变化')
    message.success('个人信息保存成功')
    return
  }

  const successItems = []
  const failedItems = []
  let specialMessage = ''

  // 检查昵称更新结果
  if (updateResults.nickname.success) {
    if (updateResults.nickname.isFirstTime) {
      // 首次设置昵称有特殊奖励
      specialMessage = updateResults.nickname.message
    } else {
      successItems.push(`昵称已更新为"${formData.nickname}"`)
    }
  } else if (updateResults.nickname.message) {
    failedItems.push(`昵称更新失败：${updateResults.nickname.message}`)
  }

  // 检查邀请码更新结果
  if (updateResults.inviteCode.success) {
    successItems.push('自定义邀请码已更新')
  } else if (updateResults.inviteCode.message) {
    failedItems.push(`邀请码更新失败：${updateResults.inviteCode.message}`)
  }

  // 生成最终提示消息
  let finalMessage = ''

  if (specialMessage) {
    // 如果有首次设置昵称的特殊消息，优先显示
    finalMessage = specialMessage
  } else if (successItems.length > 0 && failedItems.length === 0) {
    // 全部成功
    if (successItems.length === 1) {
      finalMessage = successItems[0]
    } else {
      finalMessage = `个人信息已更新：${successItems.join('、')}`
    }
  } else if (successItems.length > 0 && failedItems.length > 0) {
    // 部分成功
    finalMessage = `部分更新成功：${successItems.join('、')}；${failedItems.join('、')}`
  } else if (failedItems.length > 0) {
    // 全部失败（这种情况应该在前面就return了，但为了完整性还是处理）
    finalMessage = `更新失败：${failedItems.join('、')}`
  } else {
    // 默认消息
    finalMessage = '个人信息保存成功'
  }

  // 显示最终消息
  if (failedItems.length > 0) {
    message.warning(finalMessage)
  } else {
    message.success(finalMessage)
  }

  console.log('🎯 智能提示消息:', finalMessage)
}

// 重置表单
const handleReset = () => {
  syncFormData()
  message.info('表单已重置')
}

// ==================== 样品自动审核相关方法 ====================

// 加载自动审核设置
const loadAutoAuditSettings = async () => {
  try {
    console.log('🔄 加载自动审核设置...')

    const response = await userAPI.getAutoAuditSettings()

    if (response && response.status === 100) {
      autoAuditEnabled.value = response.data.是否自动审核 === 1
      console.log('✅ 自动审核设置加载成功:', {
        是否自动审核: response.data.是否自动审核,
        设置状态: response.data.设置状态
      })
    } else {
      console.warn('⚠️ 获取自动审核设置失败:', response)
      // 默认设置为关闭
      autoAuditEnabled.value = false
    }
  } catch (error) {
    console.error('❌ 加载自动审核设置失败:', error)
    // 默认设置为关闭
    autoAuditEnabled.value = false
  }
}

// 处理自动审核开关变化
const handleAutoAuditChange = async (checked) => {
  try {
    autoAuditLoading.value = true
    console.log('🔄 更新自动审核设置:', checked)

    const response = await userAPI.setAutoAuditSettings({
      是否自动审核: checked ? 1 : 0
    })

    if (response && response.status === 100) {
      const statusText = checked ? '开启' : '关闭'
      message.success(`样品自动审核已${statusText}`)

      console.log('✅ 自动审核设置更新成功:', {
        新设置: checked,
        响应: response.data
      })

      // 通知父组件设置已更改
      emit('save', 'sample-preferences', {
        autoAudit: checked
      })
    } else {
      // 更新失败，恢复原状态
      autoAuditEnabled.value = !checked
      const errorMsg = response?.message || '设置更新失败'
      message.error(errorMsg)
      console.error('❌ 自动审核设置更新失败:', response)
    }
  } catch (error) {
    // 更新失败，恢复原状态
    autoAuditEnabled.value = !checked
    console.error('❌ 自动审核设置更新异常:', error)

    let errorMsg = '设置更新失败，请重试'
    if (error.response?.data?.message) {
      errorMsg = error.response.data.message
    } else if (error.message) {
      errorMsg = error.message
    }

    message.error(errorMsg)
  } finally {
    autoAuditLoading.value = false
  }
}

// 头像上传前验证
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return false // 阻止自动上传，手动处理
}

// 头像变化处理
const handleAvatarChange = (info) => {
  if (info.file) {
    // 预览图片
    const reader = new FileReader()
    reader.addEventListener('load', () => {
      imageUrl.value = reader.result
    })
    reader.readAsDataURL(info.file)
  }
}

// 头像上传处理
const handleAvatarUpload = async () => {
  if (!imageUrl.value) {
    message.warning('请先选择头像图片')
    return
  }

  try {
    avatarLoading.value = true

    // 这里应该调用头像上传API
    // const response = await userAPI.uploadAvatar(avatarFile)

    // 模拟上传成功
    userInfo.avatar = imageUrl.value
    showAvatarUpload.value = false
    message.success('头像更新成功')

  } catch (error) {
    console.error('❌ 头像上传失败:', error)
    message.error('头像上传失败，请重试')
  } finally {
    avatarLoading.value = false
  }
}

// 密码修改成功处理
const handlePasswordChangeSuccess = () => {
  message.success('密码修改成功')
  // 可以在这里处理密码修改后的逻辑，比如重新登录
}

// 移除未使用的handleUpdateSuccess函数，避免重复提示



// 工具函数：格式化日期
const formatDate = (dateStr) => {
  console.log('🔍 formatDate被调用，参数:', dateStr)

  if (!dateStr) {
    console.log('⚠️ 日期字符串为空，返回未知')
    return '未知'
  }

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      console.log('❌ 无效日期:', dateStr)
      return '未知'
    }

    const formatted = date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })

    return formatted
  } catch (error) {
    console.error('❌ 日期格式化错误:', error)
    return '未知'
  }
}

// 组件挂载时初始化
onMounted(() => {
  initUserInfo()
  loadAutoAuditSettings()
})
</script>

<style scoped>
.personal-info-settings {
  padding: 0;
  background: transparent; /* 确保背景透明，继承父容器背景 */
}

.settings-card {
  margin-bottom: 24px;
  border-radius: 12px;
  background: #ffffff; /* 明确设置白色背景 */
  border: 1px solid #f0f0f0; /* 添加浅色边框 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.settings-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9; /* 悬停时边框颜色稍深 */
}

/* 个人信息区域 */
.profile-section {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  border: 3px solid #f0f0f0;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  border-color: #1890ff;
}

.avatar-edit-btn {
  color: #666;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
}

.avatar-edit-btn:hover {
  color: #1890ff;
}

.info-form-section {
  flex: 1;
  min-width: 0;
}

.personal-form {
  margin-top: 0;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.form-actions {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* 安全设置区域 */
.security-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa; /* 保持浅灰色背景 */
  border: 1px solid #f0f0f0; /* 添加边框 */
  border-radius: 8px;
  transition: all 0.3s ease;
}

.security-item:hover {
  background: #f0f0f0; /* 悬停时稍微深一点的灰色 */
  border-color: #d9d9d9;
}

.security-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.security-icon {
  font-size: 20px;
  color: #1890ff;
  flex-shrink: 0;
}

.security-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.security-content p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
}

/* 统计区域 */
.stats-section {
  padding: 8px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.stat-icon {
  font-size: 24px;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
}

/* 头像上传 */
.avatar-uploader {
  display: flex;
  justify-content: center;
}

.avatar-uploader .ant-upload {
  width: 120px;
  height: 120px;
}

.avatar-uploader .ant-upload-select {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-section {
    flex-direction: column;
    gap: 16px;
  }

  .avatar-section {
    align-self: center;
  }

  .security-item {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .security-info {
    flex-direction: column;
    text-align: center;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}

/* 确保浅色主题一致性 */
.settings-card,
.security-item {
  background: #ffffff !important; /* 强制白色背景 */
}

.security-item {
  background: #fafafa !important; /* 安全设置项保持浅灰背景 */
}

.form-actions {
  border-top-color: #f0f0f0; /* 统一边框颜色 */
}

/* 样品管理偏好设置区域 */
.sample-preferences-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.preference-item:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

.preference-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.preference-icon {
  font-size: 24px;
  color: #52c41a;
  flex-shrink: 0;
  margin-top: 2px;
}

.preference-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.preference-content p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.preference-note {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #faad14;
  background: #fffbe6;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #ffe58f;
}

.preference-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.switch-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  min-width: 40px;
}

/* 保存状态样式 */
.save-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  margin-top: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.save-status.unsaved {
  color: #faad14;
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

.save-status.saved {
  color: #52c41a;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

/* 保存成功按钮样式 */
.save-success {
  background: #52c41a !important;
  border-color: #52c41a !important;
  animation: saveSuccess 0.3s ease;
}

@keyframes saveSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 自定义邀请码输入框成功状态 */
.ant-input:focus.success-input {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preference-item {
    flex-direction: column;
    gap: 16px;
  }

  .preference-control {
    flex-direction: row;
    align-self: stretch;
    justify-content: space-between;
  }
}
</style>
