<template>
  <div class="membership-timeline-card">
    <a-card :bordered="false" class="timeline-card">
      <div class="timeline-header">
        <div class="header-title">
          <history-outlined class="header-icon" />
          <h4>所有会员记录</h4>
          <a-badge :count="allMemberships.length" :color="'#722ed1'" />
        </div>
        <a-button 
          type="text" 
          @click="toggleTimeline" 
          class="toggle-btn"
        >
          {{ showTimeline ? '收起' : '展开' }}
          <component :is="showTimeline ? 'up-outlined' : 'down-outlined'" />
        </a-button>
      </div>
      
      <div v-show="showTimeline" class="timeline-content">
        <!-- 时间轴视图 -->
        <div class="timeline-view">
          <a-timeline>
            <!-- 当前生效会员 -->
            <a-timeline-item 
              v-for="membership in currentMemberships" 
              :key="membership.会员id"
              :color="'green'"
            >
              <template #dot>
                <check-circle-outlined class="timeline-icon current" />
              </template>
              <div class="timeline-item">
                <div class="item-header">
                  <component :is="getMembershipIcon(membership)" class="membership-icon current" />
                  <h5 class="membership-name">{{ membership.会员名称 }}</h5>
                  <a-tag color="success">当前生效</a-tag>
                </div>
                <div class="item-content">
                  <p class="time-range">
                    {{ formatDate(membership.开通时间) }} ~ {{ formatDate(membership.到期时间) }}
                  </p>
                  <div class="item-details">
                    <span class="detail-item">剩余：{{ membership.剩余天数 }}天</span>
                    <span class="detail-item">月费：¥{{ membership.每月费用 || 0 }}</span>
                  </div>
                </div>
              </div>
            </a-timeline-item>
            
            <!-- 即将生效会员 -->
            <a-timeline-item 
              v-for="membership in upcomingMemberships" 
              :key="membership.会员id"
              :color="'blue'"
            >
              <template #dot>
                <clock-circle-outlined class="timeline-icon upcoming" />
              </template>
              <div class="timeline-item">
                <div class="item-header">
                  <component :is="getMembershipIcon(membership)" class="membership-icon upcoming" />
                  <h5 class="membership-name">{{ membership.会员名称 }}</h5>
                  <a-tag color="blue">即将生效</a-tag>
                </div>
                <div class="item-content">
                  <p class="time-range">
                    {{ formatDate(membership.开通时间) }} ~ {{ formatDate(membership.到期时间) }}
                  </p>
                  <div class="item-details">
                    <span class="detail-item">有效期：{{ getDuration(membership) }}</span>
                    <span class="detail-item">月费：¥{{ membership.每月费用 || 0 }}</span>
                  </div>
                </div>
              </div>
            </a-timeline-item>
            
            <!-- 历史记录 -->
            <a-timeline-item 
              v-for="membership in membershipHistory" 
              :key="membership.会员id"
              :color="'gray'"
            >
              <template #dot>
                <stop-outlined class="timeline-icon history" />
              </template>
              <div class="timeline-item">
                <div class="item-header">
                  <component :is="getMembershipIcon(membership)" class="membership-icon history" />
                  <h5 class="membership-name">{{ membership.会员名称 }}</h5>
                  <a-tag color="default">已过期</a-tag>
                </div>
                <div class="item-content">
                  <p class="time-range">
                    {{ formatDate(membership.开通时间) }} ~ {{ formatDate(membership.到期时间) }}
                  </p>
                  <div class="item-details">
                    <span class="detail-item">使用时长：{{ getDuration(membership) }}</span>
                    <span class="detail-item">月费：¥{{ membership.每月费用 || 0 }}</span>
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
        
        <!-- 统计信息 -->
        <div class="timeline-stats">
          <a-row :gutter="16">
            <a-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ currentMemberships.length }}</div>
                <div class="stat-label">当前生效</div>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ upcomingMemberships.length }}</div>
                <div class="stat-label">即将生效</div>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ membershipHistory.length }}</div>
                <div class="stat-label">历史记录</div>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ getTotalSpent() }}</div>
                <div class="stat-label">累计消费</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  HistoryOutlined,
  UpOutlined,
  DownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  CrownOutlined,
  GoldOutlined,
  RocketOutlined,
  StarOutlined
} from '@ant-design/icons-vue'

// 组件属性
const props = defineProps({
  allMemberships: {
    type: Array,
    default: () => []
  },
  currentMemberships: {
    type: Array,
    default: () => []
  },
  upcomingMemberships: {
    type: Array,
    default: () => []
  },
  membershipHistory: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const showTimeline = ref(false)

/**
 * 切换时间轴显示
 */
const toggleTimeline = () => {
  showTimeline.value = !showTimeline.value
}

/**
 * 获取会员图标
 */
const getMembershipIcon = (membership) => {
  if (!membership) return StarOutlined
  
  const membershipName = membership.会员名称 || ''
  if (membershipName.includes('企业')) return GoldOutlined
  if (membershipName.includes('专业')) return RocketOutlined
  if (membershipName.includes('高级')) return CrownOutlined
  return StarOutlined
}

/**
 * 格式化日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '日期格式错误'
  }
}

/**
 * 获取会员持续时间
 */
const getDuration = (membership) => {
  if (!membership.开通时间 || !membership.到期时间) return '未知'
  
  try {
    const startDate = new Date(membership.开通时间)
    const endDate = new Date(membership.到期时间)
    const diffTime = endDate - startDate
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) return `${diffDays}天`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月`
    return `${Math.floor(diffDays / 365)}年`
  } catch (error) {
    return '未知'
  }
}

/**
 * 计算累计消费
 */
const getTotalSpent = () => {
  const total = props.allMemberships.reduce((sum, membership) => {
    const monthlyFee = membership.每月费用 || 0
    const duration = getDurationInMonths(membership)
    return sum + (monthlyFee * duration)
  }, 0)
  
  return `¥${total.toFixed(0)}`
}

/**
 * 获取持续时间（月数）
 */
const getDurationInMonths = (membership) => {
  if (!membership.开通时间 || !membership.到期时间) return 0
  
  try {
    const startDate = new Date(membership.开通时间)
    const endDate = new Date(membership.到期时间)
    const diffTime = endDate - startDate
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(1, Math.ceil(diffDays / 30)) // 至少1个月
  } catch (error) {
    return 0
  }
}
</script>

<style scoped>
.membership-timeline-card {
  margin-bottom: 24px;
}

.timeline-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.timeline-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: #722ed1;
}

.header-title h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.toggle-btn:hover {
  color: #1890ff;
}

.timeline-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.timeline-view {
  margin-bottom: 24px;
}

.timeline-item {
  padding: 8px 0;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.membership-icon {
  font-size: 16px;
}

.membership-icon.current {
  color: #52c41a;
}

.membership-icon.upcoming {
  color: #1890ff;
}

.membership-icon.history {
  color: #8c8c8c;
}

.membership-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.item-content {
  margin-left: 24px;
}

.time-range {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
}

.item-details {
  display: flex;
  gap: 16px;
}

.detail-item {
  color: #999;
  font-size: 12px;
}

.timeline-icon {
  font-size: 14px;
}

.timeline-icon.current {
  color: #52c41a;
}

.timeline-icon.upcoming {
  color: #1890ff;
}

.timeline-icon.history {
  color: #8c8c8c;
}

.timeline-stats {
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

/* 自定义时间轴样式 */
:deep(.ant-timeline) {
  padding-left: 0;
}

:deep(.ant-timeline-item) {
  padding-bottom: 16px;
}

:deep(.ant-timeline-item-tail) {
  border-left: 2px solid #f0f0f0;
}

:deep(.ant-timeline-item-head) {
  background: #fff;
  border: 2px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .item-header {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .item-details {
    flex-direction: column;
    gap: 4px;
  }
  
  .timeline-stats .ant-row {
    flex-direction: column;
  }
  
  .timeline-stats .ant-col {
    width: 100% !important;
    margin-bottom: 12px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .stat-item:last-child {
    border-bottom: none;
  }
}
</style> 