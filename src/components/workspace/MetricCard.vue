<template>
  <a-card 
    :bordered="false" 
    :loading="loading"
    class="metric-card"
    :class="{ 'metric-card-hover': !loading }"
    @click="handleClick"
  >
    <template #title>
      <div class="card-title">
        <component 
          :is="titleIcon" 
          :style="{ color: color }"
          class="title-icon"
        />
        <span>{{ title }}</span>
      </div>
    </template>
    
    <div class="metrics-container">
      <div 
        v-for="(metric, index) in metrics" 
        :key="index"
        class="metric-item"
      >
        <div class="metric-main">
          <div class="metric-value">
            <span class="value-number">{{ metric.格式化数值 }}</span>
            <component 
              v-if="metric.图标"
              :is="getIcon(metric.图标)"
              :style="{ color: metric.颜色 || color }"
              class="metric-icon"
            />
          </div>
          <div class="metric-label">{{ metric.标题 }}</div>
        </div>
        
        <div v-if="metric.趋势" class="metric-trend">
          <div 
            class="trend-indicator"
            :class="getTrendClass(metric.趋势类型)"
          >
            <component 
              :is="getTrendIcon(metric.趋势类型)"
              class="trend-icon"
            />
            <span class="trend-text">{{ metric.趋势 }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部统计信息 Bottom Statistics -->
    <div v-if="showSummary" class="card-summary">
      <a-divider style="margin: 12px 0" />
      <div class="summary-stats">
        <span class="summary-label">总计</span>
        <span class="summary-value">{{ totalValue }}</span>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  WechatOutlined,
  TeamOutlined,
  MailOutlined,
  TrophyOutlined,
  UserAddOutlined,
  CheckCircleOutlined,
  FireOutlined,
  ProjectOutlined,
  DollarOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  BarChartOutlined,
  SendOutlined,
  DatabaseOutlined,
  MessageOutlined,
  InteractionOutlined,
  UsergroupAddOutlined,
  ContactsOutlined,
  VideoCameraOutlined
} from '@ant-design/icons-vue'

// Props definition
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  metrics: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: '#1890ff'
  },
  showSummary: {
    type: Boolean,
    default: false
  }
})

// Emits definition
const emit = defineEmits(['click'])

// Icon mapping for different metric types
const iconMap = {
  'wechat': WechatOutlined,
  'team': TeamOutlined,
  'mail': MailOutlined,
  'trophy': TrophyOutlined,
  'user-add': UserAddOutlined,
  'check-circle': CheckCircleOutlined,
  'fire': FireOutlined,
  'project': ProjectOutlined,
  'dollar': DollarOutlined,
  'play-circle': PlayCircleOutlined,
  'clock-circle': ClockCircleOutlined,
  'bar-chart': BarChartOutlined,
  'user': TeamOutlined,
  // 微信运营核心指标专用图标 WeChat core metrics specific icons
  'send': SendOutlined,
  'database': DatabaseOutlined,
  'message': MessageOutlined,
  'interaction': InteractionOutlined,
  'usergroup-add': UsergroupAddOutlined,
  // 达人管理新增指标图标 New talent management metrics icons
  'contacts': ContactsOutlined,
  'video-camera': VideoCameraOutlined
}

// Trend icons mapping
const trendIconMap = {
  'up': ArrowUpOutlined,
  'down': ArrowDownOutlined,
  'stable': MinusOutlined
}

// Computed properties
const titleIcon = computed(() => {
  // Determine title icon based on card title
  // 根据卡片标题确定标题图标
  const titleLower = props.title.toLowerCase()
  if (titleLower.includes('微信')) return WechatOutlined
  if (titleLower.includes('邀约')) return MailOutlined
  if (titleLower.includes('达人')) return UserAddOutlined
  if (titleLower.includes('合作') || titleLower.includes('项目')) return ProjectOutlined
  if (titleLower.includes('团队')) return TeamOutlined
  return BarChartOutlined
})

const totalValue = computed(() => {
  if (!props.showSummary || !props.metrics.length) return 0
  return props.metrics.reduce((sum, metric) => sum + (metric.数值 || 0), 0)
})

// Methods
const getIcon = (iconName) => {
  return iconMap[iconName] || BarChartOutlined
}

const getTrendIcon = (trendType) => {
  return trendIconMap[trendType] || MinusOutlined
}

const getTrendClass = (trendType) => {
  return {
    'trend-up': trendType === 'up',
    'trend-down': trendType === 'down',
    'trend-stable': trendType === 'stable'
  }
}

const handleClick = () => {
  if (!props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.metric-card {
  min-height: 220px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color, #3b82f6) 0%, rgba(147, 51, 234, 0.8) 100%);
  transform: scaleX(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.metric-card-hover {
  cursor: pointer;
}

.metric-card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 24px 40px rgba(0, 0, 0, 0.12);
}

.metric-card-hover:hover::before {
  transform: scaleX(1);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 700;
  font-size: 18px;
  color: #0f172a;
  position: relative;
  z-index: 2;
}

.title-icon {
  font-size: 20px;
  background: linear-gradient(135deg, var(--card-color, #3b82f6) 0%, rgba(147, 51, 234, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metrics-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 2;
  /* 当只有一个指标项时，增加垂直居中和更好的间距 */
  justify-content: center;
  min-height: 120px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 16px; /* 增加垂直内边距，使单个指标项更突出 */
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  border: 1px solid rgba(59, 130, 246, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  border-color: rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.metric-main {
  flex: 1;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.value-number {
  font-size: 32px; /* 增大字体，使单个指标更突出 */
  font-weight: 800;
  background: linear-gradient(135deg, var(--card-color, #3b82f6) 0%, rgba(147, 51, 234, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.metric-icon {
  font-size: 20px;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.2;
}

.metric-trend {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #52c41a;
  background-color: #f6ffed;
}

.trend-down {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.trend-stable {
  color: #8c8c8c;
  background-color: #f5f5f5;
}

.trend-icon {
  font-size: 10px;
}

.trend-text {
  white-space: nowrap;
}

.card-summary {
  margin-top: 8px;
}

.summary-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 14px;
  color: #8c8c8c;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* Responsive design */
@media (max-width: 768px) {
  .metric-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .metric-trend {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .value-number {
    font-size: 20px;
  }
}
</style>
