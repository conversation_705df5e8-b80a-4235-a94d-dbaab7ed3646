<template>
  <a-modal
    v-model:open="visible"
    title="高级筛选"
    width="600px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="advanced-filter-content">
      <a-form
        :model="filterForm"
        layout="vertical"
        @finish="handleApply"
      >
        <!-- 粉丝数范围 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="粉丝数最小值（万）">
              <a-input-number
                v-model:value="filterForm.粉丝数最小值"
                :min="0"
                :max="10000"
                placeholder="最小粉丝数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="粉丝数最大值（万）">
              <a-input-number
                v-model:value="filterForm.粉丝数最大值"
                :min="0"
                :max="10000"
                placeholder="最大粉丝数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 关注数范围 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="关注数最小值">
              <a-input-number
                v-model:value="filterForm.关注数最小值"
                :min="0"
                placeholder="最小关注数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="关注数最大值">
              <a-input-number
                v-model:value="filterForm.关注数最大值"
                :min="0"
                placeholder="最大关注数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 账号状态 -->
        <!-- 
          账号状态筛选选项：
          - 正常（value=null）：筛选账号状态为空或非1的达人
          - 已注销（value=1）：筛选账号状态为1的已注销达人
          与数据库kol.达人表中账号状态字段定义一致
        -->
        <a-form-item label="账号状态">
          <a-select
            v-model:value="filterForm.账号状态"
            placeholder="选择账号状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option :value="null">正常</a-select-option>
            <a-select-option :value="1">已注销</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 认领状态 -->
        <a-form-item label="认领状态">
          <a-select
            v-model:value="filterForm.认领状态"
            placeholder="选择认领状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option :value="true">已认领</a-select-option>
            <a-select-option :value="false">未认领</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 联系方式 -->
        <a-form-item label="联系方式">
          <a-checkbox-group v-model:value="filterForm.联系方式类型">
            <a-checkbox value="微信号">微信号</a-checkbox>
            <a-checkbox value="手机号">手机号</a-checkbox>
            <a-checkbox value="邮箱">邮箱</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <!-- 操作按钮 -->
        <div class="filter-actions">
          <a-space>
            <a-button @click="handleReset">
              重置
            </a-button>
            <a-button @click="handleCancel">
              取消
            </a-button>
            <a-button type="primary" html-type="submit">
              应用筛选
            </a-button>
          </a-space>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  // 控制弹窗显示状态
  open: {
    type: Boolean,
    default: false
  },
  // 初始筛选条件
  filters: {
    type: Object,
    default: () => ({})
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'update:open',  // 更新显示状态
  'apply',        // 应用筛选
  'reset'         // 重置筛选
])

/**
 * 响应式数据
 */
// 弹窗显示状态
const visible = ref(false)

// 筛选表单数据
const filterForm = reactive({
  粉丝数最小值: null,
  粉丝数最大值: null,
  关注数最小值: null,
  关注数最大值: null,
  账号状态: undefined,
  认领状态: undefined,
  联系方式类型: []
})

/**
 * 监听器
 */
// 监听外部 open 状态变化
watch(
  () => props.open,
  (newValue) => {
    visible.value = newValue
  },
  { immediate: true }
)

// 监听内部 visible 状态变化
watch(visible, (newValue) => {
  emit('update:open', newValue)
})

// 监听初始筛选条件变化
watch(
  () => props.filters,
  (newFilters) => {
    Object.assign(filterForm, {
      粉丝数最小值: null,
      粉丝数最大值: null,
      关注数最小值: null,
      关注数最大值: null,
      账号状态: undefined,
      认领状态: undefined,
      联系方式类型: [],
      ...newFilters
    })
  },
  { deep: true, immediate: true }
)

/**
 * 事件处理方法
 */

/**
 * 处理应用筛选
 */
const handleApply = () => {
  // 构建筛选条件对象
  const filters = {}
  
  // 处理数值类型筛选
  if (filterForm.粉丝数最小值 !== null) {
    filters.粉丝数最小值 = filterForm.粉丝数最小值 * 10000
  }
  
  if (filterForm.粉丝数最大值 !== null) {
    filters.粉丝数最大值 = filterForm.粉丝数最大值 * 10000
  }
  
  if (filterForm.关注数最小值 !== null) {
    filters.关注数最小值 = filterForm.关注数最小值
  }
  
  if (filterForm.关注数最大值 !== null) {
    filters.关注数最大值 = filterForm.关注数最大值
  }
  
  // 处理选择类型筛选
  if (filterForm.账号状态 !== undefined) {
    filters.账号状态 = filterForm.账号状态
  }
  
  if (filterForm.认领状态 !== undefined) {
    filters.认领状态 = filterForm.认领状态
  }
  
  if (filterForm.联系方式类型.length > 0) {
    filters.联系方式类型 = filterForm.联系方式类型
  }
  
  // 发送应用事件
  emit('apply', filters)
  
  // 关闭弹窗
  visible.value = false
}

/**
 * 处理重置筛选
 */
const handleReset = () => {
  // 重置表单数据
  Object.assign(filterForm, {
    粉丝数最小值: null,
    粉丝数最大值: null,
    关注数最小值: null,
    关注数最大值: null,
    账号状态: undefined,
    认领状态: undefined,
    联系方式类型: []
  })
  
  // 发送重置事件
  emit('reset')
  
  // 关闭弹窗
  visible.value = false
}

/**
 * 处理取消操作
 */
const handleCancel = () => {
  visible.value = false
}

defineOptions({
  name: 'AdvancedFilterComponent'
})
</script>

<style scoped>
/**
 * 高级筛选组件样式
 */

.advanced-filter-content {
  padding: 16px 0;
}

.filter-actions {
  margin-top: 24px;
  text-align: right;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 数字输入框样式 */
:deep(.ant-input-number) {
  width: 100%;
}

/* 复选框组样式 */
:deep(.ant-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.ant-checkbox-wrapper) {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .advanced-filter-content {
    padding: 8px 0;
  }
  
  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }
  
  .filter-actions {
    margin-top: 16px;
  }
}
</style> 