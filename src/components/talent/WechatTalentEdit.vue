<template>
  <a-modal
    v-model:open="visible"
    title="编辑微信达人信息"
    width="600px"
    :confirm-loading="saving"
    @ok="handleSave"
    @cancel="handleCancel"
    class="wechat-talent-edit-modal"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      class="edit-form"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="微信号" name="微信号">
            <a-input 
              v-model:value="formData.微信号" 
              placeholder="请输入微信号"
              :disabled="!isNewTalent"
            >
              <template #prefix>
                <WechatOutlined />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="昵称" name="昵称">
            <a-input 
              v-model:value="formData.昵称" 
              placeholder="请输入昵称"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="地区" name="地区">
            <a-select
              v-model:value="formData.地区"
              placeholder="请选择地区"
              allow-clear
            >
              <a-select-option value="北京">北京</a-select-option>
              <a-select-option value="上海">上海</a-select-option>
              <a-select-option value="广州">广州</a-select-option>
              <a-select-option value="深圳">深圳</a-select-option>
              <a-select-option value="杭州">杭州</a-select-option>
              <a-select-option value="成都">成都</a-select-option>
              <a-select-option value="重庆">重庆</a-select-option>
              <a-select-option value="武汉">武汉</a-select-option>
              <a-select-option value="南京">南京</a-select-option>
              <a-select-option value="西安">西安</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="性别" name="性别">
            <a-radio-group v-model:value="formData.性别">
              <a-radio value="男">男</a-radio>
              <a-radio value="女">女</a-radio>
              <a-radio value="未知">未知</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-form-item label="个人简介" name="个人简介">
        <a-textarea 
          v-model:value="formData.个人简介" 
          placeholder="请输入个人简介"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>
      
      <a-form-item label="头像URL" name="头像">
        <a-input 
          v-model:value="formData.头像" 
          placeholder="请输入头像URL"
        >
          <template #prefix>
            <PictureOutlined />
          </template>
        </a-input>
      </a-form-item>
      
      <!-- 联系方式 -->
      <a-divider orientation="left">联系方式</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="手机号" name="手机号">
            <a-input 
              v-model:value="formData.手机号" 
              placeholder="请输入手机号"
            >
              <template #prefix>
                <MobileOutlined />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="邮箱" name="邮箱">
            <a-input 
              v-model:value="formData.邮箱" 
              placeholder="请输入邮箱"
            >
              <template #prefix>
                <MailOutlined />
              </template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-form-item label="QQ号" name="QQ号">
        <a-input 
          v-model:value="formData.QQ号" 
          placeholder="请输入QQ号"
        >
          <template #prefix>
            <QqOutlined />
          </template>
        </a-input>
      </a-form-item>
      
      <!-- 认领信息（仅当前用户认领时显示） -->
      <template v-if="isCurrentUserClaimed">
        <a-divider orientation="left">我的认领信息</a-divider>
        
        <a-form-item label="合作状态" name="合作状态">
          <a-select
            v-model:value="formData.合作状态"
            placeholder="请选择合作状态"
          >
            <a-select-option value="未联系">未联系</a-select-option>
            <a-select-option value="已联系">已联系</a-select-option>
            <a-select-option value="洽谈中">洽谈中</a-select-option>
            <a-select-option value="合作中">合作中</a-select-option>
            <a-select-option value="已完成">已完成</a-select-option>
            <a-select-option value="已拒绝">已拒绝</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="个人备注" name="个人备注">
          <a-textarea 
            v-model:value="formData.个人备注" 
            placeholder="请输入个人备注"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item label="个人标签" name="个人标签">
          <div class="tag-input-container">
            <a-input
              v-model:value="newTag"
              placeholder="输入标签后按回车添加"
              @press-enter="addTag"
              class="tag-input"
            />
            <a-button @click="addTag" type="dashed" size="small">
              <PlusOutlined />
              添加
            </a-button>
          </div>
          
          <div class="tag-list" v-if="formData.个人标签 && formData.个人标签.length > 0">
            <a-tag
              v-for="(tag, index) in formData.个人标签"
              :key="index"
              closable
              @close="removeTag(index)"
              color="blue"
            >
              {{ tag }}
            </a-tag>
          </div>
        </a-form-item>
      </template>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  WechatOutlined, UserOutlined, PictureOutlined,
  MobileOutlined, MailOutlined, QqOutlined, PlusOutlined
} from '@ant-design/icons-vue'

// Props定义
const props = defineProps({
  // 是否显示弹窗
  modelValue: {
    type: Boolean,
    default: false
  },
  // 微信达人数据
  talent: {
    type: Object,
    default: null
  },
  // 是否是新建达人
  isNewTalent: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const newTag = ref('')

// 表单数据
const formData = reactive({
  微信号: '',
  昵称: '',
  地区: null,
  性别: null,
  个人简介: '',
  头像: '',
  手机号: '',
  邮箱: '',
  QQ号: '',
  合作状态: '未联系',
  个人备注: '',
  个人标签: []
})

// 表单验证规则
const formRules = {
  微信号: [
    { required: true, message: '请输入微信号', trigger: 'blur' },
    { max: 20, message: '微信号长度不能超过20个字符', trigger: 'blur' }
  ],
  昵称: [
    { max: 30, message: '昵称长度不能超过30个字符', trigger: 'blur' }
  ],
  手机号: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  邮箱: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  个人备注: [
    { max: 500, message: '个人备注不能超过500个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isCurrentUserClaimed = computed(() => {
  return props.talent?.当前用户认领状态?.已认领 || false
})

// 监听达人数据变化，初始化表单
watch(() => props.talent, (newTalent) => {
  if (newTalent) {
    initFormData(newTalent)
  } else {
    resetFormData()
  }
}, { immediate: true })

// 方法定义
/**
 * 初始化表单数据
 */
const initFormData = (talent) => {
  formData.微信号 = talent.微信号 || ''
  formData.昵称 = talent.昵称 || ''
  formData.地区 = talent.地区 || null
  formData.性别 = talent.性别 || null
  formData.个人简介 = talent.个人简介 || ''
  formData.头像 = talent.头像 || ''
  formData.手机号 = ''
  formData.邮箱 = ''
  formData.QQ号 = ''
  
  // 从联系方式列表中提取数据
  if (talent.联系方式列表) {
    talent.联系方式列表.forEach(contact => {
      if (contact.联系类型 === '手机') {
        formData.手机号 = contact.联系内容
      } else if (contact.联系类型 === '邮箱') {
        formData.邮箱 = contact.联系内容
      } else if (contact.联系类型 === 'QQ') {
        formData.QQ号 = contact.联系内容
      }
    })
  }
  
  // 认领信息
  if (talent.认领信息) {
    formData.合作状态 = talent.认领信息.合作状态 || '未联系'
    formData.个人备注 = talent.认领信息.个人备注 || ''
    formData.个人标签 = [...(talent.认领信息.个人标签 || [])]
  }
}

/**
 * 重置表单数据
 */
const resetFormData = () => {
  Object.keys(formData).forEach(key => {
    if (key === '个人标签') {
      formData[key] = []
    } else if (key === '合作状态') {
      formData[key] = '未联系'
    } else {
      formData[key] = key.includes('地区') || key.includes('性别') ? null : ''
    }
  })
}

/**
 * 添加标签
 */
const addTag = () => {
  const tag = newTag.value.trim()
  if (!tag) return
  
  if (formData.个人标签.includes(tag)) {
    message.warning('标签已存在')
    return
  }
  
  if (formData.个人标签.length >= 10) {
    message.warning('最多只能添加10个标签')
    return
  }
  
  formData.个人标签.push(tag)
  newTag.value = ''
}

/**
 * 移除标签
 */
const removeTag = (index) => {
  formData.个人标签.splice(index, 1)
}

/**
 * 处理保存
 */
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    saving.value = true
    
    // 构建保存数据
    const saveData = {
      ...formData,
      达人id: props.talent?.id
    }
    
    // 发出保存事件
    emit('save', saveData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

/**
 * 处理取消
 */
const handleCancel = () => {
  emit('cancel')
}

defineOptions({
  name: 'WechatTalentEdit'
})
</script>

<style scoped>
.wechat-talent-edit-modal :deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

.edit-form {
  padding: 8px 0;
}

.tag-input-container {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.tag-input {
  flex: 1;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 表单项样式优化 */
.edit-form :deep(.ant-form-item-label) {
  font-weight: 500;
}

.edit-form :deep(.ant-input-affix-wrapper) {
  border-radius: 6px;
}

.edit-form :deep(.ant-select) {
  border-radius: 6px;
}

.edit-form :deep(.ant-input) {
  border-radius: 6px;
}

/* 分割线样式 */
.edit-form :deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
  font-weight: 600;
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-input-container {
    flex-direction: column;
  }
}
</style>
