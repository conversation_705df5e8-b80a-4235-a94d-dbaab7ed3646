<template>
  <div class="platform-switcher">
    <!-- 平台切换按钮组 -->
    <div class="switcher-container">
      <div class="switcher-main">
        <a-radio-group
          v-model:value="currentPlatform"
          button-style="solid"
          size="large"
          @change="handlePlatformChange"
          class="platform-buttons"
        >
          <a-radio-button value="douyin" class="platform-button douyin">
            <VideoCameraOutlined class="platform-icon" />
            <span class="platform-text">抖音达人</span>
            <span v-if="showStats && platformStats.douyin.total > 0" class="platform-count">
              {{ platformStats.douyin.total }}
            </span>
          </a-radio-button>

          <a-radio-button value="wechat" class="platform-button wechat">
            <WechatOutlined class="platform-icon" />
            <span class="platform-text">微信达人</span>
            <span v-if="showStats && platformStats.wechat.total > 0" class="platform-count">
              {{ platformStats.wechat.total }}
            </span>
          </a-radio-button>
        </a-radio-group>

        <!-- 移除平台统计信息显示，避免与下方详细统计重复 -->
      </div>


    </div>

    <!-- 平台说明 -->
    <div class="platform-description" v-if="showDescription">
      <a-alert
        :message="currentPlatformDescription"
        type="info"
        show-icon
        closable
        class="description-alert"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { VideoCameraOutlined, WechatOutlined } from '@ant-design/icons-vue'

// Props定义
const props = defineProps({
  // 当前选中的平台
  modelValue: {
    type: String,
    default: 'douyin',
    validator: (value) => ['douyin', 'wechat'].includes(value)
  },
  // 是否显示统计信息
  showStats: {
    type: Boolean,
    default: true
  },
  // 是否显示平台说明
  showDescription: {
    type: Boolean,
    default: false
  },
  // 平台统计数据
  stats: {
    type: Object,
    default: () => ({
      douyin: { total: 0, claimed: 0, withContact: 0 },
      wechat: { total: 0, claimed: 0, withContact: 0 }
    })
  },

})

// Emits定义
const emit = defineEmits(['update:modelValue', 'platform-change'])

// 响应式数据
const currentPlatform = ref(props.modelValue)
const platformStats = ref(props.stats)

// 计算属性
const currentPlatformName = computed(() => {
  const platformNames = {
    douyin: '抖音达人',
    wechat: '微信达人'
  }
  return platformNames[currentPlatform.value] || '未知平台'
})

const currentStats = computed(() => {
  return platformStats.value[currentPlatform.value] || { total: 0, claimed: 0, withContact: 0 }
})

const currentPlatformDescription = computed(() => {
  const descriptions = {
    douyin: '抖音达人管理：通过抖音号搜索和管理抖音平台的达人资源，支持粉丝数、直播数据等多维度筛选',
    wechat: '微信达人管理：管理微信平台的达人资源，支持微信号搜索、地区筛选等功能，便于私域流量合作'
  }
  return descriptions[currentPlatform.value] || ''
})

// 方法定义
const handlePlatformChange = (e) => {
  const newPlatform = e.target.value
  console.log('🔄 平台切换:', {
    from: currentPlatform.value,
    to: newPlatform,
    timestamp: new Date().toISOString()
  })

  currentPlatform.value = newPlatform

  // 发出事件
  emit('update:modelValue', newPlatform)
  emit('platform-change', {
    platform: newPlatform,
    platformName: currentPlatformName.value,
    stats: currentStats.value
  })
}



/**
 * 更新平台统计数据
 * @param {Object} newStats - 新的统计数据
 */
const updateStats = (newStats) => {
  platformStats.value = { ...platformStats.value, ...newStats }
  console.log('📊 平台统计数据更新:', platformStats.value)
}

/**
 * 获取当前平台信息
 * @returns {Object} 当前平台的完整信息
 */
const getCurrentPlatformInfo = () => {
  return {
    platform: currentPlatform.value,
    platformName: currentPlatformName.value,
    stats: currentStats.value,
    description: currentPlatformDescription.value
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue, oldValue) => {
  if (newValue !== currentPlatform.value && oldValue !== undefined) {
    console.log('📝 PlatformSwitcher props.modelValue 变化:', { from: oldValue, to: newValue })
    currentPlatform.value = newValue
  }
}, { immediate: false })

watch(() => props.stats, (newStats) => {
  platformStats.value = newStats
}, { deep: true })

// 组件挂载时的初始化
onMounted(() => {
  console.log('🚀 PlatformSwitcher 组件初始化:', {
    currentPlatform: currentPlatform.value,
    platformName: currentPlatformName.value,
    stats: platformStats.value
  })
})

// 暴露方法给父组件
defineExpose({
  updateStats,
  getCurrentPlatformInfo,
  currentPlatform: computed(() => currentPlatform.value),
  currentPlatformName,
  currentStats
})

defineOptions({
  name: 'PlatformSwitcher'
})
</script>

<style scoped>
.platform-switcher {
  margin-bottom: 20px;
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.switcher-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  position: relative;
}

.switcher-main {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}



.platform-buttons {
  flex-shrink: 0;
}

.platform-icon {
  font-size: 14px;
  flex-shrink: 0;
  margin-right: 2px;
}

/* 平台按钮基础样式 - 极简版 */
.platform-button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 14px !important;
  min-width: 130px !important;
  font-size: 14px !important;
  font-weight: normal !important;
  border-radius: 4px !important;
  justify-content: space-between !important;
}

.platform-text {
  flex: 1;
  margin-left: 2px;
}

/* 平台按钮样式 - 极简灰色系 */
.platform-button {
  background: #f5f5f5 !important;
  color: #666 !important;
  border: 1px solid #d9d9d9 !important;
}

.platform-button:hover {
  background: #e6f7ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}

.platform-button.ant-radio-button-wrapper-checked {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: white !important;
}

.platform-count {
  background: #fff !important;
  color: #666 !important;
  border: 1px solid #d9d9d9 !important;
}

.platform-button:hover .platform-count {
  background: #fff !important;
  color: #1890ff !important;
  border-color: #91d5ff !important;
}

.platform-button.ant-radio-button-wrapper-checked .platform-count {
  background: rgba(255, 255, 255, 0.9) !important;
  color: #1890ff !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 覆盖Ant Design默认样式 - 极简版 */
.platform-buttons {
  display: inline-flex;
  background: #fafafa;
  padding: 3px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  gap: 3px;
}

.platform-buttons .ant-radio-button-wrapper {
  margin: 0 !important;
  border: none !important;
}

.platform-buttons .ant-radio-button-wrapper:not(:first-child)::before {
  display: none !important;
}

.platform-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 13px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 13px;
  color: #666;
}

.stats-item.primary {
  font-weight: 500;
  color: #333;
}

.stats-item.secondary {
  font-size: 12px;
  opacity: 0.8;
}

.stats-label {
  color: #666;
  font-weight: 400;
}

.stats-value {
  color: #1890ff;
  font-weight: 600;
  font-size: 16px;
}

.stats-unit {
  font-size: 12px;
  color: #999;
  font-weight: normal;
}

.platform-description {
  margin-top: 12px;
}

.description-alert {
  border-radius: 6px;
}

.description-alert :deep(.ant-alert-message) {
  font-size: 14px;
  line-height: 1.5;
}

.platform-count {
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
  font-weight: normal;
  padding: 2px 6px;
  border-radius: 3px;
  min-width: 18px;
  text-align: center;
  flex-shrink: 0;
  margin-left: 6px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .platform-switcher {
    padding: 12px 16px;
  }

  .switcher-container {
    gap: 16px;
    flex-direction: column;
    align-items: flex-start;
  }

  .platform-buttons {
    width: 100%;
    justify-content: center;
  }

  .platform-button {
    min-width: 140px !important;
    padding: 8px 16px !important;
  }

  .platform-stats {
    width: 100%;
    justify-content: space-around;
  }
}

@media (max-width: 768px) {
  .platform-switcher {
    padding: 10px 12px;
  }

  .platform-buttons {
    padding: 3px;
  }

  .platform-button {
    min-width: 110px !important;
    padding: 6px 12px !important;
    font-size: 13px !important;
  }

  .platform-icon {
    font-size: 13px;
  }

  .platform-stats {
    gap: 12px;
  }

  .stats-item {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .platform-button {
    padding: 10px 16px !important;
    gap: 6px !important;
  }

  .platform-icon {
    font-size: 14px;
  }

  .platform-text {
    font-size: 12px;
  }

  .platform-count {
    font-size: 10px;
    padding: 2px 5px;
    min-width: 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .platform-stats {
    background: rgba(255, 255, 255, 0.04);
    border-color: #434343;
  }

  .stats-label {
    color: #bfbfbf;
  }
}
</style>
