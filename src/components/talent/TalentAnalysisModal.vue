<!--
  团队达人详细分析模态框
  产品设计理念：为团队管理者提供数据驱动的决策支持
-->
<template>
  <a-modal
    v-model:open="visible"
    title="团队达人详细分析"
    width="1200px"
    :footer="null"
    centered
    @cancel="handleCancel"
  >
    <!-- 分析时间范围选择 -->
    <div class="analysis-header">
      <a-space>
        <span>分析时间范围：</span>
        <a-select 
          v-model:value="timeRange" 
          style="width: 120px"
          @change="loadAnalysisData"
        >
          <a-select-option value="7d">近7天</a-select-option>
          <a-select-option value="30d">近30天</a-select-option>
          <a-select-option value="90d">近90天</a-select-option>
          <a-select-option value="1y">近1年</a-select-option>
        </a-select>
        
        <a-button 
          type="primary" 
          size="small"
          @click="loadAnalysisData"
          :loading="loading"
        >
          刷新数据
        </a-button>
        
        <a-button 
          size="small"
          @click="exportReport"
        >
          导出报告
        </a-button>
      </a-space>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
      <p>正在分析团队达人数据...</p>
    </div>

    <!-- 分析内容 -->
    <div v-else class="analysis-content">
      <!-- 核心指标卡片 -->
      <div class="metrics-cards">
        <div class="metric-card">
          <div class="metric-value">{{ analysisData.totalValue || 0 }}</div>
          <div class="metric-label">团队总价值</div>
          <div class="metric-trend positive">
            ↗ {{ analysisData.valueGrowth || 0 }}%
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">{{ analysisData.efficiencyIndex || 0 }}</div>
          <div class="metric-label">效率指数</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">{{ analysisData.activityScore || 0 }}</div>
          <div class="metric-label">活跃度评分</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">{{ analysisData.participationRate || 0 }}%</div>
          <div class="metric-label">成员参与率</div>
          <div class="metric-detail">
            {{ analysisData.activeMembers || 0 }}/{{ analysisData.totalMembers || 0 }} 活跃
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <!-- 趋势分析图 -->
        <div class="chart-section">
          <h3>达人数量趋势分析</h3>
          <div 
            ref="trendChartRef" 
            class="chart-container"
            style="height: 300px;"
          ></div>
        </div>

        <!-- 类别分布图 -->
        <div class="chart-section">
          <h3>达人类别分布</h3>
          <div 
            ref="categoryChartRef" 
            class="chart-container"
            style="height: 300px;"
          ></div>
        </div>

        <!-- 粉丝分布图 -->
        <div class="chart-section">
          <h3>粉丝规模分布</h3>
          <div 
            ref="fansChartRef" 
            class="chart-container"
            style="height: 300px;"
          ></div>
        </div>
      </div>

      <!-- 成员绩效详情表格 -->
      <div class="performance-section">
        <h3>成员绩效详情</h3>
        <a-table
          :dataSource="analysisData.memberDetails || []"
          :columns="performanceColumns"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'memberName'">
              <div class="member-info">
                <a-avatar size="small" style="margin-right: 8px;">
                  {{ record.memberName?.charAt(0) || 'U' }}
                </a-avatar>
                {{ record.memberName }}
              </div>
            </template>
            
            <template v-if="column.key === 'performanceScore'">
              <a-progress 
                :percent="record.performanceScore" 
                size="small"
                :strokeColor="getScoreColor(record.performanceScore)"
              />
            </template>
            
            <template v-if="column.key === 'growthRate'">
              <span :class="{'positive-growth': record.growthRate > 0, 'negative-growth': record.growthRate < 0}">
                {{ record.growthRate }}%
              </span>
            </template>
            
            <template v-if="column.key === 'totalFans'">
              {{ formatNumber(record.totalFans) }}
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import teamTalentService from '@/services/team/teamTalent'

/**
 * 组件属性定义
 */
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  teamId: {
    type: Number,
    default: null
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:open', 'close'])

/**
 * 响应式数据
 */
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const timeRange = ref('30d')
const analysisData = ref({})

// 图表引用
const trendChartRef = ref(null)
const categoryChartRef = ref(null)
const fansChartRef = ref(null)

// 图表实例
let trendChart = null
let categoryChart = null
let fansChart = null

/**
 * 成员绩效表格列定义
 */
const performanceColumns = [
  {
    title: '成员姓名',
    dataIndex: 'memberName',
    key: 'memberName',
    width: 120
  },
  {
    title: '达人数量',
    dataIndex: 'talentCount',
    key: 'talentCount',
    width: 80,
    sorter: (a, b) => a.talentCount - b.talentCount
  },
  {
    title: '总粉丝数',
    dataIndex: 'totalFans',
    key: 'totalFans',
    width: 100
  },
  {
    title: '绩效评分',
    dataIndex: 'performanceScore',
    key: 'performanceScore',
    width: 120,
    sorter: (a, b) => a.performanceScore - b.performanceScore
  },
  {
    title: '增长率',
    dataIndex: 'growthRate',
    key: 'growthRate',
    width: 80
  },
  {
    title: '粉丝质量',
    dataIndex: 'fansQuality',
    key: 'fansQuality',
    width: 80
  },
  {
    title: '活跃度',
    dataIndex: 'activityLevel',
    key: 'activityLevel',
    width: 80
  },
  {
    title: '转化率',
    dataIndex: 'conversionRate',
    key: 'conversionRate',
    width: 80
  }
]

/**
 * 方法定义
 */

/**
 * 加载分析数据
 * 修复图表渲染时机问题，确保DOM完全渲染后再初始化图表
 */
const loadAnalysisData = async () => {
  // 增加详细的调试信息
  console.log('开始验证团队id:', {
    teamId: props.teamId,
    teamIdType: typeof props.teamId,
    teamIdValue: props.teamId,
    isNull: props.teamId === null,
    isUndefined: props.teamId === undefined,
    isEmpty: !props.teamId
  })
  
  if (!props.teamId) {
    message.warning('请先选择团队')
    return
  }

  // 确保团队id是有效的数字
  const teamIdNumber = Number(props.teamId)
  if (isNaN(teamIdNumber) || teamIdNumber <= 0) {
    message.error(`无效的团队id: ${props.teamId}`)
    return
  }

  loading.value = true
  try {
    console.log(`开始加载团队${teamIdNumber}的详细分析数据，时间范围：${timeRange.value}`)
    
    const response = await teamTalentService.getTeamTalentAnalysis(
      teamIdNumber, 
      timeRange.value
    )

    if (response.status === 100) {
      // 数据赋值 - 前端服务层已经处理好数据格式，直接使用data字段
      analysisData.value = response.data
      console.log('分析数据加载成功:', analysisData.value)
      
      message.success('分析数据加载成功')
    } else {
      message.error(response.message || '加载分析数据失败')
      // API失败时使用模拟数据
      console.warn('API调用失败，使用模拟数据')
      analysisData.value = teamTalentService.generateMockAnalysisData()
    }
  } catch (error) {
    console.error('加载分析数据异常:', error)
    message.error('加载分析数据失败，请稍后重试')
    // 异常时也使用模拟数据
    console.warn('发生异常，使用模拟数据')
    analysisData.value = teamTalentService.generateMockAnalysisData()
  } finally {
    loading.value = false
  }
}

/**
 * 渲染所有图表
 * 统一的图表渲染入口，包含错误处理和调试信息
 */
const renderCharts = () => {
  console.log('开始渲染图表，分析数据:', analysisData.value)
  
  try {
    renderTrendChart()
    renderCategoryChart()
    renderFansChart()
    console.log('所有图表渲染完成')
  } catch (error) {
    console.error('图表渲染异常:', error)
    message.error('图表渲染失败')
  }
}

/**
 * 渲染趋势图 - 修复版本
 * 增加详细的调试信息和容错处理
 */
const renderTrendChart = () => {
  console.log('开始渲染趋势图')
  console.log('DOM元素:', trendChartRef.value)
  console.log('趋势数据:', analysisData.value?.trendData)
  
  // 检查DOM元素是否存在
  if (!trendChartRef.value) {
    console.error('趋势图DOM元素不存在')
    return
  }
  
  // 检查数据是否存在
  const trendData = analysisData.value?.trendData || []
  if (trendData.length === 0) {
    console.warn('趋势数据为空，使用默认数据')
    // 使用默认数据确保图表能显示
    const defaultTrendData = generateDefaultTrendData()
    renderTrendChartWithData(defaultTrendData)
    return
  }
  
  renderTrendChartWithData(trendData)
}

/**
 * 使用数据渲染趋势图的核心逻辑
 * 分离数据处理和图表渲染逻辑，便于调试
 */
const renderTrendChartWithData = (trendData) => {
  try {
    // 销毁现有图表
    if (trendChart) {
      trendChart.dispose()
      trendChart = null
    }

    // 初始化图表
    trendChart = echarts.init(trendChartRef.value)
    
    // 数据处理
    const dates = trendData.map(item => item.date || '未知')
    const talentCounts = trendData.map(item => item.talentCount || 0)
    const fansCounts = trendData.map(item => item.totalFans || 0)
    
    console.log('图表数据处理完成:', { dates, talentCounts, fansCounts })

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].axisValue}<br/>`
          params.forEach(param => {
            const value = param.value
            const displayValue = param.seriesName === '总粉丝数' 
              ? `${(value / 10000).toFixed(1)}万` 
              : value
            result += `${param.marker}${param.seriesName}: ${displayValue}<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['达人数量', '总粉丝数']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false
      },
      yAxis: [
        {
          type: 'value',
          name: '达人数量',
          position: 'left',
          min: 0
        },
        {
          type: 'value',
          name: '总粉丝数',
          position: 'right',
          min: 0,
          axisLabel: {
            formatter: value => `${(value / 10000).toFixed(1)}万`
          }
        }
      ],
      series: [
        {
          name: '达人数量',
          type: 'line',
          data: talentCounts,
          smooth: true,
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          },
          symbolSize: 6
        },
        {
          name: '总粉丝数',
          type: 'line',
          yAxisIndex: 1,
          data: fansCounts,
          smooth: true,
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#52c41a'
          },
          symbolSize: 6
        }
      ]
    }

    trendChart.setOption(option)
    console.log('趋势图渲染成功')
  } catch (error) {
    console.error('趋势图渲染异常:', error)
  }
}

/**
 * 生成默认趋势数据
 * 当API数据为空时使用，确保图表能正常显示
 */
const generateDefaultTrendData = () => {
  const data = []
  const baseDate = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(baseDate)
    date.setDate(date.getDate() - i)
    const dateString = date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
    
    data.push({
      date: dateString,
      talentCount: 45 + Math.floor(Math.random() * 10),
      totalFans: 89000 + Math.floor(Math.random() * 5000)
    })
  }
  
  return data
}

/**
 * 渲染类别分布饼图
 * 功能：展示达人类别的分布比例，使用环形饼图
 * 优化：精简配置，提供默认数据，增强用户体验
 */
const renderCategoryChart = () => {
  // 验证DOM元素是否存在
  if (!categoryChartRef.value) {
    console.error('类别分布图DOM元素不存在')
    return
  }

  try {
    // 销毁旧图表实例，防止内存泄漏
    if (categoryChart) {
      categoryChart.dispose()
      categoryChart = null
    }

    // 初始化图表实例
    categoryChart = echarts.init(categoryChartRef.value)
    
    // 获取类别数据，提供默认数据确保图表显示
    const categoryData = analysisData.value?.categoryData || [
      { name: '美妆时尚', value: 25 },
      { name: '生活方式', value: 20 },
      { name: '科技数码', value: 15 },
      { name: '美食探店', value: 10 },
      { name: '其他', value: 30 }
    ]

    // 精简的饼图配置
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}人 ({d}%)'  // 简化提示信息
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle'
      },
      series: [{
        name: '达人类别',
        type: 'pie',
        radius: ['40%', '70%'],  // 环形饼图
        center: ['60%', '50%'],
        data: categoryData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: { show: false },      // 隐藏标签，使用图例代替
        labelLine: { show: false }   // 隐藏引导线
      }]
    }

    // 应用配置并渲染图表
    categoryChart.setOption(option)
    console.log('类别分布图渲染完成')
  } catch (error) {
    console.error('类别分布图渲染异常:', error)
  }
}

/**
 * 渲染粉丝分布柱状图
 * 功能：展示不同粉丝规模范围的达人数量分布
 * 优化：使用更细的柱子，精简配置代码，增强可读性
 */
const renderFansChart = () => {
  // 验证DOM元素是否存在
  if (!fansChartRef.value) {
    console.error('粉丝分布图DOM元素不存在')
    return
  }

  try {
    // 销毁旧图表实例，防止内存泄漏
    if (fansChart) {
      fansChart.dispose()
      fansChart = null
    }

    // 初始化图表实例
    fansChart = echarts.init(fansChartRef.value)
    
    // 获取粉丝分布数据，提供默认数据确保图表显示
    const fansDistribution = analysisData.value?.fansDistribution || [12, 28, 20, 15, 8, 3]
    const fansRanges = ['<1万', '1-5万', '5-10万', '10-50万', '50-100万', '>100万']

    // 精简的图表配置，突出核心数据展示
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => `${params[0].name}<br/>达人数量: ${params[0].value}人`
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: fansRanges
      },
      yAxis: {
        type: 'value',
        name: '达人数量',
        min: 0
      },
      series: [{
        name: '达人数量',
        type: 'bar',
        data: fansDistribution,
        barWidth: '30%',  // 优化：使用更细的柱子（从60%改为30%）
        itemStyle: {
          color: '#52c41a',  // 使用简洁的绿色，去除复杂的渐变效果
          borderRadius: [3, 3, 0, 0]  // 保持轻微圆角，增加美观度
        }
      }]
    }

    // 应用配置并渲染图表
    fansChart.setOption(option)
    console.log('粉丝分布图渲染完成')
  } catch (error) {
    console.error('粉丝分布图渲染异常:', error)
  }
}

/**
 * ===== 工具函数 =====
 * 提供数据格式化、UI交互等辅助功能
 */

/**
 * 获取绩效评分对应的颜色
 * @param {number} score - 评分值（0-100）
 * @returns {string} 颜色值（绿色/黄色/红色）
 */
const getScoreColor = (score) => {
  if (score >= 80) return '#52c41a'  // 优秀：绿色
  if (score >= 60) return '#faad14'  // 良好：黄色
  return '#f5222d'                   // 需改进：红色
}

/**
 * 格式化数字显示
 * @param {number} num - 需要格式化的数字
 * @returns {string} 格式化后的字符串（如：1.2万、3.5k）
 */
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) return `${(num / 10000).toFixed(1)}万`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}k`
  return num.toString()
}

/**
 * 导出分析报告功能
 * TODO: 实现Excel/PDF导出功能
 */
const exportReport = () => {
  message.success('导出功能开发中...')
}

/**
 * 处理模态框关闭
 * 功能：通知父组件关闭模态框
 */
const handleCancel = () => {
  emit('close')
}

/**
 * ===== 响应式监听器 =====
 * 监听组件状态变化，实现数据自动加载和图表生命周期管理
 */

/**
 * 监听模态框显示状态
 * 功能：模态框打开时自动加载数据，关闭时清理图表资源
 */
watch(visible, (newVal) => {
  if (newVal && props.teamId) {
    // 模态框打开且有团队id时，加载分析数据
    loadAnalysisData()
  } else if (!newVal) {
    // 模态框关闭时，销毁所有图表实例释放内存
    destroyAllCharts()
  }
})

/**
 * 监听团队id变化
 * 功能：团队切换时重新加载对应团队的分析数据
 */
watch(() => props.teamId, (newTeamId) => {
  if (newTeamId && visible.value) {
    loadAnalysisData()
  }
})

/**
 * 监听分析数据变化
 * 功能：数据更新时自动重新渲染所有图表
 * 优化：使用nextTick和延时确保DOM更新完成后再渲染
 */
watch(analysisData, (newVal) => {
  if (newVal && visible.value) {
    nextTick(() => {
      setTimeout(() => {
        renderCharts()
      }, 200)
    })
  }
}, { deep: true })

/**
 * 销毁所有图表实例的统一方法
 * 功能：避免代码重复，统一管理图表资源清理
 */
const destroyAllCharts = () => {
  [trendChart, categoryChart, fansChart].forEach((chart, index) => {
    if (chart) {
      chart.dispose()
      // 清空对应的图表引用
      if (index === 0) trendChart = null
      else if (index === 1) categoryChart = null
      else fansChart = null
    }
  })
}

/**
 * 组件卸载时的清理工作
 * 功能：确保所有图表实例被正确销毁，防止内存泄漏
 */
onUnmounted(() => {
  destroyAllCharts()
})
</script>

<style scoped>
.analysis-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.loading-container {
  text-align: center;
  padding: 60px 0;
}

.analysis-content {
  max-height: 70vh;
  overflow-y: auto;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  margin-top: 4px;
}

.metric-trend.positive {
  color: #52c41a;
}

.metric-detail {
  font-size: 12px;
  color: #999;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-section h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.chart-container {
  width: 100%;
}

.performance-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-section h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.member-info {
  display: flex;
  align-items: center;
}

.positive-growth {
  color: #52c41a;
}

.negative-growth {
  color: #f5222d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metric-value {
    font-size: 24px;
  }
}
</style> 