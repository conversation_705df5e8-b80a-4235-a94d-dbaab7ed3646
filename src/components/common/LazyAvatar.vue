<template>
  <div class="lazy-avatar-wrapper" :style="{ width: size + 'px', height: size + 'px' }">
    <!-- 加载中的骨架屏 -->
    <div 
      v-if="loading" 
      class="avatar-skeleton"
      :style="{ width: size + 'px', height: size + 'px' }"
    >
      <div class="skeleton-circle"></div>
    </div>
    
    <!-- 实际头像 -->
    <a-avatar
      v-else
      :size="size"
      :src="currentSrc"
      :alt="alt"
      class="lazy-avatar"
      @error="handleImageError"
    >
      <!-- 头像加载失败时的备用图标 -->
      <template #icon>
        <user-outlined />
      </template>
    </a-avatar>
  </div>
</template>

<script setup>
/**
 * 懒加载头像组件
 * 
 * 功能特性：
 * - 支持懒加载，减少初始页面加载压力
 * - 提供骨架屏，提升用户体验
 * - 自动处理加载失败，显示默认头像
 * - 支持延迟加载，避免同时加载大量图片
 * 
 * 使用示例：
 * <LazyAvatar :src="user.avatar" :size="64" :alt="user.name" :delay="100" />
 */

import { ref, watch, onMounted, nextTick, readonly } from 'vue'
import { UserOutlined } from '@ant-design/icons-vue'

// 组件属性定义
const props = defineProps({
  // 头像图片URL
  src: {
    type: String,
    default: ''
  },
  // 头像尺寸
  size: {
    type: Number,
    default: 64
  },
  // 图片alt文本
  alt: {
    type: String,
    default: '用户头像'
  },
  // 延迟加载时间（毫秒），用于避免同时加载大量图片
  delay: {
    type: Number,
    default: 0
  },
  // 是否立即加载（不使用懒加载）
  immediate: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const loading = ref(true)  // 是否正在加载
const currentSrc = ref('') // 当前显示的图片URL
const loadAttempted = ref(false) // 是否已尝试加载

/**
 * 开始加载头像图片
 * 使用Image对象预加载，确保图片完全加载后再显示
 */
const loadImage = async () => {
  if (!props.src || loadAttempted.value) {
    loading.value = false
    return
  }

  loadAttempted.value = true

  // 如果设置了延迟，先等待指定时间
  if (props.delay > 0) {
    await new Promise(resolve => setTimeout(resolve, props.delay))
  }

  // 创建图片对象进行预加载
  const img = new Image()
  
  // 图片加载成功
  img.onload = () => {
    currentSrc.value = props.src
    loading.value = false
  }
  
  // 图片加载失败
  img.onerror = () => {
    console.warn(`头像加载失败: ${props.src}`)
    currentSrc.value = '/images/default_avatar.svg' // 使用默认头像
    loading.value = false
  }
  
  // 开始加载图片
  img.src = props.src
}

/**
 * 处理图片加载错误
 * 当a-avatar组件的图片加载失败时触发
 */
const handleImageError = () => {
  console.warn(`头像显示失败: ${currentSrc.value}`)
  currentSrc.value = '/images/default_avatar.svg'
}

// 监听src变化，重新加载图片
watch(() => props.src, (newSrc) => {
  if (newSrc && newSrc !== currentSrc.value) {
    loading.value = true
    loadAttempted.value = false
    loadImage()
  }
}, { immediate: false })

// 组件挂载后处理加载逻辑
onMounted(async () => {
  await nextTick()
  
  if (props.immediate || !props.src) {
    // 立即加载模式或没有图片URL
    loading.value = false
    currentSrc.value = props.src || '/images/default_avatar.svg'
  } else {
    // 延迟加载模式
    loadImage()
  }
})

// 暴露方法供父组件调用
defineExpose({
  loadImage,
  loading: readonly(loading)
})
</script>

<style scoped>
.lazy-avatar-wrapper {
  display: inline-block;
  position: relative;
}

/* 骨架屏样式 */
.avatar-skeleton {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  overflow: hidden;
}

.skeleton-circle {
  width: 70%;
  height: 70%;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 骨架屏动画效果 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 头像样式 */
.lazy-avatar {
  transition: opacity 0.3s ease;
}

/* 响应式尺寸优化 */
@media (max-width: 768px) {
  .lazy-avatar-wrapper {
    flex-shrink: 0;
  }
}
</style> 