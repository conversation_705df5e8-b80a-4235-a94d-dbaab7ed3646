/**
 * 业务状态码常量定义
 * 统一管理所有业务状态码，避免魔法数字
 */

// 通用状态码
export const COMMON_STATUS = {
  SUCCESS: 100,           // 操作成功
  SERVER_ERROR: 500,      // 服务器错误
  PARAM_ERROR: 400,       // 参数错误
  UNAUTHORIZED: 401,      // 未授权
  FORBIDDEN: 403,         // 禁止访问
  NOT_FOUND: 404          // 资源不存在
}

// 公司管理相关状态码
export const COMPANY_STATUS = {
  CREATE_SUCCESS: 5001,           // 创建成功
  CREATE_FAILED: 5002,            // 创建失败
  PENDING_REVIEW: 5003,           // 已有未审核公司
  NAME_EXISTS: 5004,              // 公司名称重复
  CODE_EXISTS: 5005,              // 公司代码重复
  NOT_FOUND: 5006,                // 公司不存在
  AUDIT_SUCCESS: 5007,            // 审核成功
  AUDIT_FAILED: 5008,             // 审核失败
  PERMISSION_DENIED: 5009,        // 权限不足
  PARAM_ERROR: 5010               // 参数错误
}

// 团队管理相关状态码
export const TEAM_STATUS = {
  CREATE_SUCCESS: 6001,           // 创建成功
  CREATE_FAILED: 6002,            // 创建失败
  CREATE_LIMIT_REACHED: 6003,     // 创建数量已达上限
  NAME_EXISTS: 6004,              // 团队名称重复
  NOT_FOUND: 6005,                // 团队不存在
  PERMISSION_DENIED: 6006,        // 权限不足
  PARAM_ERROR: 6007,              // 参数错误
  DISSOLVE_SUCCESS: 6008,         // 解散成功
  DISSOLVE_FAILED: 6009           // 解散失败
}

// 审核状态
export const AUDIT_STATUS = {
  PENDING: 0,     // 未审核
  APPROVED: 1,    // 审核通过
  REJECTED: 2     // 审核不通过
}

// 审核状态文本映射
export const AUDIT_STATUS_TEXT = {
  [AUDIT_STATUS.PENDING]: '未审核',
  [AUDIT_STATUS.APPROVED]: '审核通过',
  [AUDIT_STATUS.REJECTED]: '审核不通过'
}

// 审核状态颜色映射
export const AUDIT_STATUS_COLOR = {
  [AUDIT_STATUS.PENDING]: 'orange',
  [AUDIT_STATUS.APPROVED]: 'green',
  [AUDIT_STATUS.REJECTED]: 'red'
}

/**
 * 获取审核状态文本
 * @param {number} status - 审核状态
 * @returns {string} 状态文本
 */
export const getAuditStatusText = (status) => {
  return AUDIT_STATUS_TEXT[status] || '未知状态'
}

/**
 * 获取审核状态颜色
 * @param {number} status - 审核状态
 * @returns {string} 状态颜色
 */
export const getAuditStatusColor = (status) => {
  return AUDIT_STATUS_COLOR[status] || 'default'
}
