import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, userAPI } from '@/services'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('crm_token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('crm_user_info') || '{}'))
  const permissions = ref(JSON.parse(localStorage.getItem('crm_permissions') || '{}'))
  
  // 权限验证缓存状态 - 用于减少API调用频率
  const lastPermissionCheck = ref(Date.now())
  const isPageRefresh = ref(true) // 默认为true，表示页面刷新状态

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => userInfo.value.昵称 || userInfo.value.phone || '未知用户')
  const userAvatar = computed(() => userInfo.value.头像 || '')
  const userRole = computed(() => userInfo.value.role || 'user')

  // 登录 - 增强错误处理和调试信息
  const login = async (loginData) => {
    try {
      console.log('🔐 用户Store开始登录:', {
        用户名: loginData.username,
        密码长度: loginData.password.length,
        时间戳: new Date().toISOString()
      })
      
      // 调用后端登录API
      const response = await authAPI.login(loginData)
      
      console.log('📡 后端登录响应:', {
        状态码: response?.status,
        是否有令牌: !!response?.data?.access_token,
        是否有用户信息: !!response?.data?.用户信息,
        响应数据类型: typeof response,
        完整响应: response
      })

      // 检查响应状态 - 支持多种成功状态码格式
      if (response.status === 0 || response.status === 1 || response.status === 100) {
        // 验证必要的响应数据
        if (!response.data?.access_token) {
          throw new Error('服务器响应缺少访问令牌')
        }

        // 保存令牌
        const accessToken = response.data.access_token
        token.value = accessToken
        
        // 保存用户基本信息（从登录响应中获取）
        const user = {
          id: response.data?.用户信息?.id,
          phone: loginData.username,
          昵称: response.data?.用户信息?.昵称 || '',
          头像: response.data?.用户信息?.头像 || '',
          role: 'user'
        }
        userInfo.value = user
        
        // 保存权限信息
        if (response.data?.权限) {
          permissions.value = response.data.权限
        }

        // 持久化到本地存储
        localStorage.setItem('crm_token', accessToken)
        localStorage.setItem('crm_user_info', JSON.stringify(user))
        localStorage.setItem('crm_permissions', JSON.stringify(permissions.value))

        console.log('✅ 登录成功，已保存用户信息:', {
          用户id: user.id,
          手机号: user.phone,
          昵称: user.昵称,
          权限数量: Object.keys(permissions.value).length
        })
        
        return { success: true }
      } else {
        // 登录失败，显示具体错误信息
        const errorMsg = response.message || response.msg || '登录失败，请检查用户名和密码'

        // 常见业务错误（如密码错误）只在开发环境记录详细日志
        const 常见业务错误状态码 = [401, 402, 405, 427, 410, 412, 413]
        const isCommonBusinessError = 常见业务错误状态码.includes(response.status)

        if (isCommonBusinessError) {
          if (import.meta.env.DEV) {
            console.warn('⚠️ 登录业务错误:', {
              状态码: response.status,
              错误消息: errorMsg
            })
          }
        } else {
          console.error('❌ 登录系统错误:', {
            状态码: response.status,
            错误消息: errorMsg,
            完整响应: response
          })
        }

        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('❌ 登录过程出错:', {
        错误类型: error.name,
        错误消息: error.message,
        错误代码: error.code,
        是否网络错误: error.code === 'ERR_NETWORK' || error.message?.includes('Network Error'),
        完整错误: error
      })
      
      // 根据错误类型提供更友好的错误消息
      let friendlyMessage = error.message || '登录失败'
      
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        friendlyMessage = '无法连接到服务器，请检查网络连接'
      } else if (error.code === 'ECONNABORTED') {
        friendlyMessage = '请求超时，请稍后重试'
      } else if (error.code === 'ERR_CONNECTION_REFUSED') {
        friendlyMessage = '服务器连接被拒绝，请联系技术支持'
      }
      
      return { success: false, message: friendlyMessage }
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      console.log('开始注册:', registerData)
      
      // 调用后端注册API
      const response = await authAPI.register(registerData)
      
      console.log('注册响应:', response)
      
      // 标准、严格的成功判断逻辑
      if (response && response.status === 100) {
        // 检查是否有token（注册成功后自动登录）
        if (response.data?.access_token) {
          const accessToken = response.data.access_token
          token.value = accessToken
          
          // 保存用户基本信息
          const user = {
            id: response.data?.用户信息?.id || response.用户信息?.id,
            phone: registerData.phone,
            昵称: response.data?.用户信息?.昵称 || response.用户信息?.昵称 || '',
            头像: response.data?.用户信息?.头像 || response.用户信息?.头像 || '',
            role: 'user'
          }
          userInfo.value = user
          
          // 保存权限信息
          if (response.data?.权限 || response.权限) {
            permissions.value = response.data?.权限 || response.权限
          }

          // 持久化到本地存储
          localStorage.setItem('crm_token', accessToken)
          localStorage.setItem('crm_user_info', JSON.stringify(user))
          localStorage.setItem('crm_permissions', JSON.stringify(permissions.value))

          console.log('注册成功并自动登录，已保存用户信息:', user)
          return { success: true, autoLogin: true, user: user }
        } else {
          // 注册成功但未自动登录
          console.log('注册成功，但需要手动登录')
          return { success: true, autoLogin: false }
        }
      } else {
        // 后端返回了明确的失败信息
        throw new Error(response.message || '注册失败，未知错误')
      }
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, message: error.message || '注册失败' }
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出API（清理服务端状态）
      await authAPI.logout()
    } catch (error) {
      console.error('服务端登出失败:', error)
    } finally {
      // 无论服务端是否成功，都清理客户端状态
      token.value = ''
      userInfo.value = {}
      permissions.value = {}
      lastPermissionCheck.value = Date.now()
      isPageRefresh.value = true

      // 清除本地存储和会话存储
      localStorage.removeItem('crm_token')
      localStorage.removeItem('crm_user_info')
      localStorage.removeItem('crm_permissions')
      sessionStorage.removeItem('crm_session_active')
      
      console.log('用户登出，已清理所有缓存状态')
    }
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    localStorage.setItem('crm_user_info', JSON.stringify(userInfo.value))
  }

  // 检查权限
  const hasPermission = (permission) => {
    if (Array.isArray(permissions.value)) {
      return permissions.value.includes(permission)
    }
    return false
  }

  // 检测是否为页面刷新（而非路由跳转）
  const detectPageRefresh = () => {
    // 检查sessionStorage中是否有访问标记
    const sessionKey = 'crm_session_active'
    const isSessionActive = sessionStorage.getItem(sessionKey)
    
    if (!isSessionActive) {
      // 会话中首次访问，标记为页面刷新
      sessionStorage.setItem(sessionKey, 'true')
      isPageRefresh.value = true
      console.log('检测到页面刷新，将进行权限验证')
    } else {
      // 会话中已有标记，说明是路由跳转
      isPageRefresh.value = false
      console.log('检测到路由跳转，跳过权限验证')
    }
    
    return isPageRefresh.value
  }

  // 重置页面刷新状态（在验证完成后调用）
  const resetRefreshState = () => {
    isPageRefresh.value = false
  }

  // 验证登录状态（通过后端权限检查接口）
  const validateLogin = async (forceCheck = false) => {
    if (!token.value) {
      console.log('无token，登录状态无效')
      return false
    }

    // 检测页面刷新状态
    const isRefresh = forceCheck || detectPageRefresh()
    
    // 如果不是页面刷新且有缓存的权限，直接返回true
    if (!isRefresh && permissions.value && Object.keys(permissions.value).length > 0) {
      console.log('使用缓存的权限信息，跳过API调用')
      return true
    }

    // 页面刷新或强制检查时才调用API
    console.log('调用权限检查API - 原因:', isRefresh ? '页面刷新' : '强制检查')
    
    try {
      const response = await userAPI.getPermissions()
      if (response.status === 0 || response.status === 1 || response.status === 100) {
        // 更新权限信息
        if (response.权限 || response.data?.权限) {
          permissions.value = response.权限 || response.data.权限
          localStorage.setItem('crm_permissions', JSON.stringify(permissions.value))
        }
        
        // 更新最后检查时间
        lastPermissionCheck.value = Date.now()
        
        // 重置页面刷新状态
        resetRefreshState()
        
        console.log('权限验证成功，已更新权限信息')
        return true
      } else {
        // token无效，清除本地状态
        console.log('权限验证失败，token无效')
        await logout()
        return false
      }
    } catch (error) {
      console.error('验证登录状态失败:', error)
      // 验证失败，清除本地状态
      await logout()
      return false
    }
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    lastPermissionCheck,
    isPageRefresh,
    
    // 计算属性
    isAuthenticated,
    userName,
    userAvatar,
    userRole,
    
    // 方法
    login,
    logout,
    updateUserInfo,
    hasPermission,
    validateLogin,
    detectPageRefresh,
    resetRefreshState,
    register
  }
}) 