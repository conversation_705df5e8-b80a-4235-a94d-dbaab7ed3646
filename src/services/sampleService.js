import api from './api'

/**
 * 样品管理服务
 * 处理样品相关的API请求
 */
class SampleService {
  constructor() {
    this.baseUrl = '/samples'  // 使用现有的API路径
  }

  /**
   * 获取样品列表
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码，从1开始
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.搜索关键词 - 搜索关键词
   * @param {number} params.审核状态 - 审核状态筛选
   * @param {number} params.快递状态 - 快递状态筛选
   * @returns {Promise} API响应
   */
  async 获取样品列表(params = {}) {
    const 请求参数 = {
      页码: params.页码 || 1,
      每页数量: params.每页数量 || 10,
      收件人: params.收件人 || undefined,  // 使用现有API的字段名
      产品ID: params.产品ID || undefined,
      审核状态: params.审核状态 !== undefined ? params.审核状态 : undefined,
      快递单号: params.快递单号 || undefined,
      快递状态: params.快递状态 !== undefined ? params.快递状态 : undefined
    }

    console.log('🔄 请求样品列表:', 请求参数)

    try {
      const response = await api.post(`${this.baseUrl}/list`, 请求参数)
      console.log('✅ 获取样品列表成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 获取样品列表失败:', error)
      throw error
    }
  }

  // 保持向后兼容的英文方法名
  async getSampleList(params = {}) {
    return this.获取样品列表(params)
  }

  /**
   * 获取样品详情
   * @param {number} 样品id - 样品id
   * @returns {Promise} API响应
   */
  async 获取样品详情(样品id) {
    const 请求参数 = { 样品id: 样品id }  // 使用现有API的字段名

    console.log('🔄 请求样品详情:', 请求参数)

    try {
      const response = await api.post(`${this.baseUrl}/detail`, 请求参数)
      console.log('✅ 获取样品详情成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 获取样品详情失败:', error)
      throw error
    }
  }

  // 保持向后兼容的英文方法名
  async getSampleDetail(sampleId) {
    return this.获取样品详情(sampleId)
  }

  /**
   * 添加样品申请
   * Add sample application
   * @param {Object} sampleData - Sample application data
   * @returns {Promise<Object>} Add result
   */
  async addSample(sampleData) {
    try {
      const response = await api.post(`${this.baseUrl}/add`, sampleData)
      return response
    } catch (error) {
      console.error('Failed to add sample:', error)
      throw error
    }
  }

  /**
   * 更新样品信息
   * Update sample information
   * @param {Object} sampleData - Sample data with ID
   * @returns {Promise<Object>} Update result
   */
  async updateSample(sampleData) {
    try {
      const response = await api.post(`${this.baseUrl}/update`, sampleData)
      return response
    } catch (error) {
      console.error('Failed to update sample:', error)
      throw error
    }
  }

  /**
   * 审核样品申请
   * Audit sample application
   * @param {Object} auditData - Audit data
   * @returns {Promise<Object>} Audit result
   */
  async auditSample(auditData) {
    try {
      const response = await api.post(`${this.baseUrl}/audit`, auditData)
      return response
    } catch (error) {
      console.error('Failed to audit sample:', error)
      throw error
    }
  }

  /**
   * 更新快递信息
   * Update express information
   * @param {Object} expressData - Express data
   * @returns {Promise<Object>} Update result
   */
  async updateExpress(expressData) {
    try {
      const response = await api.post(`${this.baseUrl}/express/update`, expressData)
      return response
    } catch (error) {
      console.error('Failed to update express:', error)
      throw error
    }
  }

  /**
   * 查询物流信息
   * Query logistics information
   * @param {string} trackingNumber - Tracking number
   * @returns {Promise<Object>} Logistics data
   */
  async queryLogistics(trackingNumber) {
    try {
      const response = await api.post(`${this.baseUrl}/get_express_info`, {
        快递单号: trackingNumber
      })
      return response
    } catch (error) {
      console.error('Failed to query logistics:', error)
      throw error
    }
  }

  /**
   * 获取快递查询配额信息
   * Get express query quota information
   * @returns {Promise<Object>} Quota information
   */
  async getExpressQueryQuota() {
    try {
      console.log('🔄 获取快递查询配额信息...')

      const response = await api.post(`${this.baseUrl}/express-query-quota`, {})

      console.log('✅ 快递查询配额信息获取成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取快递查询配额信息失败:', error)
      throw error
    }
  }

  /**
   * 获取样品统计数据
   * Get sample statistics
   * @returns {Promise<Object>} Statistics data
   */
  async getSampleStats() {
    try {
      const response = await api.post(`${this.baseUrl}/stats`)
      return response
    } catch (error) {
      console.error('Failed to fetch sample stats:', error)
      throw error
    }
  }

  /**
   * 导出样品数据
   * Export sample data
   * @param {Object} exportParams - Export parameters
   * @returns {Promise<Object>} Export result
   */
  async exportSamples(exportParams = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/export`, exportParams, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Failed to export samples:', error)
      throw error
    }
  }

  /**
   * 获取样品统计信息
   * @returns {Promise} API响应
   */
  async 获取样品统计() {
    console.log('🔄 请求样品统计信息')

    try {
      const response = await api.post(`${this.baseUrl}/stats`)  // 使用现有的API路径
      console.log('✅ 获取样品统计成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 获取样品统计失败:', error)
      throw error
    }
  }

  /**
   * 更新审核状态
   * @param {Object} params - 更新参数
   * @param {number} params.样品id - 样品id
   * @param {number} params.审核状态 - 审核状态（0=待审核，1=已审核，2=审核拒绝）
   * @param {string} params.审核备注 - 审核备注
   * @returns {Promise} API响应
   */
  async 更新审核状态(params) {
    const 请求参数 = {
      样品id: params.样品id,
      审核状态: params.审核状态,
      审核备注: params.审核备注 || null
    }

    console.log('🔄 更新审核状态:', 请求参数)

    try {
      const response = await api.post(`${this.baseUrl}/update-audit`, 请求参数)
      console.log('✅ 更新审核状态成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 更新审核状态失败:', error)
      throw error
    }
  }

  /**
   * 更新快递信息
   * @param {Object} params - 更新参数
   * @returns {Promise} API响应
   */
  async 更新快递信息(params) {
    const 请求参数 = {
      样品id: params.样品id,
      快递单号: params.快递单号 || null,
      快递状态: params.快递状态 !== undefined ? params.快递状态 : null,
      快递状态标识: params.快递状态标识 || null,
      快递状态描述: params.快递状态描述 || null,
      快递公司: params.快递公司 || null,
      物流跟踪详情: params.物流跟踪详情 || null
    }

    console.log('🔄 更新快递信息:', 请求参数)

    try {
      const response = await api.post(`${this.baseUrl}/update-express`, 请求参数)
      console.log('✅ 更新快递信息成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 更新快递信息失败:', error)
      throw error
    }
  }

  /**
   * 获取审核状态选项
   * @returns {Array} 审核状态选项
   */
  获取审核状态选项() {
    return [
      { value: 0, label: '待审核', color: 'orange' },
      { value: 1, label: '已审核', color: 'green' },
      { value: 2, label: '审核拒绝', color: 'red' }
    ]
  }

  /**
   * 获取快递状态选项
   * @returns {Array} 快递状态选项
   */
  获取快递状态选项() {
    return [
      { value: 0, label: '待发货', color: 'orange' },
      { value: 1, label: '已发货', color: 'blue' },
      { value: 2, label: '已签收', color: 'green' }
    ]
  }

  /**
   * 获取审核状态文本
   * @param {number} 状态值 - 状态值
   * @returns {string} 状态文本
   */
  获取审核状态文本(状态值) {
    const 状态映射 = {
      0: '未审核',
      1: '审核通过',
      2: '审核不通过'
    }
    return 状态映射[状态值] || '未知'
  }

  /**
   * 获取快递状态文本
   * @param {number} 状态值 - 状态值
   * @returns {string} 状态文本
   */
  获取快递状态文本(状态值) {
    const 状态映射 = {
      0: '待发货',
      1: '已发货',
      2: '已签收'
    }
    return 状态映射[状态值] || '未知'
  }

  /**
   * 获取审核状态颜色
   * @param {number} 状态值 - 状态值
   * @returns {string} 状态颜色
   */
  获取审核状态颜色(状态值) {
    const 颜色映射 = {
      0: 'orange',
      1: 'green',
      2: 'red'
    }
    return 颜色映射[状态值] || 'default'
  }

  /**
   * 获取快递状态颜色
   * @param {number} 状态值 - 状态值
   * @returns {string} 状态颜色
   */
  获取快递状态颜色(状态值) {
    const 颜色映射 = {
      0: 'orange',
      1: 'blue',
      2: 'green'
    }
    return 颜色映射[状态值] || 'default'
  }

  /**
   * 格式化时间
   * @param {string} 时间字符串 - 时间字符串
   * @returns {string} 格式化后的时间
   */
  格式化时间(时间字符串) {
    if (!时间字符串) return '-'
    try {
      return new Date(时间字符串).toLocaleString('zh-CN')
    } catch (error) {
      return 时间字符串
    }
  }

  /**
   * 获取用户产品列表（用于寄样申请）
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码，默认1
   * @param {number} params.每页数量 - 每页数量，默认20
   * @param {string} params.产品名称 - 产品名称筛选（可选）
   * @returns {Promise} API响应
   */
  async 获取用户产品列表(params = {}) {
    const 请求参数 = {
      页码: params.页码 || 1,
      每页数量: params.每页数量 || 20,
      产品名称: params.产品名称 || null
    }

    console.log('🔄 请求用户产品列表:', 请求参数)

    try {
      const response = await api.post('/kol/sample/user-products', 请求参数)
      console.log('✅ 获取用户产品列表成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 获取用户产品列表失败:', error)
      throw error
    }
  }

  /**
   * 创建寄样申请
   * @param {Object} sampleData - 寄样申请数据
   * @param {number} sampleData.达人id - 达人id
   * @param {number} sampleData.产品ID - 产品ID
   * @param {string} sampleData.产品规格 - 产品规格信息（JSON字符串）
   * @param {number} sampleData.数量 - 申请数量
   * @param {string} sampleData.收件人 - 收件人姓名
   * @param {string} sampleData.收件地址 - 收件地址
   * @param {string} sampleData.收件电话 - 收件电话
   * @param {string} sampleData.申请备注 - 申请备注（可选）
   * @returns {Promise} API响应
   */
  async 创建寄样申请(sampleData) {
    const 请求参数 = {
      达人id: sampleData.达人id,
      产品ID: sampleData.产品ID,
      产品规格: sampleData.产品规格 || null,
      数量: sampleData.数量 || 1,
      收件人: sampleData.收件人,
      收件地址: sampleData.收件地址,
      收件电话: sampleData.收件电话,
      申请备注: sampleData.申请备注 || null
    }

    console.log('🔄 创建寄样申请:', 请求参数)

    try {
      const response = await api.post('/kol/sample/create', 请求参数)
      console.log('✅ 创建寄样申请成功:', response.data)
      return response
    } catch (error) {
      console.error('❌ 创建寄样申请失败:', error)
      throw error
    }
  }

  /**
   * 获取用户寄样记录列表
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码，默认为1
   * @param {number} params.每页数量 - 每页记录数，默认为20
   * @param {number} params.审核状态 - 审核状态筛选（可选）
   * @returns {Promise} API响应
   */
  async 获取用户寄样记录列表(params = {}) {
    const 请求参数 = {
      页码: params.页码 || 1,
      每页数量: params.每页数量 || 20,
      审核状态: params.审核状态
    }

    try {
      const response = await api.post('/kol/sample/list', 请求参数)

      console.log('获取寄样记录列表成功:', response.data)
      return {
        status: 100,
        data: response.data || { 列表: [], 总数: 0 },
        message: '获取寄样记录成功'
      }
    } catch (error) {
      console.error('获取寄样记录列表失败:', error)
      throw new Error(`获取寄样记录失败: ${error.message}`)
    }
  }

  /**
   * 获取达人寄样记录列表（用于样品管理模块）
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码，默认为1
   * @param {number} params.每页数量 - 每页记录数，默认为20
   * @param {number} params.审核状态 - 审核状态筛选（可选）
   * @returns {Promise} API响应
   */
  async 获取达人寄样记录列表(params = {}) {
    const 请求参数 = {
      页码: params.页码 || 1,
      每页数量: params.每页数量 || 20,
      审核状态: params.审核状态
    }

    try {
      const response = await api.post('/kol/sample/list', 请求参数)

      console.log('获取达人寄样记录列表成功:', response.data)
      return {
        status: 100,
        data: response.data || { 列表: [], 总数: 0 },
        message: '获取达人寄样记录成功'
      }
    } catch (error) {
      console.error('获取达人寄样记录列表失败:', error)
      throw new Error(`获取达人寄样记录失败: ${error.message}`)
    }
  }

  /**
   * 格式化产品规格数据
   * @param {Object} specs - 产品规格对象
   * @returns {string} JSON字符串
   */
  格式化产品规格(specs) {
    try {
      if (!specs || typeof specs !== 'object') {
        return null
      }
      return JSON.stringify(specs)
    } catch (error) {
      console.error('格式化产品规格失败:', error)
      return null
    }
  }

  /**
   * 解析产品规格数据
   * @param {string} specsJson - 产品规格JSON字符串
   * @returns {Object} 产品规格对象
   */
  解析产品规格(specsJson) {
    try {
      if (!specsJson || typeof specsJson !== 'string') {
        return {}
      }
      return JSON.parse(specsJson)
    } catch (error) {
      console.error('解析产品规格失败:', error)
      return {}
    }
  }

  /**
   * 验证寄样申请数据
   * @param {Object} sampleData - 寄样申请数据
   * @returns {Object} 验证结果
   */
  验证寄样数据(sampleData) {
    const errors = []

    // 必填字段验证
    if (!sampleData.达人id) {
      errors.push('达人id不能为空')
    }

    if (!sampleData.产品ID) {
      errors.push('请选择要寄送的产品')
    }

    if (!sampleData.收件人 || sampleData.收件人.trim() === '') {
      errors.push('收件人姓名不能为空')
    }

    if (!sampleData.收件地址 || sampleData.收件地址.trim() === '') {
      errors.push('收件地址不能为空')
    }

    if (!sampleData.收件电话 || sampleData.收件电话.trim() === '') {
      errors.push('收件电话不能为空')
    }

    // 电话号码格式验证
    if (sampleData.收件电话) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(sampleData.收件电话.replace(/\s+/g, ''))) {
        errors.push('请输入正确的手机号码')
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 获取产品规格选项
   * @param {Object} product - 产品信息
   * @returns {Array} 规格选项列表
   */
  获取产品规格选项(product) {
    try {
      if (!product || !product.产品规格) {
        return []
      }

      const specs = typeof product.产品规格 === 'string'
        ? JSON.parse(product.产品规格)
        : product.产品规格

      if (!specs || typeof specs !== 'object') {
        return []
      }

      // 将规格对象转换为选项列表
      const options = []
      Object.keys(specs).forEach(key => {
        const value = specs[key]
        if (Array.isArray(value)) {
          // 如果是数组，每个元素作为一个选项
          value.forEach(item => {
            options.push({
              key: key,
              label: `${key}: ${item}`,
              value: item,
              category: key
            })
          })
        } else {
          // 如果是单个值
          options.push({
            key: key,
            label: `${key}: ${value}`,
            value: value,
            category: key
          })
        }
      })

      return options
    } catch (error) {
      console.error('获取产品规格选项失败:', error)
      return []
    }
  }
}

// 创建并导出服务实例
const sampleService = new SampleService()
export default sampleService
