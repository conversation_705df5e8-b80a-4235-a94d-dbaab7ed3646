// API基础配置
export { default as api, handleTokenError, getCookie } from './api'

// 认证相关API
export { 
  authAPI,
  validatePhone,
  validatePassword,
  validateVerificationCode
} from './auth'

// 用户相关API
export { 
  userAPI,
  UserStatus,
  UserType,
  ContactType
} from './user'

// 订单相关API
export { default as orderService } from './order'

// 店铺管理相关API
export { default as storeService } from './storeService'
export { default as productService } from './productService'
export { default as douyinService } from './douyinService'
export { default as sampleService } from './sampleService'

// 统一导出所有服务
export default {
  auth: () => import('./auth').then(m => m.authAPI),
  user: () => import('./user').then(m => m.userAPI),
  order: () => import('./order').then(m => m.default),
  store: () => import('./storeService').then(m => m.default),
  product: () => import('./productService').then(m => m.default),
  douyin: () => import('./douyinService').then(m => m.default),
  sample: () => import('./sampleService').then(m => m.default)
}