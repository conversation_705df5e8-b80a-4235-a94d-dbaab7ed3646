import BaseService from './BaseService'

/**
 * 认证服务类
 * 继承BaseService，提供统一的API调用和错误处理
 */
class AuthService extends BaseService {
  constructor() {
    super({
      serviceName: 'AuthService',
      baseURL: '', // 认证接口在根路径
      defaultConfig: {
        timeout: 10000 // 认证接口使用较短超时
      }
    })
  }

  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名（手机号）
   * @param {string} loginData.password - 密码
   * @returns {Promise} API响应
   */
  async login(loginData) {
    return this.post('/user/login', {
      手机号: loginData.username, // 后端期望中文字段名
      密码: loginData.password
    }, {}, {
      showSuccessMessage: true,
      successMessage: '登录成功',
      errorMessage: '登录失败，请检查用户名和密码'
    })
  }

  /**
   * 用户注册
   * @param {Object} registerData - 注册数据
   * @param {string} registerData.phone - 手机号
   * @param {string} registerData.password - 密码
   * @param {string} registerData.verification_code - 验证码
   * @returns {Promise} API响应
   */
  async register(registerData) {
    return this.post('/register', registerData, {}, {
      showSuccessMessage: true,
      successMessage: '注册成功',
      errorMessage: '注册失败，请检查输入信息'
    })
  }

  /**
   * 发送短信验证码
   * @param {Object} smsData - 验证码请求数据
   * @param {string} smsData.phone - 手机号
   * @param {string} smsData.type - 验证码类型 ('register', 'reset_password', 'login')
   * @returns {Promise} API响应
   */
  async sendSmsCode(smsData) {
    // 将英文类型转换为中文类型（后端要求）
    const typeMapping = {
      'register': '注册',
      'reset_password': '重置密码',
      'login': '登录'
    }

    return this.post('/sms_code', {
      phone: smsData.phone,
      类型: typeMapping[smsData.type] || smsData.type // 后端期望中文字段名和中文值
    }, {}, {
      showSuccessMessage: true,
      successMessage: '验证码发送成功',
      errorMessage: '验证码发送失败'
    })
  }

  /**
   * 重置密码
   * @param {Object} resetData - 重置密码数据
   * @param {string} resetData.phone - 手机号
   * @param {string} resetData.verification_code - 验证码
   * @param {string} resetData.new_password - 新密码
   * @returns {Promise} API响应
   */
  async resetPassword(resetData) {
    return this.post('/password/forgot/reset', resetData, {}, {
      showSuccessMessage: true,
      successMessage: '密码重置成功',
      errorMessage: '密码重置失败'
    })
  }

  /**
   * 修改密码（需要登录）
   * @param {Object} changeData - 修改密码数据
   * @param {string} changeData.old_password - 旧密码
   * @param {string} changeData.new_password - 新密码
   * @returns {Promise} API响应
   */
  async changePassword(changeData) {
    return this.post('/update_password', changeData, {}, {
      showSuccessMessage: true,
      successMessage: '密码修改成功',
      errorMessage: '密码修改失败'
    })
  }

  /**
   * 获取用户权限（用于验证登录状态）
   * @returns {Promise} API响应
   */
  async getPermissions() {
    return this.get('/get_permission', {}, {}, {
      showErrorMessage: false, // 权限检查失败不显示错误提示
      logResponse: false
    })
  }

  /**
   * 激活账户
   * @param {Object} activateData - 激活数据
   * @param {string} activateData.code - 激活码
   * @returns {Promise} API响应
   */
  async activateAccount(activateData) {
    return this.post('/activate_code', activateData, {}, {
      showSuccessMessage: true,
      successMessage: '账户激活成功',
      showErrorMessage: false, // 不显示通用错误提示，由组件自己处理
      logResponse: false // 不记录响应日志
    })
  }

  /**
   * 退出登录（客户端清除token）
   * @returns {Promise} 始终成功
   */
  async logout() {
    // 清除本地存储
    localStorage.removeItem('crm_token')
    localStorage.removeItem('crm_user_info')
    localStorage.removeItem('crm_permissions')

    // 清除Cookie
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'

    return Promise.resolve({
      status: 100,
      message: '已退出登录',
      success: true
    })
  }
}

// 创建认证服务实例
const authService = new AuthService()

/**
 * 认证相关API服务（保持向后兼容）
 */
export const authAPI = {
  login: (loginData) => authService.login(loginData),
  register: (registerData) => authService.register(registerData),
  sendSmsCode: (smsData) => authService.sendSmsCode(smsData),
  resetPassword: (resetData) => authService.resetPassword(resetData),
  changePassword: (changeData) => authService.changePassword(changeData),
  getPermissions: () => authService.getPermissions(),
  activateAccount: (activateData) => authService.activateAccount(activateData),
  logout: () => authService.logout()
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {Object} 验证结果
 */
export const validatePassword = (password) => {
  const result = {
    isValid: false,
    errors: []
  }

  if (!password) {
    result.errors.push('密码不能为空')
    return result
  }

  if (password.length < 6) {
    result.errors.push('密码长度至少6位')
  }

  if (password.length > 20) {
    result.errors.push('密码长度不能超过20位')
  }

  result.isValid = result.errors.length === 0
  return result
}

/**
 * 验证验证码格式
 * @param {string} code - 验证码
 * @returns {boolean} 是否有效
 */
export const validateVerificationCode = (code) => {
  const codeRegex = /^\d{6}$/
  return codeRegex.test(code)
}

// 默认导出认证服务实例
export default authService