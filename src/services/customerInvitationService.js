/**
 * 客户邀请服务
 * 提供客户邀请相关的API调用功能
 */

import api from './api.js'

// API基础路径
const API_BASE_URL = '/customer/invite'

/**
 * 客户邀请服务类
 */
class CustomerInvitationService {
  /**
   * 发送客户邀请
   * @param {Object} inviteData 邀请数据
   * @param {string} inviteData.被邀请人手机号 被邀请人手机号
   * @param {string} inviteData.邀请消息 邀请消息（可选）
   * @param {number} inviteData.有效期天数 有效期天数（默认30天）
   * @returns {Promise<Object>} 邀请结果
   */
  async sendInvitation(inviteData) {
    try {
      console.log('发送客户邀请请求:', inviteData)

      // 参数验证 - 确保数据完整性
      if (!inviteData.被邀请人手机号) {
        throw new Error('被邀请人手机号不能为空')
      }

      // 手机号格式验证
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(inviteData.被邀请人手机号)) {
        throw new Error('请输入正确的手机号格式')
      }

      const response = await api.post(`${API_BASE_URL}/create`, inviteData)

      console.log('发送客户邀请响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '邀请发送成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '邀请发送失败'
        }
      }
    } catch (error) {
      console.error('发送客户邀请失败:', error)
      
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message)
      } else if (error.response?.status === 400) {
        throw new Error('请求参数错误，请检查输入信息')
      } else if (error.response?.status === 401) {
        throw new Error('未登录或登录已过期，请重新登录')
      } else if (error.response?.status === 403) {
        throw new Error('没有权限执行此操作')
      } else if (error.response?.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error('网络错误，请检查网络连接')
      }
    }
  }

  /**
   * 获取客户邀请列表
   * @param {Object} params 查询参数
   * @param {number} params.页码 页码（默认1）
   * @param {number} params.每页数量 每页数量（默认20）
   * @param {string} params.状态筛选 状态筛选（可选）
   * @returns {Promise<Object>} 邀请列表
   */
  async getInvitationList(params = {}) {
    try {
      const queryParams = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        状态筛选: params.状态筛选 || null
      }
      
      console.log('获取客户邀请列表请求:', queryParams)
      
      const response = await api.post(`${API_BASE_URL}/list`, queryParams)

      console.log('获取客户邀请列表响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '获取邀请列表成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '获取邀请列表失败'
        }
      }
    } catch (error) {
      console.error('获取客户邀请列表失败:', error)
      throw this.handleError(error, '获取邀请列表失败')
    }
  }

  /**
   * 获取邀请详情
   * @param {string} inviteCode 邀请码
   * @returns {Promise<Object>} 邀请详情
   */
  async getInvitationDetail(inviteCode) {
    try {
      console.log('获取邀请详情请求:', inviteCode)
      
      const response = await api.get(`${API_BASE_URL}/detail/${inviteCode}`)

      console.log('获取邀请详情响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '获取邀请详情成功'
        }
      } else {
        return {
          success: false,
          status: response.status,
          message: response.message || '获取邀请详情失败'
        }
      }
    } catch (error) {
      console.error('获取邀请详情失败:', error)
      
      // 特殊处理邀请相关的错误状态
      if (error.response?.status === 404) {
        throw new Error('邀请不存在或邀请码无效')
      } else if (error.response?.status === 410) {
        throw new Error('邀请已过期')
      } else if (error.response?.status === 409) {
        throw new Error('邀请已被使用')
      } else {
        throw this.handleError(error, '获取邀请详情失败')
      }
    }
  }

  /**
   * 获取邀请统计信息
   * @returns {Promise<Object>} 邀请统计
   */
  async getInvitationStatistics() {
    try {
      console.log('获取邀请统计请求')
      
      const response = await api.get(`${API_BASE_URL}/statistics`)

      console.log('获取邀请统计响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '获取统计信息成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '获取统计信息失败'
        }
      }
    } catch (error) {
      console.error('获取邀请统计失败:', error)
      throw this.handleError(error, '获取统计信息失败')
    }
  }



  /**
   * 处理注册邀请关联
   * @param {string} phone 手机号
   * @param {number} userId 用户id
   * @returns {Promise<Object>} 关联结果
   */
  async processRegistrationInvitation(phone, userId) {
    try {
      console.log('处理注册邀请关联请求:', { phone, userId })
      
      const response = await api.post(`${API_BASE_URL}/process-registration`, {
        手机号: phone,
        用户id: userId
      })

      console.log('处理注册邀请关联响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '邀请关联成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '邀请关联失败'
        }
      }
    } catch (error) {
      console.error('处理注册邀请关联失败:', error)
      throw this.handleError(error, '邀请关联失败')
    }
  }

  /**
   * 统一错误处理
   * @param {Error} error 错误对象
   * @param {string} defaultMessage 默认错误消息
   * @returns {Error} 处理后的错误
   */
  handleError(error, defaultMessage = '操作失败') {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    } else if (error.response?.status === 400) {
      return new Error('请求参数错误，请检查输入信息')
    } else if (error.response?.status === 401) {
      return new Error('未登录或登录已过期，请重新登录')
    } else if (error.response?.status === 403) {
      return new Error('没有权限执行此操作')
    } else if (error.response?.status >= 500) {
      return new Error('服务器内部错误，请稍后重试')
    } else if (error.code === 'NETWORK_ERROR') {
      return new Error('网络错误，请检查网络连接')
    } else {
      return new Error(defaultMessage)
    }
  }

  /**
   * 撤销客户邀请
   * @param {number} invitationId 邀请id
   * @returns {Promise<Object>} 撤销结果
   */
  async cancelInvitation(invitationId) {
    try {
      console.log('撤销客户邀请请求:', invitationId)

      if (!invitationId) {
        throw new Error('邀请id不能为空')
      }

      const response = await api.post(`${API_BASE_URL}/cancel/${invitationId}`)

      console.log('撤销客户邀请响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '邀请撤销成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '邀请撤销失败'
        }
      }
    } catch (error) {
      console.error('撤销客户邀请失败:', error)
      
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message)
      } else if (error.response?.status === 404) {
        throw new Error('邀请记录不存在')
      } else if (error.response?.status === 400) {
        throw new Error('请求参数错误')
      } else if (error.response?.status === 403) {
        throw new Error('没有权限执行此操作')
      } else if (error.response?.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error('网络错误，请检查网络连接')
      }
    }
  }

  /**
   * 重新发送客户邀请
   * @param {number} invitationId 邀请id
   * @returns {Promise<Object>} 重新发送结果
   */
  async resendInvitation(invitationId) {
    try {
      console.log('重新发送客户邀请请求:', invitationId)

      if (!invitationId) {
        throw new Error('邀请id不能为空')
      }

      const response = await api.post(`${API_BASE_URL}/resend/${invitationId}`)

      console.log('重新发送客户邀请响应:', response)

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message || '邀请重新发送成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '邀请重新发送失败'
        }
      }
    } catch (error) {
      console.error('重新发送客户邀请失败:', error)
      
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message)
      } else if (error.response?.status === 404) {
        throw new Error('邀请记录不存在')
      } else if (error.response?.status === 400) {
        throw new Error('请求参数错误')
      } else if (error.response?.status === 403) {
        throw new Error('没有权限执行此操作')
      } else if (error.response?.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error('网络错误，请检查网络连接')
      }
    }
  }
}

// 创建服务实例
const customerInvitationService = new CustomerInvitationService()

export default customerInvitationService

// 导出具体的方法供直接使用
export const {
  sendInvitation,
  getInvitationList,
  getInvitationDetail,
  getInvitationStatistics,
  getInvitationQuota,
  processRegistrationInvitation,
  cancelInvitation,
  resendInvitation
} = customerInvitationService
