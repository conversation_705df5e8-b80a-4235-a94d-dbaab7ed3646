import api from '../api'
import { warnIfNonStandardFormat } from '@/utils/apiUtils';

/**
 * 产品对接进度管理服务
 * 提供产品对接进度的CRUD操作和管理功能
 * 基于微信产品对接进度表的实际数据结构优化
 */
class ProgressService {
  /**
   * 获取产品对接进度列表
   * @param {Object} params - 查询参数
   * @param {number} params.微信id - 我方微信号ID
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @param {string} params.产品名称 - 产品名称搜索
   * @param {number} params.意向状态 - 意向状态筛选
   * @param {number} params.样品状态 - 样品状态筛选
   * @param {number} params.排期状态 - 排期状态筛选
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序方式 - 排序方式(asc/desc)
   * @returns {Promise<Object>} 进度列表数据
   */
  async getProgressList(params = {}) {
    try {
      const queryParams = {
        微信id: params.微信id,
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 10,
        产品名称: params.产品名称 || null,
        对方微信号: params.对方微信号 || null,
        意向状态: params.意向状态 !== undefined ? params.意向状态 : null,
        样品状态: params.样品状态 !== undefined ? params.样品状态 : null,
        排期状态: params.排期状态 !== undefined ? params.排期状态 : null,
        排序字段: params.排序字段 || '更新时间',
        排序方式: params.排序方式 || 'desc'
      }
      
      // 必须提供微信id才能查询
      if (!queryParams.微信id) {
        console.warn('获取产品对接进度列表需要提供微信id');
        return {
          status: 100,
          data: {
            列表: [],
            总数: 0,
            页码: 1,
            每页条数: 10,
            总页数: 0
          }
        };
      }

      console.log('POST', '/wechat/board/board-list', queryParams);
      const response = await api.post('/wechat/board/board-list', queryParams)
      
      // 检查格式并发出警告（如果不规范）
      warnIfNonStandardFormat(response, '获取进度列表');
      
      if (response && response.status === 100) {
        // 处理响应数据，从data字段获取业务数据
        const responseData = response.data || {};
        
        // 对每条记录进行处理，增加前端所需的展示字段
        if (responseData.列表 && Array.isArray(responseData.列表)) {
          responseData.列表 = responseData.列表.map(item => {
            // 计算综合进度和进度百分比
            const { 综合状态, 进度百分比 } = this.calculateOverallStatus(item);
            
            return {
              ...item,
              id: item.进度ID, // 为表格添加唯一key
              综合状态,
              进度百分比,
            };
          });
        }
      
      return {
        status: 100,
          data: responseData
        };
      }
      
      // 处理错误响应
      return {
        status: response?.status || 101,
        message: response?.message || '获取列表失败',
        data: null // 确保返回结构包含data字段
      };
    } catch (error) {
      console.error('获取产品对接进度列表失败:', error)
      throw new Error(`获取进度列表失败: ${error.message}`)
    }
  }

  /**
   * 获取用户产品列表（用于创建进度时选择产品）
   * @param {Object} params - 查询参数
   * @param {string} params.productName - 产品名称
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @returns {Promise<any>}
   */
  async getUserProductList(params = {}) {
    try {
      const response = await api.post('/products/user-products', {
        productName: params.productName || '',
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20,
      });

      return {
        status: 100,
        data: response.data || { 列表: [], 总数: 0 }
      }
    } catch (error) {
      console.error('获取用户产品列表失败:', error)
      throw new Error(`获取产品列表失败: ${error.message}`)
    }
  }

  /**
   * 获取用户微信好友列表（用于创建进度时选择好友）
   * @returns {Promise<Object>} 微信好友列表
   */
  async getUserWeChatFriends() {
    try {
      const response = await api.post('/wechat/user-friends', {
        页码: 1,
        每页条数: 1000 // 获取所有好友用于选择
      })
      
      return {
        status: 100,
        data: response.data?.好友列表 || []
      }
    } catch (error) {
      console.error('获取微信好友列表失败:', error)
      throw new Error(`获取好友列表失败: ${error.message}`)
    }
  }

  /**
   * 创建产品对接进度
   * @param {Object} progressData - 进度数据
   * @param {number} progressData.用户id - 用户id
   * @param {number} progressData.合作产品ID - 合作产品ID
   * @param {number} progressData.我方微信号ID - 我方微信号ID
   * @param {number} progressData.对方微信号ID - 对方微信号ID
   * @param {string} progressData.备注 - 备注信息
   * @returns {Promise<Object>} 创建结果
   */
  async createProgress(progressData) {
    try {
      // 修正请求数据结构，匹配后端期望的字段
      const requestData = {
        用户id: progressData.用户id,
        合作产品ID: progressData.合作产品ID,
        我方微信号ID: progressData.我方微信号ID,
        对方微信号ID: progressData.对方微信号ID,
        备注: progressData.备注 || ''
      }

      const response = await api.post('/wechat/product-progress/create', requestData)
      
      return {
        status: 100,
        message: response.message || '产品对接进度创建成功',
        data: response.data
      }
    } catch (error) {
      console.error('创建产品对接进度失败:', error)
      throw new Error(`创建进度失败: ${error.message}`)
    }
  }

  /**
   * 更新产品对接进度
   * @param {Object} progressData - 进度数据
   * @param {number} progressData.进度ID - 进度ID
   * @param {number} progressData.回复状态 - 回复状态
   * @param {number} progressData.意向状态 - 意向状态
   * @param {number} progressData.样品状态 - 样品状态
   * @param {number} progressData.排期状态 - 排期状态
   * @param {string} progressData.排期开始时间 - 排期开始时间
   * @param {string} progressData.排期结束时间 - 排期结束时间
   * @param {number} progressData.开播状态 - 开播状态
   * @param {string} progressData.销售额 - 销售额
   * @returns {Promise<Object>} 更新结果
   */
  async updateProgress(progressData) {
    try {
      // 修正请求数据结构，基于实际表字段
      const requestData = {
        进度ID: progressData.进度ID,
        回复状态: progressData.回复状态,
        意向状态: progressData.意向状态,
        样品状态: progressData.样品状态,
        排期状态: progressData.排期状态,
        排期开始时间: progressData.排期开始时间,
        排期结束时间: progressData.排期结束时间,
        开播状态: progressData.开播状态,
        销售额: progressData.销售额
      }

      const response = await api.post('/wechat/product-progress/update', requestData)
      
      return {
        status: 100,
        message: response.message || '产品对接进度更新成功',
        data: response.data
      }
    } catch (error) {
      console.error('更新产品对接进度失败:', error)
      throw new Error(`更新进度失败: ${error.message}`)
    }
  }

  /**
   * 删除产品对接进度
   * @param {number} progressId - 进度ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteProgress(progressId) {
    try {
      const response = await api.post('/wechat/product-progress/delete', {
        进度ID: progressId
      })
      
      return {
        status: 100,
        message: response.message || '产品对接进度删除成功'
      }
    } catch (error) {
      console.error('删除产品对接进度失败:', error)
      throw new Error(`删除进度失败: ${error.message}`)
    }
  }

  /**
   * 设置微信对接状态
   * @param {Object} statusData - 对接状态数据
   * @param {number} statusData.我方微信号ID - 我方微信号ID
   * @param {number} statusData.对方微信号ID - 对方微信号ID
   * @param {number} statusData.合作产品ID - 合作产品ID
   * @param {number} statusData.好友状态 - 好友状态
   * @param {number} statusData.回复状态 - 回复状态
   * @param {number} statusData.意向状态 - 意向状态
   * @param {number} statusData.样品状态 - 样品状态
   * @param {number} statusData.排期状态 - 排期状态
   * @param {string} statusData.排期开始时间 - 排期开始时间
   * @param {string} statusData.排期结束时间 - 排期结束时间
   * @param {number} statusData.开播状态 - 开播状态
   * @param {string} statusData.销售额 - 销售额
   * @returns {Promise<Object>} 设置结果
   */
  async setWeChatStatus(statusData) {
    try {
      const response = await api.post('/wechat/set_wechat_status', statusData)
      
      return {
        status: 100,
        message: response.message || '微信对接状态设置成功',
        data: response.data
      }
    } catch (error) {
      console.error('设置微信对接状态失败:', error)
      throw new Error(`设置对接状态失败: ${error.message}`)
    }
  }

  /**
   * 查询微信对接状态
   * @param {Object} queryData - 查询参数
   * @param {string} queryData.我方微信号 - 我方微信号
   * @param {string} queryData.对方微信号 - 对方微信号
   * @param {number} queryData.合作产品ID - 合作产品ID
   * @returns {Promise<Object>} 查询结果
   */
  async getWeChatStatus(queryData) {
    try {
      const response = await api.post('/wechat/get_wechat_status', queryData)
      
      return {
        status: 100,
        data: response.data || {}
      }
    } catch (error) {
      console.error('查询微信对接状态失败:', error)
      throw new Error(`查询对接状态失败: ${error.message}`)
    }
  }

  /**
   * 获取或创建微信id
   * @param {string} wechatId - 微信号
   * @returns {Promise<Object>} 微信id结果
   */
  async getOrCreateWeChatId(wechatId) {
    try {
      const response = await api.post('/wechat/get_wechat_id', {
        微信号: wechatId
      })
      
      return {
        status: 100,
        data: response.data || {}
      }
    } catch (error) {
      console.error('获取或创建微信id失败:', error)
      throw new Error(`获取微信id失败: ${error.message}`)
    }
  }

  /**
   * 格式化进度数据（基于实际表结构）
   * @param {Object} progress - 原始进度数据
   * @returns {Object} 格式化后的数据
   */
  formatProgressData(progress) {
    return {
      id: progress.id,
      用户id: progress.用户id,
      合作产品ID: progress.合作产品ID,
      产品名称: progress.产品名称 || `产品${progress.合作产品ID}`,
      我方微信号ID: progress.我方微信号ID,
      对方微信号ID: progress.对方微信号ID,
      回复状态: progress.回复状态,
      意向状态: progress.意向状态,
      意向状态更新时间: progress.意向状态更新时间,
      样品状态: progress.样品状态,
      样品状态更新时间: progress.样品状态更新时间,
      排期状态: progress.排期状态,
      排期开始时间: progress.排期开始时间,
      排期结束时间: progress.排期结束时间,
      开播状态: progress.开播状态,
      销售额: progress.销售额,
      创建时间: progress.创建时间,
      更新时间: progress.更新时间,
      // 计算综合状态和进度
      综合状态: this.calculateOverallStatus(progress),
      进度百分比: this.calculateProgressPercentage(progress)
    }
  }

  /**
   * 计算综合状态
   * @param {Object} progress - 进度数据
   * @returns {string} 综合状态
   */
  calculateOverallStatus(progress) {
    // 基于多个状态字段计算综合状态
    if (progress.开播状态 > 0) return '已开播'
    if (progress.排期状态 === 2) return '已排期'
    if (progress.样品状态 >= 4) return '样品已到'
    if (progress.样品状态 === 2) return '申样通过'
    if (progress.意向状态 === 1) return '有意向'
    if (progress.回复状态 === 1) return '已回复'
    return '进行中'
  }

  /**
   * 计算进度百分比
   * @param {Object} progress - 进度数据
   * @returns {number} 进度百分比
   */
  calculateProgressPercentage(progress) {
    let percentage = 0
    
    // 基于各状态计算进度百分比
    if (progress.回复状态 === 1) percentage += 20
    if (progress.意向状态 === 1) percentage += 20
    if (progress.样品状态 >= 2) percentage += 20
    if (progress.排期状态 >= 1) percentage += 20
    if (progress.开播状态 > 0) percentage += 20
    
    return Math.min(percentage, 100)
  }

  /**
   * 获取回复状态选项
   * @returns {Array} 回复状态列表
   */
  getReplyStatusOptions() {
    return [
      { label: '未回复', value: 0 },
      { label: '已回复', value: 1 }
    ]
  }

  /**
   * 获取意向状态选项
   * @returns {Array} 意向状态列表
   */
  getIntentionStatusOptions() {
    return [
      { label: '无意向', value: -1 },
      { label: '未沟通', value: 0 },
      { label: '有意向', value: 1 }
    ]
  }

  /**
   * 获取样品状态选项
   * @returns {Array} 样品状态列表
   */
  getSampleStatusOptions() {
    return [
      { label: '初始', value: 0 },
      { label: '不需要', value: -1 },
      { label: '申样', value: 1 },
      { label: '申样被拒', value: -2 },
      { label: '申样通过', value: 2 },
      { label: '已出单', value: 3 },
      { label: '出单异常', value: -3 },
      { label: '到样', value: 4 },
      { label: '到样异常', value: -4 },
      { label: '主播已取样', value: 5 },
      { label: '主播取样失败', value: -5 }
    ]
  }

  /**
   * 获取排期状态选项
   * @returns {Array} 排期状态列表
   */
  getScheduleStatusOptions() {
    return [
      { label: '未排期', value: 0 },
      { label: '模糊排期', value: 1 },
      { label: '明确排期', value: 2 }
    ]
  }

  /**
   * 获取开播状态选项
   * @returns {Array} 开播状态列表
   */
  getBroadcastStatusOptions() {
    return [
      { label: '未开播', value: 0 },
      { label: '开播1次', value: 1 },
      { label: '开播2次', value: 2 },
      { label: '开播3次', value: 3 },
      { label: '开播4次及以上', value: 4 }
    ]
  }

  /**
   * 获取综合状态选项（用于筛选）
   * @returns {Array} 综合状态列表
   */
  getOverallStatusOptions() {
    return [
      { label: '全部', value: '' },
      { label: '进行中', value: '进行中' },
      { label: '已回复', value: '已回复' },
      { label: '有意向', value: '有意向' },
      { label: '申样通过', value: '申样通过' },
      { label: '样品已到', value: '样品已到' },
      { label: '已排期', value: '已排期' },
      { label: '已开播', value: '已开播' }
    ]
  }

  /**
   * 根据状态值获取状态标签配置
   * @param {string} statusType - 状态类型
   * @param {number} statusValue - 状态值
   * @returns {Object} 状态标签配置
   */
  getStatusTagConfig(statusType, statusValue) {
    const configs = {
      回复状态: {
        0: { color: 'default', text: '未回复' },
        1: { color: 'success', text: '已回复' }
      },
      意向状态: {
        '-1': { color: 'error', text: '无意向' },
        0: { color: 'default', text: '未沟通' },
        1: { color: 'success', text: '有意向' }
      },
      样品状态: {
        0: { color: 'default', text: '初始' },
        '-1': { color: 'warning', text: '不需要' },
        1: { color: 'processing', text: '申样' },
        '-2': { color: 'error', text: '申样被拒' },
        2: { color: 'success', text: '申样通过' },
        3: { color: 'cyan', text: '已出单' },
        '-3': { color: 'error', text: '出单异常' },
        4: { color: 'blue', text: '到样' },
        '-4': { color: 'error', text: '到样异常' },
        5: { color: 'green', text: '主播已取样' },
        '-5': { color: 'error', text: '主播取样失败' }
      },
      排期状态: {
        0: { color: 'default', text: '未排期' },
        1: { color: 'processing', text: '模糊排期' },
        2: { color: 'success', text: '明确排期' }
      },
      开播状态: {
        0: { color: 'default', text: '未开播' },
        1: { color: 'blue', text: '开播1次' },
        2: { color: 'cyan', text: '开播2次' },
        3: { color: 'green', text: '开播3次' },
        4: { color: 'purple', text: '开播4次+' }
      }
    }

    const statusConfig = configs[statusType]
    if (!statusConfig) return { color: 'default', text: '未知' }
    
    return statusConfig[statusValue] || { color: 'default', text: '未知' }
  }
}

// 创建并导出服务实例
const progressService = new ProgressService()
export default progressService 