import api from '../api'
import { isApiSuccess, getApiData, debugApiResponse, processPaginationResponse, processFormResponse } from '../../utils/apiUtils'

/**
 * 获取状态标签的工具函数
 * @param {string} status - 状态
 * @returns {Object} 状态标签对象
 */
const getStatusTag = (status) => {
  const statusMap = {
    正常: { color: 'green', text: '正常' },
    暂停: { color: 'orange', text: '暂停' },
    离职: { color: 'red', text: '离职' },
    已移除: { color: 'gray', text: '已移除' }
  };
  return statusMap[status] || { color: 'gray', text: '未知' };
}

/**
 * 获取角色权重的工具函数
 * @param {string} role - 角色
 * @returns {number} 权重数值
 */
const getRoleWeight = (role) => {
  const roleWeights = {
    '创建者': 1,
    '负责人': 2, 
    '团队负责人': 2,
    '管理员': 3,
    '成员': 4,
    '访客': 5
  };
  return roleWeights[role] || 6;
}

/**
 * 团队成员服务
 * 处理团队成员相关操作：获取成员列表、邀请成员、移除成员等
 */
export const teamMemberService = {
  /**
   * 获取团队成员列表
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 页码，默认1
   * @param {number} params.每页数量 - 每页数量，默认10
   * @param {string} params.搜索关键词 - 搜索关键词
   * @param {string} params.成员状态 - 成员状态筛选
   * @param {string} params.角色筛选 - 角色筛选
   * @returns {Promise} API响应
   */
  async getTeamMembers(params) {
    try {
      console.log("🔄 getTeamMembers 请求参数:", params);

      // 构建请求参数，统一使用中文字段名
      const requestParams = {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        搜索关键词: params.搜索关键词,
        成员状态: params.成员状态 || '正常',
        角色筛选: params.角色筛选
      };

      console.log("🔄 getTeamMembers 实际发送参数:", requestParams);
      
      const response = await api.post('/team/members', requestParams);
      
      console.log("🔄 getTeamMembers 原始响应:", response);
      debugApiResponse(response, '获取团队成员列表')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response);
        console.log("🔄 getTeamMembers 提取的数据:", data);
        
        // 修复：直接使用后端返回的中文字段名 '成员列表'
        const membersList = data?.成员列表 || [];
        console.log("🔄 getTeamMembers 成员列表原始数据:", membersList);
        
        // 简化数据格式化，避免重复处理
        const formattedMembers = membersList.map(member => ({
          ...member,
          // 确保字段名称统一
          角色: member.角色 || '成员',
          昵称: member.昵称 || member.nickname || '未知用户',
          // 格式化时间字段
          加入时间格式化: member.加入时间 ? 
            new Date(member.加入时间).toLocaleString('zh-CN') : '',
          // 状态标签 - 修复：调用独立函数
          状态标签: getStatusTag(member.状态),
          // 角色权重（用于排序）- 修复：调用独立函数
          角色权重: getRoleWeight(member.角色)
        }));
        
        console.log("🔄 getTeamMembers 格式化后的成员列表:", formattedMembers);
        
        // 按角色权重和加入时间排序
        formattedMembers.sort((a, b) => {
          if (a.角色权重 !== b.角色权重) {
            return a.角色权重 - b.角色权重;
          }
          return new Date(a.加入时间) - new Date(b.加入时间);
        });
        
        const result = {
          status: 100,
          data: {
            成员列表: formattedMembers,
            总数: data?.总数 || formattedMembers.length,
            当前页: data?.当前页 || 1,
            每页数量: data?.每页数量 || requestParams.每页数量,
            总页数: data?.总页数 || 1
          }
        };
        
        console.log("🔄 getTeamMembers 返回结果:", result);
        return result;
      } else {
        console.error("🔄 getTeamMembers 失败:", response.message || '获取团队成员列表失败');
        throw new Error(response.message || '获取团队成员列表失败');
      }
    } catch (error) {
      console.error('获取团队成员列表失败:', error)
      throw new Error('获取团队成员列表失败: ' + error.message)
    }
  },

  /**
   * 通过手机号邀请成员
   * @param {Object} params - 邀请参数
   * @param {string} params.手机号 - 手机号
   * @param {number} params.团队id - 团队id
   * @param {string} params.角色 - 角色
   * @param {Array} params.权限列表 - 权限列表
   * @returns {Promise} API响应
   */
  async inviteByPhone(params) {
    try {
      const response = await api.post('/team/invite/by-phone', {
        手机号: params.手机号,
        团队id: params.团队id,
        角色: params.角色,
        权限列表: params.权限列表 || []
      })
      
      debugApiResponse(response, '手机号邀请成员')
      
      const result = processFormResponse(response, '邀请发送成功', '邀请失败')
      
      if (result.success) {
        return {
          status: 100,
          data: result.data,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('手机号邀请失败:', error)
      throw new Error(error.message)
    }
  },

  /**
   * 批量邀请成员
   * @param {Object} params - 邀请参数
   * @param {Array} params.手机号列表 - 手机号列表
   * @param {number} params.团队id - 团队id
   * @param {string} params.角色 - 角色
   * @param {Array} params.权限列表 - 权限列表
   * @returns {Promise} API响应
   */
  async inviteMembers(params) {
    try {
      // 使用循环调用单个邀请接口实现批量邀请
      const results = []
      const totalCount = params.手机号列表.length
      let successCount = 0
      
      for (const 手机号 of params.手机号列表) {
        try {
          const response = await api.post('/team/invite/by-phone', {
            手机号: 手机号,
            团队id: params.团队id,
            角色: params.角色,
            权限列表: params.权限列表 || []
          })
          
          if (isApiSuccess(response)) {
            results.push({
              手机号: 手机号,
              成功: true,
              消息: '邀请发送成功'
            })
            successCount++
          } else {
            results.push({
              手机号: 手机号,
              成功: false,
              消息: response.message || '邀请失败'
            })
          }
        } catch (error) {
          results.push({
            手机号: 手机号,
            成功: false,
            消息: error.message || '邀请发送失败'
          })
        }
      }
      
      return {
        status: 100,
        data: {
          邀请结果: results,
          成功数量: successCount,
          总数量: totalCount,
          成功率: totalCount > 0 ? Math.round((successCount / totalCount) * 100) : 0
        },
        message: `已发送 ${successCount} 个邀请，${totalCount - successCount} 个失败`
      }
    } catch (error) {
      console.error('批量邀请失败:', error)
      throw new Error('批量邀请失败: ' + error.message)
    }
  },

  /**
   * 移除团队成员
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.用户id - 用户id
   * @param {string} params.移除原因 - 移除原因
   * @returns {Promise} API响应
   */
  async removeMember(params) {
    try {
      const response = await api.post('/team/member/remove', {
        团队id: params.团队id,
        用户id: params.用户id,
        移除原因: params.移除原因 || ''
      })
      
      debugApiResponse(response, '移除团队成员')
      
      const result = processFormResponse(response, '成员已移除', '移除成员失败')
      
      if (result.success) {
        return {
          status: 100,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('移除成员失败:', error)
      throw new Error('移除成员失败: ' + error.message)
    }
  },

  /**
   * 更新成员角色和权限
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.目标用户id - 目标用户id
   * @param {string} params.新角色 - '团队负责人' 或 '成员'
   * @returns {Promise} API响应
   */
  async updateMemberRole(params) {
    try {
      const response = await api.post('/team/members/update-role', {
        团队id: params.团队id,
        目标用户id: params.目标用户id,
        新角色: params.新角色
      })

      debugApiResponse(response, '更新成员角色')

      const result = processFormResponse(response, '角色更新成功', '更新角色失败')

      if (result.success) {
        return {
          status: 100,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('更新成员角色失败:', error)
      throw new Error('更新成员角色失败: ' + error.message)
    }
  },

  /**
   * 获取成员详细权限
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.用户id - 用户id
   * @returns {Promise} API响应
   */
  async getMemberDetailedPermissions(params) {
    try {
      const response = await api.post('/team/permissions/check-user-team-permissions', {
        团队id: params.团队id,
        用户id: params.用户id
      })
      
      debugApiResponse(response, '获取成员详细权限')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        return {
          status: 100,
          data: {
            权限列表: data?.权限列表 || [],

            权限统计: data?.权限统计 || {}
          }
        }
      } else {
        throw new Error(response.message || '获取成员权限失败')
      }
    } catch (error) {
      console.error('获取成员详细权限失败:', error)
      throw new Error('获取成员详细权限失败: ' + error.message)
    }
  },

  /**
   * 搜索用户
   * @param {Object} params - 搜索参数
   * @param {string} params.关键词 - 搜索关键词（手机号、姓名等）
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @returns {Promise} API响应
   */
  async searchUsers(params) {
    try {
      const response = await api.post('/user/search', {
        关键词: params.关键词,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10
      })
      
      debugApiResponse(response, '搜索用户')
      
      const result = processPaginationResponse(response, '用户列表')
      
      if (result.success) {
        return {
          status: 100,
          data: {
            用户列表: result.list,
            总数: result.total
          }
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('搜索用户失败:', error)
      throw new Error('搜索用户失败: ' + error.message)
    }
  }
} 