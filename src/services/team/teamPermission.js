import api from '../api'
import { isApiSuccess, getApiData, debugApiResponse, processPaginationResponse, processFormResponse } from '../../utils/apiUtils'

/**
 * 团队权限服务
 * 处理团队权限相关操作：权限检查、权限设置（基于直接权限分配）
 */
export const teamPermissionService = {
  /**
   * 检查用户在团队中的权限状态
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.用户id - 用户id（可选，不传则获取当前用户）
   * @returns {Promise} API响应
   */
  async getUserTeamPermissionStatus(params) {
    try {
      // 参数验证和类型转换
      if (!params || !params.团队id) {
        throw new Error('团队id是必需参数')
      }
      
      // 确保团队id是整数类型
      const 团队id = parseInt(params.团队id)
      if (isNaN(团队id)) {
        throw new Error('团队id必须是有效的数字')
      }
      
      // 用户id可选，如果提供则转换为整数
      let 用户id = null
      if (params.用户id !== undefined && params.用户id !== null) {
        用户id = parseInt(params.用户id)
        if (isNaN(用户id)) {
          throw new Error('用户id必须是有效的数字')
        }
      }
      
      const response = await api.post('/team/permissions/check-user-team-permissions', {
        团队id: 团队id,
        用户id: 用户id
      })
      
      debugApiResponse(response, '检查用户团队权限状态')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 标准化权限状态数据（使用中文字段名）
        const permissionStatus = {
          用户角色: data?.用户角色 || '访客',
          权限列表: data?.权限列表 || [],
          是否团队创建者: Boolean(data?.是否团队创建者),
          是否团队负责人: Boolean(data?.是否团队负责人),
          在团队中: Boolean(data?.在团队中),
          
          // 快捷权限检查（由后端计算，使用中文字段名）
          能否查看团队: Boolean(data?.能否查看团队),
          能否邀请成员: Boolean(data?.能否邀请成员),
          能否管理权限: Boolean(data?.能否管理权限),
          能否移除成员: Boolean(data?.能否移除成员),
          能否管理邀请: Boolean(data?.能否管理邀请),
          能否查看成员: Boolean(data?.能否查看成员),
          能否编辑团队: Boolean(data?.能否编辑团队),
          能否解散团队: Boolean(data?.能否解散团队),
          能否删除团队: Boolean(data?.能否删除团队),
          能否查看统计: Boolean(data?.能否查看统计),
          
          // 权限检查函数
          hasPermission: (permissionCode) => {
            if (!data?.权限列表 || !Array.isArray(data.权限列表)) {
              return false
            }
            return data.权限列表.includes(permissionCode)
          }
        }
        
        return {
          status: 100,
          data: permissionStatus
        }
      } else {
        throw new Error(response.message || '获取权限状态失败')
      }
    } catch (error) {
      console.error('获取用户团队权限状态失败:', error)
      throw new Error('获取权限状态失败: ' + error.message)
    }
  },

  /**
   * 获取团队所有可用权限列表
   * @returns {Promise<Object>} 包含按分类组织的权限列表的对象
   */
  async getPermissionList() {
    try {
      const response = await api.post('/team/permissions/list', {})
      
      debugApiResponse(response, '获取团队所有可用权限列表')
      
      // 根据 标准化API响应 格式，数据位于 data 字段
      if (isApiSuccess(response)) {
        // 数据位于data字段，这是后端标准化API响应的规范
        return {
          status: response.status,
          data: response.data
        }
      } else {
        throw new Error(response.message || '获取权限列表失败')
      }
    } catch (error) {
      console.error('获取团队权限列表失败:', error)
      throw error // 向上传递错误，由调用者处理
    }
  },

  /**
   * 更新团队成员权限
   * 
   * @param {Object} params - 请求参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.用户id - 要设置权限的用户id
   * @param {Array<string>} params.权限列表 - 要授予的权限代码列表
   * @returns {Promise<Object>} 包含状态码和数据的响应对象
   */
  async updateMemberPermissions(params) {
    try {
      // 调用后端更新成员权限API
      const response = await api.post('/team/permissions/update-member-permissions', params)
      
      debugApiResponse(response, '更新团队成员权限')
      
      // 返回标准化的响应数据
      if (isApiSuccess(response)) {
        return {
          status: response.status,
          data: response.data,
          message: response.message
        }
      } else {
        // 处理业务错误
        return {
          status: response.status,
          message: response.message || '更新权限失败'
        }
      }
    } catch (error) {
      // 处理网络或其他异常
      console.error('更新团队成员权限失败:', error)
      throw error
    }
  },

  /**
   * 获取权限变更日志
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 页码（默认1）
   * @param {number} params.每页数量 - 每页数量（默认20）
   * @param {string} params.操作类型 - 操作类型筛选（可选）
   * @param {number} params.目标用户id - 目标用户id筛选（可选）
   * @returns {Promise} API响应
   */
  async getPermissionLogs(params) {
    try {
      const response = await api.post('/team/permissions/logs/list', {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        操作类型: params.操作类型,
        目标用户id: params.目标用户id
      })
      
      debugApiResponse(response, '获取权限变更日志')
      
      return processPaginationResponse(response, '权限日志列表')
    } catch (error) {
      console.error('获取权限变更日志失败:', error)
      throw new Error('获取权限日志失败: ' + error.message)
    }
  }
} 