import api from './api'

/**
 * 店铺管理服务
 * Store Management Service
 */
class StoreService {
  constructor() {
    this.baseUrl = ''
  }

  /**
   * 获取用户店铺列表
   * Get user store list
   * @returns {Promise<Object>} Store list data
   */
  async getUserStores() {
    try {
      const response = await api.get('/shops')
      return response
    } catch (error) {
      console.error('Failed to fetch user stores:', error)
      throw error
    }
  }

  /**
   * 获取店铺详情
   * Get store detail
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Store detail data
   */
  async getStoreDetail(storeId) {
    try {
      const response = await api.post('/store/detail', {
        店铺id: storeId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch store detail:', error)
      throw error
    }
  }

  /**
   * 绑定店铺
   * Bind store to user
   * @param {Object} storeData - Store binding data
   * @returns {Promise<Object>} Binding result
   */
  async bindStore(storeData) {
    try {
      const response = await api.post('/store/bind', storeData)
      return response
    } catch (error) {
      console.error('Failed to bind store:', error)
      throw error
    }
  }

  /**
   * 解绑店铺
   * Unbind store from user
   * @param {number} storeId - Store ID
   * @returns {Promise<Object>} Unbinding result
   */
  async unbindStore(storeId) {
    try {
      const response = await api.post('/store/unbind', {
        店铺id: storeId
      })
      return response
    } catch (error) {
      console.error('Failed to unbind store:', error)
      throw error
    }
  }

  /**
   * 获取店铺统计数据
   * Get store statistics
   * @param {number} storeId - Store ID (optional)
   * @returns {Promise<Object>} Store statistics
   */
  async getStoreStats(storeId = null) {
    try {
      const requestData = {}
      if (storeId) {
        requestData.店铺id = storeId
      }
      
      const response = await api.post('/store/stats', requestData)
      return response
    } catch (error) {
      console.error('Failed to fetch store stats:', error)
      throw error
    }
  }

  /**
   * 更新店铺信息
   * Update store information
   * @param {Object} storeData - Store data to update
   * @returns {Promise<Object>} Update result
   */
  async updateStore(storeData) {
    try {
      const response = await api.post('/store/update', storeData)
      return response
    } catch (error) {
      console.error('Failed to update store:', error)
      throw error
    }
  }
}

// 创建并导出服务实例
const storeService = new StoreService()
export default storeService
