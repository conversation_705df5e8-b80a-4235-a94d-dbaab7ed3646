import api from './api'

/**
 * 产品管理服务
 * Product Management Service
 */
class ProductService {
  constructor() {
    this.baseUrl = '/product'
  }

  /**
   * Parse product information using AI
   * 使用AI解析产品信息
   * @param {Object} params - Request parameters
   * @param {string} params.productText - Product information text
   * @param {number} params.productId - Product ID (optional, for updating existing product)
   * @returns {Promise<Object>} Parsed product data
   */
  async parseProduct(params = {}) {
    try {
      // Convert text to Base64 encoding
      const base64Text = btoa(unescape(encodeURIComponent(params.productText)))

      const response = await api.post(`${this.baseUrl}/parse`, {
        产品信息文本_base64: base64Text,
        产品id: params.productId
      }, {
        timeout: 120000, // 2分钟超时，AI解析需要更长时间
        retry: false // 禁用重试，避免重复解析
      })

      return response
    } catch (error) {
      console.error('Failed to parse product:', error)
      throw error
    }
  }

  /**
   * Get product list
   * 获取产品列表
   * @param {Object} params - Request parameters
   * @param {number} params.页码 - Page number
   * @param {number} params.每页数量 - Items per page
   * @param {string} params.产品名称 - Product name filter
   * @param {string} params.产品分类 - Product category filter
   * @param {number} params.状态 - Status filter
   * @returns {Promise<Object>} Product list data
   */
  async getProductList(params = {}) {
    try {
      // 使用POST请求，参数通过请求体传递
      const requestData = {
        页码: params.页码 || 1,
        每页条数: params.每页条数 || params.每页数量 || 10
      }

      // 添加可选参数
      if (params.产品名称 || params.productName) {
        requestData.productName = params.产品名称 || params.productName
      }

      const response = await api.post(`${this.baseUrl}/list`, requestData)
      return response
    } catch (error) {
      console.error('Failed to fetch product list:', error)
      throw error
    }
  }

  /**
   * Get product detail
   * 获取产品详情
   * @param {number} productId - Product ID
   * @returns {Promise<Object>} Product detail data
   */
  async getProductDetail(productId) {
    try {
      const response = await api.post(`${this.baseUrl}/user/detail`, {
        产品ID: productId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch product detail:', error)
      throw error
    }
  }

  /**
   * Create new product
   * 创建新产品
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product data
   */
  async createProduct(productData) {
    try {
      const response = await api.post(`${this.baseUrl}/user/add`, productData)
      return response
    } catch (error) {
      console.error('Failed to create product:', error)
      throw error
    }
  }

  /**
   * Update product
   * 更新产品
   * @param {Object} productData - Product data with ID
   * @returns {Promise<Object>} Updated product data
   */
  async updateProduct(productData) {
    try {
      const response = await api.post(`${this.baseUrl}/user/update`, productData)
      return response
    } catch (error) {
      console.error('Failed to update product:', error)
      throw error
    }
  }

  /**
   * Delete product
   * 删除产品
   * @param {number} productId - Product ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteProduct(productId) {
    try {
      console.log('🔍 ProductService删除产品 - ID:', productId, '类型:', typeof productId)
      const requestData = {
        产品ID: productId
      }
      console.log('🔍 ProductService删除产品 - 请求数据:', requestData)

      const response = await api.post(`${this.baseUrl}/user/delete`, requestData)
      return response
    } catch (error) {
      console.error('Failed to delete product:', error)
      throw error
    }
  }

  /**
   * Submit single product to knowledge base
   * 提交单个产品到知识库
   * @param {Object} params - Request parameters
   * @param {number} params.产品id - Product ID
   * @param {string} params.产品名称 - Product name
   * @param {string} params.产品描述 - Product description
   * @param {string} params.产品分类 - Product category
   * @returns {Promise<Object>} Submit result
   */
  async submitToKnowledge(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/submit-to-knowledge`, {
        产品id: params.产品id,
        产品名称: params.产品名称,
        产品描述: params.产品描述,
        产品分类: params.产品分类,
        知识id: params.知识id
      })
      return response
    } catch (error) {
      console.error('Failed to submit to knowledge base:', error)
      throw error
    }
  }

  /**
   * Resubmit product to knowledge base (overwrite existing)
   * 重新提交产品到知识库（覆盖现有文档）
   * @param {Object} params - Request parameters
   * @param {number} params.产品id - Product ID
   * @param {string} params.产品名称 - Product name
   * @param {string} params.知识id - Knowledge base ID
   * @param {boolean} params.强制覆盖 - Force overwrite
   * @returns {Promise<Object>} Resubmit result
   */
  async resubmitToKnowledge(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/resubmit-to-knowledge`, {
        产品id: params.产品id,
        产品名称: params.产品名称,
        知识id: params.知识id,
        强制覆盖: params.强制覆盖
      })
      return response
    } catch (error) {
      console.error('Failed to resubmit to knowledge base:', error)
      throw error
    }
  }

  /**
   * Import products to knowledge base
   * 导入产品到知识库
   * @param {Object} params - Request parameters
   * @param {number} params.套餐关联id - Package association ID
   * @returns {Promise<Object>} Import result
   */
  async importToKnowledge(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/import-knowledge`, {
        套餐关联id: params.套餐关联id
      })
      return response
    } catch (error) {
      console.error('Failed to import to knowledge base:', error)
      throw error
    }
  }

  /**
   * Update single product in knowledge base
   * 更新单个产品到知识库
   * @param {Object} params - Request parameters
   * @param {number} params.产品id - Product ID
   * @param {number} params.套餐关联id - Package association ID
   * @returns {Promise<Object>} Update result
   */
  async updateKnowledge(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/update-knowledge`, {
        产品id: params.产品id,
        套餐关联id: params.套餐关联id
      })
      return response
    } catch (error) {
      console.error('Failed to update knowledge base:', error)
      throw error
    }
  }

  /**
   * Get knowledge base submission options
   * 获取知识库提交选项（包括默认知识库和自定义知识库）
   * @returns {Promise<Object>} Knowledge base options for product submission
   */
  async getKnowledgeBaseOptions() {
    try {
      const response = await api.post('/ai/get_knowledge_base_options')
      return response
    } catch (error) {
      console.error('Failed to fetch knowledge base options:', error)
      throw error
    }
  }

  /**
   * Get knowledge base packages
   * 获取知识库套餐列表
   * @returns {Promise<Object>} Knowledge packages list
   */
  async getKnowledgePackages() {
    try {
      // TODO: 实现获取知识库套餐的API调用
      // const response = await api.get('/knowledge/packages')

      // 临时返回模拟数据
      return {
        status: 100,
        data: [
          { id: 1, name: '基础套餐', description: '基础知识库套餐' },
          { id: 2, name: '专业套餐', description: '专业知识库套餐' },
          { id: 3, name: '企业套餐', description: '企业知识库套餐' }
        ],
        message: '获取成功'
      }
    } catch (error) {
      console.error('Failed to fetch knowledge packages:', error)
      throw error
    }
  }

  /**
   * Get knowledge base status
   * 获取知识库状态
   * @param {number} packageId - Package ID
   * @returns {Promise<Object>} Knowledge base status
   */
  async getKnowledgeStatus(packageId) {
    try {
      const response = await api.post(`${this.baseUrl}/knowledge-status`, {
        套餐关联id: packageId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch knowledge status:', error)
      throw error
    }
  }

  /**
   * Delete product from knowledge base
   * 从知识库删除产品
   * @param {Object} params - Request parameters
   * @param {number} params.产品id - Product ID
   * @param {number} params.套餐关联id - Package association ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteFromKnowledge(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/delete-knowledge`, {
        产品id: params.产品id,
        套餐关联id: params.套餐关联id
      })
      return response
    } catch (error) {
      console.error('Failed to delete from knowledge base:', error)
      throw error
    }
  }

  /**
   * Get knowledge documents list
   * 获取知识库文档列表
   * @param {number} packageId - Package ID
   * @returns {Promise<Object>} Knowledge documents list
   */
  async getKnowledgeDocuments(packageId) {
    try {
      const response = await api.post(`${this.baseUrl}/knowledge-docs`, {
        套餐关联id: packageId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch knowledge documents:', error)
      throw error
    }
  }

  /**
   * Update product card file path
   * 更新产品手卡文件路径
   * @param {Object} params - Request parameters
   * @param {number} params.产品id - Product ID
   * @param {string} params.文件路径 - File path
   * @returns {Promise<Object>} Update result
   */
  async updateCardPath(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/update-card-path`, {
        产品id: params.产品id,
        文件路径: params.文件路径
      })
      return response
    } catch (error) {
      console.error('Failed to update card path:', error)
      throw error
    }
  }

  /**
   * Get product card file path
   * 获取产品手卡文件路径
   * @param {number} productId - Product ID
   * @returns {Promise<Object>} Card file path
   */
  async getCardPath(productId) {
    try {
      const response = await api.post(`${this.baseUrl}/get-card-path`, {
        产品id: productId
      })
      return response
    } catch (error) {
      console.error('Failed to get card path:', error)
      throw error
    }
  }
}

// 创建并导出服务实例
const productService = new ProductService()
export default productService

// 导出服务类供其他地方使用
export { ProductService }
