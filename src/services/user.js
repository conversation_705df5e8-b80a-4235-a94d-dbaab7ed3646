import api from './api'

/**
 * 用户相关API服务
 */
export const userAPI = {
  /**
   * 获取用户权限信息
   * @returns {Promise} API响应
   */
  async getPermissions() {
    const response = await api.get('/get_permission')
    return response
  },

  /**
   * 获取用户订单列表
   * @returns {Promise} API响应
   */
  async getOrders() {
    const response = await api.get('/get_order')
    return response
  },

  /**
   * 获取用户详细信息
   * @returns {Promise} API响应
   */
  async getUserInfo() {
    const response = await api.get('/get_permission')
    return response
  },

  /**
   * 获取用户完整信息（包含详细资料）- 已废弃，使用下面的新方法
   * @deprecated 请使用新的getUserFullInfo方法
   * @returns {Promise} API响应
   */
  async getUserFullInfoOld() {
    const response = await api.get('/get_permission')
    return response
  },

  /**
   * 设置用户昵称
   * @param {Object} nicknameData - 昵称数据
   * @param {string} nicknameData.昵称 - 新昵称
   * @returns {Promise} API响应
   */
  async setNickname(nicknameData) {
    const response = await api.post('/users/set_nickname', nicknameData)
    return response
  },

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.old_password - 旧密码
   * @param {string} passwordData.new_password - 新密码
   * @returns {Promise} API响应
   */
  async changePassword(passwordData) {
    const response = await api.post('/update_password', passwordData)
    return response
  },

  /**
   * 设置样品自动审核
   * @param {Object} settingsData - 自动审核设置数据
   * @param {number} settingsData.是否自动审核 - 是否启用自动审核：0=关闭，1=开启
   * @returns {Promise} API响应
   */
  async setAutoAuditSettings(settingsData) {
    console.log('🔄 调用设置自动审核API:', settingsData)
    const response = await api.post('/auto-audit-settings', settingsData)
    console.log('📥 设置自动审核API响应:', response)
    return response
  },

  /**
   * 获取样品自动审核设置
   * @returns {Promise} API响应
   */
  async getAutoAuditSettings() {
    console.log('🔄 调用获取自动审核设置API')
    const response = await api.post('/get-auto-audit-settings', {})
    console.log('📥 获取自动审核设置API响应:', response)
    return response
  },

  /**
   * 上传头像
   * @param {FormData} formData - 包含头像文件的表单数据
   * @returns {Promise} API响应
   */
  async uploadAvatar(formData) {
    const response = await api.post('/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response
  },

  /**
   * 发送短信验证码
   * @param {Object} smsData - 短信数据
   * @param {string} smsData.phone - 手机号
   * @param {string} smsData.类型 - 验证码类型
   * @returns {Promise} API响应
   */
  async sendSmsCode(smsData) {
    const response = await api.post('/sms_code', smsData)
    return response
  },

  /**
   * 获取用户关联店铺
   * @returns {Promise} API响应
   */
  async getUserShops() {
    const response = await api.get('/shops')
    return response
  },

  /**
   * 获取用户团队信息
   * @returns {Promise} API响应
   */
  async getUserTeams() {
    const response = await api.get('/teams')
    return response
  },

  /**
   * 获取用户完整信息（包含算力值、邀约次数等）
   * @returns {Promise} API响应
   */
  async getUserFullInfo() {
    const response = await api.get('/full-info')
    return response
  },

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.old_password - 旧密码
   * @param {string} passwordData.new_password - 新密码
   * @returns {Promise} API响应
   */
  async updatePassword(passwordData) {
    const response = await api.post('/update_password', passwordData)
    return response
  },

  /**
   * 通过手机号查找用户
   * @param {Object} searchData - 搜索数据
   * @param {string} searchData.phone - 手机号
   * @returns {Promise} API响应
   */
  async findByPhone(searchData) {
    const response = await api.post('/find-by-phone', {
      手机号: searchData.phone // 后端期望中文字段名
    })
    return response
  },

  /**
   * 智能搜索用户
   * @param {Object} searchData - 搜索数据
   * @param {string} searchData.keyword - 搜索关键词
   * @param {boolean} [searchData.fuzzyMatch=true] - 是否模糊匹配
   * @param {number} [searchData.limit=10] - 限制数量
   * @param {string} [searchData.userType] - 用户类型
   * @param {string} [searchData.status='active'] - 状态筛选
   * @returns {Promise} API响应
   */
  async smartSearch(searchData) {
    const response = await api.post('/smart-search', {
      搜索关键词: searchData.keyword,
      模糊匹配: searchData.fuzzyMatch ?? true,
      限制数量: searchData.limit ?? 10,
      用户类型: searchData.userType,
      状态筛选: searchData.status ?? 'active'
    })
    return response
  },

  /**
   * 获取推广用户列表
   * @param {Object} listData - 列表查询数据
   * @param {number} [listData.页码=1] - 页码
   * @param {number} [listData.每页数量=10] - 每页数量
   * @param {string} [listData.搜索关键词] - 搜索关键词
   * @returns {Promise} API响应
   */
  async getPromotedUsers(listData) {
    const response = await api.post('/users/promoted_list', {
      页码: listData.页码 ?? 1,
      每页数量: listData.每页数量 ?? 10,
      搜索关键词: listData.搜索关键词
    })
    return response
  },

  /**
   * 获取公司用户列表
   * @param {Object} listData - 列表查询数据
   * @param {number} listData.companyId - 公司ID
   * @param {number} [listData.pageSize=20] - 每页数量
   * @param {number} [listData.page=1] - 页码
   * @param {string} [listData.searchKeyword] - 搜索关键词
   * @returns {Promise} API响应
   */
  async getCompanyUsers(listData) {
    const response = await api.post('/company-users', {
      公司ID: listData.companyId,
      每页数量: listData.pageSize ?? 20,
      页码: listData.page ?? 1,
      搜索关键词: listData.searchKeyword
    })
    return response
  },

  /**
   * 提交联系方式
   * @param {Object} contactData - 联系方式数据
   * @param {string} contactData.contact - 联系方式
   * @param {string} [contactData.type='微信'] - 类型
   * @param {string} contactData.source - 来源
   * @returns {Promise} API响应
   */
  async submitContact(contactData) {
    const response = await api.post('/submit_contact', {
      联系方式: contactData.contact,
      类型: contactData.type ?? '微信',
      来源: contactData.source
    })
    return response
  },

  /**
   * 邀请KOL
   * @param {Object} inviteData - 邀请数据
   * @param {string} inviteData.uid_number - UID号码
   * @param {string} inviteData.account_douyin - 抖音账号
   * @param {string} inviteData.nickname - 昵称
   * @returns {Promise} API响应
   */
  async inviteKol(inviteData) {
    const response = await api.post('/invite_kol', inviteData)
    return response
  },

  /**
   * 关联店铺
   * @param {Object} shopData - 店铺数据
   * @param {string} shopData.shop_id - 店铺id
   * @param {string} shopData.shop_name - 店铺名称
   * @param {string} shopData.avatar - 头像
   * @returns {Promise} API响应
   */
  async bindShop(shopData) {
    const response = await api.post('/bind_shop', shopData)
    return response
  },

  /**
   * 更新用户自定义邀请码
   * @param {Object} data - 更新数据
   * @param {string} data.用户自定义邀请码 - 用户自定义邀请码
   * @returns {Promise} API响应
   */
  async updateCustomInviteCode(data) {
    const response = await api.post('/user/update_custom_invite_code', data)
    return response
  },

  /**
   * 生成推广链接
   * @returns {Promise} API响应
   */
  async generatePromotionLink() {
    const response = await api.post('/user/generate_promotion_link')
    return response
  },

  /**
   * 验证自定义邀请码
   * @param {Object} data - 验证数据
   * @param {string} data.自定义邀请码 - 自定义邀请码
   * @returns {Promise} API响应
   */
  async validateCustomInviteCode(data) {
    const response = await api.post('/user/validate_custom_invite_code', data)
    return response
  },

  /**
   * 处理自定义邀请码注册关联
   * @param {Object} data - 关联数据
   * @param {string} data.手机号 - 注册手机号
   * @param {number} data.用户id - 注册用户id
   * @param {string} data.自定义邀请码 - 自定义邀请码
   * @returns {Promise} API响应
   */
  async processCustomInviteRegistration(data) {
    const response = await api.post('/user/process_custom_invite_registration', data)
    return response
  },

  /**
   * 获取推广统计数据
   * @returns {Promise} API响应
   */
  async getPromotionStats() {
    const response = await api.post('/user/get_promotion_stats')
    return response
  }
}

/**
 * 用户状态常量
 */
export const UserStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  BANNED: 'banned'
}

/**
 * 用户类型常量
 */
export const UserType = {
  NORMAL: 'normal',
  VIP: 'vip',
  ADMIN: 'admin',
  KOL: 'kol'
}

/**
 * 联系方式类型常量
 */
export const ContactType = {
  WECHAT: '微信',
  QQ: 'QQ',
  EMAIL: '邮箱',
  PHONE: '电话'
}

export default userAPI 