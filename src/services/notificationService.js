/**
 * 通知服务
 * 处理用户通知相关的API调用
 */

import api from './api'

/**
 * 智能轮询管理器
 * 根据页面可见性和用户活跃度智能调整轮询策略
 */
class SmartPollingManager {
  constructor() {
    this.pollingTimer = null
    this.isPageVisible = true
    this.lastActivity = Date.now()
    this.lastUpdateTime = 0
    this.updateCallbacks = new Set()
    this.cacheDuration = 30000 // 30秒缓存
    this.currentInterval = null // 记录当前轮询间隔，避免重复设置
    this.adjustPollingDebounceTimer = null // 防抖定时器

    // 轮询间隔配置（毫秒）
    this.intervals = {
      active: 90000,    // 活跃用户：90秒（从30秒优化到90秒）
      inactive: 300000, // 非活跃用户：5分钟
      background: 0     // 后台页面：停止轮询
    }

    this.initializeListeners()
  }
  
  /**
   * 初始化事件监听器
   */
  initializeListeners() {
    // 页面可见性变化监听
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        this.isPageVisible = !document.hidden
        this.handleVisibilityChange()
      })
      
      // 用户活跃度监听
      const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
      activityEvents.forEach(eventType => {
        document.addEventListener(eventType, () => {
          this.updateActivity()
        }, { passive: true })
      })
    }
  }
  
  /**
   * 更新用户活跃度
   */
  updateActivity() {
    this.lastActivity = Date.now()

    // 使用防抖机制，避免频繁调整轮询策略
    if (this.adjustPollingDebounceTimer) {
      clearTimeout(this.adjustPollingDebounceTimer)
    }

    this.adjustPollingDebounceTimer = setTimeout(() => {
      this.adjustPollingStrategy()
    }, 1000) // 1秒防抖
  }
  
  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (this.isPageVisible) {
      // 页面重新可见，立即刷新一次数据
      this.immediateUpdate()
      this.adjustPollingStrategy()
    } else {
      // 页面不可见，停止轮询
      this.stopPolling()
    }
  }
  
  /**
   * 获取当前应该使用的轮询间隔
   */
  getCurrentInterval() {
    if (!this.isPageVisible) {
      return this.intervals.background
    }
    
    const timeSinceLastActivity = Date.now() - this.lastActivity
    const inactiveThreshold = 5 * 60 * 1000 // 5分钟
    
    return timeSinceLastActivity > inactiveThreshold 
      ? this.intervals.inactive 
      : this.intervals.active
  }
  
  /**
   * 调整轮询策略
   */
  adjustPollingStrategy() {
    const newInterval = this.getCurrentInterval()

    if (newInterval === 0) {
      this.stopPolling()
      return
    }

    // 只有当间隔真正改变时才重新设置轮询，避免重复日志
    if (this.currentInterval !== newInterval) {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer)
        this.pollingTimer = null
      }

      this.startPolling(newInterval)
    }
  }
  
  /**
   * 开始轮询
   */
  startPolling(interval) {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }

    this.pollingTimer = setInterval(() => {
      this.executeUpdate()
    }, interval)

    // 记录当前间隔
    this.currentInterval = interval
    
    // 只在开发环境下打印轮询启动日志，减少生产环境日志量
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 智能轮询已启动，间隔: ${interval/1000}秒`)
    }
  }
  
  /**
   * 停止轮询
   */
  stopPolling() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
      this.currentInterval = null // 重置当前间隔
      
      // 只在开发环境下打印轮询停止日志，减少生产环境日志量
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 智能轮询已停止（页面不可见）')
      }
    }
  }
  
  /**
   * 立即更新（跳过缓存）
   */
  async immediateUpdate() {
    // 只在调试模式下打印日志，减少生产环境日志量
    if (process.env.NODE_ENV === 'development') {
      console.log('⚡ 立即更新通知数量')
    }
    this.lastUpdateTime = 0 // 重置缓存时间
    await this.executeUpdate()
  }
  
  /**
   * 执行更新（带缓存逻辑）
   */
  async executeUpdate() {
    const now = Date.now()

    // 检查缓存
    if (now - this.lastUpdateTime < this.cacheDuration) {
      // 减少缓存日志的打印频率，避免日志过多
      return
    }
    
    try {
      // 这里需要引用全局的notificationService实例
      const response = await notificationServiceInstance.getUnreadCount()
      if (response.status === 100 && response.data) {
        this.lastUpdateTime = now
        
        // 通知所有回调函数
        this.updateCallbacks.forEach(callback => {
          try {
            callback(response.data.未读数量)
          } catch (error) {
            console.error('回调函数执行失败:', error)
          }
        })
      }
    } catch (error) {
      console.error('智能轮询更新失败:', error)
    }
  }
  
  /**
   * 添加更新回调
   */
  addUpdateCallback(callback) {
    this.updateCallbacks.add(callback)
  }
  
  /**
   * 移除更新回调
   */
  removeUpdateCallback(callback) {
    this.updateCallbacks.delete(callback)
  }
  
  /**
   * 销毁轮询管理器
   */
  destroy() {
    this.stopPolling()
    this.updateCallbacks.clear()

    // 清理防抖定时器
    if (this.adjustPollingDebounceTimer) {
      clearTimeout(this.adjustPollingDebounceTimer)
      this.adjustPollingDebounceTimer = null
    }
  }
}

/**
 * 通知服务类
 */
class NotificationService {
  constructor() {
    // 初始化智能轮询管理器
    this.smartPollingManager = new SmartPollingManager()
  }

  /**
   * 获取通知列表
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码，从1开始
   * @param {number} params.每页数量 - 每页显示数量
   * @param {string} params.通知类型 - 通知类型筛选
   * @param {boolean} params.是否已读 - 已读状态筛选
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序顺序 - 排序顺序
   * @returns {Promise} API响应
   */
  async getNotificationList(params = {}) {
    const defaultParams = {
      页码: 1,
      每页数量: 20,
      排序字段: '创建时间',
      排序顺序: 'DESC'
    }
    
    const requestParams = { ...defaultParams, ...params }
    
    return await api.post('/user/notifications/list', requestParams)
  }

  /**
   * 获取未读通知数量
   * @returns {Promise} API响应
   */
  async getUnreadCount() {
    return await api.post('/user/notifications/unread-count', {})
  }

  /**
   * 标记单个通知为已读
   * @param {string|number} notificationId - 通知id，支持整数ID和announcement_XX格式
   * @returns {Promise} API响应
   */
  async markAsRead(notificationId) {
    // 保持通知id的原始格式，支持整数ID和announcement_XX格式
    if (!notificationId && notificationId !== 0) {
      throw new Error(`通知id不能为空: ${notificationId}`)
    }
    
    const result = await api.post('/user/notifications/mark-read', {
      通知id: String(notificationId)
    })
    
    // 标记已读后立即更新数量
    this.triggerImmediateUpdate()
    
    return result
  }

  /**
   * 批量标记通知为已读
   * @param {Array<string|number>} notificationIds - 通知id列表，支持整数ID和announcement_XX格式
   * @returns {Promise} API响应
   */
  async batchMarkAsRead(notificationIds) {
    // 保持通知id的原始格式，支持整数ID和announcement_XX格式
    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      throw new Error('通知id列表不能为空')
    }
    
    const validIds = notificationIds.map(id => {
      if (!id && id !== 0) {
        throw new Error(`通知id不能为空: ${id}`)
      }
      return String(id)
    })
    
    const result = await api.post('/user/notifications/batch-mark-read', {
      通知id列表: validIds
    })
    
    // 批量标记已读后立即更新数量
    this.triggerImmediateUpdate()
    
    return result
  }

  /**
   * 标记所有通知为已读
   * @returns {Promise} API响应
   */
  async markAllAsRead() {
    const result = await api.post('/user/notifications/mark-all-read', {})
    
    // 标记全部已读后立即更新数量
    this.triggerImmediateUpdate()
    
    return result
  }

  /**
   * 获取通知详情
   * @param {string|number} notificationId - 通知id，支持整数ID和announcement_XX格式
   * @returns {Promise} API响应
   */
  async getNotificationDetail(notificationId) {
    // 保持通知id的原始格式，支持整数ID和announcement_XX格式
    if (!notificationId && notificationId !== 0) {
      throw new Error(`通知id不能为空: ${notificationId}`)
    }
    
    return await api.post('/user/notifications/detail', {
      通知id: String(notificationId)
    })
  }

  /**
   * 获取通知中心数据
   * 包括未读数量、最新通知列表等
   * @returns {Promise} API响应
   */
  async getNotificationCenterData() {
    return await api.post('/user/notifications/center-data', {})
  }

  /**
   * 格式化通知类型显示
   * @param {string} type - 通知类型
   * @returns {string} 格式化后的类型名称
   */
  formatNotificationType(type) {
    const typeMap = {
      'system_update': '系统更新',
      'business': '业务通知',
      'invitation_status': '邀请状态',
      'sample_result': '样品结果',
      'membership_expiry': '会员到期',
      'system_maintenance': '系统维护'
    }
    return typeMap[type] || '其他'
  }

  /**
   * 格式化重要性级别显示
   * @param {number} level - 重要性级别
   * @returns {Object} 包含文本和样式的对象
   */
  formatImportanceLevel(level) {
    const levelMap = {
      1: { text: '普通', color: '#666', tag: 'default' },
      2: { text: '重要', color: '#fa8c16', tag: 'warning' },
      3: { text: '紧急', color: '#f5222d', tag: 'error' }
    }
    return levelMap[level] || levelMap[1]
  }

  /**
   * 格式化通知时间显示
   * @param {string} dateTime - 时间字符串
   * @returns {string} 友好的时间显示
   */
  formatNotificationTime(dateTime) {
    if (!dateTime) return ''
    
    const now = new Date()
    const time = new Date(dateTime)
    const diff = now - time
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    
    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < week) {
      return `${Math.floor(diff / day)}天前`
    } else if (diff < month) {
      return `${Math.floor(diff / week)}周前`
    } else {
      return time.toLocaleDateString('zh-CN')
    }
  }

  /**
   * 生成通知摘要
   * @param {Array} content - 通知内容数组
   * @param {number} maxLength - 最大长度
   * @returns {string} 摘要文本
   */
  generateNotificationSummary(content, maxLength = 50) {
    if (!Array.isArray(content) || content.length === 0) {
      return ''
    }
    
    const textContent = content
      .filter(item => item.类型 === '文本')
      .map(item => item.内容)
      .join(' ')
    
    if (textContent.length <= maxLength) {
      return textContent
    }
    
    return textContent.substring(0, maxLength) + '...'
  }

  /**
   * 获取通知图标
   * @param {string} type - 通知类型
   * @param {number} importance - 重要性级别
   * @returns {string} 图标名称
   */
  getNotificationIcon(type, importance) {
    if (importance >= 3) {
      return 'WarningOutlined'
    } else if (importance >= 2) {
      return 'ExclamationCircleOutlined'
    } else if (type === 'system_update') {
      return 'InfoCircleOutlined'
    } else {
      return 'BellOutlined'
    }
  }

  /**
   * 处理通知点击事件
   * @param {Object} notification - 通知对象
   * @param {Function} router - 路由对象
   */
  async handleNotificationClick(notification, router) {
    try {
      console.log('开始处理通知点击:', {
        id: notification.id,
        标题: notification.标题,
        是否已读: notification.是否已读,
        业务类型: notification.业务类型
      })

      // 如果未读，先标记为已读
      if (!notification.是否已读) {
        console.log('标记通知为已读:', notification.id)
        await this.markAsRead(notification.id)
        console.log('通知已标记为已读:', notification.id)
      }

      // 根据业务类型跳转到相应页面
      if (notification.业务类型) {
        console.log('根据业务类型跳转:', notification.业务类型)
        this.navigateToBusinessPage(notification.业务类型, notification.业务关联id, router)
      } else {
        console.log('通知无业务类型，仅标记已读')
      }
    } catch (error) {
      console.error('处理通知点击失败:', error)
      console.error('通知详情:', notification)
      // 重新抛出错误，让上层处理
      throw error
    }
  }

  /**
   * 根据业务类型导航到相应页面
   * @param {string} businessType - 业务类型
   * @param {string} businessId - 业务关联id
   * @param {Function} router - 路由对象
   */
  navigateToBusinessPage(businessType, businessId, router) {
    const navigationMap = {
      'invitation_status': () => router.push(`/talents/invitations?id=${businessId}`),
      'sample_result': () => router.push(`/samples?id=${businessId}`),
      'membership_expiry': () => router.push('/settings?tab=membership'),
      'system_maintenance': () => router.push('/dashboard')
    }

    const navigate = navigationMap[businessType]
    if (navigate) {
      navigate()
    } else {
      router.push('/dashboard')
    }
  }

  /**
   * 启动智能轮询（新版本）
   * @param {Function} callback - 回调函数
   * @returns {Function} 清理函数
   */
  startSmartPolling(callback) {
    // 添加回调
    this.smartPollingManager.addUpdateCallback(callback)
    
    // 立即执行一次更新
    this.smartPollingManager.immediateUpdate()
    
    // 启动智能轮询策略
    this.smartPollingManager.adjustPollingStrategy()
    
    // 返回清理函数
    return () => {
      this.smartPollingManager.removeUpdateCallback(callback)
    }
  }

  /**
   * 触发立即更新
   * 在用户执行关键操作后调用
   */
  triggerImmediateUpdate() {
    this.smartPollingManager.immediateUpdate()
  }

  /**
   * 轮询未读通知数量（保留旧版本兼容性）
   * @param {Function} callback - 回调函数
   * @param {number} interval - 轮询间隔（毫秒）
   * @returns {number} 定时器ID
   */
  startUnreadCountPolling(callback, interval = 90000) {
    console.warn('⚠️ 建议使用 startSmartPolling 替代 startUnreadCountPolling')
    
    const poll = async () => {
      try {
        const response = await this.getUnreadCount()
        if (response.status === 100 && response.data) {
          callback(response.data.未读数量)
        }
      } catch (error) {
        console.error('轮询未读通知数量失败:', error)
      }
    }

    // 立即执行一次
    poll()
    
    // 设置定时轮询
    return setInterval(poll, interval)
  }

  /**
   * 停止轮询
   * @param {number} timerId - 定时器ID
   */
  stopUnreadCountPolling(timerId) {
    if (timerId) {
      clearInterval(timerId)
    }
  }
}

// 创建服务实例
const notificationServiceInstance = new NotificationService()

// 导出服务实例
export default notificationServiceInstance
