import api from './api'

/**
 * 抖音商品管理服务
 * Douyin Products Management Service
 */
class DouyinService {
  constructor() {
    this.baseUrl = '/product/douyin'
  }

  /**
   * 获取抖音商品列表
   * Get Douyin products list
   * @param {Object} params - Request parameters
   * @param {number} params.页码 - Page number
   * @param {number} params.每页条数 - Items per page
   * @param {string} params.商品名称 - Product name filter
   * @param {string} params.平台 - Platform filter
   * @param {number} params.状态 - Status filter
   * @returns {Promise<Object>} Products list data
   */
  async getProductList(params = {}) {
    try {
      const requestData = {
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20
      }

      // 添加可选参数
      if (params.商品名称) {
        requestData.商品名称 = params.商品名称
      }
      if (params.平台) {
        requestData.平台 = params.平台
      }
      if (params.状态 !== undefined) {
        requestData.状态 = params.状态
      }

      const response = await api.post(`${this.baseUrl}/list`, requestData)
      return response
    } catch (error) {
      console.error('Failed to fetch Douyin products:', error)
      throw error
    }
  }

  /**
   * 获取抖音商品详情
   * Get Douyin product detail
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Product detail data
   */
  async getProductDetail(productId) {
    try {
      const response = await api.post(`${this.baseUrl}/detail`, {
        商品id: productId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch Douyin product detail:', error)
      throw error
    }
  }

  /**
   * 添加抖音商品
   * Add Douyin product
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Add result
   */
  async addProduct(productData) {
    try {
      const response = await api.post(`${this.baseUrl}/add`, productData)
      return response
    } catch (error) {
      console.error('Failed to add Douyin product:', error)
      throw error
    }
  }

  /**
   * 更新抖音商品
   * Update Douyin product
   * @param {Object} productData - Product data with ID
   * @returns {Promise<Object>} Update result
   */
  async updateProduct(productData) {
    try {
      const response = await api.post(`${this.baseUrl}/update`, productData)
      return response
    } catch (error) {
      console.error('Failed to update Douyin product:', error)
      throw error
    }
  }

  /**
   * 删除抖音商品
   * Delete Douyin product
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteProduct(productId) {
    try {
      const response = await api.post(`${this.baseUrl}/delete`, {
        商品id: productId
      })
      return response
    } catch (error) {
      console.error('Failed to delete Douyin product:', error)
      throw error
    }
  }

  /**
   * 同步抖音商品
   * Sync Douyin products
   * @param {Object} syncData - Sync configuration
   * @returns {Promise<Object>} Sync result
   */
  async syncProducts(syncData) {
    try {
      const response = await api.post(`${this.baseUrl}/sync`, syncData)
      return response
    } catch (error) {
      console.error('Failed to sync Douyin products:', error)
      throw error
    }
  }

  /**
   * 批量导入抖音商品
   * Batch import Douyin products
   * @param {FormData} formData - File data
   * @returns {Promise<Object>} Import result
   */
  async importProducts(formData) {
    try {
      const response = await api.post(`${this.baseUrl}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response
    } catch (error) {
      console.error('Failed to import Douyin products:', error)
      throw error
    }
  }

  /**
   * 导出抖音商品
   * Export Douyin products
   * @param {Object} exportParams - Export parameters
   * @returns {Promise<Object>} Export result
   */
  async exportProducts(exportParams = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/export`, exportParams, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Failed to export Douyin products:', error)
      throw error
    }
  }

  /**
   * 获取抖音商品分类
   * Get Douyin product categories
   * @returns {Promise<Object>} Categories data
   */
  async getCategories() {
    try {
      const response = await api.post(`${this.baseUrl}/categories`)
      return response
    } catch (error) {
      console.error('Failed to fetch Douyin categories:', error)
      throw error
    }
  }

  /**
   * 批量操作抖音商品
   * Batch operate Douyin products
   * @param {Object} batchData - Batch operation data
   * @returns {Promise<Object>} Batch operation result
   */
  async batchOperate(batchData) {
    try {
      const response = await api.post(`${this.baseUrl}/batch`, batchData)
      return response
    } catch (error) {
      console.error('Failed to batch operate Douyin products:', error)
      throw error
    }
  }
}

// 创建并导出服务实例
const douyinService = new DouyinService()
export default douyinService
