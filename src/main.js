import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 引入 Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 引入全局样式
import './assets/css/global.css'

const app = createApp(App)

// 配置应用，忽略特定的告警
app.config.warnHandler = (msg, instance, trace) => {
  // 忽略 Ant Design Vue 的 getInputElement 告警
  if (msg.includes('Customize `getInputElement`') || 
      msg.includes('allowClear') || 
      msg.includes('placeholder')) {
    return
  }
  // 其他告警正常显示
  console.warn(msg, instance, trace)
}

// 安装插件
app.use(createPinia())
app.use(router)
app.use(Antd)

// 挂载应用
app.mount('#app')

// 移除加载时的隐藏样式
document.getElementById('app').classList.add('loaded')

// 检查main.js中是否有导入api实例和setupApiFormatMonitoring函数的代码
// 如果没有，则添加相应的代码 