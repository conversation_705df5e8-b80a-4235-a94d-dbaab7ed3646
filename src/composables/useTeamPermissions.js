/**
 * 团队权限检查 Composable
 * 提供针对团队成员角色和权限的便捷检查方法
 */

import { computed } from 'vue'
import { useUserStore } from '../store/user'
import { teamPermissionService } from '../services/team/teamPermission'

/**
 * 团队权限管理组合函数
 * @returns {Object} 团队权限相关方法和计算属性
 */
export function useTeamPermissions() {
  const userStore = useUserStore()
  const currentUser = computed(() => userStore.userInfo)

  /**
   * 检查是否为创建者
   * @param {Object} team - 团队对象
   * @returns {boolean} 是否为团队创建者
   */
  const isCreator = (team) => {
    if (!team) return false
    
    return (
      team.我的角色 === '创始人' || 
      team.我的角色 === '创建者' || 
      team.创建人id === currentUser.value?.id ||
      team.权限状态?.是否团队创建者 === true
    )
  }

  /**
   * 检查是否为负责人
   * @param {Object} team - 团队对象
   * @returns {boolean} 是否为团队负责人
   */
  const isLeader = (team) => {
    if (!team) return false
    
    return (
      team.我的角色 === '负责人' || 
      team.我的角色 === '团队负责人' ||
      (team.权限状态?.用户角色 === '负责人')
    )
  }

  /**
   * 检查是否可以管理团队
   * @param {Object} team - 团队对象
   * @returns {boolean} 是否可以管理团队
   */
  const canManageTeam = (team) => {
    if (!team) return false
    
    // 创建者总是可以管理团队
    if (isCreator(team)) return true
    
    // 检查权限状态
    return !!team.权限状态?.能否管理团队
  }

  /**
   * 检查是否可以邀请成员
   * @param {Object} team - 团队对象
   * @returns {boolean} 是否可以邀请成员
   */
  const canInviteMembers = (team) => {
    if (!team) return false
    
    // 创建者总是可以邀请成员
    if (isCreator(team)) return true
    
    // 检查权限状态
    return !!team.权限状态?.能否邀请成员
  }

  /**
   * 检查是否可以移除成员
   * @param {Object} team - 团队对象
   * @param {Object} member - 成员对象
   * @returns {boolean} 是否可以移除成员
   */
  const canRemoveMember = (team, member) => {
    if (!team || !member) return false
    
    // 不能移除自己
    if (member.用户id === currentUser.value?.id) return false
    
    // 创建者可以移除任何人（除了自己）
    if (isCreator(team)) return true
    
    // 检查权限状态
    if (!team.权限状态?.能否移除成员) return false
    
    // 有权限的人不能移除创始人或负责人
    if (member.角色 === '创始人' || member.角色 === '负责人') return false
    
    return true
  }

  /**
   * 检查是否可以管理权限
   * @param {Object} team - 团队对象
   * @param {Object} member - 成员对象
   * @returns {boolean} 是否可以管理权限
   */
  const canManagePermissions = (team, member) => {
    if (!team || !member) return false
    
    // 不能修改自己的权限
    if (member.用户id === currentUser.value?.id) return false
    
    // 创建者可以修改任何人的权限（除了自己）
    if (isCreator(team)) return true
    
    // 检查权限状态
    if (!team.权限状态?.能否管理权限) return false
    
    // 有权限的人不能修改创始人或负责人的权限
    if (member.角色 === '创始人' || member.角色 === '负责人') return false
    
    return true
  }

  /**
   * 根据团队id获取权限状态
   * @param {number} teamId - 团队id
   * @returns {Promise<Object>} 权限状态对象
   */
  const getTeamPermissionStatus = async (teamId) => {
    try {
      const response = await teamPermissionService.checkUserTeamPermissions({
        团队id: teamId
      })
      
      if (response.status === 100) {
        return response.message || {}
      }
      throw new Error(response.message || '获取权限状态失败')
    } catch (error) {
      console.error('获取团队权限状态失败:', error)
      return {}
    }
  }

  return {
    isCreator,
    isLeader,
    canManageTeam,
    canInviteMembers,
    canRemoveMember,
    canManagePermissions,
    getTeamPermissionStatus
  }
} 