import { ref, reactive, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import teamService from '../services/team'

/**
 * 团队管理增强版 Composable 函数
 * 提供完整的团队管理功能，包括权限检查、错误处理、状态管理等
 */
export function useTeamManagement(options = {}) {
  const route = useRoute()
  const router = useRouter()
  
  // 基础状态
  const loading = ref(false)
  const submitting = ref(false)
  const error = ref(null)
  
  // 团队数据
  const teams = ref([])
  const currentTeam = ref(null)
  const teamMembers = ref([])
  const teamStats = ref({})
  const userPermissions = ref(null)
  
  // 筛选和分页
  const filters = reactive({
    团队关系类型: route.query.type || '',
    搜索关键词: route.query.search || '',
    成员状态: '正常',
    公司ID: null
  })
  
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    onChange: (page, pageSize) => {
      pagination.current = page
      pagination.pageSize = pageSize
      if (options.autoLoad !== false) {
        loadTeams()
      }
    }
  })
  
  // 计算属性
  const hasTeams = computed(() => teams.value.length > 0)
  const isTeamCreator = computed(() => userPermissions.value?.isCreator || false)
  const isTeamLeader = computed(() => userPermissions.value?.isLeader || false)
  const isTeamAdmin = computed(() => userPermissions.value?.isAdmin || false)
  const canManageTeam = computed(() => isTeamCreator.value || isTeamLeader.value || isTeamAdmin.value)
  
  // 错误处理
  const handleError = (err, defaultMessage = '操作失败') => {
    const errorMessage = err?.message || err || defaultMessage
    error.value = errorMessage
    message.error(errorMessage)
    console.error('团队管理错误:', err)
  }
  
  // 成功提示
  const handleSuccess = (successMessage, callback) => {
    message.success(successMessage)
    if (typeof callback === 'function') {
      callback()
    }
  }
  
  // 加载团队列表
  const loadTeams = async (showLoading = true) => {
    try {
      if (showLoading) loading.value = true
      error.value = null
      
      // 更新URL查询参数
      const query = { ...route.query }
      if (filters.团队关系类型) query.type = filters.团队关系类型
      if (filters.搜索关键词) query.search = filters.搜索关键词
      router.replace({ query })
      
      const response = await teamService.getUserTeams({
        页码: pagination.current,
        每页数量: pagination.pageSize,
        ...filters
      })
      
      if ([100, 0, 1].includes(response.status)) {
        const data = response.data || {}
        teams.value = data.团队列表 || data.list || []
        pagination.total = data.总数 || data.total || 0
      } else {
        throw new Error(response.message || '获取团队列表失败')
      }
    } catch (err) {
      handleError(err, '加载团队列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 加载团队详情
  const loadTeamDetail = async (teamId, userId = null) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await teamService.getTeamDetail(teamId, userId)
      
      if ([100, 0, 1].includes(response.status)) {
        currentTeam.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取团队详情失败')
      }
    } catch (err) {
      handleError(err, '加载团队详情失败')
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 加载团队成员列表
  const loadTeamMembers = async (teamId, params = {}) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await teamService.getTeamMembers({
        团队id: teamId,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        搜索关键词: params.搜索关键词,
        成员状态: params.成员状态 || filters.成员状态
      })
      
      if ([100, 0, 1].includes(response.status)) {
        const data = response.data || {}
        teamMembers.value = data.成员列表 || data.list || []
        return data
      } else {
        throw new Error(response.message || '获取团队成员失败')
      }
    } catch (err) {
      handleError(err, '加载团队成员失败')
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 检查用户权限
  const checkUserPermissions = async (teamId, userId = null) => {
    try {
      const response = await teamService.getUserTeamPermissionStatus({
        团队id: teamId,
        用户id: userId
      })
      
      if ([100, 0, 1].includes(response.status)) {
        userPermissions.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取权限状态失败')
      }
    } catch (err) {
      handleError(err, '检查权限失败')
      return null
    }
  }
  
  // 邀请成员（手机号）
  const inviteMemberByPhone = async (params) => {
    try {
      submitting.value = true
      error.value = null
      
      const response = await teamService.inviteByPhone(params)
      
      if ([100, 0, 1].includes(response.status)) {
        handleSuccess('邀请发送成功', () => {
          // 刷新成员列表
          if (params.团队id) {
            loadTeamMembers(params.团队id)
          }
        })
        return true
      } else {
        throw new Error(response.message || '邀请发送失败')
      }
    } catch (err) {
      handleError(err, '邀请失败')
      return false
    } finally {
      submitting.value = false
    }
  }
  
  // 邀请成员（直接邀请）
  const inviteMemberDirect = async (params) => {
    try {
      submitting.value = true
      error.value = null
      
      const response = await teamService.inviteDirect(params)
      
      if ([100, 0, 1].includes(response.status)) {
        handleSuccess('邀请发送成功', () => {
          // 刷新成员列表
          if (params.团队id) {
            loadTeamMembers(params.团队id)
          }
        })
        return true
      } else {
        throw new Error(response.message || '邀请发送失败')
      }
    } catch (err) {
      handleError(err, '邀请失败')
      return false
    } finally {
      submitting.value = false
    }
  }
  
  // 踢出成员（带确认）
  const removeMemberWithConfirm = async (params) => {
    return new Promise((resolve) => {
      Modal.confirm({
        title: '确认踢出成员',
        content: `确定要将该成员踢出团队吗？此操作不可撤销。`,
        okText: '确认踢出',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            submitting.value = true
            const response = await teamService.removeMember(params)
            
            if ([100, 0, 1].includes(response.status)) {
              handleSuccess('成员已踢出', () => {
                // 刷新成员列表
                if (params.团队id) {
                  loadTeamMembers(params.团队id)
                }
              })
              resolve(true)
            } else {
              throw new Error(response.message || '踢出成员失败')
            }
          } catch (err) {
            handleError(err, '踢出成员失败')
            resolve(false)
          } finally {
            submitting.value = false
          }
        },
        onCancel: () => resolve(false)
      })
    })
  }
  
  // 解散团队（带确认）
  const dissolveTeamWithConfirm = async (teamId, teamName = '该团队') => {
    return new Promise((resolve) => {
      Modal.confirm({
        title: '确认解散团队',
        content: `确定要解散"${teamName}"吗？解散后所有成员将被移除，团队数据将被清空，此操作不可撤销！`,
        okText: '确认解散',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            submitting.value = true
            const response = await teamService.dissolveTeam(teamId)
            
            if ([100, 0, 1].includes(response.status)) {
              handleSuccess('团队已解散', () => {
                // 导航回团队列表
                router.push('/teams')
                loadTeams()
              })
              resolve(true)
            } else {
              throw new Error(response.message || '解散团队失败')
            }
          } catch (err) {
            handleError(err, '解散团队失败')
            resolve(false)
          } finally {
            submitting.value = false
          }
        },
        onCancel: () => resolve(false)
      })
    })
  }
  
  // 创建团队
  const createTeam = async (params) => {
    try {
      submitting.value = true
      error.value = null
      
      const response = await teamService.createTeam(params)
      
      if ([100, 0, 1].includes(response.status)) {
        const teamId = response.data?.team_id || response.team_id
        handleSuccess('团队创建成功', () => {
          // 导航到新团队详情页
          if (teamId) {
            router.push(`/teams/${teamId}`)
          } else {
            router.push('/teams')
          }
        })
        return response.data
      } else {
        throw new Error(response.message || '创建团队失败')
      }
    } catch (err) {
      handleError(err, '创建团队失败')
      return null
    } finally {
      submitting.value = false
    }
  }
  
  // 搜索防抖
  let searchTimer = null
  const debounceSearch = (callback, delay = 500) => {
    clearTimeout(searchTimer)
    searchTimer = setTimeout(() => {
      pagination.current = 1
      if (typeof callback === 'function') {
        callback()
      } else {
        loadTeams()
      }
    }, delay)
  }
  
  // 重置筛选
  const resetFilters = () => {
    filters.团队关系类型 = ''
    filters.搜索关键词 = ''
    filters.成员状态 = '正常'
    filters.公司ID = null
    pagination.current = 1
    if (options.autoLoad !== false) {
      loadTeams()
    }
  }
  
  // 刷新当前数据
  const refresh = () => {
    if (currentTeam.value) {
      loadTeamDetail(currentTeam.value.团队id)
      loadTeamMembers(currentTeam.value.团队id)
    } else {
      loadTeams()
    }
  }
  
  // 监听筛选条件变化
  watch(() => filters.团队关系类型, () => {
    pagination.current = 1
    if (options.autoLoad !== false) {
      loadTeams()
    }
  })
  
  watch(() => filters.搜索关键词, () => {
    if (options.autoLoad !== false) {
      debounceSearch()
    }
  })
  
  // 初始化加载
  if (options.autoLoad !== false) {
    loadTeams()
  }
  
  return {
    // 状态
    loading,
    submitting,
    error,
    
    // 数据
    teams,
    currentTeam,
    teamMembers,
    teamStats,
    userPermissions,
    
    // 筛选和分页
    filters,
    pagination,
    
    // 计算属性
    hasTeams,
    isTeamCreator,
    isTeamLeader,
    isTeamAdmin,
    canManageTeam,
    
    // 方法
    loadTeams,
    loadTeamDetail,
    loadTeamMembers,
    checkUserPermissions,
    inviteMemberByPhone,
    inviteMemberDirect,
    removeMemberWithConfirm,
    dissolveTeamWithConfirm,
    createTeam,
    debounceSearch,
    resetFilters,
    refresh,
    
    // 工具方法
    handleError,
    handleSuccess
  }
} 