"""
团队管理工具库
统一管理团队相关的公共函数、数据处理、验证逻辑等
"""

import re
from datetime import datetime
from typing import Dict, List, Union, Tuple

# 使用统一日志系统
from 日志 import 应用日志器 as logger

class 团队状态:
    """团队状态枚举"""
    正常 = "正常"
    已解散 = "已解散"
    已暂停 = "已暂停"

class 角色类型:
    """角色类型枚举"""
    创始人 = "founder"
    负责人 = "leader"
    管理员 = "admin"
    成员 = "member"
    自定义 = "custom"

class 邀请状态:
    """邀请状态枚举"""
    待处理 = "待处理"
    已接受 = "已接受"
    已拒绝 = "已拒绝"
    已过期 = "已过期"
    已撤销 = "已撤销"

def 验证手机号格式(手机号: str) -> bool:
    """
    验证手机号格式是否正确
    
    Args:
        手机号: 待验证的手机号字符串
        
    Returns:
        bool: 格式是否正确
    """
    if not 手机号 or not isinstance(手机号, str):
        return False
    
    # 中国手机号正则：1开头，第二位是3-9，总共11位数字
    手机号正则 = r'^1[3-9]\d{9}$'
    return bool(re.match(手机号正则, 手机号.strip()))

def 验证邮箱格式(邮箱: str) -> bool:
    """
    验证邮箱格式是否正确
    
    Args:
        邮箱: 待验证的邮箱字符串
        
    Returns:
        bool: 格式是否正确
    """
    if not 邮箱 or not isinstance(邮箱, str):
        return False
    
    邮箱正则 = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    return bool(re.match(邮箱正则, 邮箱.strip()))

def 获取角色级别(角色类型: str) -> int:
    """
    获取角色的权限级别（数字越大权限越高）
    
    Args:
        角色类型: 角色类型字符串
        
    Returns:
        int: 权限级别
    """
    级别映射 = {
        角色类型.创始人: 100,
        角色类型.负责人: 80,
        角色类型.管理员: 60,
        角色类型.自定义: 40,
        角色类型.成员: 20
    }
    return 级别映射.get(角色类型, 0)

def 比较角色权限(角色1: str, 角色2: str) -> int:
    """
    比较两个角色的权限级别
    
    Args:
        角色1: 第一个角色
        角色2: 第二个角色
        
    Returns:
        int: 1表示角色1权限更高，-1表示角色2权限更高，0表示相等
    """
    级别1 = 获取角色级别(角色1)
    级别2 = 获取角色级别(角色2)
    
    if 级别1 > 级别2:
        return 1
    elif 级别1 < 级别2:
        return -1
    else:
        return 0

def 检查管理权限(角色类型: str) -> bool:
    """
    检查角色是否拥有管理权限
    
    Args:
        角色类型: 角色类型字符串
        
    Returns:
        bool: 是否有管理权限
    """
    管理角色列表 = [角色类型.创始人, 角色类型.负责人, 角色类型.管理员]
    return 角色类型 in 管理角色列表

def 格式化时间(时间对象: Union[datetime, str], 包含时间: bool = True) -> str:
    """
    格式化时间显示
    
    Args:
        时间对象: datetime对象或时间字符串
        包含时间: 是否包含具体时间
        
    Returns:
        str: 格式化后的时间字符串
    """
    if not 时间对象:
        return "暂无"
    
    try:
        if isinstance(时间对象, str):
            时间对象 = datetime.fromisoformat(时间对象.replace('Z', '+00:00'))
        
        if 包含时间:
            return 时间对象.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return 时间对象.strftime('%Y-%m-%d')
    except Exception as e:
        logger.warning(f"时间格式化失败: {e}")
        return "无效时间"

def 计算相对时间(时间对象: Union[datetime, str]) -> str:
    """
    计算相对时间（如：3天前、2小时前）
    
    Args:
        时间对象: datetime对象或时间字符串
        
    Returns:
        str: 相对时间字符串
    """
    if not 时间对象:
        return "暂无"
    
    try:
        if isinstance(时间对象, str):
            时间对象 = datetime.fromisoformat(时间对象.replace('Z', '+00:00'))
        
        现在时间 = datetime.now()
        时间差 = 现在时间 - 时间对象
        
        天数差 = 时间差.days
        小时差 = 时间差.seconds // 3600
        分钟差 = 时间差.seconds // 60
        
        if 天数差 > 7:
            return 格式化时间(时间对象, False)
        elif 天数差 > 0:
            return f"{天数差}天前"
        elif 小时差 > 0:
            return f"{小时差}小时前"
        elif 分钟差 > 0:
            return f"{分钟差}分钟前"
        else:
            return "刚刚"
    except Exception as e:
        logger.warning(f"相对时间计算失败: {e}")
        return "无效时间"

def 生成邀请码(长度: int = 8) -> str:
    """
    生成邀请码
    
    Args:
        长度: 邀请码长度
        
    Returns:
        str: 邀请码
    """
    import random
    import string
    
    字符集 = string.ascii_uppercase + string.digits
    # 排除容易混淆的字符
    字符集 = 字符集.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
    
    return ''.join(random.choice(字符集) for _ in range(长度))

def 验证团队名称(团队名称: str) -> Tuple[bool, str]:
    """
    验证团队名称格式
    
    Args:
        团队名称: 待验证的团队名称
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not 团队名称 or not isinstance(团队名称, str):
        return False, "团队名称不能为空"
    
    团队名称 = 团队名称.strip()
    
    if len(团队名称) < 2:
        return False, "团队名称长度不能少于2个字符"
    
    if len(团队名称) > 50:
        return False, "团队名称长度不能超过50个字符"
    
    # 检查是否包含特殊字符
    禁用字符 = ['<', '>', '&', '"', "'", '\\', '/', '%']
    for 字符 in 禁用字符:
        if 字符 in 团队名称:
            return False, f"团队名称不能包含特殊字符: {字符}"
    
    return True, ""

def 计算成员进度百分比(当前成员数: int, 最大成员数: int) -> int:
    """
    计算团队成员完成度百分比
    
    Args:
        当前成员数: 当前成员数量
        最大成员数: 最大成员数量
        
    Returns:
        int: 百分比（0-100）
    """
    if not 最大成员数 or 最大成员数 <= 0:
        return 0
    
    if not 当前成员数 or 当前成员数 <= 0:
        return 0
    
    进度 = (当前成员数 / 最大成员数) * 100
    return min(int(进度), 100)

def 检查邀请限制(当前成员数: int, 最大成员数: int, 待处理邀请数: int = 0) -> Tuple[bool, str]:
    """
    检查是否可以发送更多邀请
    
    Args:
        当前成员数: 当前成员数量
        最大成员数: 最大成员数量
        待处理邀请数: 待处理的邀请数量
        
    Returns:
        Tuple[bool, str]: (是否可以邀请, 限制说明)
    """
    if not 最大成员数 or 最大成员数 <= 0:
        return True, "无成员数量限制"
    
    总数 = 当前成员数 + 待处理邀请数
    
    if 总数 >= 最大成员数:
        return False, f"已达到成员上限（{最大成员数}人），请先处理待处理的邀请或扩容"
    
    剩余名额 = 最大成员数 - 总数
    return True, f"还可以邀请{剩余名额}人"

def 标准化API响应(状态码: int, 数据: any = None, 消息: str = "") -> Dict:
    """
    标准化API响应格式
    
    Args:
        状态码: 状态码
        数据: 响应数据
        消息: 响应消息
        
    Returns:
        Dict: 标准化的响应数据
    """
    是否成功 = 状态码 in [100, 0, 1]
    
    if not 消息:
        消息 = "操作成功" if 是否成功 else "操作失败"
    
    return {
        "status": 状态码,
        "message": 消息,
        "data": 数据,
        "success": 是否成功,
        "timestamp": datetime.now().isoformat()
    }

def 清理敏感数据(数据: Dict, 敏感字段: List[str] = None) -> Dict:
    """
    清理响应数据中的敏感信息
    
    Args:
        数据: 原始数据字典
        敏感字段: 需要清理的敏感字段列表
        
    Returns:
        Dict: 清理后的数据
    """
    if not isinstance(数据, dict):
        return 数据
    
    if 敏感字段 is None:
        敏感字段 = ['密码', 'password', '令牌', 'token', '密钥', 'secret']
    
    清理后数据 = 数据.copy()
    
    for 字段 in 敏感字段:
        if 字段 in 清理后数据:
            del 清理后数据[字段]
    
    return 清理后数据

def 记录操作日志(操作类型: str, 操作者ID: int, 目标资源: str, 详细信息: Dict = None):
    """
    记录用户操作日志
    
    Args:
        操作类型: 操作类型（如：创建团队、邀请成员等）
        操作者ID: 操作者用户id
        目标资源: 目标资源标识
        详细信息: 操作的详细信息
    """
    try:
        日志信息 = {
            "时间": datetime.now().isoformat(),
            "操作类型": 操作类型,
            "操作者ID": 操作者ID,
            "目标资源": 目标资源,
            "详细信息": 详细信息 or {},
            "IP地址": "待获取",  # 实际使用时需要从请求中获取
        }
        
        logger.info(f"用户操作日志: {日志信息}")
        
        # 这里可以扩展为写入数据库或专门的日志系统
        
    except Exception as e:
        logger.error(f"记录操作日志失败: {e}")

def 分页处理(数据列表: List, 页码: int, 每页数量: int) -> Tuple[List, Dict]:
    """
    对数据进行分页处理
    
    Args:
        数据列表: 原始数据列表
        页码: 页码（从1开始）
        每页数量: 每页显示的数量
        
    Returns:
        Tuple[List, Dict]: (分页后的数据, 分页信息)
    """
    if not 数据列表:
        return [], {"总数": 0, "页码": 页码, "每页数量": 每页数量, "总页数": 0}
    
    总数 = len(数据列表)
    总页数 = (总数 + 每页数量 - 1) // 每页数量
    
    # 确保页码在有效范围内
    页码 = max(1, min(页码, 总页数)) if 总页数 > 0 else 1
    
    开始索引 = (页码 - 1) * 每页数量
    结束索引 = 开始索引 + 每页数量
    
    分页数据 = 数据列表[开始索引:结束索引]
    
    分页信息 = {
        "总数": 总数,
        "页码": 页码,
        "每页数量": 每页数量,
        "总页数": 总页数,
        "是否有上一页": 页码 > 1,
        "是否有下一页": 页码 < 总页数
    }
    
    return 分页数据, 分页信息


async def 记录团队操作日志(
    团队id: int,
    操作人ID: int,
    操作类型: str,
    操作内容: str,
    备注: str = None
) -> bool:
    """
    记录团队操作日志到数据库
    
    Args:
        团队id: 团队id
        操作人ID: 操作人用户id
        操作类型: 操作类型（如：创建团队、邀请成员等）
        操作内容: 操作内容描述
        备注: 操作备注信息
        
    Returns:
        bool: 记录是否成功
    """
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
        from 日志 import 错误日志器
        
        插入SQL = """
        INSERT INTO 团队操作日志表 (团队id, 操作人ID, 操作类型, 操作内容, 操作时间)
        VALUES ($1, $2, $3, $4, NOW())
        """

        # 如果有备注，将其添加到操作内容中
        完整操作内容 = 操作内容
        if 备注:
            完整操作内容 += f" (备注: {备注})"

        await 异步连接池实例.执行插入(插入SQL, (团队id, 操作人ID, 操作类型, 完整操作内容))
        return True
        
    except Exception as e:
        from 日志 import 错误日志器
        错误日志器.error(f"记录团队操作日志失败: 团队id={团队id}, 操作人ID={操作人ID}, 错误={e}")
        return False